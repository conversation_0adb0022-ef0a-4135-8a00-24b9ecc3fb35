# YYBMac

## 环境配置

### 完整环境配置
运行以下命令进行完整的环境配置（安装Pod）：
```bash
cd shell
bash ./init_pro.sh
```

### copy 及 编译 模拟器包


指定qemu路径，直接运行以下脚本
```bash 
# path： qemu路径；
# type： 传all时，会执行一次rebuild。不传则在objs进行copy、codesign。
# 例如： ./copy_simulator.sh  /Users/<USER>/work/yyb-mac-engine/external/qemu all
cd yyb-mac-app/shell
bash ./copy_simulator.sh [path] [type]
```
备注：如果aosp有更新，需要替换新的image到yyb-mac-engine/external/qemu/yyb-mac/res/vms

## 构建说明

1. 打开 `YYBMacApp.xcodeproj`
2. 选择目标设备
3. 点击运行按钮或使用快捷键 Cmd+R 构建运行项目 

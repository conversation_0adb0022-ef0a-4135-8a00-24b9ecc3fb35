# JSAPI 文档

> 本文档由JSAPI文档生成器自动生成

## 目录

- [window](#module-window)
- [file](#module-file)
- [directory](#module-directory)
- [basicInfo](#module-basicInfo)
- [application](#module-application)
- [downloader](#module-downloader)
- [aria2ForMac](#module-aria2ForMac)
- [appManager](#module-appManager)

## <a name='module-window'></a> 模块: window

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/components/business-components/webview/JSAPI/module/YYBWindowModule.m`

### GetWebWindowId

**描述**: 向其他窗口广播消息

**参数**:

- `Dictionary` message 要广播的消息内容

**返回值**:

-  无

---

### GetApiList

**描述**: 添加广播消息监听器

**参数**:

-  无

**返回值**:

-  无

**事件**:

- `broadcastEvent`: 当收到广播消息时触发

  **事件数据 (broadcastEvent)**: 

  - `Dictionary` message 广播内容

---

### ShowTop

**描述**: 将窗口置顶显示

**参数**:

-  无

**返回值**:

-  无

---

### minimize

**描述**: 最小化窗口

**参数**:

-  无

**返回值**:

-  无

---

### close

**描述**: 关闭窗口

**参数**:

-  无

**返回值**:

-  无

---

### IsMaximize

**描述**: 窗口是否放大状态

**参数**:

-  无

**返回值**:

- `Boolean` 窗口是否放大状态

---

### OpenWebview

**描述**: 打开新的WebView窗口

**参数**:

- `JSWebViewObject` object WebView配置对象

**返回值**:

-  无

---

### CloseWebview

**描述**: 关闭指定的WebView窗口

**参数**:

- `String` windowId 要关闭的窗口ID

**返回值**:

-  无

---

### Broadcast

**描述**: 向其他窗口广播消息

**参数**:

- `Dictionary` message 要广播的消息内容

**返回值**:

-  无

---

### AddBroadcastListener

**描述**: 添加广播消息监听器

**参数**:

-  无

**返回值**:

-  无

**事件**:

- `broadcastEvent`: 当收到广播消息时触发

  **事件数据 (broadcastEvent)**: 

  - `Dictionary` message 广播内容

---

## <a name='module-file'></a> 模块: file

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/components/business-components/webview/JSAPI/module/YYBFileModule.m`

### Exists

**描述**: 检查文件是否存在

**参数**:

- `String` path 文件路径

**返回值**:

- `Number` 文件存在返回成功码，不存在返回错误码

---

### Delete

**描述**: 删除文件

**参数**:

- `String` path 要删除的文件路径

**返回值**:

- `Number` 文件存在返回成功码，不存在返回错误码

---

### getMd5

**描述**: 获取文件的MD5值

**参数**:

- `String` path 文件路径

**返回值**:

- `Object` 包含文件MD5值的对象

  **属性**:

  - `String` md5 文件的MD5值

---

## <a name='module-directory'></a> 模块: directory

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/components/business-components/webview/JSAPI/module/YYBDirectoryModule.m`

### Create

**描述**: 创建目录

**参数**:

- `String` path 要创建的目录路径

**返回值**:

- `Number` 创建目录成功返回成功码，不成功返回错误码

---

### OpenFilePath

**描述**: 在Finder中打开文件所在的目录并选中文件

**参数**:

- `String` path 文件路径

**返回值**:

- `Number` 打开目录成功返回成功码，不成功返回错误码

---

## <a name='module-basicInfo'></a> 模块: basicInfo

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/components/business-components/webview/JSAPI/module/YYBBasicInfoModule.mm`

### GetBaseboardVendor

**描述**: 获取主板型号

**参数**:

-  无

**返回值**:

- `String` 主板制造商名称

---

### GetFreeDiskSize

**描述**: 获取可用硬盘大小

**参数**:

-  无

**返回值**:

- `Number` 可用硬盘空间大小（字节）

---

### GetFreeMemorySize

**描述**: 获取可用内存大小

**参数**:

-  无

**返回值**:

- `Number` 可用内存大小（字节）

---

### GetAndroidFreeSize

**描述**: 获取Android设备可用存储空间大小

**参数**:

-  无

**返回值**:

- `Number` Android设备可用存储空间大小（字节）

---

### GetGraphicsInfos

**描述**: 获取图形信息

**参数**:

-  无

**返回值**:

- `Object` 图形信息对象

  **属性**:

  - `Object` primary_graphics 主显卡信息
  - `String` driver_version 驱动版本
  - `String` model 显卡型号
  - `String` opengl_version OpenGL版本
  - `String` vendor 厂商
  - `String` device_id 设备ID
  - `String` driver_date 驱动日期
  - `String` vendor_id 厂商ID（可选）
  - `Object` vice_graphics 副显卡信息（可选，结构同primary_graphics）

---

## <a name='module-application'></a> 模块: application

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/YYBMacApp/WebView/JSAPI/YYBApplicationModule.mm`

### GetMainVersion

**描述**: 获取客户端主版本号

**参数**:

-  无

**返回值**:

- `String` 客户端版本号

---

### GetClientType

**描述**: 获取客户端类型

**参数**:

-  无

**返回值**:

- `Number` 客户端类型码

---

### SetClientType

**描述**: 设置客户端类型

**参数**:

- `Number` type 客户端类型码

**返回值**:

-  无

---

### GetCpuInfo

**描述**: 获取CPU信息

**参数**:

-  无

**返回值**:

- `JSCPUInfo` CPU信息对象

  **属性**:

  - `Number` core_number CPU核心数
  - `Number` vendor CPU厂商类型(0:未知, 1:Intel, 2:AMD, 3:其他, 4:Apple)

---

### AddPseudoProtocolListener

**描述**: 添加伪协议监听器

**参数**:

-  无

**返回值**:

-  无

**事件**:

- `pseudoEvent`: 当收到伪协议时触发

  **事件数据 (pseudoEvent)**: 

  - `String|Array` 伪协议内容或伪协议数组

---

### GenerateDiagnosisZip

**描述**: 生成诊断日志压缩包

**参数**:

-  无

**返回值**:

- `String` 生成的诊断日志压缩包路径

---

### GetBootTimeUtcMsec

**描述**: 获取系统启动时间（UTC毫秒）

**参数**:

-  无

**返回值**:

- `Number` 系统启动时间（UTC毫秒）

---

### GetChannel

**描述**: 获取应用渠道号

**参数**:

-  无

**返回值**:

- `String` 应用渠道号

---

### GetGuid

**描述**: 获取设备GUID

**参数**:

-  无

**返回值**:

- `String` 设备GUID（Qimei36）

---

### NotifyAppState

**描述**: 通知应用状态变化

**参数**:

- `YYBApkState` apkState 应用状态对象

**返回值**:

-  无

---

### SetLoginParam

**描述**: 设置登录参数

**参数**:

- `YYBLoginInfo` loginInfo 登录信息对象

**返回值**:

-  无

---

## <a name='module-downloader'></a> 模块: downloader

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/YYBMacApp/WebView/JSAPI/YYBDownloaderModule.m`

### DownloadRootPath

**描述**: 获取下载根目录路径

**参数**:

-  无

**返回值**:

- `String` 下载根目录的完整路径

---

### ApkDirName

**描述**: 获取APK下载目录名称

**参数**:

-  无

**返回值**:

- `String` APK下载目录名称

---

## <a name='module-aria2ForMac'></a> 模块: aria2ForMac

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/YYBMacApp/WebView/JSAPI/YYBAria2Module.m`

### GetAria2WebSocketConned

**描述**: 获取Aria2 WebSocket连接状态

**参数**:

-  无

**返回值**:

- `String` 连接状态，"1"表示已连接，"2"表示未连接

---

### CallMethod

**描述**: 调用Aria2的方法，直接透传名称和参数

**参数**:

- `String` method 方法名称
- `Array` param 方法参数

**返回值**:

- `Dictionary` 调用结果

---

### AddListener

**描述**: 添加Aria2事件监听器

**参数**:

- `String` eventName 事件名称
- `String` gid 下载任务ID

**返回值**:

-  无

**事件**:

- `Aria2DownloadEvent`: 下载事件触发时回调，不支持单taskId查询

  **事件数据 (Aria2DownloadEvent)**: 

  - `Number` event 事件类型
  - `String` gid 下载任务ID
  - `Dictionary` info 下载任务信息
- `Aria2DownloadProgressEvent`: 下载进度变化时回调，支持taskId，当code == -1时，表示连接失败

  **事件数据 (Aria2DownloadProgressEvent)**: 

  - `String` gid 下载任务ID
  - `Dictionary` info 下载任务进度信息

---

### RemoveListener

**描述**: 移除Aria2事件监听器

**参数**:

- `String` eventName 事件名称
- `String` removedCallbackId 要移除的回调ID

**返回值**:

-  无

---

## <a name='module-appManager'></a> 模块: appManager

文件路径: `/Users/<USER>/Documents/UGit/yyb-mac-app/YYBMacApp/WebView/JSAPI/AppManager/YYBAppManagerModule.mm`

### InstallApk

**描述**: 安装APK应用

**参数**:

- `InstallApkInfo` info 安装信息对象

**返回值**:

-  无

**事件**:

- `installEvent`: 安装状态变化时触发

  **事件数据 (installEvent)**: 

  - `String` package_name 包名
  - `Number` state 安装状态码

---

### UnInstall

**描述**: 卸载APK应用

**参数**:

- `String` pkgName 要卸载的应用包名

**返回值**:

-  无

**事件**:

- `installEvent`: 卸载状态变化时触发

---

### GetInstalledList

**描述**: 获取已安装的应用列表

**参数**:

-  无

**返回值**:

- `JSGetInstalledListInfo` 已安装应用列表

  **属性**:

  - `Array` list 应用列表，每个元素为InstallApkInfo对象
  - `String` pkg_name 包名（前端接收到的字段名）
  - `String` name 应用名称
  - `String` icon 图标URL（前端接收到的字段名）
  - `String` file_path 文件路径（前端接收到的字段名）
  - `String` appPath 应用路径
  - `String` iconPath 图标路径
  - `String` version_name 版本名称（前端接收到的字段名）
  - `String` md5 文件MD5值
  - `InstallApkExtendInfo` 扩展信息

---

### LaunchApk

**描述**: 启动APK应用

**参数**:

- `String` pkgName 要启动的应用包名

**返回值**:

-  无

---

### AddInstallEventListener

**描述**: 添加安装事件监听器

**参数**:

-  无

**返回值**:

-  无

**事件**:

- `installEvent`: 安装状态变化时触发

  **事件数据 (installEvent)**: 

  - `String` package_name 包名
  - `Number` state 安装状态码，见InstallState
  - `Number` code 错误码，见InstallErrorCode
  - `String` message 错误详情
  - `String` install_rom 安装来源，见InstallFrom
  - `String` name app名

---


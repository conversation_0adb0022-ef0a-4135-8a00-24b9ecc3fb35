# JSAPI 注释规范与文档生成指南

## 注释规范

为了自动生成完整的JSAPI文档，请在每个JSAPI方法前添加标准格式的注释。注释应包含以下部分：

### 基本格式

```objc
/**
 * @description 方法的描述信息
 * @param {类型} 参数描述（如果有多个参数，使用多个@param行）
 * @return {类型} 返回值描述
 * @event 可选，方法触发的事件名称及描述
 * @error 可选，可能的错误情况描述
 * @todo 可选，标记未实现的方法
 */
YYB_EXPORT_METHOD(方法名) {
    // 方法实现
}
```

### 复杂返回值

对于返回复杂对象的方法，可以使用@property标记来描述对象的属性：

```objc
/**
 * @description 获取图形信息
 * @param 无
 * @return {Object} 图形信息对象
 *   @property {Object} primary_graphics 主显卡信息
 *     @property {String} driver_version 驱动版本
 *     @property {String} model 显卡型号
 *   @property {Object} vice_graphics 副显卡信息（可选）
 */
YYB_EXPORT_METHOD(GetGraphicsInfos) {
    // 方法实现
}
```

### 事件标记

对于会触发事件的方法，使用@event标记来描述事件名称和事件数据：

```objc
/**
 * @description 添加广播消息监听器
 * @param 无
 * @return 无
 * @event broadcastEvent 当收到广播消息时触发
 *   @property {Object} 广播消息内容
 *     @property {String} type 消息类型
 *     @property {String} content 消息内容
 */
YYB_EXPORT_METHOD(AddBroadcastListener) {
    YYB_ADD_EVENT_LISTENER(JSAPIBroadcastEvent);
    // 方法实现
}
```

### 错误标记

对于可能返回错误的方法，使用@error标记来描述可能的错误情况：

```objc
/**
 * @description 读取文件内容
 * @param {String} path 文件路径
 * @return {String} 文件内容
 * @error 文件不存在或无法访问时返回错误
 */
YYB_EXPORT_METHOD(ReadFile) {
    YYB_DECLARE_PARAM(0, NSString, path);
    // 方法实现
    if (![fileManager fileExistsAtPath:path]) {
        YYB_SEND_ERROR(@"文件不存在");
        return;
    }
    // 读取文件内容
    YYB_SEND_SUCCESS(fileContent);
}
```

## 脚本工具使用方法

### 文档生成脚本 (generate_jsapi_docs.py)

#### 基本用法

```bash
python generate_jsapi_docs.py <目录路径> [-o 输出文件路径]
```

#### 参数说明

- `<目录路径>`: 要扫描的项目目录，脚本将递归搜索该目录下所有的.m和.mm文件
- `-o, --output`: 输出的Markdown文档路径，默认为当前目录下的jsapi_docs.md

#### 示例

```bash
# 扫描当前目录并生成文档
python generate_jsapi_docs.py .

# 扫描指定目录并指定输出文件
python generate_jsapi_docs.py /path/to/project -o /path/to/output/api_docs.md
```

### 注释添加脚本 (add_jsapi_comments.py)

#### 基本用法

```bash
python add_jsapi_comments.py <目录路径> [--dry-run]
```

#### 参数说明

- `<目录路径>`: 要扫描的项目目录，脚本将递归搜索该目录下所有的.m和.mm文件
- `--dry-run`: 试运行模式，只显示将要添加的注释，不实际修改文件

#### 示例

```bash
# 扫描指定目录并添加注释
python add_jsapi_comments.py /path/to/project

# 试运行模式，只显示将要添加的注释，不实际修改文件
python add_jsapi_comments.py /path/to/project --dry-run
```

#### 功能说明

注释添加脚本会：

1. 递归扫描指定目录下的所有 .m 和 .mm 文件
2. 识别 YYBJSAPIModule 子类和使用了 YYB_EXPORT_MODULE 宏的模块
3. 查找没有标准注释的 YYB_EXPORT_METHOD 方法
4. 分析方法实现，尝试提取参数信息和推断返回类型
5. 根据注释规范，为这些方法添加标准格式的注释

脚本会智能分析方法实现，尝试提取参数信息和推断返回类型，生成符合规范的注释。对于复杂的返回值，您可能需要手动完善注释中的 @property 部分。

## 注意事项

1. 脚本会自动识别继承自`YYBJSAPIModule`的类
2. 只有使用`YYB_EXPORT_MODULE`和`YYB_EXPORT_METHOD`宏的模块和方法会被识别
3. 确保注释格式正确，否则可能无法正确解析
4. 生成的文档为Markdown格式，可以转换为HTML或PDF等其他格式

## 示例

### 源代码示例

```objc
@implementation YYBBasicInfoModule

YYB_EXPORT_MODULE(basicInfo)

/**
 * @description 获取主板型号
 * @param 无
 * @return {String} 主板制造商名称
 */
YYB_EXPORT_METHOD(GetBaseboardVendor) {
    SystemInfo& sysInfo = SystemInfo::getInstance();
    SystemInfo::MotherboardInfo motherboardInfo = sysInfo.getMotherboardInfo();
    YYB_SEND_SUCCESS([NSString stringWithUTF8String:motherboardInfo.manufacturer.c_str()]);
}

@end
```

### 生成的文档示例

```markdown
## 模块: basicInfo

文件路径: `/path/to/YYBBasicInfoModule.mm`

### GetBaseboardVendor

**描述**: 获取主板型号

**参数**: 无

**返回值**:

- `String` 主板制造商名称

**示例**:

```javascript
basicInfo.GetBaseboardVendor((result) => {
  console.log(result);
});
```
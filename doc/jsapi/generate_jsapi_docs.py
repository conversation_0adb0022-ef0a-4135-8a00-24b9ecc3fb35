#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JSAPI文档生成器
用于扫描YYBJSAPIModule的子类，提取模块名和方法信息，生成JSAPI文档
"""

import os
import re
import sys
import argparse
from collections import defaultdict

class JSAPIMethod:
    def __init__(self, name, description="", params=None, returns=None, events=None, errors=None, todo=False):
        self.name = name
        self.description = description
        self.params = params or []
        self.returns = returns or {}
        self.events = events or []
        self.errors = errors or []
        self.todo = todo

class JSAPIModule:
    def __init__(self, name, file_path):
        self.name = name
        self.file_path = file_path
        self.methods = []

def parse_file(file_path):
    """解析单个文件，提取模块和方法信息"""
    print(f"正在解析文件: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        print(f"  警告: 无法以UTF-8编码读取文件 {file_path}，尝试使用其他编码")
        try:
            with open(file_path, 'r', encoding='latin1') as f:
                content = f.read()
        except Exception as e:
            print(f"  错误: 无法读取文件 {file_path}: {e}")
            return None
    except Exception as e:
        print(f"  错误: 无法读取文件 {file_path}: {e}")
        return None
    
    # 检查是否是YYBJSAPIModule的子类或者使用了YYB_EXPORT_MODULE宏
    is_jsapi_module = False
    
    # 检查是否明确继承自YYBJSAPIModule
    if re.search(r'@implementation\s+\w+\s*:\s*YYBJSAPIModule', content) or \
       re.search(r'@interface\s+\w+\s*:\s*YYBJSAPIModule', content):
        is_jsapi_module = True
        print(f"  检测到YYBJSAPIModule子类")
    
    # 检查是否使用了YYB_EXPORT_MODULE宏（这通常表明它是一个JSAPI模块）
    module_match = re.search(r'YYB_EXPORT_MODULE\((\w+)\)', content)
    if module_match:
        is_jsapi_module = True
        print(f"  检测到YYB_EXPORT_MODULE宏，模块名: {module_match.group(1)}")
        
    if not is_jsapi_module:
        print(f"  不是JSAPI模块，跳过")
        return None
    
    # 提取模块名
    module_match = re.search(r'YYB_EXPORT_MODULE\((\w+)\)', content)
    if not module_match:
        print(f"  未找到YYB_EXPORT_MODULE宏，跳过")
        return None
    
    module_name = module_match.group(1)
    module = JSAPIModule(module_name, file_path)
    print(f"  创建模块: {module_name}")
    
    # 提取所有方法
    all_methods = []
    
    # 1. 查找带标准注释的方法 (/**...*/ YYB_EXPORT_METHOD)
    standard_method_pattern = re.compile(r'/\*\*\s*(.*?)\s*\*/\s*YYB_EXPORT_METHOD\((\w+)\)', re.DOTALL)
    standard_methods = list(standard_method_pattern.finditer(content))
    print(f"  找到 {len(standard_methods)} 个带标准注释的方法")
    all_methods.extend([("standard", match) for match in standard_methods])
    
    # 2. 查找带单行注释的方法 (// ... YYB_EXPORT_METHOD)
    inline_method_pattern = re.compile(r'//\s*(.*?)\s*\n\s*YYB_EXPORT_METHOD\((\w+)\)', re.DOTALL)
    inline_methods = list(inline_method_pattern.finditer(content))
    
    # 过滤掉已经有标准注释的方法
    filtered_inline_methods = []
    for match in inline_methods:
        method_name = match.group(2)
        already_has_standard_comment = False
        for _, standard_match in all_methods:
            if standard_match.group(2) == method_name:
                already_has_standard_comment = True
                break
        if not already_has_standard_comment:
            filtered_inline_methods.append(match)
    
    print(f"  找到 {len(filtered_inline_methods)} 个带单行注释的方法")
    all_methods.extend([("inline", match) for match in filtered_inline_methods])
    
    # 3. 查找没有注释的方法
    no_comment_methods = []
    export_method_pattern = re.compile(r'YYB_EXPORT_METHOD\((\w+)\)', re.DOTALL)
    for match in export_method_pattern.finditer(content):
        method_name = match.group(1)
        # 检查这个方法是否已经被前面的模式匹配到
        already_found = False
        for comment_type, existing_match in all_methods:
            if comment_type == "standard" and existing_match.group(2) == method_name:
                already_found = True
                break
            elif comment_type == "inline" and existing_match.group(2) == method_name:
                already_found = True
                break
        if not already_found:
            no_comment_methods.append(("none", match))
    
    print(f"  找到 {len(no_comment_methods)} 个没有注释的方法")
    all_methods.extend(no_comment_methods)
    
    print(f"  总共找到 {len(all_methods)} 个方法")
    
    for comment_type, match in all_methods:
        try:
            if comment_type == "standard":
                # 处理带标准注释的方法
                comment = match.group(1)
                method_name = match.group(2)
                print(f"  处理带标准注释的方法: {method_name}")
                
                # 解析注释
                description = ""
                params = []
                returns = {}
                events = []
                errors = []
                todo = False
                
                # 尝试从代码中提取参数信息
                code_params = extract_method_params_from_code(content, method_name)
                if code_params:
                    params = code_params
                
                desc_match = re.search(r'@description\s+(.*?)(?=\s*@|\s*\*\/)', comment, re.DOTALL)
                if desc_match:
                    description = desc_match.group(1).strip().replace('*', '').strip()
                else:
                    # 尝试提取第一行作为描述
                    first_line = comment.strip().split('\n')[0].strip('* ')
                    if first_line:
                        description = first_line
                
                param_matches = re.finditer(r'@param\s+(?:\{(.*?)\})?\s*(.*?)(?=\s*@|\s*\*\/)', comment, re.DOTALL)
                for param_match in param_matches:
                    param_type = param_match.group(1) or ""
                    param_desc = param_match.group(2).strip().replace('*', '').strip()
                    params.append({"type": param_type, "description": param_desc})
                
                # 使用更宽松的模式匹配返回值
                return_match = re.search(r'@return\s+(?:\{([^}]*)\})?\s*(.*?)(?=\s*@(?!property)|\s*\*\/|$)', comment, re.DOTALL)
                if return_match:
                    return_type = return_match.group(1) or ""
                    return_desc = return_match.group(2).strip().replace('*', '').strip()
                    # 如果描述包含多行，只取第一行作为主要描述
                    if '\n' in return_desc:
                        return_desc = return_desc.split('\n')[0].strip()
                    returns = {"type": return_type, "description": return_desc}
                    
                    # 找到@return标签后的所有内容，直到注释结束
                    return_start = comment.find('@return')
                    if return_start != -1:
                        # 查找下一个非@property的标签位置
                        next_tag_match = re.search(r'@(?!property)(?!return)', comment[return_start+7:], re.DOTALL)
                        if next_tag_match:
                            return_end = return_start + 7 + next_tag_match.start()
                            return_section = comment[return_start:return_end]
                        else:
                            # 如果没有找到下一个标签，则使用整个注释的剩余部分
                            return_section = comment[return_start:]
                        
                        # 清理返回部分，去除行首的星号和空格
                        return_section = '\n'.join([line.strip().replace('*', '').strip() for line in return_section.split('\n')])
                        
                        # 解析返回对象的属性（只在返回值部分查找）
                        # 首先将返回部分按行分割，便于处理缩进
                        return_lines = return_section.split('\n')
                        properties = []
                        
                        # 查找所有@property行
                        for i, line in enumerate(return_lines):
                            # 去除行首的星号和空格
                            clean_line = line.strip()
                            prop_match = re.search(r'@property\s+(?:\{(.*?)\})?\s*(.*?)$', clean_line)
                            if prop_match:
                                prop_type = prop_match.group(1) or ""
                                prop_desc = prop_match.group(2).strip()
                                # 检查是否是嵌套属性（通过缩进判断）
                                indent_level = 0
                                if i > 0:  # 确保不是第一行
                                    # 计算缩进级别
                                    original_line = return_lines[i]
                                    leading_spaces = len(original_line) - len(original_line.lstrip())
                                    indent_level = leading_spaces // 2  # 假设每级缩进是2个空格
                                
                                properties.append({
                                    "type": prop_type, 
                                    "description": prop_desc,
                                    "indent_level": indent_level
                                })
                        
                        if properties:
                            returns["properties"] = properties
                
                # 解析事件信息
                # 首先找到所有@event标签的位置
                event_positions = []
                for event_match in re.finditer(r'@event\s+(\w+)', comment, re.DOTALL):
                    event_positions.append((event_match.start(), event_match.group(1)))
                
                # 如果有多个事件，处理每个事件
                if event_positions:
                    for i in range(len(event_positions)):
                        start_pos = event_positions[i][0]
                        event_name = event_positions[i][1]
                        
                        # 确定事件内容的结束位置
                        if i < len(event_positions) - 1:
                            end_pos = event_positions[i+1][0]
                        else:
                            # 最后一个事件，找下一个标签或注释结束
                            next_tag_match = re.search(r'@(?!property|event)', comment[start_pos:], re.DOTALL)
                            if next_tag_match:
                                end_pos = start_pos + next_tag_match.start()
                            else:
                                end_pos = len(comment)
                        
                        # 提取事件内容
                        event_content = comment[start_pos:end_pos].strip()
                        
                        # 提取事件描述（第一行，去掉@event标签）
                        event_desc_match = re.search(r'@event\s+\w+\s+(.*?)(?=\n|$)', event_content, re.DOTALL)
                        event_desc = event_desc_match.group(1).strip() if event_desc_match else ""
                        
                        event_info = {"name": event_name, "description": event_desc}
                        
                        # 解析事件属性
                        event_prop_matches = re.finditer(r'@property\s+(?:\{(.*?)\})?\s*(.*?)(?=\s*@property|\s*@|\s*$)', event_content, re.DOTALL)
                        
                        event_properties = []
                        for prop_match in event_prop_matches:
                            prop_type = prop_match.group(1) or ""
                            prop_desc = prop_match.group(2).strip().replace('*', '').strip()
                            event_properties.append({"type": prop_type, "description": prop_desc})
                        
                        if event_properties:
                            event_info["properties"] = event_properties
                        
                        events.append(event_info)
                
                # 解析错误信息
                error_matches = re.finditer(r'@error\s+(.*?)(?=\s*@|\s*\*\/)', comment, re.DOTALL)
                for error_match in error_matches:
                    error_desc = error_match.group(1).strip().replace('*', '').strip()
                    errors.append(error_desc)
                
                if re.search(r'@todo', comment):
                    todo = True
                
            elif comment_type == "inline":
                # 处理带单行注释的方法
                comment = match.group(1)
                method_name = match.group(2)
                print(f"  处理带单行注释的方法: {method_name}")
                
                # 提取真正的注释内容，过滤掉方法实现代码
                # 查找第一个 TODO 或 // 注释
                todo_match = re.search(r'(TODO|todo):\s*(.*?)(?=\[|\{|$)', comment, re.DOTALL)
                if todo_match:
                    description = todo_match.group(2).strip()
                    todo = True
                else:
                    # 尝试提取第一行作为描述，避免包含方法实现代码
                    first_line = comment.strip().split('\n')[0].strip()
                    if first_line and not first_line.startswith('[') and not first_line.startswith('{'):
                        description = first_line
                    else:
                        description = f"{method_name} 方法"
                
                # 如果描述中包含方法实现代码，则截断
                code_start = re.search(r'(\[|\{)', description)
                if code_start:
                    description = description[:code_start.start()].strip()
                
                params = []
                returns = {}
                
                # 尝试从代码中提取参数信息
                code_params = extract_method_params_from_code(content, method_name)
                if code_params:
                    params = code_params
                
            else:  # comment_type == "none"
                # 处理没有注释的方法
                method_name = match.group(1)
                print(f"  处理没有注释的方法: {method_name}")
                
                description = f"{method_name} 方法"
                params = []
                returns = {}
                todo = False
            
            method = JSAPIMethod(method_name, description, params, returns, events, errors, todo)
            module.methods.append(method)
            
        except Exception as e:
            print(f"  错误: 处理方法 {match.group(0)[:30]}... 时出错: {e}")
            continue
    
    return module

def scan_directory(directory):
    """递归扫描目录，查找所有Objective-C文件并解析"""
    modules = []
    
    print(f"递归扫描目录: {directory}")
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(('.m', '.mm')):
                file_path = os.path.join(root, file)
                module = parse_file(file_path)
                if module:
                    modules.append(module)
    
    return modules

def generate_markdown(modules, output_file):
    """生成Markdown格式的JSAPI文档"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# JSAPI 文档\n\n")
        f.write("> 本文档由JSAPI文档生成器自动生成\n\n")
        f.write("## 目录\n\n")
        
        # 生成目录
        for module in modules:
            f.write(f"- [{module.name}](#module-{module.name})\n")
        
        f.write("\n")
        
        # 生成详细内容
        for module in modules:
            f.write(f"## <a name='module-{module.name}'></a> 模块: {module.name}\n\n")
            f.write(f"文件路径: `{module.file_path}`\n\n")
            
            for method in module.methods:
                f.write(f"### {method.name}\n\n")
                
                if method.todo:
                    f.write("**注意: 此方法尚未实现**\n\n")
                
                f.write(f"**描述**: {method.description}\n\n")
                
                if method.params:
                    f.write("**参数**:\n\n")
                    for param in method.params:
                        param_type = f"`{param['type']}`" if param['type'] else ""
                        f.write(f"- {param_type} {param['description']}\n")
                else:
                    f.write("**参数**: 无\n")
                
                f.write("\n**返回值**:\n\n")
                if method.returns and (method.returns.get('type') or method.returns.get('description')):
                    return_type = f"`{method.returns['type']}`" if method.returns.get('type') else ""
                    return_desc = method.returns.get('description', '')
                    if return_desc:
                        f.write(f"- {return_type} {return_desc}\n")
                    else:
                        f.write(f"- {return_type}\n")
                    
                    # 显示返回值属性（如果有）
                    if 'properties' in method.returns and method.returns['properties']:
                        f.write("\n  **属性**:\n\n")
                        for prop in method.returns['properties']:
                            prop_type = f"`{prop['type']}`" if prop['type'] else ""
                            # 根据缩进级别添加额外的空格
                            indent = "  "
                            if 'indent_level' in prop:
                                indent = "  " + "  " * prop['indent_level']
                            f.write(f"{indent}- {prop_type} {prop['description']}\n")
                else:
                    f.write("- 无\n")
                
                # 添加事件信息
                if method.events:
                    f.write("\n**事件**:\n\n")
                    for event in method.events:
                        f.write(f"- `{event['name']}`: {event['description']}\n")
                        if 'properties' in event:
                            f.write(f"\n  **事件数据 ({event['name']})**: \n\n")
                            for prop in event['properties']:
                                prop_type = f"`{prop['type']}`" if prop['type'] else ""
                                f.write(f"  - {prop_type} {prop['description']}\n")
                
                # 添加错误信息
                if method.errors:
                    f.write("\n**可能的错误**:\n\n")
                    for error in method.errors:
                        f.write(f"- {error}\n")
                
                f.write("\n")
                
                f.write("---\n\n")

def extract_method_params_from_code(content, method_name):
    """从方法实现中提取参数信息"""
    # 查找方法实现
    method_pattern = re.compile(r'YYB_EXPORT_METHOD\(\s*' + re.escape(method_name) + r'\s*\)\s*{(.*?)(?=YYB_EXPORT_METHOD|\@end)', re.DOTALL)
    method_match = method_pattern.search(content)
    
    if not method_match:
        return []
    
    method_body = method_match.group(1)
    
    # 查找YYB_DECLARE_PARAM宏
    param_pattern = re.compile(r'YYB_DECLARE_PARAM\(\s*(\w+)\s*,\s*(\w+)\s*\)', re.DOTALL)
    param_matches = param_pattern.finditer(method_body)
    
    params = []
    for param_match in param_matches:
        param_type = param_match.group(1)
        param_name = param_match.group(2)
        params.append({"type": param_type, "name": param_name, "description": f"{param_name} ({param_type})"})
    
    return params

def generate_comment_adder_script(modules, output_file):
    """生成一个脚本，用于为缺少标准注释的方法添加注释"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("#!/usr/bin/env python3\n")
        f.write("# -*- coding: utf-8 -*-\n\n")
        f.write("'''\n")
        f.write("JSAPI注释添加脚本\n")
        f.write("用于为YYBJSAPIModule的子类中缺少标准注释的方法添加注释\n")
        f.write("'''\n\n")
        f.write("import os\n")
        f.write("import re\n")
        f.write("import sys\n\n")
        
        f.write("def add_comments():\n")
        f.write("    print('开始添加JSAPI标准注释...')\n")
        f.write("    files_modified = 0\n")
        f.write("    methods_commented = 0\n\n")
        
        for module in modules:
            file_path = module.file_path
            f.write(f"    # 处理文件: {file_path}\n")
            f.write(f"    try:\n")
            f.write(f"        with open('{file_path}', 'r', encoding='utf-8') as file:\n")
            f.write(f"            content = file.read()\n")
            f.write(f"    except UnicodeDecodeError:\n")
            f.write(f"        try:\n")
            f.write(f"            with open('{file_path}', 'r', encoding='latin1') as file:\n")
            f.write(f"                content = file.read()\n")
            f.write(f"        except Exception as e:\n")
            f.write(f"            print(f'  错误: 无法读取文件 {file_path}: {{e}}')\n")
            f.write(f"            continue\n")
            f.write(f"    except Exception as e:\n")
            f.write(f"        print(f'  错误: 无法读取文件 {file_path}: {{e}}')\n")
            f.write(f"        continue\n\n")
            
            f.write(f"    modified = False\n")
            
            for method in module.methods:
                # 只处理没有标准注释的方法
                if not method.description.startswith("@description") and "/**" not in method.description:
                    method_name = method.name
                    f.write(f"    # 为方法 {method_name} 添加注释\n")
                    f.write(f"    method_pattern = re.compile(r'(\\s*)YYB_EXPORT_METHOD\\({method_name}\\)')\n")
                    f.write(f"    method_match = method_pattern.search(content)\n")
                    f.write(f"    if method_match:\n")
                    f.write(f"        indent = method_match.group(1)\n")
                    f.write(f"        comment = f'''/**\\n")
                    f.write(f" * @description {method_name} 方法\\n")
                    f.write(f" * @param 无\\n")
                    f.write(f" * @return {{Object}} 返回值描述\\n")
                    f.write(f" */'''\n")
                    f.write(f"        replacement = indent + comment + '\\n' + indent + f'YYB_EXPORT_METHOD({method_name})'\n")
                    f.write(f"        content = content.replace(method_match.group(0), replacement)\n")
                    f.write(f"        modified = True\n")
                    f.write(f"        methods_commented += 1\n")
                    f.write(f"        print(f'  已为方法 {method_name} 添加注释')\n\n")
            
            f.write(f"    if modified:\n")
            f.write(f"        try:\n")
            f.write(f"            with open('{file_path}', 'w', encoding='utf-8') as file:\n")
            f.write(f"                file.write(content)\n")
            f.write(f"            files_modified += 1\n")
            f.write(f"            print(f'  已更新文件: {file_path}')\n")
            f.write(f"        except Exception as e:\n")
            f.write(f"            print(f'  错误: 无法写入文件 {file_path}: {{e}}')\n\n")
        
        f.write("    print(f'完成! 已修改 {{files_modified}} 个文件，为 {{methods_commented}} 个方法添加了注释')\n\n")
        
        f.write("if __name__ == '__main__':\n")
        f.write("    add_comments()\n")
    
    print(f"注释添加脚本已生成: {output_file}")
    print("运行此脚本可为缺少标准注释的方法添加注释")

def main():
    parser = argparse.ArgumentParser(description='生成JSAPI文档')
    parser.add_argument('directory', help='要扫描的目录路径')
    parser.add_argument('-o', '--output', default='jsapi_docs.md', help='输出文件路径')
    parser.add_argument('--generate-comment-script', action='store_true', help='生成一个脚本，用于为缺少标准注释的方法添加注释')
    parser.add_argument('--comment-script-output', default='add_jsapi_comments.py', help='注释添加脚本的输出路径')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.directory):
        print(f"错误: 目录 '{args.directory}' 不存在")
        return 1
    
    print(f"正在扫描目录: {args.directory}")
    modules = scan_directory(args.directory)
    
    if not modules:
        print("未找到任何JSAPI模块")
        return 0
    
    print(f"找到 {len(modules)} 个JSAPI模块")
    for module in modules:
        print(f"- {module.name} ({len(module.methods)} 个方法)")
    
    generate_markdown(modules, args.output)
    print(f"文档已生成: {args.output}")
    
    if args.generate_comment_script:
        generate_comment_adder_script(modules, args.comment_script_output)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
#!/bin/bash

set -e  # 出错时退出

# 签名证书标识
SIGNING_IDENTITY="Developer ID Application: Tencent Technology (Shenzhen) Company Limited (88L2Q4487U)"

# 解析命令行参数
while getopts "e:a:" opt; do
  case "${opt}" in
    e) ENTITLEMENTS="${OPTARG}" ;;
    a) APP_PATH="${OPTARG}" ;;
    *) echo "Usage: $0 -e <entitlements_path> -a <app_path>" ; exit 1 ;;
  esac
done

# 检查参数是否已提供
if [ -z "${ENTITLEMENTS}" ] || [ -z "${APP_PATH}" ]; then
  echo "Error: Both -e (entitlements) and -a (app_path) must be provided."
  exit 1
fi

# 签名命令
codesign --force --verbose --deep --sign "${SIGNING_IDENTITY}" \
  --entitlements "${ENTITLEMENTS}" \
  --options runtime \
  --timestamp \
  "${APP_PATH}"
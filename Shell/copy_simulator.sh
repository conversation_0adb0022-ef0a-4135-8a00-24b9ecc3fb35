#!/bin/bash

# 用法
# sh copy_simulator.sh <target_directory> [build_config] [-pwd <apple_password>]
# <target_directory>: 目标目录。yyb_mac.app的编译产物目录，一般为[你的工作目录]/yyb-mac-engine/external/qemu
# [build_config]: 可选参数，指定构建配置。默认为all（全量构建），也可以是none（仅复制）。
# -pwd <apple_password>: 可选参数，指定Apple ID密码，用于代码签名。该参数不传时使用临时签名，不公证

Validate input parameters
if [ $# -lt 1 ]; then
target_dir="$(cd "$(dirname "$0")/../.." && pwd)/yyb-mac-engine/external/qemu"
build_config=all  # Default to copy only
#    echo "Usage: $0 <target_directory> [build_config]"
#    echo "build_config: all (full build),  (cpack+copy)"
#    exit 1
else
# 解析 path
target_dir="$1"
shift

# 判断下一个参数是否为 -pwd
if [[ "$1" == "-pwd" ]]; then
    build_config=all
    shift
    if [[ -n "$1" ]]; then
        apple_password="$1"
    fi
elif [[ -n "$1" ]]; then
    build_config="$1"
    shift
    if [[ "$1" == "-pwd" ]]; then
        shift
        if [[ -n "$1" ]]; then
            apple_password="$1"
        fi
    fi
fi
fi
echo "Target directory: $target_dir"
echo "Build configuration: $build_config"
echo "Apple password: ${apple_password:-not provided}"

# Verify target directory exists
if [ ! -d "$target_dir" ]; then
    echo "Error: Target directory '$target_dir' does not exist"
    exit 1
fi

# Get absolute path of script location
script_dir="$(cd "$(dirname "$0")" && pwd)"

# Function to execute rebuild step
execute_rebuild() {
    echo "Executing rebuild in $target_dir"
    (cd "$target_dir" && android/rebuild.sh --ccache /opt/homebrew/bin/ccache --task-disable ctest --config release --cmake_option OPTION_ASAN_IN_DEBUG=FALSE) 
    
    if [ $? -ne 0 ]; then
        echo "Error: Rebuild failed"
        exit 1
    fi
}

# Function to execute cpack step
execute_cpack() {
    echo "Executing cpack in $target_dir/objs"
    if [ ! -d "$target_dir/objs" ]; then
        echo "Error: objs directory not found in $target_dir"
        exit 1
    fi
    
    (cd "$target_dir/objs" && cpack)
    if [ $? -ne 0 ]; then
        echo "Error: cpack failed"
        exit 1
    fi
}

# Function to copy application
copy_application() {
    # Search for app bundle in CPack output directory
    # app_source=$(find "$target_dir/objs/_CPack_Packages" -type d -name "yyb_mac.app" -print -quit)
    app_source="$target_dir/objs/distribution/emulator/yyb_mac.app"
    if [ -z "$app_source" ] || [ ! -d "$app_source" ]; then
        echo "Error: Application bundle not found in $target_dir/objs/_CPack_Packages"
        exit 1
    fi
    
    # Copy application to Application Support directory
    app_support_dir="$HOME/Library/Application Support/com.tencent.yybmac/YYBEngineDownload"
    echo "Copying application from $app_source to $app_support_dir"
    mkdir -p "$(dirname "$app_support_dir")"
    rsync -a --delete "$app_source" "$app_support_dir"
    echo "Application copied to $app_support_dir"

    # Copy vms to Application Support directory
    vms_source="$target_dir/yyb-mac/res/vms/"
    vms_dest="$HOME/Library/Application Support/com.tencent.yybmac.engine/vms/"
    echo "Copying vms from $vms_source to $vms_dest"
    mkdir -p "$(dirname "$vms_dest")"
    rsync -a --delete "$vms_source" "$vms_dest"
    echo "VMs copied to $vms_dest"



    if [ -z "${apple_password}" ]; then
        echo "run codesign_simulator.sh without apple password"
        sh "$script_dir/codesign_simulator.sh" "$app_support_dir/yyb_mac.app"
        ret=$?
        script_name="codesign_simulator.sh"
    else
        echo "run codesign_simulator_release.sh with apple password"
        sh "$script_dir/codesign_simulator_release.sh" -path "$app_support_dir/yyb_mac.app" -pwd "$apple_password"
        ret=$?
        script_name="codesign_simulator_release.sh"
    fi
    
    if [ $ret -ne 0 ]; then
        echo "Error: $script_name failed"
        exit 1
    fi
}

# Execute steps based on build configuration
case $build_config in
    all)
        execute_rebuild
        # execute_cpack
        copy_application
        ;;
    *)
        # execute_cpack
        copy_application
        ;;
esac

echo "Operation completed successfully"


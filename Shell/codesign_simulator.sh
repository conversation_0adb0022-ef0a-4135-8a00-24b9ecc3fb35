parent_dir="$(dirname "$(pwd)")"  # parent_dir is the parent directory of the current working directory

if [ -z "$1" ]; then
    echo "错误：请提供yyb_mac.app的路径"
    exit 1
fi
SUB_APP_PATH=$1
echo "app路径:${SUB_APP_PATH}"
# SUB_APP_PATH="${parent_dir}/yyb_mac.app"
entitlements_path="${parent_dir}/YYBMacApp/entitlements.plist"
if [ ! -d "${SUB_APP_PATH}" ]; then
    echo "错误: 子应用未找到: ${SUB_APP_PATH}"
    exit 1
fi

chmod -R a+xr "${SUB_APP_PATH}"

# Modify Info.plist to add LSUIElement property
plist_file="${SUB_APP_PATH}/Contents/Info.plist"
if [ ! -f "$plist_file" ]; then
    echo "Error: Info.plist not found at $plist_file"
    exit 1
fi
    
# Add LSUIElement property
/usr/libexec/PlistBuddy -c "Add :LSUIElement bool true" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :LSUIElement true" "$plist_file"
# /usr/libexec/PlistBuddy -c "Delete :CFBundleName" "$plist_file" 2>/dev/null || true
/usr/libexec/PlistBuddy -c "Set :CFBundleName yyb_mac_engine" "$plist_file" 2>/dev/null || true
/usr/libexec/PlistBuddy -c "Add :NSLocationUsageDescription string 需要您的位置权限以提供定位服务" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :NSLocationUsageDescription 需要您的位置权限以提供定位服务" "$plist_file"
/usr/libexec/PlistBuddy -c "Add :NSLocationWhenInUseUsageDescription string 需要您的位置权限以提供定位服务" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :NSLocationWhenInUseUsageDescription 需要您的位置权限以提供定位服务" "$plist_file"
/usr/libexec/PlistBuddy -c "Add :NSCameraUsageDescription string 摄像头权限" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :NSCameraUsageDescription 摄像头权限" "$plist_file"

codesign --remove-signature "${SUB_APP_PATH}" || {
    echo "错误: 移除签名失败"
    exit 1
}

# 使用 --deep 递归签名
codesign -s "-" --force --deep --entitlements "${entitlements_path}" "${SUB_APP_PATH}"

# 详细验证签名
codesign -vvv "${SUB_APP_PATH}"

xattr -dr com.apple.quarantine "${SUB_APP_PATH}"

echo "应用已成功重签名并移除隔离属性。"

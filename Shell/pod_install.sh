#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}错误: $1 未安装${NC}"
        return 1
    fi
    return 0
}

# 检查并安装 xcodeproj
echo -e "${YELLOW}检查 xcodeproj 安装...${NC}"
if ! check_command "gem"; then
    echo -e "${RED}错误: Ruby gem 未安装，请先安装 Ruby${NC}"
    exit 1
fi

if ! gem list xcodeproj -i &> /dev/null; then
    echo -e "${YELLOW}正在安装 xcodeproj...${NC}"
    gem install xcodeproj || {
        echo -e "${RED}xcodeproj 安装失败${NC}"
        exit 1
    }
    echo -e "${GREEN}xcodeproj 安装成功${NC}"
else
    echo -e "${GREEN}xcodeproj 已安装${NC}"
fi

# 获取 xcodeproj 版本并修复
echo -e "${YELLOW}获取 xcodeproj 版本...${NC}"
XCODEPROJ_VERSION=$(gem list xcodeproj | grep xcodeproj | cut -d'(' -f2 | cut -d')' -f1)
echo -e "${GREEN}xcodeproj 版本: $XCODEPROJ_VERSION${NC}"

# 修复 xcodeproj 文件
echo -e "${YELLOW}正在修复 xcodeproj 文件...${NC}"
XCODEPROJ_PATH=$(gem info xcodeproj --version $XCODEPROJ_VERSION | grep 'Installed at:' | cut -d':' -f 2 | xargs)
sed -i -e 's/build_phase.name/build_phase.display_name/g' "$XCODEPROJ_PATH/gems/xcodeproj-$XCODEPROJ_VERSION/lib/xcodeproj/project/object/file_system_synchronized_exception_set.rb"
echo -e "${GREEN}xcodeproj 文件修复成功${NC}"

# 安装 Pod 依赖
echo -e "${YELLOW}正在安装 Pod 依赖...${NC}"
pod install || {
    echo -e "${RED}Pod 依赖安装失败${NC}"
    exit 1
}
echo -e "${GREEN}Pod 依赖安装成功${NC}" 
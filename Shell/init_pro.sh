#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}错误: $1 未安装${NC}"
        return 1
    fi
    return 0
}

# 检查并安装 CocoaPods
echo -e "${YELLOW}检查 CocoaPods 安装...${NC}"
if ! check_command "pod"; then
    echo -e "${YELLOW}正在安装 CocoaPods...${NC}"
    brew install cocoapods || {
        echo -e "${RED}CocoaPods 安装失败${NC}"
        exit 1
    }
    echo -e "${GREEN}CocoaPods 安装成功${NC}"
else
    echo -e "${GREEN}CocoaPods 已安装${NC}"
fi

echo -e "${GREEN}所有依赖安装和配置完成！${NC}"

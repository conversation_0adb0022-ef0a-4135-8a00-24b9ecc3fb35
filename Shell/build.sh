#!/bin/bash

# ------ 配置区域 (根据项目修改) ------
PROJECT_NAME="YYBMacApp"
SCHEME_NAME="YYBMacApp"
CONFIGURATION="Release"
SIGNING_IDENTITY="Apple Distribution: Tencent Technology (Shenzhen) Company Limited (88L2Q4487U)"
ENTITLEMENTS="Contents/Resources/entitlements.plist"
# PROVISIONING_PROFILE="path/to/Profile.mobileprovision"
DMG_BACKGROUND_IMAGE="dmg_background.png"  # 可选背景图
OUTPUT_DIR="$(pwd)/Output"
# -----------------------------------

# 清理旧构建
rm -rf "${OUTPUT_DIR}"
mkdir -p "${OUTPUT_DIR}"

# 步骤1: 构建应用
echo "🚀 开始构建应用程序..."
xcodebuild clean build \
  -workspace "${PROJECT_NAME}.xcworkspace" \
  -scheme "${SCHEME_NAME}" \
  -configuration "${CONFIGURATION}" \
  -derivedDataPath "${OUTPUT_DIR}/DerivedData"

# 检查构建结果
if [ $? -ne 0 ]; then
    echo "❌ 构建失败，退出脚本"
    exit 1
fi

APP_PATH="${OUTPUT_DIR}/DerivedData/Build/Products/${CONFIGURATION}/${PROJECT_NAME}.app"

# 步骤2: 重签名
echo "🔑 开始重签名操作..."
# cp "${PROVISIONING_PROFILE}" "${APP_PATH}/Contents/embedded.provisionprofile"

codesign --force --verbose --deep --sign "-" \
  --entitlements "${APP_PATH}/${ENTITLEMENTS}" \
  --options runtime \
  --timestamp \
  "${APP_PATH}"

if [ $? -ne 0 ]; then
    echo "❌ 重签名失败，退出脚本"
    exit 1
fi
# 验证签名
codesign --verify --verbose "${APP_PATH}"
if [ $? -ne 0 ]; then
    echo "❌ 签名验证失败，退出脚本"
    exit 1
fi
# spctl -a -t exec -vv "${APP_PATH}"

# 步骤3: 准备DMG内容
echo "📦 准备DMG内容..."
DMG_TEMP="${OUTPUT_DIR}/temp"
mkdir -p "${DMG_TEMP}"
cp -R "${APP_PATH}" "${DMG_TEMP}"
ln -s "/Applications" "${DMG_TEMP}/Applications"

# 步骤4: 创建DMG
echo "🖥 创建DMG磁盘映像..."
VERSION=$(agvtool what-version -terse)
DMG_FINAL="${OUTPUT_DIR}/${PROJECT_NAME}_v${VERSION}.dmg" 
hdiutil create -srcfolder "${DMG_TEMP}" \
  -volname "${PROJECT_NAME}" \
  -fs HFS+ \
  -fsargs "-c c=64,a=16,e=16" \
  -format UDZO \
  -imagekey zlib-level=9 \
  -ov "${DMG_FINAL}"

# 清理临时文件
rm -rf "${DMG_TEMP}"

echo "✅ 打包完成! DMG文件位于: ${DMG_FINAL}"

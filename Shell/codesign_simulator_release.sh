# 用法:
# sh codesign_simulator_release.sh -path /path/to/yyb_mac.app -pwd your_apple_id_password
# 注意: 需要提供yyb_mac.app的路径和Apple ID密码
#!/bin/bash

set -e  # 出错时退出


# 解析参数
while [[ $# -gt 0 ]]; do
  case "$1" in
    -path)
      SUB_APP_PATH="$2"
      shift 2
      ;;
    -pwd)
      APPLE_PASSWORD="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

parent_dir="$(dirname "$(pwd)")"  # parent_dir is the parent directory of the current working directory

# 检查必需的参数
if [ -z "${SUB_APP_PATH}" ]; then
    echo "错误：请提供yyb_mac.app的路径"
    exit 1
fi
if [ -z "${APPLE_PASSWORD}" ]; then
    echo "错误：请提供公证账号的Apple ID密码"
    exit 1
fi

echo "app路径:${SUB_APP_PATH}"

entitlements_path="${parent_dir}/YYBMacApp/entitlements.plist"
# 修改yyb_mac.app权限
chmod -R a+xr "${SUB_APP_PATH}"

# Modify Info.plist to add LSUIElement property
plist_file="${SUB_APP_PATH}/Contents/Info.plist"
if [ ! -f "$plist_file" ]; then
    echo "Error: Info.plist not found at $plist_file"
    exit 1
fi
    
# Add LSUIElement property
/usr/libexec/PlistBuddy -c "Add :LSUIElement bool true" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :LSUIElement true" "$plist_file"
/usr/libexec/PlistBuddy -c "Delete :CFBundleName" "$plist_file" 2>/dev/null || true
/usr/libexec/PlistBuddy -c "Add :NSLocationUsageDescription string 需要您的位置权限以提供定位服务" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :NSLocationUsageDescription 需要您的位置权限以提供定位服务" "$plist_file"
/usr/libexec/PlistBuddy -c "Add :NSLocationWhenInUseUsageDescription string 需要您的位置权限以提供定位服务" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :NSLocationWhenInUseUsageDescription 需要您的位置权限以提供定位服务" "$plist_file"
/usr/libexec/PlistBuddy -c "Add :NSCameraUsageDescription string 摄像头权限" "$plist_file" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :NSCameraUsageDescription 摄像头权限" "$plist_file"


# 移除签名
codesign --remove-signature "${SUB_APP_PATH}" || {
    echo "错误: 移除签名失败"
    exit 1
}

PROJECT_NAME="yyb_mac"
SIGNING_IDENTITY="Developer ID Application: Tencent Technology (Shenzhen) Company Limited (88L2Q4487U)"
OUTPUT_DIR="${parent_dir}/Output"
ZIP_PATH="${OUTPUT_DIR}/${PROJECT_NAME}.zip"
APPLE_ID="<EMAIL>"
TEAM_ID="88L2Q4487U"

# 清理旧构建
rm -rf "${OUTPUT_DIR}"
mkdir -p "${OUTPUT_DIR}"

# 签名
echo "🔑 开始重签名操作..."
SIGNING_IDENTITY="Developer ID Application: Tencent Technology (Shenzhen) Company Limited (88L2Q4487U)"
codesign --force --verbose --deep --sign "${SIGNING_IDENTITY}" \
  --entitlements "${entitlements_path}" \
  --options runtime \
  --timestamp \
  "${SUB_APP_PATH}"

echo "🔑 重签名完成"

# 详细验证签名
echo "🔍 正在验证签名..."
codesign -vvv "${SUB_APP_PATH}"
echo "✅ 签名验证完成"
if [ $? -ne 0 ]; then
    echo "❌ 重签名失败，退出脚本"
    exit 1
fi

# 压缩应用
echo "开始压缩应用..."
/usr/bin/ditto -c -k --sequesterRsrc --keepParent "${SUB_APP_PATH}" "${ZIP_PATH}"
echo "应用压缩完成: ${ZIP_PATH}"
if [ $? -ne 0 ]; then
    echo "❌ 压缩失败，退出脚本"
    exit 1
fi

# 提交公证
echo "开始提交公证..."
xcrun notarytool submit --apple-id "$APPLE_ID" --password "$APPLE_PASSWORD" --team-id "$TEAM_ID" --wait ${ZIP_PATH}
echo "公证提交完成。"
if [ $? -ne 0 ]; then
    echo "❌ 公证提交失败，退出脚本"
    exit 1
fi


# 解压公证后的zip包
echo "开始解压公证后的zip包..."
ditto -x -k "${ZIP_PATH}" "${OUTPUT_DIR}"
echo "解压完成: ${OUTPUT_DIR}"

echo "删除临时zip包..."
rm -rf "${ZIP_PATH}"
echo "临时zip包已删除: ${ZIP_PATH}"

APP_PATH="${OUTPUT_DIR}/${PROJECT_NAME}.app"
echo "应用路径: ${APP_PATH}"


# 贴签
echo "开始贴签..."
xcrun stapler staple "${APP_PATH}"
stapler validate "${APP_PATH}"


# 验证贴签
echo "开始验证应用..."
spctl --assess --type execute --verbose "${APP_PATH}"
echo "应用已成功重签名并提交公证。"

# 重新压缩应用
echo "开始重新压缩应用..."
/usr/bin/ditto -c -k --sequesterRsrc --keepParent "${APP_PATH}" "${ZIP_PATH}"
echo "重新压缩完成: ${ZIP_PATH}"

echo "开始压缩vms..."
cd "$HOME/Library/Application Support/com.tencent.yybmac.engine" && \
  zip -r "${OUTPUT_DIR}/vms.zip" vms
echo "vms压缩完成: ${OUTPUT_DIR}/vms.zip"


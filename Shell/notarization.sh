#!/bin/bash

set -e  # 出错时退出

SIGNING_IDENTITY="Developer ID Application: Tencent Technology (Shenzhen) Company Limited (88L2Q4487U)"
APPLE_ID="<EMAIL>"
TEAM_ID="88L2Q4487U"

# 解析参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -a|--app-path)
      APP_PATH="$2"
      shift 2
      ;;
    -p|--apple-password)
      APPLE_PASSWORD="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# 检查 APP_PATH
if [[ -z "$APP_PATH" ]]; then
  echo "Error: APP_PATH 未设置。请通过 -a/--app-path 参数或 APP_PATH 环境变量传入。"
  exit 1
fi

# 检查 APPLE_PASSWORD
if [[ -z "$APPLE_PASSWORD" ]]; then
  echo "Error: APPLE_PASSWORD 未设置。请通过 -p/--apple-password 参数或 APPLE_PASSWORD 环境变量传入。"
  exit 1
fi

# 自动生成 OUTPUT_DIR 和 ZIP_PATH
OUTPUT_DIR="$(dirname "$APP_PATH")/Output"
ZIP_PATH="$OUTPUT_DIR/temp.zip"

# 确保输出目录存在
mkdir -p "$OUTPUT_DIR"

/usr/bin/ditto -c -k --sequesterRsrc --keepParent "${APP_PATH}" "${ZIP_PATH}"

echo "开始提交公证..."
xcrun notarytool submit --apple-id "$APPLE_ID" --password "$APPLE_PASSWORD" --team-id "$TEAM_ID" --wait ${ZIP_PATH}
echo "公证提交完成。"

open "$OUTPUT_DIR"
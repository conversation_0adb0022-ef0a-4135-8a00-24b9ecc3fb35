#!/bin/bash

set -e  # 出错时退出

# 解析参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -p|--apple-password)
      APPLE_PASSWORD="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# 检查 APPLE_PASSWORD
if [[ -z "$APPLE_PASSWORD" ]]; then
  echo "Error: APPLE_PASSWORD 未设置。请通过 -p/--apple-password 参数或 APPLE_PASSWORD 环境变量传入。"
  exit 1
fi

# ------ 配置区域 (根据项目修改) ------
PROJECT_NAME="YYBMacApp"
DISPLAY_NAME="腾讯应用宝"
SCHEME_NAME="YYBMacApp"
CONFIGURATION="Release"
SIGNING_IDENTITY="Developer ID Application: Tencent Technology (Shenzhen) Company Limited (88L2Q4487U)"
ENTITLEMENTS="Contents/Resources/entitlements.plist"
DMG_BACKGROUND_IMAGE="dmg_background.png"  # 可选背景图
OUTPUT_DIR="$(pwd)/Output"
ZIP_PATH="${OUTPUT_DIR}/${DISPLAY_NAME}.zip"
APPLE_ID="<EMAIL>"
TEAM_ID="88L2Q4487U"
# -----------------------------------

# 清理旧构建
rm -rf "${OUTPUT_DIR}"
mkdir -p "${OUTPUT_DIR}"

# 步骤1: 构建应用
echo "🚀 开始构建应用程序..."
xcodebuild clean build \
  -workspace "${PROJECT_NAME}.xcworkspace" \
  -scheme "${SCHEME_NAME}" \
  -configuration "${CONFIGURATION}" \
  -derivedDataPath "${OUTPUT_DIR}/DerivedData"

# 检查构建结果
if [ $? -ne 0 ]; then
    echo "❌ 构建失败，退出脚本"
    exit 1
fi

APP_PATH="${OUTPUT_DIR}/DerivedData/Build/Products/${CONFIGURATION}/${DISPLAY_NAME}.app"
ZIP_PATH="${OUTPUT_DIR}/DerivedData/Build/Products/${CONFIGURATION}/${DISPLAY_NAME}.zip"

# 步骤2: 重签名
echo "🔑 开始重签名操作..."

# 签名Sparkle自动更新组件
codesign --timestamp --options=runtime --deep -f -s "${SIGNING_IDENTITY}" \
"${APP_PATH}/Contents/Frameworks/YYBMacFusionSDK.framework/Versions/A/Frameworks/Sparkle.framework/Autoupdate"
if [ $? -ne 0 ]; then
    echo "❌ Sparkle自动更新组件签名失败，退出脚本"
    exit 1
fi
echo "✅ Sparkle自动更新组件签名成功"

# 先签名YYBPackage.app
codesign --force --verbose --deep --sign "${SIGNING_IDENTITY}" \
  --options runtime \
  --timestamp \
  "${APP_PATH}/Contents/Resources/YYBPackage.app"

if [ $? -ne 0 ]; then
    echo "❌ 腾讯应用宝.app重签名失败，退出脚本"
    exit 1
fi

# 再签名主应用
codesign --force --verbose --deep --sign "${SIGNING_IDENTITY}" \
  --entitlements "${APP_PATH}/${ENTITLEMENTS}" \
  --options runtime \
  --timestamp \
  "${APP_PATH}"

if [ $? -ne 0 ]; then
    echo "❌ 主应用重签名失败，退出脚本"
    exit 1
fi

# 验证签名
codesign --verify --verbose "${APP_PATH}"
if [ $? -ne 0 ]; then
    echo "❌ 签名验证失败，退出脚本"
    exit 1
fi

codesign -vvv --deep --strict ${APP_PATH}
# open ${APP_PATH}

# 将APP拷贝到临时目录并压缩
/usr/bin/ditto -c -k --sequesterRsrc --keepParent "${APP_PATH}" "${ZIP_PATH}"

echo "开始提交公证..."
# 保留日志输出，同时捕获结果
NOTARY_RESULT=$(xcrun notarytool submit --apple-id "$APPLE_ID" --password "$APPLE_PASSWORD" --team-id "$TEAM_ID" --wait ${ZIP_PATH} | tee /dev/tty)

# 检查公证结果
if [[ $NOTARY_RESULT != *"status: Accepted"* ]]; then
  echo "❌ 公证失败，结果如下："
  echo "$NOTARY_RESULT"
  exit 1
fi

echo "公证提交完成。"

# 步骤3: 准备DMG内容
echo "📦 准备DMG内容..."
DMG_TEMP="${OUTPUT_DIR}/temp"
mkdir -p "${DMG_TEMP}"
unzip -o "${ZIP_PATH}" -d "${DMG_TEMP}"

# 钉合公证
echo "📦 钉合公证..."
xcrun stapler staple "${DMG_TEMP}/${DISPLAY_NAME}.app"

#将app文件拷贝到output目录并压缩
/usr/bin/ditto -c -k --sequesterRsrc --keepParent "${DMG_TEMP}/${DISPLAY_NAME}.app" "${OUTPUT_DIR}/${DISPLAY_NAME}.zip"


ln -s "/Applications" "${DMG_TEMP}/Applications"

# 步骤4: 创建DMG
echo "🖥 创建DMG磁盘映像..."
TIMESTAMP=$(date "+%Y%m%d%H%M%S")
DMG_FINAL="${OUTPUT_DIR}/${DISPLAY_NAME}_${TIMESTAMP}.dmg"
hdiutil create -srcfolder "${DMG_TEMP}" \
  -volname "${DISPLAY_NAME}" \
  -fs HFS+ \
  -fsargs "-c c=64,a=16,e=16" \
  -format UDZO \
  -imagekey zlib-level=9 \
  -ov "${DMG_FINAL}"

# 清理临时文件
rm -rf "${DMG_TEMP}"

echo "✅ 打包完成! DMG文件位于: ${DMG_FINAL}"

open "${OUTPUT_DIR}"


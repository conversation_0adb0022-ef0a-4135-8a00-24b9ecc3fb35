//
//  main.m
//  YYBUninstaller
//
//  Created by <PERSON> on 2025/7/23.
//

#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <signal.h>

void removeFile(NSString *path) {
    NSFileManager *fm = [NSFileManager defaultManager];
    if ([fm fileExistsAtPath:path]) {
        NSLog(@"Removing: %@", path);
        
        NSError *error;
        if ([fm removeItemAtPath:path error:&error]) {
            NSLog(@"Successfully removed: %@", path);
        } else {
            NSLog(@"Attempting with elevated privileges...");
                                NSTask *sudoTask = [[NSTask alloc] init];
            [sudoTask setLaunchPath:@"/usr/bin/sudo"];
            [sudoTask setArguments:@[@"rm", @"-rf", path]];
            [sudoTask launch];
            [sudoTask waitUntilExit];
            
            if ([sudoTask terminationStatus] == 0) {
                NSLog(@"Successfully removed with sudo: %@", path);
            } else {
                NSLog(@"Failed to remove with sudo: %@", path);
            }
        }
    } else {
        NSLog(@"Path does not exist: %@", path);
    }
}


void removeFiles(void) {
    // 要删除的路径
    NSString *homeDir = NSHomeDirectory();
    NSString *bundleID = @"com.tencent.yybmac";
    NSArray *paths = @[
        @"/Applications/腾讯应用宝.app",
        // 快捷方式
        [NSString stringWithFormat:@"%@/Applications/YYBApplications", homeDir],
        
        // 日志文件
        @"/tmp/yyb_mac.log",
        @"/tmp/yybservice.err",
        @"/tmp/yybservice.log",
    ];
    
    NSArray *bundlePaths = @[
        // 应用支持文件
        [NSString stringWithFormat:@"%@/Library/Application Support", homeDir],

        // 缓存文件
        [NSString stringWithFormat:@"%@/Library/Caches", homeDir],

        [NSString stringWithFormat:@"%@/Library/WebKit", homeDir],

        // 偏好设置
        [NSString stringWithFormat:@"%@/Library/Preferences", homeDir],

        // 容器目录
        [NSString stringWithFormat:@"%@/Library/Containers", homeDir],

        // 保存的应用状态
        [NSString stringWithFormat:@"%@/Library/Saved Application State", homeDir],

        [NSString stringWithFormat:@"%@/Library/HTTPStorages", homeDir],];
    
    // 删除文件和目录
    NSFileManager *fm = [NSFileManager defaultManager];
    for (NSString *path in paths) {
        removeFile(path);
    }
    for (NSString *path in bundlePaths) {
        NSError *error = nil;
        NSArray *contents = [fm contentsOfDirectoryAtPath:path error:&error];
        if (error) {
            NSLog(@"读取目录失败: %@", error);
        } else {
            for (NSString *item in contents) {
                if ([item hasPrefix:bundleID]) {
                    NSString *fullPath = [path stringByAppendingPathComponent:item];
                    if ([fm removeItemAtPath:fullPath error:&error]) {
                        NSLog(@"已删除: %@", fullPath);
                    } else {
                        NSLog(@"删除失败: %@, 错误: %@", fullPath, error);
                    }
                }
            }
        }
    }
}


// 终止进程函数
void killProcessByName(NSString *processName) {
    NSTask *pgrepTask = [[NSTask alloc] init];
    [pgrepTask setLaunchPath:@"/usr/bin/pgrep"];
    [pgrepTask setArguments:@[@"-x", processName]];
    [pgrepTask setStandardOutput:[NSPipe pipe]];
    
    @try {
        [pgrepTask launch];
        [pgrepTask waitUntilExit];
        
        if ([pgrepTask terminationStatus] == 0) {
            NSLog(@"Force quitting all instances of %@", processName);
            
            // 正常终止
            NSTask *pkillTask = [[NSTask alloc] init];
            [pkillTask setLaunchPath:@"/usr/bin/pkill"];
            [pkillTask setArguments:@[@"-x", processName]];
            [pkillTask launch];
            [pkillTask waitUntilExit];
            
            sleep(1);
            
            // 强制终止
            NSTask *pkill9Task = [[NSTask alloc] init];
            [pkill9Task setLaunchPath:@"/usr/bin/pkill"];
            [pkill9Task setArguments:@[@"-9", @"-x", processName]];
            [pkill9Task launch];
            [pkill9Task waitUntilExit];
            
            if ([pkill9Task terminationStatus] == 0) {
                NSLog(@"Successfully forced quit all instances of %@", processName);
            } else {
                NSLog(@"Failed to force quit %@", processName);
            }
        } else {
            NSLog(@"%@ is already closed", processName);
        }
    } @catch (NSException *exception) {
        NSLog(@"Error killing process %@: %@", processName, exception.reason);
    }
}

// 快捷方式的bundle ID模糊匹配
 void terminateAppsByBundleIDPrefix(NSString *bundleIDPrefix) {
    NSLog(@"Terminating apps with bundle ID prefix: %@", bundleIDPrefix);
    
    NSArray<NSRunningApplication *> *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
    for (NSRunningApplication *app in runningApps) {
        NSString *bundleID = app.bundleIdentifier;
        if (bundleID && [bundleID hasPrefix:bundleIDPrefix]) {
            NSLog(@"Terminating app: %@", bundleID);
            
            NSApplicationTerminateReply reply = [app terminate];
            if (reply != NSTerminateNow) {
                [app forceTerminate];
                NSLog(@"Force terminated: %@", bundleID);
            }
        }
    }
}


// 终止守护进程
void terminateDaemonsByIDPrefix(NSString *identifierPrefix) {
    NSLog(@"Terminating daemons with ID prefix: %@", identifierPrefix);
    
    NSTask *task = [[NSTask alloc] init];
    [task setLaunchPath:@"/bin/launchctl"];
    [task setArguments:@[@"remove", [NSString stringWithFormat:@"%@.*", identifierPrefix]]];
    
    @try {
        [task launch];
        [task waitUntilExit];
    } @catch (NSException *exception) {
        NSLog(@"Error terminating daemons: %@", exception.reason);
    }
}

void terminateCollectedApps(NSSet<NSRunningApplication *> *apps, NSTimeInterval delay) {
    if (apps.count == 0) {
        NSLog(@"No applications to terminate");
        return;
    }
    
    NSLog(@"Found %lu applications to terminate", apps.count);
    
    // 优雅终止阶段
    for (NSRunningApplication *app in apps) {
        NSLog(@"Sending terminate to: %@ (PID: %d)",
              app.bundleIdentifier ?: app.executableURL.lastPathComponent,
              app.processIdentifier);
        
        if (![app terminate]) {
            NSLog(@"Graceful termination failed for PID %d", app.processIdentifier);
        }
    }
    
    // 等待优雅退出
    [NSThread sleepForTimeInterval:delay];
    
    // 强制终止阶段
    NSMutableSet<NSRunningApplication *> *remainingApps = [NSMutableSet set];
    for (NSRunningApplication *app in apps) {
        if (!app.terminated) {
            [remainingApps addObject:app];
        }
    }
    
    if (remainingApps.count > 0) {
        NSLog(@"Force terminating %lu applications", remainingApps.count);
        for (NSRunningApplication *app in remainingApps) {
            [app forceTerminate];
        }
    }
}

void terminateMultipleTargets(NSArray<NSString *> *bundleIDPrefixes, NSArray<NSString *> *processNames, NSTimeInterval delay) {
    NSArray<NSRunningApplication *> *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
    NSMutableSet<NSRunningApplication *> *appsToTerminate = [NSMutableSet set];
    
    for (NSRunningApplication *app in runningApps) {
        for (NSString *prefix in bundleIDPrefixes) {
            if ([app.bundleIdentifier hasPrefix:prefix]) {
                [appsToTerminate addObject:app];
                break;
            }
        }
        
        if (![appsToTerminate containsObject:app]) {
            NSString *appProcessName = app.executableURL.lastPathComponent;
            for (NSString *name in processNames) {
                if ([appProcessName isEqualToString:name]) {
                    [appsToTerminate addObject:app];
                    break;
                }
            }
        }
    }
    
    terminateCollectedApps(appsToTerminate, delay);
}


// 卸载守护进程
void unloadDaemon(void) {
    NSLog(@"Daemon start unload");

    NSString *daemonLabel = @"com.tencent.yybmac.yybService";
    NSString *daemonPlist = [NSHomeDirectory() stringByAppendingPathComponent:
                             [NSString stringWithFormat:@"Library/LaunchAgents/%@.plist", daemonLabel]];
    
    NSFileManager *fm = [NSFileManager defaultManager];
    if ([fm fileExistsAtPath:daemonPlist]) {
        NSLog(@"Unloading daemon: %@", daemonLabel);
        
        // 卸载服务
        NSTask *unloadTask = [[NSTask alloc] init];
        [unloadTask setLaunchPath:@"/bin/launchctl"];
        NSString *domain = [NSString stringWithFormat:@"gui/%d", getuid()];
        [unloadTask setArguments:@[@"bootout", [NSString stringWithFormat:@"%@/%@", domain, daemonLabel]]];
        [unloadTask launch];
        [unloadTask waitUntilExit];
        
        // 删除plist文件
        NSError *error;
        if ([fm removeItemAtPath:daemonPlist error:&error]) {
            NSLog(@"Successfully removed daemon plist: %@", daemonPlist);
        } else {
            NSLog(@"Failed to remove daemon plist: %@, error: %@", daemonPlist, error);
        }
    } else {
        NSLog(@"Daemon plist not found: %@", daemonPlist);
    }

}

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        // 设置日志文件
        NSString *logPath = @"/Users/<USER>/YYBUninstaller.log";
        freopen([logPath fileSystemRepresentation], "a", stdout);
        freopen([logPath fileSystemRepresentation], "a", stderr);
        
        NSLog(@"=== STARTING UNINSTALL ===");
        
        // 主卸载流程 - 静默终止应用
        NSArray *bundlePrefixes = @[@"com.tencent.yybmac"];
        NSArray *processNames = @[@"yyb-mac-aarch64", @"yyb_aria2", @"YYBPackage"];
            
        terminateMultipleTargets(bundlePrefixes, processNames, 2.0);
        
        unloadDaemon();
        NSLog(@"Starting uninstallation process...");
        
        // 再次检查并强制终止仍在运行的进程
        for (NSString *process in processNames) {
            killProcessByName(process);
        }
        
        removeFiles();

        NSLog(@"Uninstallation process completed.");
    }
    return 0;
}


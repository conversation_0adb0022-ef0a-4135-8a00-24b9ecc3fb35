#import <QuartzCore/QuartzCore.h>
#import "LogoAnimationWindow.h"

@interface LogoAnimationWindow ()
@property (nonatomic, strong) CALayer *animateLayer;
@property (nonatomic, strong) NSImageView *logoImageView;
@property (nonatomic, copy) void (^ completionBlock)(void);
@property (nonatomic, assign) BOOL isAnimating;

@end

@implementation LogoAnimationWindow

+ (instancetype)sharedWindow {
    static LogoAnimationWindow *instance = nil;
    static dispatch_once_t onceToken;

    dispatch_once(&onceToken, ^{
        instance = [[LogoAnimationWindow alloc] init];
    });
    return instance;
}

- (instancetype)init {
    // 创建透明窗口
    NSRect screenFrame = [[NSScreen screens].firstObject frame];
    NSRect windowFrame = NSMakeRect(0, 0, screenFrame.size.width, screenFrame.size.height);

    self = [super initWithContentRect:windowFrame
                            styleMask:NSWindowStyleMaskBorderless
                              backing:NSBackingStoreBuffered
                                defer:NO];

    if (self) {
        [self setupWindow];
        [self setupLogoView];
    }

    return self;
}

- (void)setupWindow {
    self.contentView.wantsLayer = YES;
    self.backgroundColor = [NSColor clearColor];
    self.opaque = NO;
    self.hasShadow = NO;
    self.level = NSFloatingWindowLevel;
    self.ignoresMouseEvents = YES;
    self.collectionBehavior = NSWindowCollectionBehaviorCanJoinAllSpaces | NSWindowCollectionBehaviorStationary;
    self.orderedIndex = 0;
    [self center];
}

- (void)setupLogoView {
    NSSize size = self.contentView.frame.size;
    CGFloat width = floor(size.height / 5);
    NSRect animateRect = NSMakeRect((size.width - width) / 2, (size.height - width) / 2, width, width);

    NSBundle *bundle = [NSBundle bundleForClass:[self class]];
    NSString *path = [bundle pathForResource:@"logo" ofType:@"png"];

    self.animateLayer = [CALayer layer];
    self.animateLayer.frame = animateRect;
    self.animateLayer.contents = [[NSImage alloc] initWithContentsOfFile:path];
    [self.contentView.layer addSublayer:self.animateLayer];
}

- (void)startLoading {
    // 旋转动画
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.removedOnCompletion = NO;
    rotationAnimation.fillMode = kCAFillModeForwards;

    rotationAnimation.fromValue = @(0);
    rotationAnimation.toValue = @(2 * M_PI);
    rotationAnimation.duration = 1;
    rotationAnimation.repeatCount = HUGE_VAL;
    rotationAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    [self.animateLayer addAnimation:rotationAnimation forKey:@"loadingAnimation"];
    [self makeKeyAndOrderFront:nil];

}

- (void)showLogoAnimationWithCompletion:(void (^)(void))completion {
    if (self.isAnimating) {
        return;
    }
    [self.animateLayer removeAllAnimations];
    CGFloat fps = 30;
    CGFloat angle = 160.f;
    CGFloat rotationDuration = 16 / fps;
    CGFloat scaleDuration = 16 / fps;
    CGFloat scaleStartTime = 0 / fps;
    CGFloat opacityDuration = 17 / fps;
    CGFloat opacityStartTime = 0 / fps;
    CGFloat finalScale = 4;

    // 创建动画组
    CAAnimationGroup *animationGroup = [CAAnimationGroup animation];
    animationGroup.duration = MAX(rotationDuration, MAX(scaleStartTime + scaleDuration, opacityStartTime + opacityDuration));
    animationGroup.removedOnCompletion = NO;
    animationGroup.fillMode = kCAFillModeForwards;

    // 旋转动画
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.fromValue = @([[self.animateLayer valueForKeyPath:@"transform.rotation.z"] floatValue]);
    rotationAnimation.toValue = @(angle * M_PI / 180.0);
    rotationAnimation.duration = rotationDuration;
    rotationAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    rotationAnimation.repeatCount = 0;
    [self.animateLayer addAnimation:animationGroup forKey:@"loadingAnimation"];

    // 缩放动画
    CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    scaleAnimation.fromValue = @1.0;
    scaleAnimation.toValue = @(finalScale);
    scaleAnimation.beginTime = scaleStartTime;
    scaleAnimation.duration = scaleDuration;
    scaleAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];

    // 透明度动画
    CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
    opacityAnimation.fromValue = @1.0;
    opacityAnimation.toValue = @(0);
    opacityAnimation.beginTime = opacityStartTime;
    opacityAnimation.duration = opacityDuration;
    opacityAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];

    animationGroup.animations = @[scaleAnimation, opacityAnimation];  //rotationAnimation,
    [self.animateLayer addAnimation:animationGroup forKey:@"logoAnimation"];

    self.isAnimating = YES;
    self.completionBlock = completion;

    [self makeKeyAndOrderFront:nil];
}

- (void)hideWindow {
    [self orderOut:nil];
}

- (void)stopAnimation {
    [self.logoImageView.layer removeAllAnimations];
    self.isAnimating = NO;

    if (self.completionBlock) {
        self.completionBlock();
    }

    [self hideWindow];
}

@end

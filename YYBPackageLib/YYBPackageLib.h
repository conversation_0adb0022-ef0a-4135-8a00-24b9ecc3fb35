//
//  YYBPackageLib.h
//  YYBPackageLib
//
//  Created by <PERSON> on 2025/8/3.
//

#import <Foundation/Foundation.h>

//! Project version number for YYBPackageLib.
FOUNDATION_EXPORT double YYBPackageLibVersionNumber;

//! Project version string for YYBPackageLib.
FOUNDATION_EXPORT const unsigned char YYBPackageLibVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <YYBPackageLib/PublicHeader.h>

#import <YYBPackageLib/YYBAppDelegate.h>

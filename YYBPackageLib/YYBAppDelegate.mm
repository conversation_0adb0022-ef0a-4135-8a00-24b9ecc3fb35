//
//  YYBAppDelegate.m
//  YYBPackageLib
//
//  Created by <PERSON> on 2025/8/1.
//

#import <AppKit/AppKit.h>
#include <dlfcn.h>
#include <unistd.h>
#import <YYBMacFusionSDK/YYBMacFusionSDK.h>
#import <YYBMacFusionSDK/YYBMacFusionSDKConfig.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "LogoAnimationWindow.h"
#import "YYBAppDelegate.h"
#import "YYBDefine.h"
#import "YYBSocketEngine.h"

static NSString *const kTag = @"AppPackage";

typedef NSString *YYBSimCMD;
static YYBSimCMD const YYBSimCMDStart = @"startApp";
static YYBSimCMD const YYBSimCMDClose = @"close";

typedef NS_ENUM(NSInteger, YYBStartAppError) {
    YYBStartAppErrorNotInstall = -1023,        // 未安装
};

typedef NS_ENUM(NSInteger, YYBActionType) {
    YYBActionNone,  // 无操作不显示
    YYBGotoInstall, // 去下载
    YYBForceReOpen, // 强制重新打开
};

typedef NS_ENUM(NSInteger, YYBStartState) {
    YYBStartNone,  // 初始
    YYBEngineDownloading, // 引擎需下载
    YYBEngineStarting, // 引擎下载完成，开始启动
    YYBEngineIsReady, // engine ready
    YYBAppWillStart,    // app即将启动
    YYBAppDidStart, // app已启动，模拟器窗口已展示
    YYBAppUnInstall, // 启动失败
    YYBAppStartFailed, // 启动失败
};

@interface YYBAppDelegate ()
@property (nonatomic, copy) NSString *packageName;
@property (nonatomic, copy) NSString *appName;
@property (nonatomic, copy) NSString *yybModeDir;
@property (nonatomic, copy) NSString *modeBundleId;
@property (nonatomic, copy) NSString *iconUrl;
@property (nonatomic, assign) BOOL isTerminating;
@property (nonatomic, assign) BOOL isClosingByEngine;
@property (nonatomic, assign) BOOL appIsDisplayed;
@property (nonatomic, assign) BOOL engineTimeout;
@property (nonatomic, assign) YYBStartState startState;

@property (nonatomic, strong) NSWindow *window;
@property (nonatomic, strong) NSTextField *label;
@property (nonatomic, strong) NSView *buttonContainerView;
@property (nonatomic, strong) NSButton *actionBtn;
@end

@implementation YYBAppDelegate

- (void)applicationDidFinishLaunching:(NSNotification *)aNotification {
    NSDictionary *infoDict = [[NSBundle mainBundle] infoDictionary];

    self.yybModeDir = infoDict[@"YYBModeDir"];
    self.packageName = infoDict[@"YYBPackageName"];
    self.modeBundleId = infoDict[@"YYBModeBundleId"];
    self.iconUrl = infoDict[@"YYBIconUrl"] ? : @"";

    self.appName = [infoDict objectForKey:@"CFBundleDisplayName"];

    if (!self.appName) {
        self.appName = @"应用";
    }

    if (self.yybModeDir.length == 0 ||
        self.packageName.length == 0) {
        NSLog(@"[Error] Required configuration missing from Info.plist");
        exit(-1);
    }

    [self setupFustionSDK];
    [self initSocket];
    [[LogoAnimationWindow sharedWindow] startLoading];
    [self registerNotification];
}

- (void)registerNotification {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(onAppDidBecomeActive)
                                                 name:NSApplicationDidBecomeActiveNotification
                                               object:nil];
}

- (void)onAppDidBecomeActive {
    if (self.appIsDisplayed) {
        [self openEngineApp];
    }
}

- (BOOL)applicationShouldHandleReopen:(NSApplication *)sender hasVisibleWindows:(BOOL)flag {
    YYBMacLogInfo(kTag, @"Application reopening");
    [self openEngineApp];
    return YES;
}

- (void)applicationWillTerminate:(NSNotification *)aNotification {
    YYBMacLogInfo(kTag, @"Application will terminate:%d", self.isTerminating);

    if (self.isTerminating) {
        return;
    }

    self.isTerminating = YES;

    if (!self.isClosingByEngine) {
        [self closeEngineApp];
    }
}

- (NSApplicationTerminateReply)applicationShouldTerminate:(NSApplication *)sender {
    YYBMacLogInfo(kTag, @"Application should terminate:%d", self.isTerminating);

    if (self.isTerminating) {
        return NSTerminateNow;
    }

    self.isTerminating = YES;

    // 仅当非远程触发时才发送关闭消息
    if (!self.isClosingByEngine) {
        [self closeEngineApp];
    }

    return NSTerminateNow;
}

- (BOOL)applicationSupportsSecureRestorableState:(NSApplication *)app {
    NSLog(@"applicationSupportsSecureRestorableState");
    return YES;
}

- (void)dealloc {
    YYBMacLogInfo(kTag, @"dealloc");
}

- (void)setupFustionSDK {
    // bugly: https://bugly.woa.com/v2/setting/product?productId=e084796a57&pid=4  产品名称：YYB-MAC-APP
    YYBMacFusionSDKConfig *config = [[YYBMacFusionSDKConfig alloc] init];

    config.identifier = self.packageName;
    config.buglyAppId = @"e084796a57";
    config.buglyAppKey = @"73f003ac-a161-461f-8705-110f23020a82";

    // 大同轻量版：https://datong.woa.com/#/lite/BUSI303/list?inDTFrame=true&appId=dt_demoapp  产品名称：MAC-主端
    config.dataReportAppKey = @"0MAC06I37GRAL16X";

    // shiply相关：https://shiply.tds.woa.com/project/setting?projectId=693&appId=1060&envId=101685&scope=&tab=normal  项目名称: 应用宝-MAC-主端 产品名称：主App
    config.shiplyAppId = @"d74ec3f5fe";
    config.shiplyAppKey = @"141fa24e-fd08-4344-970a-88a8c3312bb8";
#ifdef DEBUG
    config.isReleaseApp = NO;
#else
    config.isReleaseApp = YES;
#endif
    config.isReleaseEnvironment = NO;
    config.userId = @"";
    config.channel = @"";
    // 指定根目录，确保log和mmkv都会聚合在同一个文件夹下（此文件夹为：商店bundleid默认生成文件夹）
    config.destDictName = @"com.tencent.yybmac";

    [[YYBMacFusionSDK sharedInstance] setupWithConfig:config
                                           completion:^(BOOL success, NSError *_Nullable error) {
        YYBMacLogInfo(kTag, @"setupFustionSDK succes %@, error = %@", @(success), error);
    }];
}

- (void)initSocket {
    bool ret = YYBSocketEngine::instance().connect(self.packageName.UTF8String, (int)getpid());

    if (ret) {
        [self registerHandlers];
        [self openEngineApp];
    } else {
        YYBMacLogError(kTag, @"initSocket failed");
    }
}

- (void)showLoading {
    if (self.window) {
        [self.window makeKeyAndOrderFront:nil];
        return;
    }

    NSWindowStyleMask style = NSWindowStyleMaskTitled | NSWindowStyleMaskFullSizeContentView;

    self.window = [[NSWindow alloc] initWithContentRect:NSMakeRect(0, 0, 300, 300)
                                              styleMask:style
                                                backing:NSBackingStoreBuffered
                                                  defer:NO];
    [self.window setTitleVisibility:NSWindowTitleHidden];
    [self.window setTitlebarAppearsTransparent:YES];
    [[self.window standardWindowButton:NSWindowCloseButton] setHidden:YES];
    [[self.window standardWindowButton:NSWindowMiniaturizeButton] setHidden:YES];
    [[self.window standardWindowButton:NSWindowZoomButton] setHidden:YES];
    [self.window center];

    NSTextField *label = [[NSTextField alloc] initWithFrame:NSMakeRect(25, 140, 250, 100)];
    [label setStringValue:[NSString stringWithFormat:@"%@\nLoading...", self.appName]];
    [label setFont:[NSFont systemFontOfSize:24]];
    [label setAlignment:NSTextAlignmentCenter];
    [label setBezeled:NO];
    [label setDrawsBackground:NO];
    [label setEditable:NO];
    [label setSelectable:NO];
    [label setLineBreakMode:NSLineBreakByWordWrapping];
    [label setUsesSingleLineMode:NO];

    if ([label respondsToSelector:@selector(setMaximumNumberOfLines:)]) {
        [label setMaximumNumberOfLines:0];
    }

    self.label = label;
    [[self.window contentView] addSubview:label];

    NSView *buttonContainerView = [[NSView alloc] initWithFrame:NSMakeRect(50, 40, 200, 40)];

    // 添加“重新打开”按钮
    NSButton *reopenButton = [[NSButton alloc] initWithFrame:NSMakeRect(0, 4, 90, 32)];
    [reopenButton setTitle:@"重新打开"];
    [reopenButton setButtonType:NSButtonTypeMomentaryPushIn];
    [reopenButton setBezelStyle:NSBezelStyleRounded];
    [reopenButton setTarget:self];
    [reopenButton setAction:@selector(reopenAction:)];
    [buttonContainerView addSubview:reopenButton];
    self.actionBtn = reopenButton;

    // 添加“关闭”按钮
    NSButton *closeButton = [[NSButton alloc] initWithFrame:NSMakeRect(110, 4, 90, 32)];
    [closeButton setTitle:@"关闭"];
    [closeButton setButtonType:NSButtonTypeMomentaryPushIn];
    [closeButton setBezelStyle:NSBezelStyleRounded];
    [closeButton setTarget:self];
    [closeButton setAction:@selector(closeAction:)];
    [buttonContainerView addSubview:closeButton];

    [[self.window contentView] addSubview:buttonContainerView];

    self.buttonContainerView = buttonContainerView;
    self.buttonContainerView.hidden = YES;
    self.buttonContainerView.identifier = @"none";
    [self.window makeKeyAndOrderFront:nil];
}

- (void)reopenAction:(NSButton *)sender {
    YYBMacLogInfo(kTag, @"重新打开按钮被点击");
    YYBActionType actionType = (YYBActionType)sender.tag;

    if (actionType == YYBForceReOpen) {
        [self openEngineAppWithForce:YES];
    } else if (actionType == YYBGotoInstall) {
        [self openAppDetailPage];
    }
}

- (void)openAppDetailPage {
    // 去详情页
    NSString *scheme = [NSString stringWithFormat:@"androws://app/callAppAutoAdaptiveEnv?game_type=1&pkgname=%@&yyb_downloader_channel_id=10000", self.packageName];
    NSURL *url = [NSURL URLWithString:scheme];

    if ([[NSWorkspace sharedWorkspace] openURL:url]) {
        NSLog(@"成功打开 scheme");
    } else {
        [self showMessage:@"请确定应用宝正确安装" startState:YYBAppStartFailed];
    }

    [NSApp terminate:self];
}

- (void)closeAction:(id)sender {
    YYBMacLogInfo(kTag, @"关闭按钮被点击");
    [NSApp terminate:self];
}

- (void)showMessage:(NSString *)message startState:(YYBStartState)startState {
    [self showMessage:message showAction:YYBActionNone startState:startState];
}

- (void)showMessage:(NSString *)message showAction:(YYBActionType)actionType startState:(YYBStartState)startState {
    if (self.startState == YYBAppDidStart) {
        // 已经启动不再接受
        return;
    }

    if (startState == YYBAppWillStart || startState == YYBEngineIsReady) {
        [self hideWindow];
        [[LogoAnimationWindow sharedWindow] showLogoAnimationWithCompletion:^{
        }];
    } else if (startState == YYBEngineStarting) {
        [self hideWindow];
        [[LogoAnimationWindow sharedWindow] startLoading];
    } else if (startState == YYBAppDidStart) {
        [self hideWindow];
        [[LogoAnimationWindow sharedWindow] hideWindow];
    } else {
        if (startState == YYBEngineDownloading) {
            [[LogoAnimationWindow sharedWindow] stopAnimation];
        }
        [self showLoading];
        self.label.stringValue = [NSString stringWithFormat:@"%@\n%@", self.appName, message];
        self.buttonContainerView.hidden = (actionType == YYBActionNone);
        self.actionBtn.tag = actionType;

        if (actionType == YYBGotoInstall) {
            self.actionBtn.title = @"去安装";
        } else if (actionType == YYBForceReOpen) {
            self.actionBtn.title = @"重新打开";
        }

        [self.window makeKeyAndOrderFront:nil];
        [[LogoAnimationWindow sharedWindow] hideWindow];
    }

    self.startState = startState;
}

- (void)hideWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.window orderOut:nil];
    });
}

- (void)openEngineApp {
    [self openEngineAppWithForce:NO];
}

- (void)openEngineAppWithForce:(BOOL)force {
    YYBSocketMsgData request;

    request.action = YYBSimCMDStart.UTF8String;
    request.to = kProcessEngine.UTF8String;
    request.info = std::map<std::string, std::string> {
        {
            "appName", self.appName.UTF8String
        },
        {
            "force", force ? "1" : "0"
        },
        {
            "iconUrl", self.iconUrl.UTF8String
        },
    };
    __weak typeof(self) weakSelf = self;
    YYBSocketEngine::instance().sendRequest(
        request,
        [weakSelf](const YYBSocketMsgData& response, bool is_error) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        if (strongSelf.appIsDisplayed) {
            return;
        }

        if (is_error || response.error_code != 0) {
            YYBMacLogInfo(kTag, @"openApp error:%lld", response.error_code);
            if (response.error_code == YYBStartAppErrorNotInstall) {
                [strongSelf showMessage:[NSString stringWithFormat:@"未安装:errCode:%lld", response.error_code] showAction:YYBGotoInstall startState:YYBAppStartFailed];
            } else if (response.error_code == 1001) {
                [strongSelf showMessage:[NSString stringWithFormat:@"打开超时:errCode:%lld", response.error_code] startState:YYBAppStartFailed];
            } else {
                [strongSelf showMessage:[NSString stringWithFormat:@"打开失败:errCode:%lld", response.error_code] startState:YYBAppStartFailed];
            }

            return;
        }

        if (response.info.has_value()) {
            const std::string state = response.info->at("state");
            YYBMacLogInfo(kTag, @"appIsDisplayed:%s", state.c_str());

            if (state == "0" || state == "true") {
                // TODO: 兼容两种回调数据，后续去掉
                strongSelf.appIsDisplayed = true;
                [strongSelf showMessage:nil startState:YYBAppDidStart];
            } else if (state == "-1023") {
                [strongSelf showMessage:[NSString stringWithFormat:@"未安装 :state:%@", [NSString stringWithUTF8String:state.c_str()]] startState:YYBAppUnInstall];
            } else if (state == "1001") {
                [strongSelf showMessage:[NSString stringWithFormat:@"打开超时 :state:%@", [NSString stringWithUTF8String:state.c_str()]] startState:YYBAppStartFailed];
            } else {
                [strongSelf showMessage:[NSString stringWithFormat:@"打开失败:state:%@", [NSString stringWithUTF8String:state.c_str()]] startState:YYBAppStartFailed];
            }
        } else {
            [strongSelf showMessage:[NSString stringWithFormat:@"打开失败"] startState:YYBAppStartFailed];
        }
    },
        10000
        );
}

- (void)closeEngineApp {
    YYBMacLogInfo(kTag, @"Close Engine App");
    std::string pkgName = self.packageName.UTF8String;
    YYBSocketMsgData msg;
    msg.action = YYBSimCMDClose.UTF8String;
    msg.to = kProcessEngine.UTF8String;
    msg.info = std::map<std::string, std::string> {
        {
            "appName", self.appName.UTF8String
        },
    };
    YYBSocketEngine::instance().sendMessage(msg);
}

- (void)registerHandlers {
    __weak typeof(self) weakSelf = self;
    YYBSocketEngine::MessageHandler engineIsReadyHandler = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        YYBMacLogInfo(kTag, @"startState:engineIsReady");
        [strongSelf showMessage:@"引擎Ready，正在打开" startState:YYBEngineIsReady];
        [strongSelf openEngineApp];
    };
    YYBSocketEngine::MessageHandler appWillDisplayedHandler = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        YYBMacLogInfo(kTag, @"startState appWillDisplayed");
        [strongSelf showMessage:nil startState:YYBAppWillStart];
    };

    YYBSocketEngine::MessageHandler engineCloseAppHandler = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        [strongSelf engineCloseApp];
    };

    YYBSocketEngine::MessageHandler appIsDisplayedHandler = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        if (msg.info.has_value()) {
            const std::string state = msg.info->at("state");
            YYBMacLogInfo(kTag, @"appIsDisplayed:%s", state.c_str());

            if (state == "0" || state == "true") {
                // TODO: 兼容两种回调数据，后续去掉
                strongSelf.appIsDisplayed = true;
                [strongSelf showMessage:nil startState:YYBAppDidStart];
            } else {
                [strongSelf showMessage:[NSString stringWithFormat:@"打开失败:state:%@", [NSString stringWithUTF8String:state.c_str()]] showAction:YYBForceReOpen startState:YYBAppStartFailed];
            }
        } else {
            strongSelf.appIsDisplayed = true;
            [strongSelf showMessage:nil startState:YYBAppDidStart];
        }
    };

    YYBSocketEngine::MessageHandler engineDownloadHandler = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        [strongSelf showMessage:@"引擎正在下载中..." startState:YYBEngineDownloading];
    };

    YYBSocketEngine::MessageHandler startEngineCallback = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        if (msg.info.has_value()) {
            const std::string status = msg.info->at("ret");

            if (status == "0") {
                [strongSelf showMessage:@"引擎启动成功\n正在加载引擎..." startState:YYBEngineStarting];
            } else {
                [strongSelf showMessage:@"引擎启动失败..." startState:YYBAppStartFailed];
            }
        }
    };
    YYBSocketEngine::MessageHandler engineReadyTimeoutCallback = [weakSelf](const YYBSocketMsgData& msg) {
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (!strongSelf) {
            return;
        }

        strongSelf.engineTimeout = YES;
        [strongSelf showMessage:@"引擎启动超时..." startState:YYBAppStartFailed];
    };

    std::unordered_map<std::string, YYBSocketEngine::MessageHandler> handlers;
    handlers["engineIsReady"] = engineIsReadyHandler;
    handlers["appWillDisplayed"] = appWillDisplayedHandler;
    handlers["engineCloseApp"] = engineCloseAppHandler;
    handlers["appIsDisplayed"] = appIsDisplayedHandler;
    handlers["engineCanNotBeUsed"] = engineDownloadHandler;
    handlers["startEngineResult"] = startEngineCallback;
    handlers["engineReadyTimeout"] = engineReadyTimeoutCallback;

    YYBSocketEngine::instance().registerMessageHandler(handlers);
}

- (void)engineCloseApp {
    YYBMacLogInfo(kTag, @"engineCloseApp:%d", self.isTerminating);

    if (self.isTerminating) {
        return;
    }

    self.isClosingByEngine = YES;
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSApp terminate:self];
    });
}

- (void)uninstallApp {
    if (![[NSFileManager defaultManager] fileExistsAtPath:@"/Applications/YYBMacApp.app"]) {
        YYBMacLogInfo(kTag, @"uninstallApp real");
        NSString *scriptPath = [[self yybSupportPath] stringByAppendingString:[NSString stringWithFormat:@"/helper/YYBUninstaller"]];
        NSTask *chmodTask = [[NSTask alloc] init];

        chmodTask.launchPath = @"/bin/chmod";
        chmodTask.arguments = @[@"+x", scriptPath];
        [chmodTask launch];
        [chmodTask waitUntilExit];

        NSTask *uninstallTask = [[NSTask alloc] init];
        uninstallTask.launchPath = scriptPath;
        [uninstallTask launch];
    }
}

- (NSString *)yybSupportPath {
    NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *appSupportPath = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/%@", self.modeBundleId]];

    return appSupportPath;
}

@end

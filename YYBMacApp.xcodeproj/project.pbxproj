// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		1804BB8F2E1BB80F00F38172 /* UniformTypeIdentifiers.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1804BB8E2E1BB80F00F38172 /* UniformTypeIdentifiers.framework */; };
		1876C76C2E0E768A00221428 /* libYYBIPC.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 18AB734D2E0D64080090400C /* libYYBIPC.dylib */; };
		1876C76D2E0E768A00221428 /* libYYBIPC.dylib in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 18AB734D2E0D64080090400C /* libYYBIPC.dylib */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		18889FF62E3F02D3006B55F0 /* YYBPackageLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 18889FF02E3F02D3006B55F0 /* YYBPackageLib.framework */; };
		18889FF72E3F02D3006B55F0 /* YYBPackageLib.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 18889FF02E3F02D3006B55F0 /* YYBPackageLib.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		1888A00F2E3F0589006B55F0 /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36517B852DE811CB00DAB6DF /* AppKit.framework */; };
		1888A0102E3F068A006B55F0 /* YYBPackageLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 18889FF02E3F02D3006B55F0 /* YYBPackageLib.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		1888A0122E3F0CAF006B55F0 /* YYBMacFusionSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */; };
		1888A0132E3F0CAF006B55F0 /* YYBMacFusionSDK.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		189440DC2E1E8CF000AA78E4 /* libYYBMacBusinessComponents.dylib in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 360253C12E0D1F9700368AFA /* libYYBMacBusinessComponents.dylib */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		18989AF22E30ED67004CAB31 /* YYBUninstaller in Copy Service Lib */ = {isa = PBXBuildFile; fileRef = 18989AE92E30E804004CAB31 /* YYBUninstaller */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		18AB73862E0D67C40090400C /* YYBService in Copy Service Lib */ = {isa = PBXBuildFile; fileRef = 18AB73712E0D65950090400C /* YYBService */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		18AB73A92E0D82E40090400C /* YYBPackage.app in Copy Package App */ = {isa = PBXBuildFile; fileRef = 18AB73902E0D767E0090400C /* YYBPackage.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		18AB746A2E0D93960090400C /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36517B852DE811CB00DAB6DF /* AppKit.framework */; };
		2E4316832E0432C500B635F0 /* YYBMacFusionSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */; };
		2E4316842E0432C500B635F0 /* YYBMacFusionSDK.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		360253D32E0D204C00368AFA /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36517B812DE811C100DAB6DF /* WebKit.framework */; };
		360253D42E0D205200368AFA /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36517B852DE811CB00DAB6DF /* AppKit.framework */; };
		360253D52E0D207E00368AFA /* YYBMacFusionSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */; };
		360253DD2E0D22DF00368AFA /* libYYBMacBusinessComponents.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 360253C12E0D1F9700368AFA /* libYYBMacBusinessComponents.dylib */; };
		3633D7B22E40D681007F258B /* libYYBIPC.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 18AB734D2E0D64080090400C /* libYYBIPC.dylib */; };
		3633EF792E4209CC007F258B /* YYBMacFusionSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */; };
		3695E8F92DEEFBF4002CF22A /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36517B812DE811C100DAB6DF /* WebKit.framework */; };
		3695E8FB2DEEFBFE002CF22A /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36517B852DE811CB00DAB6DF /* AppKit.framework */; settings = {ATTRIBUTES = (Required, ); }; };
		BCDC9BCC70A9433CF97E65E9 /* libPods-YYBMacApp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4061C13A2D093BAA83EF08E4 /* libPods-YYBMacApp.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1876C76E2E0E768A00221428 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 36517B4A2DE80EE200DAB6DF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18AB734C2E0D64080090400C;
			remoteInfo = YYBIPC;
		};
		18889FF42E3F02D3006B55F0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 36517B4A2DE80EE200DAB6DF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18889FEF2E3F02D3006B55F0;
			remoteInfo = YYBPackageLib;
		};
		189440DA2E1E8C7B00AA78E4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 36517B4A2DE80EE200DAB6DF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 360253C02E0D1F9700368AFA;
			remoteInfo = YYBMacBusinessComponents;
		};
		18AB73842E0D65A10090400C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 36517B4A2DE80EE200DAB6DF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18AB734C2E0D64080090400C;
			remoteInfo = YYBIPC;
		};
		3633D5BF2E40CEA5007F258B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 36517B4A2DE80EE200DAB6DF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 18AB734C2E0D64080090400C;
			remoteInfo = YYBIPC;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		185322DE2E055FA70062C00E /* Copy Service Lib */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 6;
			files = (
				18989AF22E30ED67004CAB31 /* YYBUninstaller in Copy Service Lib */,
				18AB73862E0D67C40090400C /* YYBService in Copy Service Lib */,
			);
			name = "Copy Service Lib";
			runOnlyForDeploymentPostprocessing = 0;
		};
		1888A0142E3F0CAF006B55F0 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				1888A0132E3F0CAF006B55F0 /* YYBMacFusionSDK.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		18989AE72E30E804004CAB31 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = /usr/share/man/man1/;
			dstSubfolderSpec = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 1;
		};
		18AB73A82E0D82C70090400C /* Copy Package App */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				18AB73A92E0D82E40090400C /* YYBPackage.app in Copy Package App */,
			);
			name = "Copy Package App";
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E2967EA2E1D22FA0056870E /* Copy Aria2 dylib */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = aria2c_libs;
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Copy Aria2 dylib";
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E4316852E0432C500B635F0 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				189440DC2E1E8CF000AA78E4 /* libYYBMacBusinessComponents.dylib in Embed Frameworks */,
				1876C76D2E0E768A00221428 /* libYYBIPC.dylib in Embed Frameworks */,
				18889FF72E3F02D3006B55F0 /* YYBPackageLib.framework in Embed Frameworks */,
				2E4316842E0432C500B635F0 /* YYBMacFusionSDK.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1804BB8E2E1BB80F00F38172 /* UniformTypeIdentifiers.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UniformTypeIdentifiers.framework; path = System/Library/Frameworks/UniformTypeIdentifiers.framework; sourceTree = SDKROOT; };
		185322DC2E0558710062C00E /* ServiceManagement.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ServiceManagement.framework; path = System/Library/Frameworks/ServiceManagement.framework; sourceTree = SDKROOT; };
		18889FF02E3F02D3006B55F0 /* YYBPackageLib.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = YYBPackageLib.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1888A00D2E3F03CC006B55F0 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		18989AE92E30E804004CAB31 /* YYBUninstaller */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = YYBUninstaller; sourceTree = BUILT_PRODUCTS_DIR; };
		18AB734D2E0D64080090400C /* libYYBIPC.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libYYBIPC.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		18AB73712E0D65950090400C /* YYBService */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = YYBService; sourceTree = BUILT_PRODUCTS_DIR; };
		18AB73902E0D767E0090400C /* YYBPackage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = YYBPackage.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = YYBMacFusionSDK.framework; path = "components/base-sdk/YYBMacFusionSDK.framework"; sourceTree = "<group>"; };
		360253C12E0D1F9700368AFA /* libYYBMacBusinessComponents.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libYYBMacBusinessComponents.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		36517B522DE80EE200DAB6DF /* 腾讯应用宝.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "腾讯应用宝.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		36517B812DE811C100DAB6DF /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		36517B832DE811C600DAB6DF /* AGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AGL.framework; path = System/Library/Frameworks/AGL.framework; sourceTree = SDKROOT; };
		36517B852DE811CB00DAB6DF /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = System/Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		3668A19C2E00410B00499B4E /* libQt6DBusAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6DBusAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6DBusAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A19D2E00410B00499B4E /* libQt6CoreAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6CoreAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6CoreAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A19E2E00410B00499B4E /* libQt6GuiAndroidEmu.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6GuiAndroidEmu.dylib; path = "components/qt/darwin-aarch64/lib/libQt6GuiAndroidEmu.dylib"; sourceTree = "<group>"; };
		3668A19F2E00410B00499B4E /* libQt6QuickAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6QuickAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6QuickAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1A42E00413800499B4E /* platforms */ = {isa = PBXFileReference; lastKnownFileType = folder; name = platforms; path = "components/qt/darwin-aarch64/plugins/platforms"; sourceTree = "<group>"; };
		3668A1A62E0042CB00499B4E /* libQt6GuiAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6GuiAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6GuiAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1A82E00430A00499B4E /* libQt6WidgetsAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6WidgetsAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6WidgetsAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1AA2E00438400499B4E /* libQt6QmlAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6QmlAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6QmlAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1AC2E0043B000499B4E /* libQt6NetworkAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6NetworkAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6NetworkAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1AE2E0043C500499B4E /* libQt6PositioningAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6PositioningAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6PositioningAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1B02E0043D700499B4E /* libQt6QmlModelsAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6QmlModelsAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6QmlModelsAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1B22E0043EF00499B4E /* libQt6QuickAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6QuickAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6QuickAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1B42E00440500499B4E /* libQt6QuickWidgetsAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6QuickWidgetsAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6QuickWidgetsAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1B62E00441300499B4E /* libQt6SvgAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6SvgAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6SvgAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1B82E00441D00499B4E /* libQt6SvgWidgetsAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6SvgWidgetsAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6SvgWidgetsAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1BA2E00445400499B4E /* libQt6WebChannelAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6WebChannelAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6WebChannelAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1BB2E00445400499B4E /* libQt6WebEngineWidgetsAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6WebEngineWidgetsAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6WebEngineWidgetsAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1BC2E00445400499B4E /* libQt6WebEngineCoreAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6WebEngineCoreAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6WebEngineCoreAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1BD2E00445400499B4E /* libQt6WebSocketsAndroidEmu.6.5.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libQt6WebSocketsAndroidEmu.6.5.3.dylib; path = "components/qt/darwin-aarch64/lib/libQt6WebSocketsAndroidEmu.6.5.3.dylib"; sourceTree = "<group>"; };
		3668A1C22E00447C00499B4E /* imageformats */ = {isa = PBXFileReference; lastKnownFileType = folder; name = imageformats; path = "components/qt/darwin-aarch64/plugins/imageformats"; sourceTree = "<group>"; };
		3668A1C32E00447C00499B4E /* styles */ = {isa = PBXFileReference; lastKnownFileType = folder; name = styles; path = "components/qt/darwin-aarch64/plugins/styles"; sourceTree = "<group>"; };
		3668A1C42E00447C00499B4E /* iconengines */ = {isa = PBXFileReference; lastKnownFileType = folder; name = iconengines; path = "components/qt/darwin-aarch64/plugins/iconengines"; sourceTree = "<group>"; };
		4061C13A2D093BAA83EF08E4 /* libPods-YYBMacApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-YYBMacApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B97ABE3407FE8EB3F4EC48B9 /* Pods-YYBMacApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YYBMacApp.debug.xcconfig"; path = "Target Support Files/Pods-YYBMacApp/Pods-YYBMacApp.debug.xcconfig"; sourceTree = "<group>"; };
		FB76CA9058E1223DECBE83E8 /* Pods-YYBMacApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YYBMacApp.release.xcconfig"; path = "Target Support Files/Pods-YYBMacApp/Pods-YYBMacApp.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		180DA2F32E4F74E2000F1AFA /* Exceptions for "Resources" folder in "YYBMacApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				logo.png,
			);
			target = 36517B512DE80EE200DAB6DF /* YYBMacApp */;
		};
		180DA2F42E4F74E2000F1AFA /* Exceptions for "Resources" folder in "YYBPackageLib" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				logo.png,
			);
			target = 18889FEF2E3F02D3006B55F0 /* YYBPackageLib */;
		};
		18889FFB2E3F02D3006B55F0 /* Exceptions for "YYBPackageLib" folder in "YYBPackageLib" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			publicHeaders = (
				YYBAppDelegate.h,
				YYBPackageLib.h,
			);
			target = 18889FEF2E3F02D3006B55F0 /* YYBPackageLib */;
		};
		1888A00B2E3F0346006B55F0 /* Exceptions for "components/ipc" folder in "YYBPackageLib" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				YYBServerInstaller.h,
				YYBServerInstaller.mm,
				YYBSocketClient/YYBSocketClient.cpp,
				YYBSocketClient/YYBSocketClient.h,
				YYBSocketClient/YYBSocketEngine.cpp,
				YYBSocketClient/YYBSocketEngine.h,
				YYBSocketClient/YYBSocketMsgData.cpp,
				YYBSocketClient/YYBSocketMsgData.h,
			);
			target = 18889FEF2E3F02D3006B55F0 /* YYBPackageLib */;
		};
		18AB73882E0D747E0090400C /* Exceptions for "YYBService" folder in "YYBMacApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				yybService.plist,
			);
			target = 36517B512DE80EE200DAB6DF /* YYBMacApp */;
		};
		2E7D4A452E2E74C700B45C01 /* Exceptions for "components/business-components" folder in "YYBMacBusinessComponents" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Aria2/aria2c_libs/libcares.2.dylib,
				Aria2/aria2c_libs/libcrypto.3.dylib,
				Aria2/aria2c_libs/libssh2.1.dylib,
				Aria2/aria2c_libs/libssl.3.dylib,
			);
			target = 360253C02E0D1F9700368AFA /* YYBMacBusinessComponents */;
		};
		36517B9B2DE8342900DAB6DF /* Exceptions for "YYBMacApp" folder in "YYBMacApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 36517B512DE80EE200DAB6DF /* YYBMacApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */
		1888A00C2E3F0346006B55F0 /* Exceptions for "components/ipc" folder in "Compile Sources" phase from "YYBPackageLib" target */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			buildPhase = 18889FEC2E3F02D3006B55F0 /* Sources */;
			membershipExceptions = (
				YYBSocketClient/nlohmann/json_fwd.hpp,
				YYBSocketClient/nlohmann/json.hpp,
			);
		};
		2E7D4A4B2E2E74D000B45C01 /* Exceptions for "components/business-components" folder in "Copy Aria2 dylib" phase from "YYBMacApp" target */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			attributesByRelativePath = {
				Aria2/aria2c_libs/libaria2.0.dylib = (CodeSignOnCopy, );
				Aria2/aria2c_libs/libcares.2.dylib = (CodeSignOnCopy, );
				Aria2/aria2c_libs/libcrypto.3.dylib = (CodeSignOnCopy, );
				Aria2/aria2c_libs/libssh2.1.dylib = (CodeSignOnCopy, );
				Aria2/aria2c_libs/libssl.3.dylib = (CodeSignOnCopy, );
			};
			buildPhase = 2E2967EA2E1D22FA0056870E /* Copy Aria2 dylib */;
			membershipExceptions = (
				Aria2/aria2c_libs/libaria2.0.dylib,
				Aria2/aria2c_libs/libcares.2.dylib,
				Aria2/aria2c_libs/libcrypto.3.dylib,
				Aria2/aria2c_libs/libssh2.1.dylib,
				Aria2/aria2c_libs/libssl.3.dylib,
			);
		};
/* End PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		18889FF12E3F02D3006B55F0 /* YYBPackageLib */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				18889FFB2E3F02D3006B55F0 /* Exceptions for "YYBPackageLib" folder in "YYBPackageLib" target */,
			);
			path = YYBPackageLib;
			sourceTree = "<group>";
		};
		18989AEA2E30E804004CAB31 /* YYBUninstaller */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = YYBUninstaller;
			sourceTree = "<group>";
		};
		18AB734E2E0D64080090400C /* components/ipc */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				1888A00B2E3F0346006B55F0 /* Exceptions for "components/ipc" folder in "YYBPackageLib" target */,
				1888A00C2E3F0346006B55F0 /* Exceptions for "components/ipc" folder in "Compile Sources" phase from "YYBPackageLib" target */,
			);
			path = components/ipc;
			sourceTree = "<group>";
		};
		18AB73722E0D65950090400C /* YYBService */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				18AB73882E0D747E0090400C /* Exceptions for "YYBService" folder in "YYBMacApp" target */,
			);
			path = YYBService;
			sourceTree = "<group>";
		};
		18AB73912E0D767E0090400C /* Package */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Package;
			sourceTree = "<group>";
		};
		360253E92E0D30D900368AFA /* components/business-components */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				2E7D4A4B2E2E74D000B45C01 /* Exceptions for "components/business-components" folder in "Copy Aria2 dylib" phase from "YYBMacApp" target */,
				2E7D4A452E2E74C700B45C01 /* Exceptions for "components/business-components" folder in "YYBMacBusinessComponents" target */,
			);
			path = "components/business-components";
			sourceTree = "<group>";
		};
		36517B542DE80EE200DAB6DF /* YYBMacApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				36517B9B2DE8342900DAB6DF /* Exceptions for "YYBMacApp" folder in "YYBMacApp" target */,
			);
			path = YYBMacApp;
			sourceTree = "<group>";
		};
		3695EB172DF17B7F002CF22A /* Resources */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				180DA2F32E4F74E2000F1AFA /* Exceptions for "Resources" folder in "YYBMacApp" target */,
				180DA2F42E4F74E2000F1AFA /* Exceptions for "Resources" folder in "YYBPackageLib" target */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		18889FED2E3F02D3006B55F0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1888A00F2E3F0589006B55F0 /* AppKit.framework in Frameworks */,
				1888A0122E3F0CAF006B55F0 /* YYBMacFusionSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18989AE62E30E804004CAB31 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB736E2E0D65950090400C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB738D2E0D767E0090400C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1888A0102E3F068A006B55F0 /* YYBPackageLib.framework in Frameworks */,
				18AB746A2E0D93960090400C /* AppKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		360253BF2E0D1F9700368AFA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3633D7B22E40D681007F258B /* libYYBIPC.dylib in Frameworks */,
				360253D52E0D207E00368AFA /* YYBMacFusionSDK.framework in Frameworks */,
				360253D42E0D205200368AFA /* AppKit.framework in Frameworks */,
				360253D32E0D204C00368AFA /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3633EF742E420959007F258B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3633EF792E4209CC007F258B /* YYBMacFusionSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		36517B4F2DE80EE200DAB6DF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1804BB8F2E1BB80F00F38172 /* UniformTypeIdentifiers.framework in Frameworks */,
				1876C76C2E0E768A00221428 /* libYYBIPC.dylib in Frameworks */,
				360253DD2E0D22DF00368AFA /* libYYBMacBusinessComponents.dylib in Frameworks */,
				18889FF62E3F02D3006B55F0 /* YYBPackageLib.framework in Frameworks */,
				3695E8FB2DEEFBFE002CF22A /* AppKit.framework in Frameworks */,
				3695E8F92DEEFBF4002CF22A /* WebKit.framework in Frameworks */,
				2E4316832E0432C500B635F0 /* YYBMacFusionSDK.framework in Frameworks */,
				BCDC9BCC70A9433CF97E65E9 /* libPods-YYBMacApp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		36021F852E03ED6B00368AFA /* Qt */ = {
			isa = PBXGroup;
			children = (
				36021F872E03ED9D00368AFA /* plugins */,
				36021F862E03ED8F00368AFA /* lib */,
			);
			name = Qt;
			sourceTree = "<group>";
		};
		36021F862E03ED8F00368AFA /* lib */ = {
			isa = PBXGroup;
			children = (
				3668A1BA2E00445400499B4E /* libQt6WebChannelAndroidEmu.6.5.3.dylib */,
				3668A1BC2E00445400499B4E /* libQt6WebEngineCoreAndroidEmu.6.5.3.dylib */,
				3668A1BB2E00445400499B4E /* libQt6WebEngineWidgetsAndroidEmu.6.5.3.dylib */,
				3668A1BD2E00445400499B4E /* libQt6WebSocketsAndroidEmu.6.5.3.dylib */,
				3668A1B82E00441D00499B4E /* libQt6SvgWidgetsAndroidEmu.6.5.3.dylib */,
				3668A1B62E00441300499B4E /* libQt6SvgAndroidEmu.6.5.3.dylib */,
				3668A1B42E00440500499B4E /* libQt6QuickWidgetsAndroidEmu.6.5.3.dylib */,
				3668A1B22E0043EF00499B4E /* libQt6QuickAndroidEmu.6.5.3.dylib */,
				3668A1B02E0043D700499B4E /* libQt6QmlModelsAndroidEmu.6.5.3.dylib */,
				3668A1AE2E0043C500499B4E /* libQt6PositioningAndroidEmu.6.5.3.dylib */,
				3668A1AC2E0043B000499B4E /* libQt6NetworkAndroidEmu.6.5.3.dylib */,
				3668A1AA2E00438400499B4E /* libQt6QmlAndroidEmu.6.5.3.dylib */,
				3668A1A82E00430A00499B4E /* libQt6WidgetsAndroidEmu.6.5.3.dylib */,
				3668A1A62E0042CB00499B4E /* libQt6GuiAndroidEmu.6.5.3.dylib */,
				3668A19D2E00410B00499B4E /* libQt6CoreAndroidEmu.6.5.3.dylib */,
				3668A19C2E00410B00499B4E /* libQt6DBusAndroidEmu.6.5.3.dylib */,
				3668A19E2E00410B00499B4E /* libQt6GuiAndroidEmu.dylib */,
				3668A19F2E00410B00499B4E /* libQt6QuickAndroidEmu.6.5.3.dylib */,
			);
			name = lib;
			sourceTree = "<group>";
		};
		36021F872E03ED9D00368AFA /* plugins */ = {
			isa = PBXGroup;
			children = (
				3668A1A42E00413800499B4E /* platforms */,
				3668A1C42E00447C00499B4E /* iconengines */,
				3668A1C22E00447C00499B4E /* imageformats */,
				3668A1C32E00447C00499B4E /* styles */,
			);
			name = plugins;
			sourceTree = "<group>";
		};
		36517B492DE80EE200DAB6DF = {
			isa = PBXGroup;
			children = (
				36517B542DE80EE200DAB6DF /* YYBMacApp */,
				360253E92E0D30D900368AFA /* components/business-components */,
				18AB734E2E0D64080090400C /* components/ipc */,
				18AB73722E0D65950090400C /* YYBService */,
				18AB73912E0D767E0090400C /* Package */,
				18989AEA2E30E804004CAB31 /* YYBUninstaller */,
				18889FF12E3F02D3006B55F0 /* YYBPackageLib */,
				3695EB172DF17B7F002CF22A /* Resources */,
				36517B802DE811C100DAB6DF /* Frameworks */,
				36517B532DE80EE200DAB6DF /* Products */,
				9D972FCA88BDE0F5B4874C87 /* Pods */,
			);
			sourceTree = "<group>";
		};
		36517B532DE80EE200DAB6DF /* Products */ = {
			isa = PBXGroup;
			children = (
				36517B522DE80EE200DAB6DF /* 腾讯应用宝.app */,
				360253C12E0D1F9700368AFA /* libYYBMacBusinessComponents.dylib */,
				18AB734D2E0D64080090400C /* libYYBIPC.dylib */,
				18AB73712E0D65950090400C /* YYBService */,
				18AB73902E0D767E0090400C /* YYBPackage.app */,
				18989AE92E30E804004CAB31 /* YYBUninstaller */,
				18889FF02E3F02D3006B55F0 /* YYBPackageLib.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		36517B802DE811C100DAB6DF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1888A00D2E3F03CC006B55F0 /* libc++.tbd */,
				1804BB8E2E1BB80F00F38172 /* UniformTypeIdentifiers.framework */,
				185322DC2E0558710062C00E /* ServiceManagement.framework */,
				2E43167E2E0432A800B635F0 /* YYBMacFusionSDK.framework */,
				36021F852E03ED6B00368AFA /* Qt */,
				36517B852DE811CB00DAB6DF /* AppKit.framework */,
				36517B832DE811C600DAB6DF /* AGL.framework */,
				36517B812DE811C100DAB6DF /* WebKit.framework */,
				4061C13A2D093BAA83EF08E4 /* libPods-YYBMacApp.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9D972FCA88BDE0F5B4874C87 /* Pods */ = {
			isa = PBXGroup;
			children = (
				B97ABE3407FE8EB3F4EC48B9 /* Pods-YYBMacApp.debug.xcconfig */,
				FB76CA9058E1223DECBE83E8 /* Pods-YYBMacApp.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		18889FEB2E3F02D3006B55F0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB73492E0D64080090400C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		360253BD2E0D1F9700368AFA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		18889FEF2E3F02D3006B55F0 /* YYBPackageLib */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18889FF82E3F02D3006B55F0 /* Build configuration list for PBXNativeTarget "YYBPackageLib" */;
			buildPhases = (
				18889FEB2E3F02D3006B55F0 /* Headers */,
				18889FEC2E3F02D3006B55F0 /* Sources */,
				18889FED2E3F02D3006B55F0 /* Frameworks */,
				18889FEE2E3F02D3006B55F0 /* Resources */,
				1888A0142E3F0CAF006B55F0 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18889FF12E3F02D3006B55F0 /* YYBPackageLib */,
			);
			name = YYBPackageLib;
			productName = YYBPackageLib;
			productReference = 18889FF02E3F02D3006B55F0 /* YYBPackageLib.framework */;
			productType = "com.apple.product-type.framework";
		};
		18989AE82E30E804004CAB31 /* YYBUninstaller */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18989AED2E30E804004CAB31 /* Build configuration list for PBXNativeTarget "YYBUninstaller" */;
			buildPhases = (
				18989AE52E30E804004CAB31 /* Sources */,
				18989AE62E30E804004CAB31 /* Frameworks */,
				18989AE72E30E804004CAB31 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18989AEA2E30E804004CAB31 /* YYBUninstaller */,
			);
			name = YYBUninstaller;
			productName = YYBUninstaller;
			productReference = 18989AE92E30E804004CAB31 /* YYBUninstaller */;
			productType = "com.apple.product-type.tool";
		};
		18AB734C2E0D64080090400C /* YYBIPC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18AB73532E0D64080090400C /* Build configuration list for PBXNativeTarget "YYBIPC" */;
			buildPhases = (
				18AB73492E0D64080090400C /* Headers */,
				18AB734A2E0D64080090400C /* Sources */,
				3633EF742E420959007F258B /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18AB734E2E0D64080090400C /* components/ipc */,
			);
			name = YYBIPC;
			productName = YYBIPC;
			productReference = 18AB734D2E0D64080090400C /* libYYBIPC.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
		18AB73702E0D65950090400C /* YYBService */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18AB73752E0D65950090400C /* Build configuration list for PBXNativeTarget "YYBService" */;
			buildPhases = (
				18AB736D2E0D65950090400C /* Sources */,
				18AB736E2E0D65950090400C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				18AB73852E0D65A10090400C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				18AB73722E0D65950090400C /* YYBService */,
			);
			name = YYBService;
			productName = YYBService;
			productReference = 18AB73712E0D65950090400C /* YYBService */;
			productType = "com.apple.product-type.tool";
		};
		18AB738F2E0D767E0090400C /* YYBPackage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18AB73A02E0D767F0090400C /* Build configuration list for PBXNativeTarget "YYBPackage" */;
			buildPhases = (
				18AB738C2E0D767E0090400C /* Sources */,
				18AB738D2E0D767E0090400C /* Frameworks */,
				18AB738E2E0D767E0090400C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18AB73912E0D767E0090400C /* Package */,
			);
			name = YYBPackage;
			productName = Package;
			productReference = 18AB73902E0D767E0090400C /* YYBPackage.app */;
			productType = "com.apple.product-type.application";
		};
		360253C02E0D1F9700368AFA /* YYBMacBusinessComponents */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 360253C72E0D1F9700368AFA /* Build configuration list for PBXNativeTarget "YYBMacBusinessComponents" */;
			buildPhases = (
				360253BD2E0D1F9700368AFA /* Headers */,
				360253BE2E0D1F9700368AFA /* Sources */,
				360253BF2E0D1F9700368AFA /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				3633D5C02E40CEA5007F258B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				360253E92E0D30D900368AFA /* components/business-components */,
			);
			name = YYBMacBusinessComponents;
			productName = YYBMacComponents;
			productReference = 360253C12E0D1F9700368AFA /* libYYBMacBusinessComponents.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
		36517B512DE80EE200DAB6DF /* YYBMacApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 36517B652DE80EE300DAB6DF /* Build configuration list for PBXNativeTarget "YYBMacApp" */;
			buildPhases = (
				1A94B5D969DCAF73DA2F9E39 /* [CP] Check Pods Manifest.lock */,
				36517B4E2DE80EE200DAB6DF /* Sources */,
				36517B4F2DE80EE200DAB6DF /* Frameworks */,
				36517B502DE80EE200DAB6DF /* Resources */,
				2E4316852E0432C500B635F0 /* Embed Frameworks */,
				185322DE2E055FA70062C00E /* Copy Service Lib */,
				18AB73A82E0D82C70090400C /* Copy Package App */,
				1876CA2A2E153A5F00221428 /* Write YYBService MD5 To Plist */,
				2E2967EA2E1D22FA0056870E /* Copy Aria2 dylib */,
				A9289647FECB276A58AA32ED /* [CP] Copy Pods Resources */,
				18986EC22E2F99A7004CAB31 /* Auto Increase Build Number */,
			);
			buildRules = (
			);
			dependencies = (
				189440DB2E1E8C7B00AA78E4 /* PBXTargetDependency */,
				1876C76F2E0E768A00221428 /* PBXTargetDependency */,
				18889FF52E3F02D3006B55F0 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				36517B542DE80EE200DAB6DF /* YYBMacApp */,
				3695EB172DF17B7F002CF22A /* Resources */,
			);
			name = YYBMacApp;
			productName = YYBMacApp;
			productReference = 36517B522DE80EE200DAB6DF /* 腾讯应用宝.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		36517B4A2DE80EE200DAB6DF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1630;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					18889FEF2E3F02D3006B55F0 = {
						CreatedOnToolsVersion = 16.4;
					};
					18989AE82E30E804004CAB31 = {
						CreatedOnToolsVersion = 16.4;
					};
					18AB734C2E0D64080090400C = {
						CreatedOnToolsVersion = 16.4;
					};
					18AB73702E0D65950090400C = {
						CreatedOnToolsVersion = 16.4;
					};
					18AB738F2E0D767E0090400C = {
						CreatedOnToolsVersion = 16.4;
					};
					360253C02E0D1F9700368AFA = {
						CreatedOnToolsVersion = 16.4;
					};
					36517B512DE80EE200DAB6DF = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 36517B4D2DE80EE200DAB6DF /* Build configuration list for PBXProject "YYBMacApp" */;
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 36517B492DE80EE200DAB6DF;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 36517B532DE80EE200DAB6DF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				36517B512DE80EE200DAB6DF /* YYBMacApp */,
				360253C02E0D1F9700368AFA /* YYBMacBusinessComponents */,
				18AB734C2E0D64080090400C /* YYBIPC */,
				18AB73702E0D65950090400C /* YYBService */,
				18AB738F2E0D767E0090400C /* YYBPackage */,
				18989AE82E30E804004CAB31 /* YYBUninstaller */,
				18889FEF2E3F02D3006B55F0 /* YYBPackageLib */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		18889FEE2E3F02D3006B55F0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB738E2E0D767E0090400C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		36517B502DE80EE200DAB6DF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1876CA2A2E153A5F00221428 /* Write YYBService MD5 To Plist */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Write YYBService MD5 To Plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\nAPP_BUNDLE_PATH=\"${BUILT_PRODUCTS_DIR}/${CONTENTS_FOLDER_PATH}\"\nYYB_FILE=\"${APP_BUNDLE_PATH}/MacOS/YYBService\"\nPLIST_FILE=\"${APP_BUNDLE_PATH}/Resources/yybService.plist\"\n\nMD5_VALUE=$(md5 -q \"$YYB_FILE\")\n\n/usr/libexec/PlistBuddy -c \"Set :md5 $MD5_VALUE\" \"$PLIST_FILE\" 2>/dev/null\nif [ $? -ne 0 ]; then\n    /usr/libexec/PlistBuddy -c \"Add :md5 string $MD5_VALUE\" \"$PLIST_FILE\"\nfi\n\necho \"MD5 ($YYB_FILE) = $MD5_VALUE 已写入 $PLIST_FILE\"\n\nYYB_FILE=\"${APP_BUNDLE_PATH}/Resources/YYBPackage.app/Contents/MacOS/YYBPackage\"\nPLIST_FILE=\"${APP_BUNDLE_PATH}/Resources/YYBPackage.app/Contents/Info.plist\"\n\nMD5_VALUE=$(md5 -q \"$YYB_FILE\")\n\n/usr/libexec/PlistBuddy -c \"Set :md5 $MD5_VALUE\" \"$PLIST_FILE\" 2>/dev/null\nif [ $? -ne 0 ]; then\n    /usr/libexec/PlistBuddy -c \"Add :md5 string $MD5_VALUE\" \"$PLIST_FILE\"\nfi\n\necho \"MD5 ($YYB_FILE) = $MD5_VALUE 已写入 $PLIST_FILE\"\n";
		};
		18986EC22E2F99A7004CAB31 /* Auto Increase Build Number */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Auto Increase Build Number";
			outputFileListPaths = (
			);
			outputPaths = (
				"${PROJECT_DIR}/${PROJECT_NAME}/Info.plist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [ \"${CONFIGURATION}\" = \"Release\" ]; then\n\n# 读取当前 build 号\nbuildNumber=$(/usr/libexec/PlistBuddy -c \"Print :CURRENT_PROJECT_VERSION\" \"${PROJECT_DIR}/${PROJECT_NAME}.xcodeproj/project.pbxproj\" 2>/dev/null)\n\n# 也可以用 agvtool\nbuildNumber=$(agvtool what-version -terse)\n\nif ! [[ \"$buildNumber\" =~ ^[0-9]+$ ]]; then\n    buildNumber=1\nfi\n\n# 递增 build 号\nnewBuildNumber=$((buildNumber + 1))\n\n# 设置新的 build 号\nagvtool new-version -all $newBuildNumber\n\necho \"Auto-incremented build number to $newBuildNumber\"\n\nfi\n";
		};
		1A94B5D969DCAF73DA2F9E39 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-YYBMacApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A9289647FECB276A58AA32ED /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YYBMacApp/Pods-YYBMacApp-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YYBMacApp/Pods-YYBMacApp-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-YYBMacApp/Pods-YYBMacApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		18889FEC2E3F02D3006B55F0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18989AE52E30E804004CAB31 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB734A2E0D64080090400C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB736D2E0D65950090400C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		18AB738C2E0D767E0090400C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		360253BE2E0D1F9700368AFA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		36517B4E2DE80EE200DAB6DF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1876C76F2E0E768A00221428 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18AB734C2E0D64080090400C /* YYBIPC */;
			targetProxy = 1876C76E2E0E768A00221428 /* PBXContainerItemProxy */;
		};
		18889FF52E3F02D3006B55F0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18889FEF2E3F02D3006B55F0 /* YYBPackageLib */;
			targetProxy = 18889FF42E3F02D3006B55F0 /* PBXContainerItemProxy */;
		};
		189440DB2E1E8C7B00AA78E4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 360253C02E0D1F9700368AFA /* YYBMacBusinessComponents */;
			targetProxy = 189440DA2E1E8C7B00AA78E4 /* PBXContainerItemProxy */;
		};
		18AB73852E0D65A10090400C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18AB734C2E0D64080090400C /* YYBIPC */;
			targetProxy = 18AB73842E0D65A10090400C /* PBXContainerItemProxy */;
		};
		3633D5C02E40CEA5007F258B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 18AB734C2E0D64080090400C /* YYBIPC */;
			targetProxy = 3633D5BF2E40CEA5007F258B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		18889FF92E3F02D3006B55F0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 35;
				DEAD_CODE_STRIPPING = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 35;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_CFLAGS = "-fvisibility=default";
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac.app.YYBPackageLib;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		18889FFA2E3F02D3006B55F0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 35;
				DEAD_CODE_STRIPPING = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 35;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_CFLAGS = "-fvisibility=default";
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac.app.YYBPackageLib;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		18989AEE2E30E804004CAB31 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				INSTALL_PATH = "";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		18989AEF2E30E804004CAB31 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				INSTALL_PATH = "";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		18AB73542E0D64080090400C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 35;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				EXECUTABLE_PREFIX = lib;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				LD_DYLIB_INSTALL_NAME = "@rpath/libYYBIPC.dylib";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		18AB73552E0D64080090400C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 35;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				EXECUTABLE_PREFIX = lib;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				LD_DYLIB_INSTALL_NAME = "@rpath/libYYBIPC.dylib";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		18AB73762E0D65950090400C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Manual;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/YYBService/utils/**";
				INSTALL_PATH = "";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac.yybService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		18AB73772E0D65950090400C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Manual;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/YYBService/utils/**";
				INSTALL_PATH = "";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac.yybService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		18AB73A12E0D767F0090400C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_ENTITLEMENTS = Package/Package.entitlements;
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 35;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Package/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "手机应用宝";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				INSTALL_PATH = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
			};
			name = Debug;
		};
		18AB73A22E0D767F0090400C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_ENTITLEMENTS = Package/Package.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 35;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Package/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "手机应用宝";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				INSTALL_PATH = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
			};
			name = Release;
		};
		360253C82E0D1F9700368AFA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 35;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				FRAMEWORK_SEARCH_PATHS = "$(PROJECT_DIR)/components/base-sdk";
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				HEADER_SEARCH_PATHS = "";
				LD_DYLIB_INSTALL_NAME = "@rpath/$(EXECUTABLE_PATH)";
				LIBRARY_SEARCH_PATHS = "$(PROJECT_DIR)/components/business-components/Aria2/aria2c_libs";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		360253C92E0D1F9700368AFA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 35;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				FRAMEWORK_SEARCH_PATHS = "$(PROJECT_DIR)/components/base-sdk";
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				HEADER_SEARCH_PATHS = "";
				LD_DYLIB_INSTALL_NAME = "@rpath/$(EXECUTABLE_PATH)";
				LIBRARY_SEARCH_PATHS = "$(PROJECT_DIR)/components/business-components/Aria2/aria2c_libs";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		36517B632DE80EE300DAB6DF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
			};
			name = Debug;
		};
		36517B642DE80EE300DAB6DF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "RELEASE=1";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
			};
			name = Release;
		};
		36517B662DE80EE300DAB6DF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B97ABE3407FE8EB3F4EC48B9 /* Pods-YYBMacApp.debug.xcconfig */;
			buildSettings = {
				ARCHS = arm64;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = YYBMacApp/YYBMacApp.entitlements;
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 35;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = YYBMacApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "腾讯应用宝";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac;
				PRODUCT_NAME = "腾讯应用宝";
				PROVISIONING_PROFILE_SPECIFIER = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SYSTEM_FRAMEWORK_SEARCH_PATHS = "";
				SYSTEM_HEADER_SEARCH_PATHS = "";
			};
			name = Debug;
		};
		36517B672DE80EE300DAB6DF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FB76CA9058E1223DECBE83E8 /* Pods-YYBMacApp.release.xcconfig */;
			buildSettings = {
				ARCHS = arm64;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = YYBMacApp/YYBMacApp.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 35;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/components/base-sdk",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = YYBMacApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "腾讯应用宝";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.tencent.yybmac;
				PRODUCT_NAME = "腾讯应用宝";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SYSTEM_FRAMEWORK_SEARCH_PATHS = "";
				SYSTEM_HEADER_SEARCH_PATHS = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18889FF82E3F02D3006B55F0 /* Build configuration list for PBXNativeTarget "YYBPackageLib" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18889FF92E3F02D3006B55F0 /* Debug */,
				18889FFA2E3F02D3006B55F0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18989AED2E30E804004CAB31 /* Build configuration list for PBXNativeTarget "YYBUninstaller" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18989AEE2E30E804004CAB31 /* Debug */,
				18989AEF2E30E804004CAB31 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18AB73532E0D64080090400C /* Build configuration list for PBXNativeTarget "YYBIPC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18AB73542E0D64080090400C /* Debug */,
				18AB73552E0D64080090400C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18AB73752E0D65950090400C /* Build configuration list for PBXNativeTarget "YYBService" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18AB73762E0D65950090400C /* Debug */,
				18AB73772E0D65950090400C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18AB73A02E0D767F0090400C /* Build configuration list for PBXNativeTarget "YYBPackage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18AB73A12E0D767F0090400C /* Debug */,
				18AB73A22E0D767F0090400C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		360253C72E0D1F9700368AFA /* Build configuration list for PBXNativeTarget "YYBMacBusinessComponents" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				360253C82E0D1F9700368AFA /* Debug */,
				360253C92E0D1F9700368AFA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		36517B4D2DE80EE200DAB6DF /* Build configuration list for PBXProject "YYBMacApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				36517B632DE80EE300DAB6DF /* Debug */,
				36517B642DE80EE300DAB6DF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		36517B652DE80EE300DAB6DF /* Build configuration list for PBXNativeTarget "YYBMacApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				36517B662DE80EE300DAB6DF /* Debug */,
				36517B672DE80EE300DAB6DF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 36517B4A2DE80EE200DAB6DF /* Project object */;
}

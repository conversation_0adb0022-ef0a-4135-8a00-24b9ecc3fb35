//
//  YYBMacLaunchTimer.m
//  YYBService
//
//  Created by bethahuang on 2025/8/14.
//

#import "YYBMacLaunchTimer.h"
#import "ProcessUtils.h"

 static const NSInteger kIntervalSec = 60 * 60 * 5;
//static const NSInteger kIntervalSec = 30;
@interface YYBMacLaunchTimer()
@property (nonatomic, strong) dispatch_source_t timer;
@end

@implementation YYBMacLaunchTimer


- (void)start {
    if (YES) {
        NSLog(@"定时器开关关闭");
    }
    [self cancel];
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    self.timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    
    uint64_t interval = kIntervalSec * NSEC_PER_SEC;
    uint64_t start = dispatch_time(DISPATCH_TIME_NOW, interval);
    dispatch_source_set_timer(self.timer, start, interval, 0); // 最后一个参数是容忍度，可填0表示最精准
    dispatch_source_set_event_handler(self.timer, ^{
        NSLog(@"try to launch app.");
        [[ProcessUtils sharedInstance] launchYYBMacProcessWhenNotFound];
    });

    dispatch_resume(self.timer);
}

- (void)cancel {
    if (self.timer) {
        dispatch_source_cancel(self.timer);
        self.timer = nil;
    }
}

@end


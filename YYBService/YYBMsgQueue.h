//
//  YYBMsgQueue.h
//  YYBService
//
//  Created by <PERSON> on 2025/8/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, YYBMsgQueueError) {
    ERROR_NONE = 0,        // 无错误
    ERROR_TIMEOUT = 1001,  // 请求超时
    ERROR_SERVICE = 1002,  // SERVICE错误（中间层）
    ERROR_SERVER = 1003,  // 服务器异常
    ERROR_NOT_FOUND = 1004, // 未找到响应
    ERROR_UNKNOWN = 9999        // 未知错误

};

extern NSErrorDomain const YYBMsgQueueErrorDomain;

typedef void(^MessageQueueHandler)(id message);
typedef void(^MessageErrorHandler)(id message, NSError *error);

@interface YYBMsgQueue : NSObject
@property (nonatomic, assign, readonly) NSUInteger messageCount;
@property (nonatomic, assign, readonly) BOOL isReady;
@property (nonatomic, copy, nullable) MessageQueueHandler messageHandler; // 消息处理的回调。
@property (nonatomic, copy, nullable) MessageErrorHandler errorHandler; // 消息错误的回调。

/// 初始化
/// @param capacity 0 表示不限制消息存储大小；>0 表示限制。
- (instancetype)initWithIdentifier:(NSString *)identifier capacity:(NSUInteger)capacity NS_DESIGNATED_INITIALIZER;


/// @brief 向队列末尾添加一条带超时的消息。
/// @param message 消息内容
- (void)addMessage:(NSDictionary *)message;

/// @brief 处理response，从超时列表中移除
- (void)handleResponse:(NSDictionary *)response;

/// @brief 启动消息处理。
/// @discussion 将队列状态设置为"就绪"。如果队列中已有消息，将立即开始处理。
- (void)startProcessing;

/// @brief 暂停消息处理。
/// @discussion 将队列状态设置为"未就绪"。当前正在处理的消息会执行完毕。
- (void)stopProcessing;

/// @brief 处理所有超时消息
/// @discussion 遍历队列中的所有消息，对超时的消息调用timeoutHandler
- (void)processTimeoutMessages;

/// @brief 清空所有消息并触发错误处理
- (void)clearAllMessagesWithTimeout;

/// @brief 清除requestid 所有消息
- (void)clearMessageFrom:(NSString *)from;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;
@end

NS_ASSUME_NONNULL_END

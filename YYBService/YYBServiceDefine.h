//
//  YYBServiceDefine.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/3.
//

#ifndef YYBServiceDefine_h
#define YYBServiceDefine_h
#import "YYBDefine.h"

static NSString *const kMessageTypeKey = @"type";
static NSString *const kMessageRequestIDKey = @"request_id";
static NSString *const kMessageTimeoutKey = @"timeout_ms";
static NSString *const kMessageFromKey = @"from";
static NSString *const kMessageToKey = @"to";
static NSString *const kMessageActionKey = @"action";
static NSString *const kMessageErrorCodeKey = @"error_code";
static NSString *const kMessageInternalTimestampKey = @"__timestamp__";

static NSString *const kMessageTypeRequest = @"request";
static NSString *const kMessageTypeResponse = @"response";
static NSString *const kMessageTypeEvent = @"event";

static NSString *const kMessageActionEngineReadyTimeout = @"engineReadyTimeout";
static NSString *const kMessageActionEngineIsReady = @"engineIsReady";
static NSString *const kMessageActionStartEngineResult = @"startEngineResult";
static NSString *const kMessageActionStartEngine = @"startEngine";
static NSString *const kMessageActionDisconnect = @"disconnect";
static NSString *const kMessageActionClientConnect =@"client_connect";
static NSString *const kMessageActionClose =@"close";

#endif /* YYBServiceDefine_h */

//
//  YYBMsgQueue.m
//  YYBService
//
//  Created by <PERSON> on 2025/8/1.
//

#import "YYBMsgQueue.h"
#import "YYBServiceDefine.h"
NSErrorDomain const YYBMsgQueueErrorDomain = @"YYBMsgQueueErrorDomain";

@interface YYBMsgQueue () {
    BOOL _isReady;
    BOOL _isCurrentlyProcessing; // 防止在处理一个耗时任务时，重复派发下一个任务
    NSMutableArray<NSDictionary *> *_msgList;
    NSMutableDictionary<id, NSDictionary *> *_pendingRequests;

    void *_queueKey;
}
@property (nonatomic, strong) NSString *identifier;
@property (nonatomic, assign) NSUInteger capacity;
@property (nonatomic, strong) dispatch_queue_t syncQueue;//同步对内部状态（消息数组、isReady标志）的访问
@property (nonatomic, strong) dispatch_queue_t processingQueue;// 后台串行队列来执行消息处理
@property (nonatomic, strong) dispatch_source_t timeoutTimer;
@end

@implementation YYBMsgQueue

- (instancetype)initWithIdentifier:(NSString *)identifier capacity:(NSUInteger)capacity {
    self = [super init];

    if (self) {
        _identifier = identifier;
        _capacity = capacity;

        _msgList = [NSMutableArray array];
        _pendingRequests = [NSMutableDictionary dictionary];

        NSString *syncQueueLabel = [NSString stringWithFormat:@"com.tencent.yybmac.msgqueue.sync.%@", identifier];
        _syncQueue = dispatch_queue_create([syncQueueLabel UTF8String], DISPATCH_QUEUE_SERIAL);

        _queueKey = &syncQueueLabel;
        dispatch_queue_set_specific(_syncQueue, _queueKey, _queueKey, NULL);

        NSString *processingQueueLabel = [NSString stringWithFormat:@"com.tencent.yybmac.msgqueue.processing.%@", identifier];
        _processingQueue = dispatch_queue_create([processingQueueLabel UTF8String], DISPATCH_QUEUE_SERIAL);

        _isReady = NO;
        _isCurrentlyProcessing = NO;
    }

    return self;
}

- (void)dealloc {
    [self stopTimeoutTimer];
}

#pragma mark - Public Properties

- (BOOL)isReady {
    if (dispatch_get_specific(_queueKey)) {
        return _isReady;
    }

    __block BOOL ready;
    dispatch_sync(_syncQueue, ^{
        ready = self->_isReady;
    });
    return ready;
}

- (NSUInteger)messageCount {
    if (dispatch_get_specific(_queueKey)) {
        return _msgList.count;
    }

    __block NSUInteger count;
    dispatch_sync(_syncQueue, ^{
        count = self->_msgList.count;
    });
    return count;
}

- (NSUInteger)pendingRequestCount {
    if (dispatch_get_specific(_queueKey)) {
        return _pendingRequests.count;
    }

    __block NSUInteger count;
    dispatch_sync(_syncQueue, ^{
        count = self->_pendingRequests.count;
    });
    return count;
}

#pragma mark - Public Methods

- (void)addMessage:(NSDictionary *)message {
    if (!message || ![message isKindOfClass:[NSDictionary class]] || message.count == 0) {
        NSLog(@"[MsgQueue-%@] Invalid message provided.", self.identifier);
        return;
    }

    dispatch_async(self.syncQueue, ^{
        if (self->_capacity > 0 && self->_msgList.count >= self->_capacity) {
            NSDictionary *oldestMsg = [self->_msgList firstObject];
            [self->_msgList removeObjectAtIndex:0];
            [self handleErrorForMessage:oldestMsg code:ERROR_SERVICE description:@"Queue capacity exceeded"];
            NSLog(@"[MsgQueue-%@] Capacity exceeded. Removed oldest message.", self.identifier);
        }

        [self->_msgList addObject:message];

        NSString *type = message[kMessageTypeKey];

        if ([type isEqualToString:kMessageTypeRequest]) {
            id requestId = message[kMessageRequestIDKey];
            NSNumber *timeout = message[kMessageTimeoutKey];

            if (requestId && [timeout doubleValue] > 0) {
                self->_pendingRequests[requestId] = message;
            }
        }

        NSLog(@"[MsgQueue-%@] Message added. Queue count: %lu, Pending requests: %lu, ready:%d", self.identifier, (unsigned long)self->_msgList.count, (unsigned long)self->_pendingRequests.count, self.isReady);

        [self tryProcessNextMessageSync];
    });
}

- (void)handleResponse:(NSDictionary *)response {
    id responseForId = response[kMessageRequestIDKey];

    if (!responseForId) {
        return;
    }

    dispatch_async(self.syncQueue, ^{
        if (self->_pendingRequests[responseForId]) {
            [self->_pendingRequests removeObjectForKey:responseForId];
            if (self->_pendingRequests.count == 0) {
                [self stopTimeoutTimer];
            }
        }
    });
}

- (void)startProcessing {
    dispatch_async(self.syncQueue, ^{
        if (self->_isReady) {
            return;
        }

        self->_isReady = YES;
        NSLog(@"[MsgQueue-%@] Processing started.", self->_identifier);
        [self tryProcessNextMessageSync];
    });
}

- (void)stopProcessing {
    dispatch_async(self.syncQueue, ^{
        if (!self->_isReady) {
            return;
        }

        self->_isReady = NO;
        NSLog(@"[MsgQueue-%@] Processing stopped.", self->_identifier);
        [self clearAllMessagesWithTimeout];
    });
}

- (void)clearAllMessagesWithTimeout {
    dispatch_async(self.syncQueue, ^{
        [self->_msgList removeAllObjects];

        for (id requestId in [self->_pendingRequests allKeys]) {
            NSDictionary *requestMessage = self->_pendingRequests[requestId];
            [self handleErrorForMessage:requestMessage code:ERROR_TIMEOUT description:@"Queue cleared"];
        }

        [self->_pendingRequests removeAllObjects];

        [self stopTimeoutTimer];
        NSLog(@"[MsgQueue-%@] All messages cleared with timeout.", self.identifier);
    });
}

- (void)clearMessageFrom:(NSString *)from {
    dispatch_async(self.syncQueue, ^{
        NSMutableArray *deleteArray = [NSMutableArray arrayWithArray:self->_msgList];
        for (NSDictionary *message in self->_msgList) {
            if ([message[@"from"] isEqualToString:from]) {
                [deleteArray removeObject:message];
            }
        }
        self->_msgList = deleteArray;
        NSDictionary *pendingRequests = [NSDictionary dictionaryWithDictionary:self->_pendingRequests];
        [pendingRequests enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
            if ([obj[@"from"] isEqualToString:from]) {
                [self->_pendingRequests removeObjectForKey:key];
            }
        } ];
        NSLog(@"[MsgQueue-%@] clearMessageFrom：%@", self.identifier, from);
    });
}

- (void)processTimeoutMessages {
    dispatch_async(self.syncQueue, ^{
        [self processTimeoutMessagesSync];
    });
}

#pragma mark - Private
/**
 * @brief 尝试处理队列中的下一条消息。
 * @discussion 它会检查所有条件（就绪状态、有消息、当前未在处理、handler存在），
 * 如果满足，则派发一个处理任务。
 */
- (void)tryProcessNextMessageSync {
    dispatch_assert_queue(_syncQueue);

    if (!_isReady || _isCurrentlyProcessing || _msgList.count == 0) {
        return;
    }

    if (!self.messageHandler) {
        NSLog(@"[MsgQueue-%@] Warning: Queue is ready, but messageHandler is not set.", self.identifier);
        return;
    }

    _isCurrentlyProcessing = YES;
    NSDictionary *messageToProcess = _msgList.firstObject;
    [_msgList removeObjectAtIndex:0];

    NSLog(@"[MsgQueue-%@] Processing message... Remaining in queue: %lu", self.identifier, (unsigned long)_msgList.count);

    dispatch_async(self.processingQueue, ^{
        self.messageHandler(messageToProcess);
        dispatch_async(self.syncQueue, ^{
            // 消息开始处理，更新时间戳
            NSString *requestId = messageToProcess[kMessageRequestIDKey];
            if (requestId) {
                NSDictionary *request = self->_pendingRequests[requestId];
                NSMutableDictionary *requestWithTimestamp = [NSMutableDictionary dictionaryWithDictionary:request];
                requestWithTimestamp[kMessageInternalTimestampKey] = @([[NSDate date] timeIntervalSince1970]);
                self->_pendingRequests[requestId] = requestWithTimestamp.copy;
            }
           
            [self startTimeoutTimerIfNeeded];
            self->_isCurrentlyProcessing = NO;
            [self tryProcessNextMessageSync];
        });
    });
}

#pragma mark - Timeout Handling
- (void)processTimeoutMessagesSync {
    dispatch_assert_queue(self.syncQueue);

    if (_pendingRequests.count == 0) {
        [self stopTimeoutTimer];
        return;
    }

    NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
    NSMutableArray *timedOutRequestIDs = [NSMutableArray array];

    for (id requestId in [_pendingRequests allKeys]) {
        NSDictionary *request = _pendingRequests[requestId];
        double timestamp = [request[kMessageInternalTimestampKey] doubleValue];
        double timeout = [request[kMessageTimeoutKey] doubleValue];

        if (timestamp > 0 && (now - timestamp) * 1000 >= timeout) {
            [timedOutRequestIDs addObject:requestId];
        }
    }

    if (timedOutRequestIDs.count > 0) {
        NSLog(@"[MsgQueue-%@] Found %lu timed-out requests.", self.identifier, (unsigned long)timedOutRequestIDs.count);

        NSSet *timedOutIDSet = [NSSet setWithArray:timedOutRequestIDs];

        for (id requestId in timedOutRequestIDs) {
            NSDictionary *timedOutRequest = _pendingRequests[requestId];

            if (timedOutRequest) {
                [self handleErrorForMessage:timedOutRequest code:ERROR_TIMEOUT description:@"Request timed out"];
                [_pendingRequests removeObjectForKey:requestId];
            }
        }

        NSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL (NSDictionary *message, NSDictionary *bindings) {
            if ([message[kMessageTypeKey] isEqualToString:kMessageTypeRequest]) {
                id requestId = message[kMessageRequestIDKey];

                if (requestId && [timedOutIDSet containsObject:requestId]) {
                    return NO;
                }
            }

            return YES;
        }];

        [self->_msgList filterUsingPredicate:predicate];
        NSLog(@"[MsgQueue-%@] Removed timed-out requests from main queue. New queue count: %lu", self.identifier, (unsigned long)self->_msgList.count);
    }

    if (_pendingRequests.count == 0) {
        [self stopTimeoutTimer];
    }
}

- (void)startTimeoutTimerIfNeeded {
    dispatch_assert_queue(self.syncQueue);

    if (_timeoutTimer || _pendingRequests.count == 0) {
        return;
    }

    _timeoutTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, self.syncQueue);

    if (!_timeoutTimer) {
        return;
    }

    dispatch_source_set_timer(_timeoutTimer, dispatch_time(DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC), 1 * NSEC_PER_SEC, 0.1 * NSEC_PER_SEC);

    __weak typeof(self) weakSelf = self;
    dispatch_source_set_event_handler(_timeoutTimer, ^{
        [weakSelf processTimeoutMessagesSync];
    });

    dispatch_resume(_timeoutTimer);
    NSLog(@"[MsgQueue-%@] Timeout timer started.", self.identifier);
}

- (void)stopTimeoutTimer {
    dispatch_assert_queue(self.syncQueue);

    if (_timeoutTimer) {
        dispatch_source_cancel(_timeoutTimer);
        _timeoutTimer = nil;
        NSLog(@"[MsgQueue-%@] Timeout timer stopped.", self.identifier);
    }
}

#pragma mark - Error Handling Helper
- (void)handleErrorForMessage:(NSDictionary *)message code:(YYBMsgQueueError)code description:(NSString *)description {
    if (self.errorHandler) {
        NSMutableDictionary *originalMessage = [NSMutableDictionary dictionaryWithDictionary:message];
        [originalMessage removeObjectForKey:kMessageInternalTimestampKey];

        NSError *error = [NSError errorWithDomain:YYBMsgQueueErrorDomain
                                             code:code
                                         userInfo:@{ NSLocalizedDescriptionKey: description }];

        dispatch_async(self.processingQueue, ^{
            self.errorHandler(originalMessage, error);
        });
    }
}

@end

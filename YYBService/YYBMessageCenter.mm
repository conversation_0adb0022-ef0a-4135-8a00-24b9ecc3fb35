//
//  YYBMessageCenter.m
//  YYBService
//
//  Created by <PERSON> on 2025/6/23.
//

#include <cstdio>
#include <cstdlib>
#include <cstring>
#import <CoreServices/CoreServices.h>
#import <Foundation/Foundation.h>
#include <mach-o/dyld.h>
#import <os/lock.h>
#include <sys/socket.h>
#include <sys/ucred.h>
#include <sys/un.h>
#include <unistd.h>
#import "ProcessUtils.h"
#import "YYBMessageCenter.h"
#import "YYBMsgQueue.h"
#import "YYBServiceDefine.h"
#import "YYBMacLaunchTimer.h"

#define BUFFER_SIZE 4096
static double enginStartTimeout = 60; // 引擎启动超时时间（秒）
static NSString *appName = @"腾讯应用宝";
static NSString *const kMainAppPath = @"/Applications/腾讯应用宝.app";

void eventCallback(
    ConstFSEventStreamRef         streamRef,
    void                          *clientCallBackInfo,
    size_t                        numEvents,
    void                          *eventPaths,
    const FSEventStreamEventFlags eventFlags[],
    const FSEventStreamEventId    eventIds[]
    ) {
    CFArrayRef paths = (CFArrayRef)eventPaths;

    for (size_t i = 0; i < numEvents; i++) {
        CFStringRef cfPath = (CFStringRef)CFArrayGetValueAtIndex(paths, i);

        if (cfPath) {
            NSString *path = (__bridge NSString *)cfPath;

            if ([path isEqualToString:kMainAppPath]) {
                if (![[NSFileManager defaultManager] fileExistsAtPath:kMainAppPath]) {
                    NSLog(@"ItemRemoved");
                    [YYBMessageCenter uninstallApp];
                }

                break;
            }
        } else {
            NSLog(@"eventCallback: NULL path at index %zu", i);
        }
    }
}

@interface YYBMessageCenter ()
@property (nonatomic) os_unfair_lock clientMapLock;
@property (nonatomic, strong) NSMutableDictionary<NSNumber *, YYBClientConnection *> *clientMap; // 管理client
@property (nonatomic, strong) dispatch_queue_t processingQueue; // 接收数据Queue，并行
@property (nonatomic, strong) dispatch_source_t acceptSource;

@property (nonatomic, strong) YYBMsgQueue *engineMsgQueue;// 引擎消息队列
@property (nonatomic, strong) YYBMsgQueue *storeMsgQueue;// store消息队列

@property (nonatomic, assign) FSEventStreamRef stream;

// 引擎启动相关
@property (nonatomic, strong) dispatch_source_t engineStartTimer;
@property (nonatomic, assign) BOOL isEngineStarting;
@property (nonatomic, strong) NSDate *engineStartTime;


@property (nonatomic, strong) YYBMacLaunchTimer* launchTimer;

@end

@implementation YYBMessageCenter

- (instancetype)init {
    self = [super init];

    if (self) {
        NSLog(@"YYBMessageCenter: %@", kMainAppPath);
        [self startMonitoring];

        _clientMapLock = OS_UNFAIR_LOCK_INIT;
        _clientMap = [NSMutableDictionary dictionary];
        _processingQueue = dispatch_queue_create("com.tencent.yybmac.yybService", DISPATCH_QUEUE_CONCURRENT);
        _isEngineStarting = NO;
        _launchTimer = [[YYBMacLaunchTimer alloc] init];
        [self setupSocketServer];
        [_launchTimer start];
    }

    return self;
}

- (void)dealloc {
    NSLog(@"dealloc");

    if (_stream) {
        FSEventStreamStop(_stream);
        FSEventStreamInvalidate(_stream);
        FSEventStreamRelease(_stream);
        _stream = NULL;
    }

    [self stopEngineStartTimer];
}

- (YYBMsgQueue *)engineMsgQueue {
    if (!_engineMsgQueue) {
        _engineMsgQueue = [[YYBMsgQueue alloc] initWithIdentifier:kProcessEngine capacity:0];
        __weak typeof(self) weakSelf = self;
        _engineMsgQueue.messageHandler = ^(id _Nonnull message) {
            __strong typeof(weakSelf) strongSelf = weakSelf;

            if (strongSelf) {
                [strongSelf forwardMessage:message];
            }
        };
        _engineMsgQueue.errorHandler = ^(NSDictionary *_Nonnull message, NSError *_Nonnull error) {
            __strong typeof(weakSelf) strongSelf = weakSelf;
            NSLog(@"error message:%@: %@", message[@"request_id"], error);

            if (strongSelf && [message[kMessageTypeKey] isEqualToString:kMessageTypeRequest]) {
                NSMutableDictionary *response = [NSMutableDictionary dictionaryWithDictionary:message];
                response[kMessageTypeKey] = kMessageTypeResponse;
                NSString *from = message[kMessageFromKey];
                response[kMessageFromKey] = kProcessService;
                response[kMessageActionKey] = [NSString stringWithFormat:@"%@Response", message[kMessageActionKey]];
                response[kMessageToKey] = from;
                response[kMessageErrorCodeKey] = [NSString stringWithFormat:@"%ld", static_cast<long>(error.code)];
                [strongSelf forwardMessage:response.copy];
            }
        };
    }

    return _engineMsgQueue;
}

- (YYBMsgQueue *)storeMsgQueue {
    if (!_storeMsgQueue) {
        _storeMsgQueue = [[YYBMsgQueue alloc] initWithIdentifier:@"store" capacity:0];
        __weak typeof(self) weakSelf = self;
        _storeMsgQueue.messageHandler = ^(id _Nonnull message) {
            __strong typeof(weakSelf) strongSelf = weakSelf;

            if (strongSelf) {
                [strongSelf forwardMessage:message];
            }
        };
    }

    return _storeMsgQueue;
}

// 创建UDS服务器
- (void)setupSocketServer {
    // 1. 创建socket文件路径
    NSString *socketPath = @"/tmp/yybMac.socket";

    // socket存在检查
    if (access(socketPath.UTF8String, F_OK) == 0) {
        int testSock = socket(AF_UNIX, SOCK_STREAM, 0);
        struct sockaddr_un addr;
        memset(&addr, 0, sizeof(addr));
        addr.sun_family = AF_UNIX;
        strncpy(addr.sun_path, socketPath.UTF8String, sizeof(addr.sun_path) - 1);

        // 如果能连接成功，说明已有实例运行
        if (connect(testSock, (struct sockaddr *)&addr, sizeof(addr)) == 0) {
            close(testSock);
            NSLog(@"Service already running via socket check");
            exit(EXIT_SUCCESS);
        }

        close(testSock);
        unlink(socketPath.UTF8String);
    }

    // 2. 创建socket
    int sockfd = socket(AF_UNIX, SOCK_STREAM, 0);

    if (sockfd < 0) {
        NSLog(@"socket creation failed");
        exit(EXIT_FAILURE);
    }

    unlink(socketPath.UTF8String);

    // 3. 绑定socket
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socketPath.UTF8String, sizeof(addr.sun_path) - 1);

    if (bind(sockfd, (struct sockaddr *)&addr, sizeof(addr)) < 0) {
        NSLog(@"bind failed");
        close(sockfd);
        exit(EXIT_FAILURE);
    }

    // 4. 监听连接, 最大等待 accept 的连接数
    if (listen(sockfd, 6) < 0) {
        NSLog(@"listen failed");
        close(sockfd);
        exit(EXIT_FAILURE);
    }

    // 设置为非阻塞，支持异步读取
    fcntl(sockfd, F_SETFL, O_NONBLOCK);
    NSLog(@"Server listening on %@\n", socketPath);

    // 监听新的client
    self.acceptSource = dispatch_source_create(DISPATCH_SOURCE_TYPE_READ, sockfd, 0, _processingQueue);
    dispatch_source_set_event_handler(self.acceptSource, ^{
        NSLog(@"accept source event");

        while (1) { // 同时有多个连接时处理
            int clientFd = accept(sockfd, NULL, NULL);

            if (clientFd < 0) {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    break;     // 没有更多连接，退出循环
                } else {
                    NSLog(@"accept error: %s", strerror(errno));
                    break;
                }
            }

            NSLog(@"Client connected: %d", clientFd);
            [self acceptNewConnection:clientFd];
        }
    });
    dispatch_resume(self.acceptSource);
    NSLog(@"Daemon started. Listening at: %s\n", socketPath.UTF8String);
}

// 接受新连接
- (void)acceptNewConnection:(int)clientFd {
    YYBClientConnection *connection = [[YYBClientConnection alloc] init];

    connection.socketFd = clientFd;
    connection.isAlive = true;
    os_unfair_lock_lock(&_clientMapLock);
    self.clientMap[@(clientFd)] = connection;
    os_unfair_lock_unlock(&_clientMapLock);

    dispatch_source_t readSource = dispatch_source_create(DISPATCH_SOURCE_TYPE_READ, clientFd, 0, _processingQueue);

    dispatch_source_set_event_handler(readSource, ^{
        char buffer[BUFFER_SIZE];
        ssize_t len = read(clientFd, buffer, sizeof(buffer));

        if (len > 0) {
            NSData *data = [NSData dataWithBytes:buffer length:len];
            YYBClientConnection *info = self.clientMap[@(clientFd)];
            [info.buffer appendData:data];

            while (info.buffer.length >= 4) {
                uint32_t len = 0;
                [info.buffer getBytes:&len length:4];
                len = ntohl(len);

                if (info.buffer.length < 4 + len) {
                    break;
                }

                NSData *msgData = [info.buffer subdataWithRange:NSMakeRange(4, len)];
                [self processMessage:msgData fromSocket:clientFd];

                [info.buffer replaceBytesInRange:NSMakeRange(0, 4 + len) withBytes:NULL length:0];
            }
        } else if (len == 0) {
            // 连接正常关闭
            dispatch_source_cancel(readSource);
        } else {
            if (errno != EAGAIN && errno != EWOULDBLOCK) {
                // 连接异常关闭
                dispatch_source_cancel(readSource);
            }
        }
    });

    // 设置连接关闭事件
    dispatch_source_set_cancel_handler(readSource, ^{
        [self handleDisconnect:clientFd];
    });

    dispatch_resume(readSource);
}

// 修改断开连接处理
- (void)handleDisconnect:(int)clientFd {
    YYBClientConnection *conn = self.clientMap[@(clientFd)];

    NSLog(@"断开链接：%@， pid=%d", conn.processType, conn.processPID);

    close(clientFd);

    os_unfair_lock_lock(&_clientMapLock);
    [self.clientMap removeObjectForKey:@(clientFd)];
    os_unfair_lock_unlock(&_clientMapLock);

    if ([conn.processType isEqualToString:kProcessEngine] && ![self isProcessRegister:kProcessEngine]) {
        [self killShortCutProcess];
        [self.engineMsgQueue stopProcessing];

        // 停止引擎启动定时器
        [self stopEngineStartTimer];
    } else if ([conn.processType isEqualToString:kProcessAppStore] && ![self isProcessRegister:kProcessAppStore]) {
        [self.storeMsgQueue stopProcessing];
    } else {
        [self sendDisconnectMsg:conn.processType];
        [self.engineMsgQueue clearMessageFrom:conn.processType];
        [self.storeMsgQueue clearMessageFrom:conn.processType];
    }

}

// 解析和处理消息
- (void)processMessage:(NSData *)data fromSocket:(NSInteger)socketFd {
    NSError *jsonError;
    NSDictionary *message = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];

    if (jsonError || !message[kMessageTypeKey]) {
        NSLog(@"Invalid message format");
        return;
    }

    NSString *action = message[kMessageActionKey];
    NSString *to = message[kMessageToKey];
    NSLog(@"%ld收到完整消息: %@", socketFd, message);
    [self.engineMsgQueue handleResponse:message];
    [self.storeMsgQueue handleResponse:message];

    if (to.length == 0 || [to isEqualToString:kProcessService]) {
        // server 接收处理
        if ([action isEqualToString:@"regist"]) { // 注册事件
            [self registerSocket:socketFd msgInfo:message];
        }
    } else if ([to isEqualToString:kProcessAppStore]) {
        [self handleToStoreEvent:message msgData:data];
    } else if ([to isEqualToString:kProcessEngine]) {
        [self handleToEngineEvent:message msgData:data];
    } else if ([to isEqualToString:kProcessBoastcast]) {
        [self handleBoastcastEvent:message msgData:data];
    } else {
        // 其他： 快捷方式
        [self forwardMessageTo:to msgData:data];
    }
}

// 注册新进程
- (void)registerSocket:(NSInteger)socketFd msgInfo:(NSDictionary *)message {
    YYBClientConnection *info = self.clientMap[@(socketFd)];

    if (!info) {
        NSLog(@"client not found:%ld", socketFd);
        return;
    }

    info.processType = message[kMessageFromKey];
    info.processPID = [message[@"pid"] intValue];
    NSLog(@"Process registered: %@", info.processType);

    NSDictionary *infoDict = message[@"info"];

    if (infoDict && [infoDict isKindOfClass:NSDictionary.class]) {
        if ([infoDict[kMessageActionEngineIsReady] isEqualToString:@"true"]) {
            [self handleEngineIsReady:YES];
        }
    }
    
    if ([info.processType isEqualToString:kProcessAppStore]) {
        [self.storeMsgQueue startProcessing];
    } else if ([info.processType isEqualToString:kProcessEngine]) {
        [self startEngineStartTimer];
    }

    NSLog(@"Process registered: %@ pid:%@", info.processType, @(info.processPID));
}

- (void)handleBoastcastEvent:(NSDictionary *)msg msgData:(NSData *)msgData {
    if ([msg[kMessageActionKey] isEqualToString:kMessageActionEngineIsReady]) {
        // 引擎已启动完成
        [self handleEngineIsReady:YES];
    } else if ([msg[kMessageActionKey] isEqualToString:kMessageActionStartEngineResult]) {
        // 引擎进程启动
        [self startEngineStartTimer];
    }

    for (YYBClientConnection *targetInfo in self.clientMap.allValues) {
        if (![msg[kMessageFromKey] isEqualToString:targetInfo.processType]) {
            [self forwardMessageTo:targetInfo.processType msgData:msgData];
        }
    }
}

- (void)handleToStoreEvent:(NSDictionary *)msg msgData:(NSData *)msgData {
    [self.storeMsgQueue addMessage:msg];

    // 商店未ready,启动商店
    if (![self clientConnFromProcess:kProcessAppStore]) {
        [[ProcessUtils sharedInstance] launchYYBMacProcessWhenNotFound];
    } else {
        [self.storeMsgQueue startProcessing];
    }
}

- (void)handleToEngineEvent:(NSDictionary *)msg msgData:(NSData *)msgData {
    [self.engineMsgQueue addMessage:msg];

    if (![self clientConnFromProcess:kProcessEngine] &&
        ![msg[kMessageActionKey] isEqualToString:kMessageActionClose]) {
        [self sendStartEngineMsg:msg[kMessageFromKey]];
    }
}

- (void)handleEngineIsReady:(BOOL)engineIsReady {
    NSLog(@"EngineIsReady:%d", engineIsReady);

    if (engineIsReady) {
        [self stopEngineStartTimer];
        [self.engineMsgQueue startProcessing];
    } else {
        [self.engineMsgQueue stopProcessing];
    }
}

- (void)forwardMessage:(NSDictionary *)msg {
    // 处理消息
    NSLog(@"take msg：from:%@：to:%@,action:%@, requestId:%@",msg[kMessageFromKey], msg[kMessageToKey], msg[kMessageActionKey], msg[kMessageRequestIDKey]);
    NSError *jsonError;
    NSData *msgData = [NSJSONSerialization dataWithJSONObject:msg options:0 error:&jsonError];

    if (msgData) {
        [self forwardMessageTo:msg[kMessageToKey] msgData:msgData];
    } else {
        NSLog(@"JSONObjectWithData:%@", jsonError);
    }
}

// 消息转发
- (void)forwardMessageTo:(NSString *)targetID msgData:(NSData *)message {
    dispatch_async(self.processingQueue, ^{
        YYBClientConnection *targetInfo = [self clientConnFromProcess:targetID];

        if (!targetInfo || !targetInfo.isAlive) {
            NSLog(@"Target process not available:%@", targetID);
            return;
        }

        // 添加消息长度前缀
        uint32_t len = htonl((uint32_t)message.length);
        NSMutableData *sendData = [NSMutableData dataWithBytes:&len length:sizeof(len)];
        [sendData appendData:message];

        // 转发消息
        ssize_t size = write(targetInfo.socketFd, sendData.bytes, sendData.length);

        if (size != sendData.length) {
            NSLog(@"write to target:%@ socket fd size: %ld, result size:%ld", targetInfo.processType,  sendData.length, size);
        }
    });
}

- (void)sendStartEngineMsg:(NSString *)from {
    NSMutableDictionary *message = [NSMutableDictionary dictionary];

    message[kMessageTypeKey] = kMessageTypeEvent;
    message[kMessageToKey] = kProcessAppStore;
    message[kMessageActionKey] = kMessageActionStartEngine;
    message[kMessageFromKey] = from;

    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:message options:0 error:&error];

    if (data) {
        NSLog(@"start engine:%@, store isconnnect:%d", from, [self isProcessRegister:kProcessAppStore]);
        [self handleToStoreEvent:message msgData:data];
    }
}

- (void)sendConnectMsg:(NSString *)from {
    NSMutableDictionary *message = [NSMutableDictionary dictionary];

    message[kMessageTypeKey] = kMessageTypeEvent;
    message[kMessageToKey] = kProcessBoastcast;
    message[kMessageActionKey] = kMessageActionClientConnect;
    message[kMessageFromKey] = from;
    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:message options:0 error:&error];

    if (data) {
        [self handleBoastcastEvent:message msgData:data];
    }
}

- (void)sendDisconnectMsg:(NSString *)from {
    NSMutableDictionary *message = [NSMutableDictionary dictionary];

    message[kMessageTypeKey] = kMessageTypeEvent;
    message[kMessageToKey] = kProcessBoastcast;
    message[kMessageActionKey] = kMessageActionDisconnect;
    message[kMessageFromKey] = from;
    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:message options:0 error:&error];

    if (data) {
        [self handleBoastcastEvent:message msgData:data];
    }
}

- (void)sendEngineReadyTimeoutMsg {
    NSMutableDictionary *message = [NSMutableDictionary dictionary];

    message[kMessageTypeKey] = kMessageTypeEvent;
    message[kMessageToKey] = kProcessBoastcast;
    message[kMessageActionKey] = kMessageActionEngineReadyTimeout;
    message[kMessageFromKey] = kProcessService;
    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:message options:0 error:&error];

    if (data) {
        [self handleBoastcastEvent:message msgData:data];
    }
}

- (BOOL)isProcessRegister:(NSString *)processType {
    YYBClientConnection *connection = [self clientConnFromProcess:processType];
    return connection != nil;
}

- (YYBClientConnection *)clientConnFromProcess:(NSString *)processType {
    NSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL (YYBClientConnection *obj, NSDictionary *bindings) {
        return [obj.processType isEqualToString:processType];
    }];
    NSArray<YYBClientConnection *> *matchedObjects = [self.clientMap.allValues filteredArrayUsingPredicate:predicate];

    if (matchedObjects.count == 0) {
        return nil;
    }
    NSLog(@"current conn:%@-%d", processType, matchedObjects.lastObject.processPID);
    return matchedObjects.lastObject;
}

- (void)killShortCutProcess {
    // 实现正常的进程终止（SIGTERM）
    for (YYBClientConnection *conn in self.clientMap.allValues) {
        if (![conn.processType isEqualToString:kProcessAppStore] && ![conn.processType isEqualToString:kProcessEngine]) {
            if (conn.processPID > 0) {
                kill(conn.processPID, SIGTERM);
                NSLog(@"已终止关联进程: %@,pid=%d", conn.processType, conn.processPID);
            }
        }
    }
}

+ (void)uninstallApp {
    if (![[NSFileManager defaultManager] fileExistsAtPath:kMainAppPath] && ![[ProcessUtils sharedInstance] isYYBMacRunning]) {
        NSLog(@"uninstallApp real");
        NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
        NSString *scriptPath = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/%@/helper/YYBUninstaller", @"com.tencent.yybmac"]];
        NSTask *chmodTask = [[NSTask alloc] init];

        chmodTask.launchPath = @"/bin/chmod";
        chmodTask.arguments = @[@"+x", scriptPath];
        [chmodTask launch];
        [chmodTask waitUntilExit];

        NSTask *uninstallTask = [[NSTask alloc] init];
        uninstallTask.launchPath = scriptPath;
        [uninstallTask launch];
    }
}

- (void)startMonitoring {
    NSLog(@"startMonitoring");
    NSArray *pathsToWatch = @[kMainAppPath];
    FSEventStreamContext context = {
        0, (__bridge void *)self, NULL, NULL, NULL
    };
    FSEventStreamRef stream = FSEventStreamCreate(NULL,
                                                  &eventCallback,
                                                  &context,
                                                  (__bridge CFArrayRef)pathsToWatch,
                                                  kFSEventStreamEventIdSinceNow,
                                                  1.0,
                                                  kFSEventStreamCreateFlagUseCFTypes | kFSEventStreamCreateFlagFileEvents);

    if (!stream) {
        return;
    }

    FSEventStreamScheduleWithRunLoop(stream, CFRunLoopGetCurrent(), kCFRunLoopDefaultMode);
    FSEventStreamStart(stream);
    self.stream = stream;
}

#pragma mark - Engine Start Timeout Handling

- (void)startEngineStartTimer {
    if (self.isEngineStarting && self.engineMsgQueue.isReady) {
        return;
    }

    self.isEngineStarting = YES;
    self.engineStartTime = [NSDate date];

    NSLog(@"Starting engine start timer with timeout: %.1f seconds", enginStartTimeout);

    // 创建定时器
    dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, dispatch_get_main_queue());
    dispatch_source_set_timer(timer,
                              dispatch_time(DISPATCH_TIME_NOW, enginStartTimeout * NSEC_PER_SEC),
                              DISPATCH_TIME_FOREVER,
                              0);

    __weak typeof(self) weakSelf = self;
    dispatch_source_set_event_handler(timer, ^{
        __strong typeof(weakSelf) strongSelf = weakSelf;

        if (strongSelf) {
            [strongSelf handleEngineStartTimeout];
        }
    });
    dispatch_source_set_cancel_handler(timer, ^{
        weakSelf.engineStartTimer = nil;
    });

    dispatch_resume(timer);
    self.engineStartTimer = timer;
}

- (void)stopEngineStartTimer {
    NSLog(@"Engine stop engineStartTimer");
    if (self.engineStartTimer) {
        dispatch_source_cancel(self.engineStartTimer);
        self.engineStartTimer = nil;
    }

    self.isEngineStarting = NO;
    self.engineStartTime = nil;
}

- (void)handleEngineStartTimeout {
    NSLog(@"Engine start timeout after %.1f seconds", enginStartTimeout);

    self.isEngineStarting = NO;
    self.engineStartTimer = nil;

    // 处理引擎消息队列中的所有超时消息
    [self.engineMsgQueue clearAllMessagesWithTimeout];

    [self sendEngineReadyTimeoutMsg];
}

@end

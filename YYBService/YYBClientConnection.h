//
//  YYBClientConnection.h
//  YYBService
//
//  Created by <PERSON> on 2025/6/23.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
// 进程注册信息
@interface YYBClientConnection : NSObject
@property (nonatomic, copy) NSString* processType;   // 进程标识符
@property (nonatomic, assign) int processPID; // 进程ID
@property (nonatomic, assign) int socketFd; // 对应的socket连接
@property (nonatomic, assign) BOOL isAlive;        // 连接状态
@property (nonatomic, strong) NSMutableData *buffer;
@end

NS_ASSUME_NONNULL_END

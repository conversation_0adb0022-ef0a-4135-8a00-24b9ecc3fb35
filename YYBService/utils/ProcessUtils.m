//
//  ProcessUtils.m
//  YYBService
//
//  Created by bethahuang on 2025/7/10.
//

#import "ProcessUtils.h"

NS_ASSUME_NONNULL_BEGIN

@implementation ProcessUtils

+ (instancetype)sharedInstance {
    static ProcessUtils *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[ProcessUtils alloc] init];
    });
    return instance;
}

// 商店进程是否在运行
- (BOOL)isYYBMacRunning {
    return [self isProcessRunning:@"com.tencent.yybmac"];
}

// 商店是否存在
- (BOOL)isYYBMacExist {
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:@"/Applications/腾讯应用宝.app"];
    if (!exist) {
        NSLog(@"yybMac is not exist.");
    }
    return exist;
}

- (void)launchYYBMacProcessWhenNotFound {
    NSLog(@"launch start");
    if (![self isYYBMacRunning] && [self isYYBMacExist]) {
        NSURL *appURL = [NSURL fileURLWithPath:@"/Applications/腾讯应用宝.app"];
        NSWorkspaceOpenConfiguration *config = [NSWorkspaceOpenConfiguration configuration];
        config.arguments = @[@"--runBackground"];

        [[NSWorkspace sharedWorkspace] openApplicationAtURL:appURL
                                              configuration:config
                                          completionHandler:^(NSRunningApplication * _Nullable app, NSError * _Nullable error) {
            if (error) {
                NSLog(@"launch failed: %@", error);
            } else {
                NSLog(@"App launched: %@", app);
            }
        }];
    } else{
        NSLog(@"launch is running");
    }
}


- (BOOL)isProcessRunning:(NSString*)bundleId {
    if (!bundleId || [bundleId length] == 0) {
        NSLog(@"bundle id 为空，查询失败");
        return NO;
    }
    for (NSRunningApplication *app in [[NSWorkspace sharedWorkspace] runningApplications]) {
        if ([app.bundleIdentifier isEqualToString:bundleId]) {
            NSLog(@"app status: hidden:%d, isActive:%d, isTerminated=%d, isFinishedLaunching=%d", app.isHidden, app.isActive, app.isTerminated, app.isFinishedLaunching);
            return YES;
        }
    }
    return NO;
}

@end

NS_ASSUME_NONNULL_END

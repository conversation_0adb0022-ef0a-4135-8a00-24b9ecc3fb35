//
//  YYBApkInfoModel.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// apk完整状态（下载-》安装-》更新）
typedef NSString *YYBApkState NS_STRING_ENUM;

/** 开始安装  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateBeginInstall;
/** 安装错误  - 按钮文案：【重试】*/
FOUNDATION_EXPORT YYBApkState const YYBApkStateInstallError;
/** 安装完成/已安装 - 按钮文案：【打开】  */
FOUNDATION_EXPORT YYBApkState const YYBApkStateInstalled;
/** 安装中  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateInstalling;

/** 开始卸载 - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateBeginUninstall;
/** 卸载中 - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateUnInstalling;
/** 卸载错误  - 按钮文案：【重试】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateUnInstallError;
/** 卸载取消 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateUnInstallCancel;
/** 结束卸载/完成卸载 - 按钮文案：【安装】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateEndUninstall;

/** 初始化状态 - 按钮文案：【安装】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateINIT;
/** 下载中 (Aria状态)   - 按钮文案：【暂停/百分比】*/
FOUNDATION_EXPORT YYBApkState const YYBApkStateActive;
/** 等待中 (Aria状态)  - 按钮文案：【加载中icon（不可点击）】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateWaiting;
/** 暂停中 (Aria状态)  - 按钮文案：【继续】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStatePaused;
/** 下载错误 (Aria状态)  - 按钮文案：【重试】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateError;
/** 下载完成 (Aria状态)  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateComplete;
/** 删除下载任务 (Aria状态) /已移除 - 按钮文案：【安装】*/
FOUNDATION_EXPORT YYBApkState const YYBApkStateRemoved;

/** 需要更新 - 按钮文案：【更新】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateNeedUpdate;

/** 开始启动  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateBeginLaunch;
/** 启动完成  - 按钮文案：【打开】 */
FOUNDATION_EXPORT YYBApkState const YYBApkStateCompleteLaunch;

@interface YYBApkInfoModel : NSObject

@property (nonatomic, copy) NSString *pkgName;            // 包名
@property (nonatomic, copy) NSString *name;               // 应用/游戏 名
@property (nonatomic, copy) NSString *versionName;        // 版本名称
@property (nonatomic, copy) NSString *versionCode;        // 版本code
@property (nonatomic, copy) NSString *md5;                // md5信息

@property (nonatomic, copy, readonly) YYBApkState apkState;         // apk状态
@property (nonatomic, assign) double downloadPercent;               // 下载进度

/// apk下一步(操作)外显文案(用于下载按钮等外显)
- (NSString *)apkNextStepShowText;

/// apk状态日志文案（用来打印当前状态）
- (NSString *)apkStateLogText;

/// 更新apk状态
- (void)updateApkState:(YYBApkState)currentApkState;

@end

NS_ASSUME_NONNULL_END

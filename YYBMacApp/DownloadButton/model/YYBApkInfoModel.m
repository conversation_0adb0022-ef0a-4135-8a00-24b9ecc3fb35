//
//  YYBApkInfoModel.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "YYBApkInfoModel.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kLogTag = @"YYBApkInfoModel";

// YYBApkState
YYBApkState const YYBApkStateBeginInstall = @"BeginInstall";
YYBApkState const YYBApkStateInstallError = @"InstallError";
YYBApkState const YYBApkStateInstalled = @"Installed";
YYBApkState const YYBApkStateInstalling = @"Installing";

YYBApkState const YYBApkStateBeginUninstall = @"BeginUninstall";
YYBApkState const YYBApkStateUnInstalling = @"UnInstalling";
YYBApkState const YYBApkStateUnInstallError = @"UnInstallError";
YYBApkState const YYBApkStateUnInstallCancel = @"UnInstallCancel";
YYBApkState const YYBApkStateEndUninstall = @"EndUninstall";

YYBApkState const YYBApkStateINIT = @"INIT";
YYBApkState const YYBApkStateActive = @"active";
YYBApkState const YYBApkStateWaiting = @"waiting";
YYBApkState const YYBApkStatePaused = @"paused";
YYBApkState const YYBApkStateError = @"error";
YYBApkState const YYBApkStateComplete = @"complete";
YYBApkState const YYBApkStateRemoved = @"removed";

YYBApkState const YYBApkStateNeedUpdate = @"NeedUpdate";

YYBApkState const YYBApkStateBeginLaunch = @"BeginLaunch";
YYBApkState const YYBApkStateCompleteLaunch = @"CompleteLaunch";

@interface YYBApkInfoModel ()

@property (nonatomic, copy, readwrite) YYBApkState apkState;         // apk状态

@end


@implementation YYBApkInfoModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.apkState = YYBApkStateINIT;
    }
    return self;
}

/// apk下一步(操作)外显文案(主要用户下载按钮等外显))
- (NSString *)apkNextStepShowText {
    static NSDictionary<YYBApkState, NSString *> *nextStepTextDict = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        nextStepTextDict = @{
            YYBApkStateBeginInstall:      @"加载中...",
            YYBApkStateInstallError:      @"重试",
            YYBApkStateInstalled:         @"打开",
            YYBApkStateInstalling:        @"加载中...",
            YYBApkStateBeginUninstall:    @"加载中...",
            YYBApkStateUnInstalling:      @"加载中...",
            YYBApkStateUnInstallError:    @"重试",
            YYBApkStateUnInstallCancel:   @"打开",
            YYBApkStateEndUninstall:      @"安装",
            YYBApkStateINIT:              @"安装",
            YYBApkStateActive:            @"暂停",
            YYBApkStateWaiting:           @"加载中...",
            YYBApkStatePaused:            @"继续",
            YYBApkStateError:             @"重试",
            YYBApkStateComplete:          @"加载中...",
            YYBApkStateRemoved:           @"安装",
            YYBApkStateNeedUpdate:        @"更新",
            YYBApkStateBeginLaunch:       @"加载中...",
            YYBApkStateCompleteLaunch:    @"打开",
        };
    });
    NSString *showText = nextStepTextDict[self.apkState];
    if (!showText) {
        // 兜底保护，避免非法状态导致UI崩溃
        YYBMacLogError(kLogTag, @"[警告] apkState未知: %@，apkStateLogText: %@, 使用默认文案", self.apkState ?: @"(null)", self.apkStateLogText);
        NSAssert(NO, @"%@,apkState未知 apkState = %@", kLogTag, self.apkState);
        showText = @"安装";
    } else {
        // info日志便于业务跟踪，有需要可开启
        YYBMacLogInfo(kLogTag, @"apkState=%@, apkStateLogText=%@, 显示为:%@", self.apkState, self.apkStateLogText, showText);
    }
    return showText;
}

/// apk状态日志文案（用来打印当前状态）
- (NSString *)apkStateLogText {
    static NSDictionary<YYBApkState, NSString *> *stateLogTextDict = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        stateLogTextDict = @{
            YYBApkStateBeginInstall:      @"安装-开始",
            YYBApkStateInstallError:      @"安装-错误",
            YYBApkStateInstalled:         @"安装-完成",
            YYBApkStateInstalling:        @"安装-中",
            YYBApkStateBeginUninstall:    @"卸载-开始",
            YYBApkStateUnInstalling:      @"卸载-中",
            YYBApkStateUnInstallError:    @"卸载-出错",
            YYBApkStateUnInstallCancel:   @"卸载-取消",
            YYBApkStateEndUninstall:      @"卸载-完成",
            YYBApkStateINIT:              @"初始化",
            YYBApkStateActive:            @"下载-中",
            YYBApkStateWaiting:           @"下载-等待中",
            YYBApkStatePaused:            @"下载-暂停中",
            YYBApkStateError:             @"下载-出错",
            YYBApkStateComplete:          @"下载-完成",
            YYBApkStateRemoved:           @"下载-取消",
            YYBApkStateNeedUpdate:        @"需要更新",
            YYBApkStateBeginLaunch:       @"打开-中",
            YYBApkStateCompleteLaunch:    @"打开-完成",
        };
    });
    NSString *showText = stateLogTextDict[self.apkState];
    if (!showText) {
        showText = @"未知！！！";
    }
    return showText;
}

/// 更新apk状态
- (void)updateApkState:(YYBApkState)currentApkState {
    if ([self.apkState isEqualToString:currentApkState]) {
        return;
    }
    NSString *oldApkStateString = self.apkStateLogText;
    self.apkState = [currentApkState copy];
    YYBMacLogInfo(kLogTag, @"[updateApkState] oldApkState=%@, newApkState=%@", oldApkStateString, self.apkStateLogText);
}

@end

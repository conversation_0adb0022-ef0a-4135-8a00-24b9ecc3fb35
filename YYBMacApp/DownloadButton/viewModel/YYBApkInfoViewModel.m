//
//  YYBApkInfoViewModel.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "YYBApkInfoViewModel.h"
#import "YYBApkInfoModel.h"
#import "InstallApkInfo.h"
#import "YYBAria2DownloadManager.h"
#import "YYBApkPackage.h"
#import "YYBApkInfoFilter.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kLogTag = @"YYBApkInfoViewModel";

@interface YYBApkInfoViewModel ()

/// apk下载状态信息
@property (nonatomic, strong, readwrite) YYBAria2Task *downloadApkInfo;
/// apk安装状态信息
@property (nonatomic, strong, readwrite) InstallApkInfo *installApkInfo;

@property (nonatomic, strong) YYBApkInfoFilter *filter;

@end

@implementation YYBApkInfoViewModel

#pragma mark - 初始化
- (instancetype)initWithModel:(YYBApkInfoModel *)model {
    if (self = [super init]) {
        _model = model;
        _filter = [[YYBApkInfoFilter alloc] init];
        [self initApkInfo];
        [self addToListener];
    }
    return self;
}

/// 初始化apkinfo信息
- (void)initApkInfo {
    //优先更新安装信息
    InstallApkInfo *installApkInfo = [[YYBApkPackage shared] getInstallApkInfoForListener:self];
    [self updateInstallApkInfo:installApkInfo];
    
    YYBAria2Task *downloadApkInfo = [[YYBAria2DownloadManager sharedManager] getAppDownloadTaskInfo:self];
    [self updateDownloadApkInfo:downloadApkInfo];
}

// 注册监听
- (void)addToListener {
    //优先更新安装信息
    [[YYBApkPackage shared] addInstallUninstallListener:self];
    [[YYBAria2DownloadManager sharedManager] addDownloadListener:self];
}

#pragma mark - 状态更新
- (void)updateDownloadApkInfo:(YYBAria2Task *)downloadApkInfo {
    if (![self.filter shouldAcceptDownloadInfo:downloadApkInfo]) {
        YYBMacLogInfo(kLogTag, @"[updateDownloadApkInfo], downloadApkInfo 已过滤，跳过UI状态更新， %@", downloadApkInfo);
        return;
    }
    
    self.downloadApkInfo = downloadApkInfo;
    if (!downloadApkInfo) {
        // 如果下载信息为空，且有安装信息，则同步安装信息
        if ([self inInstall:self.installApkInfo]) {
            [self updateInstallApkInfo:self.installApkInfo];
        } else {
            [self.model updateApkState:YYBApkStateINIT];
        }
        return;
    }
    
    // 更新进度
    self.model.downloadPercent = MAX(0, MIN(1.0, downloadApkInfo.progress));
    
    // 更新状态
    switch (downloadApkInfo.status) {
        case YYBAria2TaskStatusPending:
            [self.model updateApkState:YYBApkStateINIT];
            break;
            
        case YYBAria2TaskStatusWaitingForNetwork:
        case YYBAria2TaskStatusWaitingForService:
        case YYBAria2TaskStatusRetrying:
        case YYBAria2TaskStatusWaiting:
            [self.model updateApkState:YYBApkStateWaiting];
            break;
            
        case YYBAria2TaskStatusActive:
            [self.model updateApkState:YYBApkStateActive];
            break;
        case YYBAria2TaskStatusPaused:
            [self.model updateApkState:YYBApkStatePaused];
            break;
        case YYBAria2TaskStatusComplete:
            [self.model updateApkState:YYBApkStateComplete];
            break;
        case YYBAria2TaskStatusErrorNetwork:
            [self.model updateApkState:YYBApkStateError];
            break;
        case YYBAria2TaskStatusErrorFatal:
            [self.model updateApkState:YYBApkStateError];
            break;
        case YYBAria2TaskStatusRemoved:
            [self.model updateApkState:YYBApkStateRemoved];
            break;
    }
}

- (void)updateInstallApkInfo:(InstallApkInfo *)installApkInfo {
    if (![self.filter shouldAcceptInstallInfo:installApkInfo]) {
        YYBMacLogInfo(kLogTag, @"[updateInstallApkInfo], installApkInfo 已过滤，跳过UI状态更新， %@", installApkInfo);
        return;
    }
    self.installApkInfo = installApkInfo;
    // 安装只处理明确的状态，其他状态不处理（兼容本地安装，以及初始化时相关状态），安装状态优先级理论上大于下载状态
    switch (installApkInfo.installStatus) {
        case InstallApkStatusStartInstalling:
            // 安装-开始
            [self.model updateApkState:YYBApkStateBeginInstall];
            break;
        case InstallApkStatusInstalling:
            // 安装-中
            [self.model updateApkState:YYBApkStateInstalling];
            break;
        case InstallApkStatusInstallCompleted:
            // 安装-完成
            [self.model updateApkState:YYBApkStateInstalled];
            break;
        default: {
            if (self.downloadApkInfo) {
                // 其他状态，还原为下载时的状态
                [self updateDownloadApkInfo:self.downloadApkInfo];
            } else {
                [self.model updateApkState:YYBApkStateINIT];
            }
        }
    }
}

/// 是否正在安装中
- (BOOL)inInstall:(InstallApkInfo *)installApkInfo {
    if (!installApkInfo) {
        return NO;
    }
    
    return installApkInfo.installStatus == InstallApkStatusStartInstalling ||
    installApkInfo.installStatus == InstallApkStatusInstalling ||
    installApkInfo.installStatus == InstallApkStatusInstallCompleted;
}

#pragma mark - 下载listener代理
+ (nonnull NSString *)downloadKeyForTask:(nonnull YYBAria2Task *)task {
    return task.md5 ?: @"";
}

- (nonnull NSString *)listenerDownloadKey { 
    return self.model.md5 ?: @"";
}

- (void)onDownloadProgress:(nonnull YYBAria2Task *)task progress:(double)progress {
    [self updateDownloadApkInfo:task];
}

- (void)onDownloadStatusChanged:(nonnull YYBAria2Task *)task status:(YYBAria2TaskStatus)status error:(NSError * _Nullable)error {
    [self updateDownloadApkInfo:task];
}

#pragma mark - 安装listener代理
- (nonnull NSString *)listenerPkgName {
    return self.model.pkgName ?: @"";
}

- (void)onInstallStatusChanged:(nonnull InstallApkInfo *)apkInfo {
    [self updateInstallApkInfo:apkInfo];
}


@end

//
//  YYBApkInfoViewModel.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import <Foundation/Foundation.h>
#import "YYBDownloadListener.h"
#import "YYBInstallUninstallListener.h"

@class YYBApkInfoModel;
@class YYBAria2Task;
@class InstallApkInfo;

NS_ASSUME_NONNULL_BEGIN

@interface YYBApkInfoViewModel : NSObject <YYBDownloadListener, YYBInstallUninstallListener>

/// apk完整状态信息
@property (nonatomic, strong, readonly) YYBApkInfoModel *model;
/// apk下载状态信息
@property (nonatomic, strong, readonly) YYBAria2Task *downloadApkInfo;
/// apk安装状态信息
@property (nonatomic, strong, readonly) InstallApkInfo *installApkInfo;


- (instancetype)initWithModel:(YYBApkInfoModel *)model;

@end

NS_ASSUME_NONNULL_END

//
//  ApkInstallTask.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/6.
//

#import <Cocoa/Cocoa.h>
#import "ApkInfoGenerater.h"
#import "ApkInstallTask.h"
#import "SocketDataSender.h"
#import "YYBDefine.h"
#import "YYBMacFusionSDK/YYBMacLog.h"
#include "YYBMessageSender.h"
#include "YYBSocketEngine.h"


@interface ApkInstallTask () {
    NSString *_kTag;
}
@property (nonatomic, assign) BOOL executing;
@property (nonatomic, strong) InstallApkInfo *apkInfo;
@property (nonatomic, strong) NSArray<InstallCompletionHandle> *taskCompleteCallbacks;
@property (nonatomic, assign) BOOL shouldCreateShotcut;
@property (nonatomic, assign) BOOL shareFile; // 是否使用引擎的文件共享能力。
@property (nonatomic, strong) NSString *launchPath;
@property (nonatomic, strong) NSString *defaultPath;

@end

@implementation ApkInstallTask

- (instancetype _Nonnull)initWithApkInfo:(InstallApkInfo *_Nonnull)apkInfo {
    self = [super init];

    if (self) {
        _kTag = @"ApkInstallTask";
        _apkInfo = apkInfo;
        self.taskCompleteCallbacks = @[];
        self.shareFile = YES;
        self.launchPath = [ApkInfoGenerater launchAppsPathFromName:apkInfo.name];
        self.defaultPath = [ApkInfoGenerater defaultAppPathForName:apkInfo.name];
    }

    return self;
}

- (BOOL)isExecuting {
    return _executing;
}

- (void)addTaskCompletionCallback:(InstallCompletionHandle _Nonnull)callbacks {
    if (![self.taskCompleteCallbacks containsObject:callbacks]) {
        self.taskCompleteCallbacks = [self.taskCompleteCallbacks arrayByAddingObject:callbacks];
    }
}

- (void)exeTaskCompletionCallbacks:(NSInteger)retCode errorMessage:(NSString *)errorMessage {
    for (InstallCompletionHandle handle in self.taskCompleteCallbacks) {
        handle(self.apkInfo, retCode, errorMessage);
    }
}

- (void)createShotcut {
    [self installAppWithShotcut:YES shouldInstallToEngine:NO];

}

- (void)installAppWithShotcut:(BOOL)shouldCreateShotcut {
    [self installAppWithShotcut:shouldCreateShotcut shouldInstallToEngine:YES];
}

- (void)installAppWithShotcut:(BOOL)shouldCreateShotcut shouldInstallToEngine:(BOOL)shouldInstallToEngine {
    if (self.executing) {
        return;
    }

    self.executing = YES;
    self.shouldCreateShotcut = shouldCreateShotcut;
    dispatch_group_t group = dispatch_group_create();

    __block BOOL installSuccess = NO;
    __block NSInteger installErrorCode = 0;
    __block NSString *installErrorMessage = nil;

    __weak typeof(self) weakSelf = self;
    YYBMacLogInfo(_kTag, @"install app start:%@", self.apkInfo.pkgName);
    // 安装 APK
    if (shouldInstallToEngine) {
        dispatch_group_enter(group);
        [self installToEngineWithCompletion:^(BOOL success, NSInteger errorCode, NSString *errorMessage) {
            installSuccess = success;
            installErrorCode = errorCode;
            installErrorMessage = errorMessage;
            weakSelf.apkInfo.isInstallInEngine = success;
            YYBMacLogInfo(self->_kTag, @"install app engine install end:%@, retCode:%ld", self.apkInfo.pkgName, static_cast<long>(errorCode));
            dispatch_group_leave(group);
        }];
    } else {
        installSuccess = YES;
    }

    // 创建 App
    __block BOOL createAppSuccess = NO;
    dispatch_group_enter(group);
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        createAppSuccess = [weakSelf createCocoaAppWithPackageInfo];
        if (createAppSuccess && shouldCreateShotcut) {
            createAppSuccess = [weakSelf createShotcutAppWithApkInfo];
            if (createAppSuccess) {
                weakSelf.apkInfo.hasShotcut = YES;
            }
            YYBMacLogInfo(self->_kTag, @"install app create shotcut end:%@, success:%d", self.apkInfo.pkgName, createAppSuccess);
        }
        YYBMacLogInfo(self->_kTag, @"install app create default end:%@, success:%d", self.apkInfo.pkgName, createAppSuccess);
        dispatch_group_leave(group);
    });

    // 等待 安装APK 和 创建App 完成后执行 创建快捷方式
    dispatch_group_notify(group, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        self.executing = NO;
        // 检查是否都成功
        if (installSuccess && createAppSuccess) {
            // 所有任务成功完成
            [self exeTaskCompletionCallbacks:0 errorMessage:nil];
        } else {
            // 有任务失败，返回第一个遇到的错误
            if (!installSuccess) {
                [self exeTaskCompletionCallbacks:installErrorCode errorMessage:installErrorMessage];
            } else if (!createAppSuccess) {
                [self exeTaskCompletionCallbacks:-1 errorMessage:@"create app failed"];
            }
        }
        YYBMacLogInfo(self->_kTag, @"install app all task finished:%@", self.apkInfo.pkgName);
        [self.delegate apkInstallTaskDidFinished:self.apkInfo];
    });
}

- (BOOL)createShotcutAppWithApkInfo {
    BOOL ret = [self createCocoaAppWithPackageInfo];

    if (!ret) {
        return false;
    }

    // 将修改好的模版复制到applications文件夹内。
    NSString *finalAppPath = self.launchPath;
    NSError *error;
    NSFileManager *fm = [NSFileManager defaultManager];

    if ([fm fileExistsAtPath:finalAppPath]) {
        return true;
    }

    NSString *defaultAppPath = self.defaultPath;

    if (![fm linkItemAtPath:defaultAppPath toPath:finalAppPath error:&error]) {
        YYBMacLogError(@"ApkInstallTask", @"Failed to rename app: %@", error);
        return false;
    }
    return true;
}

- (void)installToEngineWithCompletion:(void (^)(BOOL success, NSInteger errorCode, NSString *errorMessage))completion {
    if (self.apkInfo.isInstallInEngine) {
        completion(YES, 0, @"");
        return;
    }

    if (!self.apkInfo.filePath || ![[NSFileManager defaultManager] fileExistsAtPath:self.apkInfo.filePath]) {
        if (completion) {
            completion(NO, ErrorFileNotExists, @"apk 安装不存在");
        }

        return;
    }

    [SocketDataSender sendRequest:@"installApp"
                             from:kProcessAppStore
                             with:@{ @"apkPath": self.apkInfo.filePath, @"shareFile": self.shareFile ? @"1" : @"0" }.mutableCopy
                               to:kProcessEngine
                         callback:^(NSDictionary *_Nullable response, NSError *_Nullable error) {
        if (error) {
            if (completion) {
                completion(NO, error.code, error.localizedDescription);
            }

            return;
        }

        if (completion) {
            completion(YES, 0, @"");
        }
    } timeout:(self.apkInfo.size / 1024 / 1024 < 1000) ? 2 * 60000 : 5 * 60000];
}

// 创建默认路径下app
- (BOOL)createCocoaAppWithPackageInfo {
    NSError *error;
    NSFileManager *fm = [NSFileManager defaultManager];

    if ([[NSFileManager defaultManager] fileExistsAtPath:self.defaultPath]) {
        return true;
    }

    NSString *templatePath = [[NSBundle mainBundle] pathForResource:@"YYBPackage.app" ofType:nil];
    BOOL isDir = NO;

    if (![fm fileExistsAtPath:templatePath isDirectory:&isDir] || !isDir) {
        YYBMacLogError(@"ApkInstallTask", @"Template package is not a directory: %@", templatePath);
        return NO;
    }

    NSString *temporaryAppPath = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSString stringWithFormat:@"TempApp_%@.app", self.apkInfo.name]];

    if ([fm fileExistsAtPath:temporaryAppPath]) {
        if (![fm removeItemAtPath:temporaryAppPath error:&error]) {
            YYBMacLogError(@"ApkInstallTask", @"Failed to remove existing temp app: %@", error);
        }
    }

    if (![fm copyItemAtPath:templatePath toPath:temporaryAppPath error:&error]) {
        YYBMacLogError(@"ApkInstallTask", @"Failed to copy template app: %@", error);
        return NO;
    }

    NSBundle *tempAppBundle = [NSBundle bundleWithPath:temporaryAppPath];

    if (self.apkInfo.iconPath.length == 0 && self.apkInfo.iconUrl.length > 0) {
        YYBMacLogInfo(@"ApkInstallTask", @"download app icon");
        NSImage *badge = [NSImage imageNamed:@"BadgeIcon"];
        dispatch_semaphore_t sema = dispatch_semaphore_create(0);
        [ApkInfoGenerater generateAppIconFromURL:[NSURL URLWithString:self.apkInfo.iconUrl]
                                           badge:badge
                                      badgeFloat:0.25
                                      completion:^(NSString *_Nonnull icnsPath) {
            self.apkInfo.iconPath = icnsPath;
            dispatch_semaphore_signal(sema);
        }];
        dispatch_semaphore_wait(sema, DISPATCH_TIME_FOREVER);
    }

    if (self.apkInfo.iconPath) {
        NSString *resourcesPath = [tempAppBundle resourcePath];
        NSString *destPath = [resourcesPath stringByAppendingPathComponent:@"AppIcon.icns"];

        if (![[NSFileManager defaultManager] copyItemAtPath:self.apkInfo.iconPath toPath:destPath error:nil]) {
            YYBMacLogInfo(@"ApkInstallTask", @"copy path is error");
        }
    }

    // 将pkg信息写入模版app
    NSString *infoPlistPath = [temporaryAppPath stringByAppendingPathComponent:@"Contents/Info.plist"];
    NSMutableDictionary *infoPlist = [NSMutableDictionary dictionaryWithDictionary:tempAppBundle.infoDictionary];

    if (infoPlist) {
        NSDictionary *newerInfoDict = [ApkInfoGenerater configAppInfoPlistDict:infoPlist pkgInfo:self.apkInfo];

        if (![newerInfoDict writeToFile:infoPlistPath atomically:YES]) {
            YYBMacLogError(@"ApkInstallTask", @"Failed to write Info.plist");
            return false;
        }
    } else {
        YYBMacLogError(@"ApkInstallTask", @"Failed to read Info.plist");
        return false;
    }

    if (![fm moveItemAtPath:temporaryAppPath toPath:self.defaultPath error:&error]) {
        YYBMacLogError(@"ApkInstallTask", @"Failed to rename app: %@", error);
        return false;
    }
    [ApkInfoGenerater codesign:self.defaultPath];
    return YES;
}

@end

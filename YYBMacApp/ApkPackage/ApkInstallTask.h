//
//  ApkInstallTask.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/6.
//

#import <Foundation/Foundation.h>
#import "YYBApkPackageModel.h"
#import "InstallApkInfo.h"
NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, ApkInstallError) {
    ErrorFileNotExists = -1001,
    ErrorInstallIntoEngine = -1002,
    ErrorParameterInvalid = -1003,
};
typedef void (^InstallCompletionHandle)(InstallApkInfo * _Nullable apkInfo, NSInteger retCode, NSString *_Nullable errorMessage);

@protocol ApkInstallTaskDelegate <NSObject>

- (void)apkInstallTaskDidFinished:(InstallApkInfo *)apkInfo;

@end

@interface ApkInstallTask : NSObject
@property (nonatomic, weak) id<ApkInstallTaskDelegate> delegate;
@property (readonly, getter=isExecuting) BOOL executing;

- (void)addTaskCompletionCallback:(InstallCompletionHandle _Nonnull)completionCallback;
- (instancetype _Nonnull)initWithApkInfo:(InstallApkInfo * _Nonnull)apkInfo;

- (void)installAppWithShotcut:(BOOL)shouldCreateShotcut;
- (void)createShotcut;
@end
NS_ASSUME_NONNULL_END

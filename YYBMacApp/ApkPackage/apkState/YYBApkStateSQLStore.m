//
//  YYBApkStateSQLStore.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/28.
//

#import "YYBApkStateSQLStore.h"
#import "YYBApkStateInfo.h"
#import <sqlite3.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYModel.h"
#import "FileUtils.h"

static NSString *const kLogTag = @"YYBApkStateSQLStore";
static NSString *const kTableName = @"t_yyb_apk_state";
static NSString *const kDefaultLocalApkDBDir = @"localApkStateDB";
static const int kCurrentDBVersion = 1;

static void *kDBQueueSpecificKey = &kDBQueueSpecificKey;

@interface YYBApkStateSQLStore () {
    sqlite3 *_db;
}
@property (nonatomic, strong) dispatch_queue_t dbQueue;
@end

@implementation YYBApkStateSQLStore

#pragma mark - 单例

+ (instancetype)sharedStore {
    static YYBApkStateSQLStore *store;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        store = [[YYBApkStateSQLStore alloc] init];
    });
    return store;
}

- (instancetype)init {
    if (self = [super init]) {
        _dbQueue = dispatch_queue_create("com.yyb.apkstate.sqlstore", DISPATCH_QUEUE_SERIAL);
        dispatch_queue_set_specific(_dbQueue, kDBQueueSpecificKey, kDBQueueSpecificKey, NULL);
        [self openDatabase];
        [self checkAndUpgradeDatabaseIfNeeded];
    }
    return self;
}

#pragma mark - 数据库文件路径与有效性检测

- (NSString *)dbPath {
    NSString *localApkDir = [FileUtils getApplicationSupportSubDir:kDefaultLocalApkDBDir];
    
    // 加到诊断日志打包中
    [[YYBMacLogUpload sharedInstance] addExtraFilePath:localApkDir];
    
    YYBMacLogInfo(kLogTag, @"localDownload dbPath = %@", localApkDir);
    return [localApkDir stringByAppendingPathComponent:@"yyb_apk_state.sqlite"];
}

- (BOOL)isDatabaseFileValid {
    NSString *dbPath = [self dbPath];
    return [[NSFileManager defaultManager] fileExistsAtPath:dbPath];
}

#pragma mark - 数据库打开/关闭/重建

- (void)openDatabase {
    if (dispatch_get_specific(kDBQueueSpecificKey)) {
        [self _openDatabaseImpl];
    } else {
        __weak typeof(self) weakSelf = self;
        dispatch_sync(_dbQueue, ^{
            __strong typeof(weakSelf) self = weakSelf;
            [self _openDatabaseImpl];
        });
    }
}

- (void)_openDatabaseImpl {
    if (_db) {
        YYBMacLogInfo(kLogTag, @"[openDatabase] 已有数据库连接，无需重复打开");
        return;
    }
    int result = sqlite3_open([self.dbPath UTF8String], &self->_db);
    if (result != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[openDatabase] 打开数据库失败: %d", result);
        self->_db = NULL;
        return;
    }
    // 设置WAL模式
    char *errMsg = NULL;
    int walRc = sqlite3_exec(self->_db, "PRAGMA journal_mode=WAL;", NULL, NULL, &errMsg);
    if (walRc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[openDatabase] 设置WAL模式失败: %s", errMsg);
        sqlite3_free(errMsg);
    } else {
        YYBMacLogInfo(kLogTag, @"[openDatabase] WAL模式设置成功");
    }
    // 初始化表结构
    NSString *sql = [NSString stringWithFormat:
        @"CREATE TABLE IF NOT EXISTS %@ ("
        "pkgname TEXT PRIMARY KEY NOT NULL,"
        "actualPkgname TEXT,"
        "percent TEXT,"
        "status TEXT,"
        "completedLength TEXT,"
        "totalLength TEXT,"
        "downloadSpeed TEXT,"
        "gameType INTEGER,"
        "engineProgress INTEGER,"
        "source TEXT,"
        "recommend_id TEXT,"
        "code INTEGER,"
        "installing_market INTEGER,"
        "first_launch INTEGER"
        ")", kTableName];
    int rc = sqlite3_exec(self->_db, [sql UTF8String], NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[openDatabase] 创建表失败: %s", errMsg);
        sqlite3_free(errMsg);
    } else {
        YYBMacLogInfo(kLogTag, @"[openDatabase] 数据库和表初始化成功");
    }
    [self setDBVersion:kCurrentDBVersion];
}

- (void)closeDatabase {
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (self->_db) {
            int rc = sqlite3_close(self->_db);
            if (rc != SQLITE_OK) {
                YYBMacLogError(kLogTag, @"[closeDatabase] 关闭数据库失败: %d", rc);
            } else {
                YYBMacLogInfo(kLogTag, @"[closeDatabase] 数据库关闭成功");
            }
            self->_db = NULL;
        }
    });
}

- (void)ensureDatabaseAvailable {
    if (!_db || ![self isDatabaseFileValid]) {
        YYBMacLogWarn(kLogTag, @"[ensureDatabaseAvailable] 数据库文件不存在或句柄失效，自动重建");
        if (_db) {
            int rc = sqlite3_close(_db);
            if (rc != SQLITE_OK) {
                YYBMacLogError(kLogTag, @"[ensureDatabaseAvailable] 关闭旧数据库失败: %d", rc);
            }
            _db = NULL;
        }
        [self openDatabase];
    }
}

#pragma mark - 数据库升级

- (int)dbVersion {
    int version = 0;
    if (!_db) return version;
    NSString *sql = @"PRAGMA user_version";
    sqlite3_stmt *stmt = NULL;
    if (sqlite3_prepare_v2(_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            version = sqlite3_column_int(stmt, 0);
        }
        sqlite3_finalize(stmt);
    }
    return version;
}

- (void)setDBVersion:(int)version {
    if (!_db) return;
    NSString *sql = [NSString stringWithFormat:@"PRAGMA user_version = %d", version];
    char *errMsg = NULL;
    int rc = sqlite3_exec(_db, [sql UTF8String], NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[setDBVersion] 设置数据库版本失败: %s", errMsg);
        sqlite3_free(errMsg);
    }
}

- (void)checkAndUpgradeDatabaseIfNeeded {
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        [self ensureDatabaseAvailable];
        int version = [self dbVersion];
        if (version < kCurrentDBVersion) {
            // 这里可根据历史版本做ALTER TABLE等升级操作
            [self setDBVersion:kCurrentDBVersion];
            YYBMacLogInfo(kLogTag, @"[checkAndUpgradeDatabaseIfNeeded] 数据库升级到版本%d", kCurrentDBVersion);
        }
    });
}

#pragma mark - 增删查改

- (void)insertOrUpdateApkState:(YYBApkStateInfo *)apkState {
    if (!apkState || !apkState.pkgname) {
        NSAssert(NO, @"请不要插入空apkState");
        return;
    }
    YYBMacLogInfo(kLogTag, @"[insertOrUpdateApkState] ----------- :  %@", apkState);
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !apkState) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[insertOrUpdateApkState] 数据库不可用，写入失败");
                return;
            }
            [self _insertOrUpdateApkState:apkState];
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[insertOrUpdateApkState] 异常: %@", e);
        }
    });
}

- (void)_insertOrUpdateApkState:(YYBApkStateInfo *)apkState {
    if (!_db || !apkState) return;
    NSString *sql = [NSString stringWithFormat:
        @"REPLACE INTO %@ (pkgname, actualPkgname, percent, status, completedLength, totalLength, downloadSpeed, gameType, engineProgress, source, recommend_id, code, installing_market, first_launch) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", kTableName];
    sqlite3_stmt *stmt = NULL;
    if (sqlite3_prepare_v2(_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, [apkState.pkgname UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 2, [apkState.actualPkgname UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 3, [apkState.percent UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 4, [apkState.status UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 5, [apkState.completedLength UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 6, [apkState.totalLength UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 7, [apkState.downloadSpeed UTF8String], -1, NULL);
        sqlite3_bind_int(stmt, 8, (int)apkState.gameType);
        sqlite3_bind_int(stmt, 9, apkState.engineProgress ? 1 : 0);
        sqlite3_bind_text(stmt, 10, [apkState.source UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 11, [apkState.recommend_id UTF8String], -1, NULL);
        if (apkState.code) {
            sqlite3_bind_int(stmt, 12, [apkState.code intValue]);
        } else {
            sqlite3_bind_null(stmt, 12);
        }
        sqlite3_bind_int(stmt, 13, apkState.installing_market ? 1 : 0);
        sqlite3_bind_int(stmt, 14, apkState.first_launch ? 1 : 0);
        if (sqlite3_step(stmt) != SQLITE_DONE) {
            YYBMacLogError(kLogTag, @"[insertOrUpdateApkState] 插入/更新失败: %s", sqlite3_errmsg(_db));
        } else {
            YYBMacLogInfo(kLogTag, @"[insertOrUpdateApkState] 成功: pkgname=%@", apkState.pkgname);
        }
        sqlite3_finalize(stmt);
    } else {
        YYBMacLogError(kLogTag, @"[insertOrUpdateApkState] 预编译失败: %s", sqlite3_errmsg(_db));
    }
}

- (void)batchInsertOrUpdateApkStates:(NSArray<YYBApkStateInfo *> *)apkStates {
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || apkStates.count == 0) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[batchInsertOrUpdateApkStates] 数据库不可用，批量写入失败");
                return;
            }
            sqlite3_exec(self->_db, "BEGIN TRANSACTION", NULL, NULL, NULL);
            for (YYBApkStateInfo *state in apkStates) {
                [self _insertOrUpdateApkState:state];
            }
            sqlite3_exec(self->_db, "COMMIT", NULL, NULL, NULL);
            YYBMacLogInfo(kLogTag, @"[batchInsertOrUpdateApkStates] 批量更新%lu个apk状态", (unsigned long)apkStates.count);
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[batchInsertOrUpdateApkStates] 异常: %@", e);
            if (self->_db) sqlite3_exec(self->_db, "ROLLBACK", NULL, NULL, NULL);
        }
    });
}

- (void)deleteApkStateByPkgname:(NSString *)pkgname {
    YYBMacLogInfo(kLogTag, @"[deleteApkStateByPkgname] : pkgname = %@,", pkgname);
    if (!pkgname) return;
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !pkgname) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[deleteApkStateByPkgname] 数据库不可用，删除失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@ WHERE pkgname=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, [pkgname UTF8String], -1, NULL);
                if (sqlite3_step(stmt) != SQLITE_DONE) {
                    YYBMacLogError(kLogTag, @"[deleteApkStateByPkgname] 删除失败: %s", sqlite3_errmsg(self->_db));
                } else {
                    YYBMacLogInfo(kLogTag, @"[deleteApkStateByPkgname] 成功: pkgname=%@", pkgname);
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[deleteApkStateByPkgname] 异常: %@", e);
        }
    });
}

- (nullable YYBApkStateInfo *)apkStateForPkgname:(NSString *)pkgname {
    if (!pkgname) return nil;
    __block YYBApkStateInfo *state = nil;
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !pkgname) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[apkStateForPkgname] 数据库不可用，查询失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE pkgname=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, [pkgname UTF8String], -1, NULL);
                if (sqlite3_step(stmt) == SQLITE_ROW) {
                    state = [self apkStateFromStmt:stmt];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[apkStateForPkgname] 异常: %@", e);
        }
    });
    return state;
}

- (NSArray<YYBApkStateInfo *> *)allApkStates {
    __block NSMutableArray *arr = [NSMutableArray array];
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[allApkStates] 数据库不可用，查询失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                while (sqlite3_step(stmt) == SQLITE_ROW) {
                    YYBApkStateInfo *state = [self apkStateFromStmt:stmt];
                    if (state) [arr addObject:state];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[allApkStates] 异常: %@", e);
        }
    });
    return arr;
}

- (void)clearAllApkStates {
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[clearAllApkStates] 数据库不可用，清空失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@", kTableName];
            char *errMsg = NULL;
            int rc = sqlite3_exec(self->_db, [sql UTF8String], NULL, NULL, &errMsg);
            if (rc != SQLITE_OK) {
                YYBMacLogError(kLogTag, @"[clearAllApkStates] 清空失败: %s", errMsg);
                sqlite3_free(errMsg);
            } else {
                YYBMacLogInfo(kLogTag, @"[clearAllApkStates] 全部清空成功");
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[clearAllApkStates] 异常: %@", e);
        }
    });
}

#pragma mark - 辅助

- (YYBApkStateInfo *)apkStateFromStmt:(sqlite3_stmt *)stmt {
    YYBApkStateInfo *state = [[YYBApkStateInfo alloc] init];
    state.pkgname = [self stringFromStmt:stmt col:0];
    state.actualPkgname = [self stringFromStmt:stmt col:1];
    state.percent = [self stringFromStmt:stmt col:2];
    state.status = [self stringFromStmt:stmt col:3];
    state.completedLength = [self stringFromStmt:stmt col:4];
    state.totalLength = [self stringFromStmt:stmt col:5];
    state.downloadSpeed = [self stringFromStmt:stmt col:6];
    state.gameType = sqlite3_column_int(stmt, 7);
    state.engineProgress = sqlite3_column_int(stmt, 8) ? YES : NO;
    state.source = [self stringFromStmt:stmt col:9];
    state.recommend_id = [self stringFromStmt:stmt col:10];
    int codeVal = sqlite3_column_type(stmt, 11) == SQLITE_NULL ? 0 : sqlite3_column_int(stmt, 11);
    state.code = codeVal ? @(codeVal) : nil;
    state.installing_market = sqlite3_column_int(stmt, 12) ? YES : NO;
    state.first_launch = sqlite3_column_int(stmt, 13) ? YES : NO;
    return state;
}

- (NSString *)stringFromStmt:(sqlite3_stmt *)stmt col:(int)col {
    const unsigned char *cstr = sqlite3_column_text(stmt, col);
    return cstr ? [NSString stringWithUTF8String:(const char *)cstr] : @"";
}

@end

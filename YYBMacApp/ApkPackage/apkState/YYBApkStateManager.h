//
//  YYBApkStateManager.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/28.
//

#import <Foundation/Foundation.h>
@class YYBApkStateInfo;

NS_ASSUME_NONNULL_BEGIN

/// 全局APK状态管理器，负责内存同步、状态查询、更新、退出前检测等
@interface YYBApkStateManager : NSObject

/// 单例
+ (instancetype)sharedManager;

/// 查询所有apk状态（线程安全，返回副本）
- (NSArray<YYBApkStateInfo *> *)allApkStates;

/// 查询指定包名的apk状态
- (nullable YYBApkStateInfo *)apkStateForPkgname:(NSString *)pkgname;

/// 插入或更新apk状态（主键pkgname）
- (void)insertOrUpdateApkState:(YYBApkStateInfo *)apkState;

/// 批量插入或更新
- (void)batchInsertOrUpdateApkStates:(NSArray<YYBApkStateInfo *> *)apkStates;

/// 删除指定包名的apk状态
- (void)deleteApkStateByPkgname:(NSString *)pkgname;

/// 清空所有apk状态
- (void)clearAllApkStates;

/// 查询是否有正在下载/安装中的apk（用于退出前弹窗）
- (BOOL)hasActiveOrInstallingApk;

/// 持久化所有内存apk状态到数据库（退出时调用）
- (void)persistAllToDatabase;

@end

NS_ASSUME_NONNULL_END

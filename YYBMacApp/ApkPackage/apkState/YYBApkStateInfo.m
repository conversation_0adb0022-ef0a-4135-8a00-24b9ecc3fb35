//
//  YYBApkStateInfo.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/28.
//

#import "YYBApkStateInfo.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kLogTag = @"YYBApkState";

// DownLoadAllState
DownLoadAllState const DownLoadAllStateBeginInstall = @"BeginInstall";
DownLoadAllState const DownLoadAllStateInstallError = @"InstallError";
DownLoadAllState const DownLoadAllStateInstalled = @"Installed";
DownLoadAllState const DownLoadAllStateInstalling = @"Installing";
DownLoadAllState const DownLoadAllStateBeginUninstall = @"BeginUninstall";
DownLoadAllState const DownLoadAllStateUnInstalling = @"UnInstalling";
DownLoadAllState const DownLoadAllStateUnInstallError = @"UnInstallError";
DownLoadAllState const DownLoadAllStateUnInstallCancel = @"UnInstallCancel";
DownLoadAllState const DownLoadAllStateEndUninstall = @"EndUninstall";
DownLoadAllState const DownLoadAllStateINIT = @"INIT";
DownLoadAllState const DownLoadAllStateActive = @"active";
DownLoadAllState const DownLoadAllStateWaiting = @"waiting";
DownLoadAllState const DownLoadAllStatePaused = @"paused";
DownLoadAllState const DownLoadAllStateError = @"error";
DownLoadAllState const DownLoadAllStateComplete = @"complete";
DownLoadAllState const DownLoadAllStateRemoved = @"removed";
DownLoadAllState const DownLoadAllStateNeedUpdate = @"NeedUpdate";
DownLoadAllState const DownLoadAllStateBeginLaunch = @"BeginLaunch";
DownLoadAllState const DownLoadAllStateCompleteLaunch = @"CompleteLaunch";
DownLoadAllState const DownLoadAllStateLaunchError = @"LaunchError";
DownLoadAllState const DownLoadAllStateStoreLaunchComplete = @"StoreLaunchComplete";
DownLoadAllState const DownLoadAllStateStoreLaunchError = @"StoreLaunchError";
DownLoadAllState const DownLoadAllStateBeginParse = @"BeginParse";
DownLoadAllState const DownLoadAllStateParseError = @"ParseError";
DownLoadAllState const DownLoadAllStateParsed = @"Parsed";
DownLoadAllState const DownLoadAllStateParseTimeout = @"ParseTimeout";
DownLoadAllState const DownLoadAllStateNotSupportInstall = @"NotSupportInstall";

@implementation NSString (DownLoadAllState)

- (DownLoadAllState)downloadAllState {
    NSString *string = self;
    if ([string isEqualToString:DownLoadAllStateBeginInstall]) return DownLoadAllStateBeginInstall;
    if ([string isEqualToString:DownLoadAllStateInstallError]) return DownLoadAllStateInstallError;
    if ([string isEqualToString:DownLoadAllStateInstalled]) return DownLoadAllStateInstalled;
    if ([string isEqualToString:DownLoadAllStateInstalling]) return DownLoadAllStateInstalling;
    
    if ([string isEqualToString:DownLoadAllStateBeginUninstall]) return DownLoadAllStateBeginUninstall;
    if ([string isEqualToString:DownLoadAllStateUnInstalling]) return DownLoadAllStateUnInstalling;
    if ([string isEqualToString:DownLoadAllStateUnInstallError]) return DownLoadAllStateUnInstallError;
    if ([string isEqualToString:DownLoadAllStateUnInstallCancel]) return DownLoadAllStateUnInstallCancel;
    if ([string isEqualToString:DownLoadAllStateEndUninstall]) return DownLoadAllStateEndUninstall;
    
    if ([string isEqualToString:DownLoadAllStateINIT]) return DownLoadAllStateINIT;
    if ([string isEqualToString:DownLoadAllStateActive]) return DownLoadAllStateActive;
    if ([string isEqualToString:DownLoadAllStateWaiting]) return DownLoadAllStateWaiting;
    if ([string isEqualToString:DownLoadAllStatePaused]) return DownLoadAllStatePaused;
    if ([string isEqualToString:DownLoadAllStateError]) return DownLoadAllStateError;
    if ([string isEqualToString:DownLoadAllStateComplete]) return DownLoadAllStateComplete;
    if ([string isEqualToString:DownLoadAllStateRemoved]) return DownLoadAllStateRemoved;
    if ([string isEqualToString:DownLoadAllStateNeedUpdate]) return DownLoadAllStateNeedUpdate;
    
    if ([string isEqualToString:DownLoadAllStateBeginLaunch]) return DownLoadAllStateBeginLaunch;
    if ([string isEqualToString:DownLoadAllStateCompleteLaunch]) return DownLoadAllStateCompleteLaunch;
    if ([string isEqualToString:DownLoadAllStateLaunchError]) return DownLoadAllStateLaunchError;
    
    if ([string isEqualToString:DownLoadAllStateStoreLaunchComplete]) return DownLoadAllStateStoreLaunchComplete;
    if ([string isEqualToString:DownLoadAllStateStoreLaunchError]) return DownLoadAllStateStoreLaunchError;
    
    if ([string isEqualToString:DownLoadAllStateBeginParse]) return DownLoadAllStateBeginParse;
    if ([string isEqualToString:DownLoadAllStateParseError]) return DownLoadAllStateParseError;
    if ([string isEqualToString:DownLoadAllStateParsed]) return DownLoadAllStateParsed;
    if ([string isEqualToString:DownLoadAllStateParseTimeout]) return DownLoadAllStateParseTimeout;
    if ([string isEqualToString:DownLoadAllStateNotSupportInstall]) return DownLoadAllStateNotSupportInstall;
    
    return nil;
}

@end

@implementation YYBApkStateInfo

- (void)setStatus:(NSString *)status {
    DownLoadAllState validState = [status downloadAllState];
    if (validState) {
        _status = validState;
    } else {
        _status = nil;
        YYBMacLogError(kLogTag, @"[setStatus:] 非法状态字符串: %@，已置为nil", status);
    }
}

- (NSString *)primaryKey {
    return self.pkgname ?: @"";
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<YYBApkState: pkgname=%@, status=%@, percent=%@, completedLength=%@, totalLength=%@, downloadSpeed=%@, gameType=%ld, engineProgress=%d, code=%@, installing_market=%d, first_launch=%d>",
            self.pkgname,
            self.status,
            self.percent,
            self.completedLength,
            self.totalLength,
            self.downloadSpeed,
            (long)self.gameType,
            self.engineProgress,
            self.code,
            self.installing_market,
            self.first_launch];
}

@end

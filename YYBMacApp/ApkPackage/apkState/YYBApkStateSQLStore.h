//
//  YYBApkStateSQLStore.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/28.
//

#import <Foundation/Foundation.h>
@class YYBApkStateInfo;

NS_ASSUME_NONNULL_BEGIN

/// APK状态SQL存储层，负责所有apk状态的增删查改和表结构管理
@interface YYBApkStateSQLStore : NSObject

/// 单例
+ (instancetype)sharedStore;

/// 插入或更新apk状态（主键为pkgname）
- (void)insertOrUpdateApkState:(YYBApkStateInfo *)apkState;

/// 批量插入或更新
- (void)batchInsertOrUpdateApkStates:(NSArray<YYBApkStateInfo *> *)apkStates;

/// 删除指定包名的apk状态
- (void)deleteApkStateByPkgname:(NSString *)pkgname;

/// 查询单个apk状态
- (nullable YYBApkStateInfo *)apkStateForPkgname:(NSString *)pkgname;

/// 查询所有apk状态
- (NSArray<YYBApkStateInfo *> *)allApkStates;

/// 清理所有apk状态
- (void)clearAllApkStates;

/// 数据库升级（如有字段变更自动升级）
- (void)checkAndUpgradeDatabaseIfNeeded;

@end

NS_ASSUME_NONNULL_END

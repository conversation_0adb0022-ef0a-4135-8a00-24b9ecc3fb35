//
//  YYBApkStateManager.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/28.
//


#import "YYBApkStateManager.h"
#import "YYBApkStateInfo.h"
#import "YYBApkStateSQLStore.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kLogTag = @"YYBApkStateManager";

static NSSet<NSString *> *kActiveOrInstallingStates = nil;

@interface YYBApkStateManager ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, YYBApkStateInfo *> *apkStateDict;
@property (nonatomic, strong) dispatch_queue_t syncQueue;
@end

@implementation YYBApkStateManager

+ (void)initialize {
    if (self == [YYBApkStateManager class]) {
        // 集中管理所有需要弹窗挽留的状态（开始安装+安装中+下载中）
        kActiveOrInstallingStates = [NSSet setWithObjects:
                                     DownLoadAllStateBeginInstall,
                                     DownLoadAllStateInstalling,
                                     DownLoadAllStateActive,
                                     nil];
    }
}

+ (instancetype)sharedManager {
    static YYBApkStateManager *manager;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[YYBApkStateManager alloc] init];
    });
    return manager;
}

- (instancetype)init {
    if (self = [super init]) {
        _apkStateDict = [NSMutableDictionary dictionary];
        _syncQueue = dispatch_queue_create("com.yyb.apkstate.manager", DISPATCH_QUEUE_CONCURRENT);
        // 初始化时同步加载，保证后续查询立即可用
        [self reloadFromDatabaseSync];
    }
    return self;
}

#pragma mark - 查询

- (NSArray<YYBApkStateInfo *> *)allApkStates {
    __block NSArray *arr = nil;
    dispatch_sync(_syncQueue, ^{
        arr = [self.apkStateDict.allValues copy];
    });
    return arr;
}

- (nullable YYBApkStateInfo *)apkStateForPkgname:(NSString *)pkgname {
    if (!pkgname) return nil;
    __block YYBApkStateInfo *state = nil;
    dispatch_sync(_syncQueue, ^{
        state = self.apkStateDict[pkgname];
    });
    return state;
}

#pragma mark - 插入/更新/删除

- (void)insertOrUpdateApkState:(YYBApkStateInfo *)apkState {
    if (!apkState || !apkState.pkgname) return;
    dispatch_barrier_async(_syncQueue, ^{
        YYBApkStateInfo *oldState = self.apkStateDict[apkState.pkgname];
        self.apkStateDict[apkState.pkgname] = apkState;
        if (oldState.status != apkState.status) {
            // 状态变化才写磁盘，避免下载过程中频繁写sql
            [[YYBApkStateSQLStore sharedStore] insertOrUpdateApkState:apkState];
        }
    });
}

- (void)batchInsertOrUpdateApkStates:(NSArray<YYBApkStateInfo *> *)apkStates {
    if (apkStates.count == 0) return;
    dispatch_barrier_async(_syncQueue, ^{
        for (YYBApkStateInfo *state in apkStates) {
            if (state.pkgname) {
                self.apkStateDict[state.pkgname] = state;
            }
        }
        [[YYBApkStateSQLStore sharedStore] batchInsertOrUpdateApkStates:apkStates];
        YYBMacLogInfo(kLogTag, @"[batchInsertOrUpdateApkStates] count=%lu", (unsigned long)apkStates.count);
    });
}

- (void)deleteApkStateByPkgname:(NSString *)pkgname {
    if (!pkgname) return;
    dispatch_barrier_async(_syncQueue, ^{
        [self.apkStateDict removeObjectForKey:pkgname];
        [[YYBApkStateSQLStore sharedStore] deleteApkStateByPkgname:pkgname];
        YYBMacLogInfo(kLogTag, @"[deleteApkStateByPkgname] pkgname=%@", pkgname);
    });
}

- (void)clearAllApkStates {
    dispatch_barrier_async(_syncQueue, ^{
        [self.apkStateDict removeAllObjects];
        [[YYBApkStateSQLStore sharedStore] clearAllApkStates];
        YYBMacLogInfo(kLogTag, @"[clearAllApkStates]");
    });
}

#pragma mark - 状态检测

/// 查询是否有正在下载/安装中的apk（用于退出前弹窗）
- (BOOL)hasActiveOrInstallingApk {
    __block BOOL found = NO;
    dispatch_sync(_syncQueue, ^{
        for (YYBApkStateInfo *state in self.apkStateDict.allValues) {
            if (state.status && [kActiveOrInstallingStates containsObject:state.status]) {
                found = YES;
                break;
            }
        }
    });
    return found;
}

#pragma mark - 数据库同步

/// 同步加载数据库到内存（初始化时调用，保证后续查询立即可用）
- (void)reloadFromDatabaseSync {
    NSArray<YYBApkStateInfo *> *dbStates = [[YYBApkStateSQLStore sharedStore] allApkStates];
    dispatch_barrier_sync(_syncQueue, ^{
        [self.apkStateDict removeAllObjects];
        for (YYBApkStateInfo *state in dbStates) {
            if (state.pkgname) {
                self.apkStateDict[state.pkgname] = state;
            }
        }
        YYBMacLogInfo(kLogTag, @"[reloadFromDatabaseSync] loaded %lu states", (unsigned long)dbStates.count);
    });
}

/// 异步加载数据库到内存（带回调，适合业务场景异步刷新）
- (void)reloadFromDatabaseAsyncWithCompletion:(void(^)(void))completion {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSArray<YYBApkStateInfo *> *dbStates = [[YYBApkStateSQLStore sharedStore] allApkStates];
        dispatch_barrier_async(self.syncQueue, ^{
            [self.apkStateDict removeAllObjects];
            for (YYBApkStateInfo *state in dbStates) {
                if (state.pkgname) {
                    self.apkStateDict[state.pkgname] = state;
                }
            }
            YYBMacLogInfo(kLogTag, @"[reloadFromDatabaseAsyncWithCompletion] loaded %lu states", (unsigned long)dbStates.count);
            if (completion) {
                completion();
            }
        });
    });
}

- (void)persistAllToDatabase {
    NSArray<YYBApkStateInfo *> *allStates = [self allApkStates];
    [[YYBApkStateSQLStore sharedStore] batchInsertOrUpdateApkStates:allStates];
    YYBMacLogInfo(kLogTag, @"[persistAllToDatabase] persisted %lu states", (unsigned long)allStates.count);
}

@end

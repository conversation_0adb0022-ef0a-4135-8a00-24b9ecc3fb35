//
//  YYBApkStateInfo.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/28.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * DownLoadAllState
 */

typedef NSString *DownLoadAllState NS_STRING_ENUM;

/** 开始安装  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateBeginInstall;
/** 安装错误  - 按钮文案：【重试】*/
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateInstallError;
/** 安装完成/已安装 - 按钮文案：【打开】  */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateInstalled;
/** 安装中  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateInstalling;

/** 开始卸载 - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateBeginUninstall;
/** 卸载中 - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateUnInstalling;
/** 卸载错误  - 按钮文案：【重试】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateUnInstallError;
/** 卸载取消 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateUnInstallCancel;
/** 结束卸载/完成卸载 - 按钮文案：【安装】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateEndUninstall;

/** 初始化状态 - 按钮文案：【安装】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateINIT;
/** 下载中 (Aria状态)   - 按钮文案：【暂停/百分比】*/
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateActive;
/** 等待中 (Aria状态)  - 按钮文案：【加载中icon（不可点击）】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateWaiting;
/** 暂停中 (Aria状态)  - 按钮文案：【继续】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStatePaused;
/** 下载错误 (Aria状态)  - 按钮文案：【重试】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateError;
/** 下载完成 (Aria状态)  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateComplete;
/** 删除下载任务 (Aria状态) /已移除 - 按钮文案：【安装】*/
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateRemoved;
/** 需要更新 - 按钮文案：【更新】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateNeedUpdate;

/** 以下为SDK启动相关，严格意义上不算是状态 */
/** 开始启动  - 按钮文案：【加载中icon】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateBeginLaunch;
/** 启动完成  - 按钮文案：【打开】 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateCompleteLaunch;
/** 启动错误（小游戏使用） - 按钮文案：【重试】*/
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateLaunchError;

/** 以下为前端派发 */
/** 商店启动完成 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateStoreLaunchComplete;
/** 商店启动错误 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateStoreLaunchError;

/** 本地安装（派发SDK） */
/** 开始解析 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateBeginParse;
/** 解析失败 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateParseError;
/** 解析成功 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateParsed;
/** 解析超时（超过30s就无响应当超时） */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateParseTimeout;
/** 不支持安装 */
FOUNDATION_EXPORT DownLoadAllState const DownLoadAllStateNotSupportInstall;

/// 游戏类型
typedef NS_ENUM(NSInteger, AppGameType) {
    AppGameTypeUnknown = 0,
    AppGameTypeMobileApp = 1,
    AppGameTypeWxGame = 2,
    AppGameTypeWxApp = 3,
    AppGameTypeYunGame = 4,
    AppGameTypePcGame = 5,
    AppGameTypePcApp = 7,
    AppGameTypeQqGame = 16,
    AppGameTypeAiAgent = 18,
    AppGameTypeAiTool = 19,
};

@interface NSString (DownLoadAllState)
/**
 * 将NSString转换为对应的DownLoadAllState枚举值
 * @return 对应的DownLoadAllState枚举值，如果未匹配则返回nil
 */
- (nullable DownLoadAllState)downloadAllState;

@end

// apk下载&安装的状态（主键pkgname）
@interface YYBApkStateInfo : NSObject

@property (nonatomic, copy) NSString *pkgname;                ///< 包名（主键）
@property (nonatomic, copy) NSString *actualPkgname;          ///< 实际包名 ?? (PC侧终端表示没有使用到)
@property (nonatomic, copy) NSString *percent;                ///< 进度百分比（字符串）
@property (nonatomic, copy, nullable) DownLoadAllState status;///< 状态
@property (nonatomic, copy) NSString *completedLength;        ///< 已完成长度（字符串）
@property (nonatomic, copy) NSString *totalLength;            ///< 总长度（字符串）
@property (nonatomic, copy) NSString *downloadSpeed;          ///< 下载速度（字符串）
@property (nonatomic, assign) AppGameType gameType;           ///< 游戏类型
@property (nonatomic, assign) BOOL engineProgress;            ///< 是否引擎进度
@property (nonatomic, copy, nullable) NSString *source;       ///< 批量安装来源
@property (nonatomic, copy, nullable) NSString *recommend_id; ///< 广告推荐id
///< 异常状态码       详见：https://doc.weixin.qq.com/sheet/e3_APYA1wb6ACgCNR9YTiX9IS7yU6NBp?scode=AJEAIQdfAAo7De329qAPYA1wb6ACg&tab=je2b1z
@property (nonatomic, strong, nullable) NSNumber *code;
@property (nonatomic, assign) BOOL installing_market;          ///< 是否商店安装中？
@property (nonatomic, assign) BOOL first_launch;               ///< 是否首次启动

/// 便于sql主键
- (NSString *)primaryKey;

@end

NS_ASSUME_NONNULL_END

//
//  YYBInstallUninstallListener.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/15.
//  专用于APK安装卸载的接收者协议，支持多对1，唯一主键为pkgName

#import <Foundation/Foundation.h>
@class InstallApkInfo;

NS_ASSUME_NONNULL_BEGIN

@protocol YYBInstallUninstallListener <NSObject>

/// 返回这个监听者关心的PkgName（包名唯一，注册用主键）
- (NSString *)listenerPkgName;

/// 安装/卸载状态变更（含完成/失败/安装中等）
- (void)onInstallStatusChanged:(InstallApkInfo *)apkInfo;

@end

NS_ASSUME_NONNULL_END

//
//  ApkInfoGenerater.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/18.
//

#import <AppKit/AppKit.h>
#import "YYBApkPackageModel.h"
#import "InstallApkInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface ApkInfoGenerater : NSObject
+ (void)generateAppIconFromURL:(NSURL *)imageURL
                         badge:(NSImage *)badge
                    badgeFloat:(CGFloat)badgeFloat
                    completion:(void (^)(NSString *icnsPath))completion;
+ (void)getApkInfoWithPath:(NSString *)apkPath
                     badge:(NSImage *)badge
                badgeFloat:(CGFloat)badgeFloat
                completion:(void (^)(NSString *packageName, NSString *appName, NSString *icnsPath))completion;

/// 根据pkgInfo读取pkg相关内容，更新info plist
+ (NSDictionary *)configAppInfoPlistDict:(NSDictionary *)infoPlistDict pkgInfo:(InstallApkInfo *)pkgInfo;

+ (void)codesign:(NSString *)appPath;

+ (void)removeQuarantineAttribute:(NSString *)appPath;

/// 启动台App路径
+ (NSString *)launchAppsPath;

/// app默认生成路径
+ (NSString *)defaultAppPath;

+ (NSString *)launchAppsPathFromName:(NSString *)appName;
+ (NSString *)defaultAppPathForName:(NSString *)appName;

+ (NSString *)appBundleIdForPkgName:(NSString *)pkgName;
@end

NS_ASSUME_NONNULL_END

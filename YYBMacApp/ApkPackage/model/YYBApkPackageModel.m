//
//  YYBApkPackageState.m
//  YYBMacApp
//
//  Created by System on 2025/7/10.
//

#import "YYBApkPackageModel.h"


// AppSourceType
LocalAppSourceType const LocalAppSourceTypeNormal = @"normal";
LocalAppSourceType const LocalAppSourceTypeDeveloper = @"developer";
LocalAppSourceType const LocalAppSourceTypeUserDragAPK = @"user_drag_apk";
LocalAppSourceType const LocalAppSourceTypeFromEmulator = @"from_emulator";

// InstallFrom
InstallFrom const InstallFromFromStore = @"from_store";
InstallFrom const InstallFromFromAndrows = @"from_androws";
InstallFrom const InstallFromFromLocalInstall = @"from_local_install";

@implementation NSString (LocalAppSourceType)

- (nullable LocalAppSourceType)localAppSourceType {
    if ([self isEqualToString:LocalAppSourceTypeNormal]) {
        return LocalAppSourceTypeNormal;
    } else if ([self isEqualToString:LocalAppSourceTypeDeveloper]) {
        return LocalAppSourceTypeDeveloper;
    } else if ([self isEqualToString:LocalAppSourceTypeUserDragAPK]) {
        return LocalAppSourceTypeUserDragAPK;
    } else if ([self isEqualToString:LocalAppSourceTypeFromEmulator]) {
        return LocalAppSourceTypeFromEmulator;
    } else {
        return nil;
    }
}

@end

@implementation LaunchApkInfo

@end

@implementation InstallApkExtendInfo

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"installFrom": @"install_from",
    };
}

@end

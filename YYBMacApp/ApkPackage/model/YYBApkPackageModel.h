//
//  YYBApkPackageState.h
//  YYBMacApp
//
//  Created by System on 2025/7/10.
//

#import <Foundation/Foundation.h>
#import "YYModel.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * LocalAppSourceType
 */

typedef NSString * LocalAppSourceType NS_STRING_ENUM;
FOUNDATION_EXPORT LocalAppSourceType const LocalAppSourceTypeNormal;        // 普通
FOUNDATION_EXPORT LocalAppSourceType const LocalAppSourceTypeDeveloper;     // 开发者
FOUNDATION_EXPORT LocalAppSourceType const LocalAppSourceTypeUserDragAPK;   // 用户拖拽
FOUNDATION_EXPORT LocalAppSourceType const LocalAppSourceTypeFromEmulator;  // 模拟器来源

typedef NSString *InstallFrom NS_STRING_ENUM;
FOUNDATION_EXPORT InstallFrom const InstallFromFromStore;
FOUNDATION_EXPORT InstallFrom const InstallFromFromAndrows;
FOUNDATION_EXPORT InstallFrom const InstallFromFromLocalInstall;

@interface NSString (LocalAppSourceType)

/**
 * 将NSString转换为对应的LocalAppSourceType枚举值
 * @return 对应的LocalAppSourceType枚举值，如果未匹配则返回nil
 */
- (nullable LocalAppSourceType)localAppSourceType;

@end

typedef NS_ENUM(NSInteger, AppOrientation) {
    Unknown = -1,
    Landscape = 0,  // 横屏
    Portrait = 1,   // 竖屏
};

@interface InstallApkExtendInfo : NSObject<YYModel>

@property (nonatomic, strong) InstallFrom installFrom;  // TODO: halehuang 尚未适配

@end

// 启动apk信息
@interface LaunchApkInfo : NSObject

@property (nonatomic, strong) NSString *pkgname;

@end

NS_ASSUME_NONNULL_END

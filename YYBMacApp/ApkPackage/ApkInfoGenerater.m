//
//  ApkInfoGenerater.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/18.
//

#import "ApkInfoGenerater.h"
#import "YYBMacFusionSDK/YYBMacLog.h"
#import "MacroUtils.h"
#import <CommonCrypto/CommonDigest.h>

static NSString *const kTag = @"ApkPackage";
static const NSArray<NSNumber *> *iconSizes = @[@16, @64, @256]; // 1x & 2x base
@implementation ApkInfoGenerater
+ (NSString *)sha256:(NSString *)string {
    const char *cstr = [string cStringUsingEncoding:NSUTF8StringEncoding];
    unsigned char result[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(cstr, (CC_LONG)strlen(cstr), result);
    NSMutableString *hash = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [hash appendFormat:@"%02x", result[i]];
    }
    return hash;
}

+ (void)generateAppIconFromURL:(NSURL *)imageURL
                         badge:(NSImage *)badge
                    badgeFloat:(CGFloat)badgeFloat
                    completion:(void (^)(NSString *imagePath))completion {
    [self downloadImageFromURL:imageURL
                    completion:^(NSImage *image) {
        NSImage *mergedImage = [self imageByMainImage:image
                                                badge:badge
                                            badgeSize:badgeFloat];
        NSArray *resizedImages = [self resizeImage:mergedImage];
        NSString *icnsPath = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.icns", [self sha256:imageURL.path]]];

        if (![self createICNSFileWithImages:resizedImages
                                     atPath:icnsPath]) {
            YYBMacLogInfo(kTag, @"ICNS successfully generated");
        }

        if (completion) {
            completion(icnsPath);
        }
    }];
}

+ (NSString *)generateAppIcon:(NSImage *)image {
    NSArray *resizedImages = [self resizeImage:image];
    NSString *icnsPath = [NSTemporaryDirectory() stringByAppendingPathComponent:@"AppIcon.icns"];

    if ([self createICNSFileWithImages:resizedImages atPath:icnsPath]) {
        YYBMacLogInfo(kTag, @"ICNS successfully generated");
        return icnsPath;
    }

    return nil;
}

+ (void)getApkInfoWithPath:(NSString *)apkPath
                     badge:(NSImage *)badge
                badgeFloat:(CGFloat)badgeFloat
                completion:(void (^)(NSString *packageName, NSString *appName, NSString *icnsPath))completion {
    YYBMacLogInfo(kTag, @"fetch apk info from apkPath path");
    NSString *aaptPath = [[NSBundle mainBundle] pathForResource:@"aapt" ofType:nil];

    if (aaptPath) {
        NSTask *chmodTask = [[NSTask alloc] init];
        chmodTask.launchPath = @"/bin/chmod";
        chmodTask.arguments = @[@"+x", aaptPath];
        NSFileHandle *nullHandle = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
        chmodTask.standardError = nullHandle;
        chmodTask.standardOutput = nullHandle;
        [chmodTask launch];
        [chmodTask waitUntilExit];
    }

    // 1. 创建 NSTask
    NSTask *task = [[NSTask alloc] init];
    task.launchPath = aaptPath;
    task.arguments = @[@"dump", @"badging", apkPath];

    // 2. 创建管道
    NSPipe *pipe = [NSPipe pipe];
    task.standardOutput = pipe;

    // 3. 启动任务
    [task launch];
    [task waitUntilExit];

    // 4. 读取输出
    NSData *data = [[pipe fileHandleForReading] readDataToEndOfFile];
    NSString *output = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];

    if (!output) {
        if (completion) {
            completion(@"", @"", @"");
        }
    }

    NSError *error = nil;

    // 取package name
    NSRegularExpression *packageRegex = [NSRegularExpression regularExpressionWithPattern:@"package: name='([^']+)'" options:0 error:&error];
    NSTextCheckingResult *packageMatch = [packageRegex firstMatchInString:output options:0 range:NSMakeRange(0, output.length)];
    NSString *packageName = nil;

    if (packageMatch && [packageMatch numberOfRanges] > 1) {
        NSRange range = [packageMatch rangeAtIndex:1];

        if (range.location != NSNotFound) {
            packageName = [output substringWithRange:range];
        }
    }

    // app name
    NSRegularExpression *appNameRegex = [NSRegularExpression regularExpressionWithPattern:@"application-label:'([^']+)'" options:0 error:&error];
    NSTextCheckingResult *appNameMatch = [appNameRegex firstMatchInString:output options:0 range:NSMakeRange(0, output.length)];
    NSString *appName = nil;

    if (appNameMatch && [appNameMatch numberOfRanges] > 1) {
        NSRange range = [appNameMatch rangeAtIndex:1];

        if (range.location != NSNotFound) {
            appName = [output substringWithRange:range];
        }
    }

    YYBMacLogInfo(kTag, @"fetch apk info:%@ - %@", appName, packageName);

    // 图标
    NSString *apkIconPath = nil;
    NSString *icnsPath = nil;
    NSRegularExpression *iconRegex = [NSRegularExpression regularExpressionWithPattern:@"application-icon-\\d+:'([^']+)'" options:0 error:nil];
    NSArray *iconMatches = [iconRegex matchesInString:output options:0 range:NSMakeRange(0, output.length)];

    if (iconMatches.count > 0) {
        NSTextCheckingResult *result = iconMatches.lastObject;
        apkIconPath = [output substringWithRange:[result rangeAtIndex:1]];
    }

    if (apkIconPath) {
        NSTask *unzipTask = [[NSTask alloc] init];
        [unzipTask setLaunchPath:@"/usr/bin/unzip"];
        [unzipTask setArguments:@[@"-p", apkPath, apkIconPath]];

        NSPipe *iconPipe = [NSPipe pipe];
        [unzipTask setStandardOutput:iconPipe];
        [unzipTask launch];
        [unzipTask waitUntilExit];

        NSData *iconData = [[iconPipe fileHandleForReading] readDataToEndOfFile];

        if (iconData.length > 0) {
            NSImage *mainImage = [[NSImage alloc] initWithData:iconData];

            if (mainImage) {
                NSImage *mergedImage = [self imageByMainImage:mainImage badge:badge badgeSize:badgeFloat];
                icnsPath = [self generateAppIcon:mergedImage];
            }
        }
    }

    YYBMacLogInfo(kTag, @"fetch apk info icon:%@", icnsPath);

    if (completion) {
        completion(packageName, appName, icnsPath);
    }
}

+ (NSArray *)resizeImage:(NSImage *)sourceImage {
    NSMutableArray *resizedImages = [NSMutableArray array];

    for (int i = 0; i < iconSizes.count * 2; i++) {
        NSInteger index = i / 2;
        CGFloat width = (i % 2 == 0) ? iconSizes[index].intValue : iconSizes[index].intValue * 2;
        CGSize size = CGSizeMake(width, width);
        YYBMacLogInfo(kTag, @"resizeImage:%@", NSStringFromSize(size));
        NSImage *resized = [[NSImage alloc] initWithSize:size];

        [resized lockFocus];
        [sourceImage drawInRect:NSMakeRect(0, 0, size.width, size.height)
                       fromRect:NSMakeRect(0, 0, sourceImage.size.width, sourceImage.size.height)
                      operation:NSCompositingOperationCopy
                       fraction:1.0];
        [resized unlockFocus];

        [resizedImages addObject:resized];
    }

    return [resizedImages copy];
}

+ (BOOL)createICNSFileWithImages:(NSArray *)images atPath:(NSString *)outputPath {
    NSMutableArray *imageReps = [NSMutableArray array];

    for (NSImage *img in images) {
        NSBitmapImageRep *rep = [NSBitmapImageRep imageRepWithData:[img TIFFRepresentation]];
        [imageReps addObject:rep];
    }

    NSString *iconsetDir = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSString stringWithFormat:@"%@_temp.iconset", outputPath.lastPathComponent.stringByDeletingPathExtension]];
    [[NSFileManager defaultManager] createDirectoryAtPath:iconsetDir withIntermediateDirectories:YES attributes:nil error:nil];

    for (int i = 0; i < iconSizes.count * 2; i++) {
        NSInteger index = i / 2;
        NSString *fileName = [NSString stringWithFormat:@"icon_%@x%@@%@.png", iconSizes[index], iconSizes[index], (i % 2 == 0) ? @"" : @"2x" ];
        YYBMacLogInfo(kTag, @"createICNSFileWithImages:%@", fileName);
        NSString *filePath = [iconsetDir stringByAppendingPathComponent:fileName];

        NSData *pngData = [[imageReps objectAtIndex:i] representationUsingType:NSBitmapImageFileTypePNG properties:@{}];
        [pngData writeToFile:filePath atomically:YES];
    }

    NSTask *task = [[NSTask alloc] init];
    [task setLaunchPath:@"/usr/bin/iconutil"];
    [task setArguments:@[@"-c", @"icns", @"-o", outputPath, iconsetDir]];
    NSPipe *pipe = [NSPipe pipe];
    task.standardError = pipe;
    task.standardOutput = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
    @try {
        [task launch];
        [task waitUntilExit];
        int status = [task terminationStatus];
        if (status != 0) {
            NSData *data = [[pipe fileHandleForReading] readDataToEndOfFile];
            NSString *output = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            YYBMacLogError(kTag, @"Failed to iconutil app:%@", output ?: @"Unknown error");
        }
        return status == 0;
    } @catch (NSException *exception) {
        YYBMacLogInfo(kTag, @"ICNS creation failed: %@", exception);
        return NO;
    }
}

+ (void)downloadImageFromURL:(NSURL *)url completion:(void (^)(NSImage *image))completion {
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url
                                                             completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (!error && data) {
            NSImage *image = [[NSImage alloc] initWithData:data];

            if (image) {
                DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
                                   completion(image);
                               });
                return;
            }
        }

        YYBMacLogInfo(kTag, @"Image download failed: %@", error);
    }];

    [task resume];
}

+ (NSImage *)imageByMainImage:(NSImage *)mainImage
                        badge:(NSImage *)badge
                    badgeSize:(CGFloat)badgeFloat {
    if (mainImage == nil) {
        return nil;
    }

    if (badge == nil) {
        return mainImage;
    }

    // 合成一张1024的图片，苹果ICON设计规范：内容在840x840以内
    NSSize mainSize = NSMakeSize(1024, 1024);
    NSRect contentRect = NSMakeRect(92, 92, mainSize.width - 92 * 2, mainSize.height - 92 * 2);
    CGFloat cornerRadius = 181; // 正常为180，但由于要切掉白边，直接180会有些白色边缘

    CGFloat badgeWidth = mainSize.width * ((badgeFloat > 0) ? badgeFloat : 0.25);
    CGFloat badgeHeight = badge.size.height / badge.size.width * badgeWidth;
    CGSize badgeSize = CGSizeMake(badgeWidth, badgeHeight);
    NSRect badgeRect = NSMakeRect(mainSize.width - badgeSize.width - 92,
                                  92,
                                  badgeSize.width,
                                  badgeSize.height);

    NSImage *newImage = [[NSImage alloc] initWithSize:mainSize];
    [newImage lockFocus];

    NSBezierPath *clipPath = [NSBezierPath bezierPathWithRoundedRect:contentRect xRadius:cornerRadius yRadius:cornerRadius];
    [clipPath addClip];

    [mainImage drawInRect:contentRect
                 fromRect:NSZeroRect
                operation:NSCompositingOperationSourceOver
                 fraction:1.0];

    [NSGraphicsContext saveGraphicsState];
    [badge drawInRect:badgeRect
             fromRect:NSZeroRect
            operation:NSCompositingOperationSourceOver
             fraction:1.0];
    [NSGraphicsContext restoreGraphicsState];

    [newImage unlockFocus];
    return newImage;
}


+ (NSDictionary *)configAppInfoPlistDict:(NSDictionary *)infoPlistDict pkgInfo:(InstallApkInfo *)pkgInfo {
    NSString *bundleIdentifier = [[NSBundle mainBundle] bundleIdentifier];
    NSMutableDictionary *infoPlist = [NSMutableDictionary dictionaryWithDictionary:infoPlistDict];

    infoPlist[@"CFBundleName"] = pkgInfo.name;
    infoPlist[@"CFBundleDisplayName"] = pkgInfo.name;
    infoPlist[@"CFBundleIdentifier"] = [self appBundleIdForPkgName:pkgInfo.pkgName];
    infoPlist[@"YYBModeDir"] = [[NSBundle mainBundle] bundlePath];
    infoPlist[@"YYBModeBundleId"] = bundleIdentifier;
    infoPlist[@"YYBPackageName"] = pkgInfo.pkgName;
    infoPlist[@"YYBIconUrl"] = pkgInfo.iconUrl;
    return infoPlist.copy;
}


+ (void)codesign:(NSString *)appPath {
    if (!appPath || ![[NSFileManager defaultManager] fileExistsAtPath:appPath]) {
        return;
    }

    NSTask *task = [[NSTask alloc] init];

    task.launchPath = @"/usr/bin/codesign";
    task.arguments = @[@"--force", @"--sign", @"-", appPath];
    NSPipe *pipe = [NSPipe pipe];
    task.standardError = pipe;
    task.standardOutput = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
    [task launch];
    [task waitUntilExit];
    NSData *data = [[pipe fileHandleForReading] readDataToEndOfFile];
    NSString *output = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];

    if (output) {
        YYBMacLogError(kTag, @"Failed to codesign app:%@, error:%@", [appPath lastPathComponent], output);
    }
}

+ (void)removeQuarantineAttribute:(NSString *)appPath {
    NSTask *task = [[NSTask alloc] init];

    task.launchPath = @"/usr/bin/xattr";
    task.arguments = @[@"-d", @"com.apple.quarantine", appPath];
    task.standardOutput = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
    task.standardError = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
    [task launch];
    [task waitUntilExit];
}

+ (NSString *)launchAppsPath {
    return [NSHomeDirectory() stringByAppendingPathComponent:@"Applications/YYBApplications"];
}

+ (NSString *)defaultAppPath {
    NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *defaultAppsDir = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/%@/Applications", [[NSBundle mainBundle] bundleIdentifier]]];
    return  defaultAppsDir;
}

/// 快捷方式路径
+ (NSString *)launchAppsPathFromName:(NSString *)appName {
    if (!appName) {
        return nil;
    }
    return [[self launchAppsPath] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.app", appName]];
}

/// app默认生成路径
+ (NSString *)defaultAppPathForName:(NSString *)appName {
    if (!appName) {
        return nil;
    }
    return [[self defaultAppPath] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.app", appName]];
}

+ (NSString *)appBundleIdForPkgName:(NSString *)pkgName {
    NSString *bundleIdentifier = [[NSBundle mainBundle] bundleIdentifier];
    return [NSString stringWithFormat:@"%@.app.%@", bundleIdentifier, pkgName];
}
@end

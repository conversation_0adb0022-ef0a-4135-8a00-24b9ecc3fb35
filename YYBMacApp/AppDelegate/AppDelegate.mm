//
//  AppDelegate.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/7.
//

#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <UserNotifications/UserNotifications.h>
#import <YYBMacFusionSDK/YYBMacResHub.h>
#import <unistd.h>
#import "AppDelegate.h"
#import "system_info.h"
#import "NotificationCenter.h"
#import "YYBMacFusionSDKManager.h"
#import "YYBSocketEngine.h"
#import "YYBMessageReceiver.h"
#import "YYBLibAria2ServiceFacade.h"
#import "AppDelegate+Menu.h"
#import "YYBRoutes.h"
#import "YYBPseudoProtocol.h"
#import "AsyncInitTaskManager.h"
#import "YYBApkPackage.h"
#import "YYBAppConfig.h"
#import "UninstallManager.h"
#import "YYBLibAria2SessionManager.h"
#import "YYBApkStateManager.h"
#import "MacroUtils.h"
#import "YYBAlert.h"
#import "YYBAppConfig.h"
#import "BussinessMessageCenter.h"
#import "IdentifierConstants.h"
#import "LoadingWindow.h"
#import "YYBDefine.h"
#import "YYBMessageSender.h"
#import "ResRefreshListener.h"
#import "MainWindow.h"

static NSString *const kTag = @"AppDelegate";
static BOOL const useNewUI = YES;

@interface AppDelegate () <NSWindowDelegate>

@property (strong, nonatomic) NSWindow *window;
@property (nonatomic, assign) BOOL isBackgroundMode;
@property (strong, nonatomic) NSStatusItem *statusItem;
@property (nonatomic, strong) AsyncInitTaskManager* syncTaskManager;
@property (nonatomic, assign) Boolean hasShowingLoadingWindow;
@property(nonatomic, strong) ResRefreshListener* refreshListener;
// 退出全屏后是否要隐藏窗口（全屏下点关闭按钮需要隐藏）
@property (nonatomic, assign) BOOL shouldHideAfterFullScreen;

@end

@implementation AppDelegate

#pragma mark - Application Life Cycle
- (void)applicationWillFinishLaunching:(NSNotification *)aNotification {
    // 初始化所有基础库(初始化前不能调用YYBMacLog，否则日志会在非配置目录中生成！！！)
    [YYBMacFusionSDKManager setupFustionSDK:^(BOOL success, NSError * _Nullable error) {
        if (!self.refreshListener) {
            self.refreshListener = [[ResRefreshListener alloc] init];
        }
        
        [[YYBMacResHub sharedInstance] addResRefreshListener:self.refreshListener];
    }];
}

- (void)applicationDidFinishLaunching:(NSNotification *)aNotification {
    YYBMacLogInfo(kTag, @"applicationDidFinishLaunch begine");
//  ----------------------------------------------------------------------
//
    // 初始化窗口外观
    if (useNewUI) {
        [NSApp setAppearance:[NSAppearance appearanceNamed:NSAppearanceNameDarkAqua]];
    } else {
        [NSApp setAppearance:[NSAppearance appearanceNamed:NSAppearanceNameAqua]];
    }
    // 打印系统信息
    SystemInfo::getInstance().printSystemInfo();
    
    // 加载应用配置
    [YYBAppConfig loadConfig];
    // 开启后台服务
    [self startService];
    // 注册监听
    [self registerCallbacks];
    // 初始化下载服务(每次都杀死再重启)
    [[YYBLibAria2ServiceFacade sharedService] startWithCompletion:^(BOOL success, NSError * _Nullable error) {
        YYBMacLogInfo(kTag, @"startAria2c result = %@, error = %@", @(success), error);
        [self startSyncTasks];
    }];
    // 初始化路由
    [YYBRoutes registerRoutes];
    // 加载视图
    [self loadContent];
    // 升级package包
    [[YYBApkPackage shared] upgradePackage];
    // 卸载
    [UninstallManager setup];
    
//
//  ----------------------------------------------------------------------
    YYBMacLogInfo(kTag, @"applicationDidFinishLaunch finish.current version:%@ build:%@", [YYBAppConfig appVersion], [YYBAppConfig bundleVersion]);
}

- (void)showLoading{
    LoadingWindow *window = [[LoadingWindow alloc] initWithContentRect:NSMakeRect(0, 0, 600, 600)];
    // 自定义配置
    [window setProgressColor:[NSColor greenColor]];
    [window setTopIcon:[NSImage imageNamed:NSImageNameInfo]]; // 系统图标
    [window setTopText:@"引擎下载中..."];
    // 显示窗口
    [window showWindow];
}

- (void)orderWindowFront {
    [self handleBackgroundToFront];
    if (self.window) {
        [self.window makeKeyAndOrderFront:nil];
    } else {
        [self requestNotificationPermission];
        [self setupMainUI];
    }
}

/// 用户点击 Dock 图标或再次启动时
- (BOOL)applicationShouldHandleReopen:(NSApplication *)sender hasVisibleWindows:(BOOL)flag {
    YYBMacLogInfo(kTag, @"applicationShouldHandleReopen: %d", flag);
    if (self.isBackgroundMode || !flag) {
        [self orderWindowFront];
        return NO;
    }
    return YES;
}

- (void)application:(NSApplication *)application openURLs:(NSArray<NSURL *> *)urls
{
    if (self.isBackgroundMode || !self.window.visible) {
        YYBMacLogInfo(kTag, @"open url in background mode. bring window to front.");
        [self orderWindowFront];
    }
    if (self.window.isMiniaturized) {
        self.window.isMiniaturized = NO;
    }
    YYBMacLogInfo(kTag, @"openURLs:%@", urls);
    NSMutableArray<NSURL *> *urlsArray = [NSMutableArray arrayWithCapacity:urls.count];
    for (NSURL *url in urls) {
        BOOL canOpen = [YYBRoutes routeURL:url];
        if (!canOpen) {
            [urlsArray addObject:url];
        }
    }
    [YYBPseudoProtocol routeURLs:urlsArray];
}

- (void)applicationWillTerminate:(NSNotification *)aNotification {
    // 正常退出，如菜单栏退出
    YYBMacLogInfo(kTag, @"applicationWillTerminate -- start");
    YYBMacLogInfo(kTag, @"applicationWillTerminate -- end");
}

- (void)applicationWillResignActive:(NSNotification *)notification {
    YYBMacLogInfo(kTag, @"applicationWillResignActive -- start");
    
    // apk状态服务：持久化可能还在内存中修改的状态至sql
    [[YYBApkStateManager sharedManager] persistAllToDatabase];
    
    YYBMacLogInfo(kTag, @"applicationWillResignActive -- end");
}

- (BOOL)applicationSupportsSecureRestorableState:(NSApplication *)app {
    NSLog(@"applicationSupportsSecureRestorableState");
    return YES;
}

- (NSApplicationTerminateReply)applicationShouldTerminate:(NSApplication *)sender {
    YYBMacLogInfo(kTag, @"applicationShouldTerminate start");
    // 用户卸载，立刻退出
    if ([UninstallManager isUninstall]) {
        YYBMacLogInfo(kTag, @"用户卸载应用，直接退出");
        return NSTerminateNow;
    }
    
    BOOL shouldTerminate = NO;
    // 检查是否有活跃/安装中任务，并弹出挽留弹窗
    if (![[YYBApkStateManager sharedManager] hasActiveOrInstallingApk]) {
        // 没有活跃任务，直接退出
        shouldTerminate = YES;
    } else {
        // 有活跃任务，弹窗挽留用户确认是否退出
        shouldTerminate = [self showRetentionAlertForExit];
    }
    
    // 用户取消退出
    if (!shouldTerminate) {
        YYBMacLogInfo(kTag, @"用户取消退出，继续运行应用");
        return NSTerminateCancel;
    }
        
    // 完成持久化相关工作后，正常退出
    [self prepareForExit];
    return NSTerminateLater;
}

/// 退出前的准备工作（持久化、停止下载等）
- (void)prepareForExit {
    // apk状态服务：持久化可能还在内存中修改的状态至sql
    [[YYBApkStateManager sharedManager] persistAllToDatabase];

    // 下载服务：需要在真正退出前，调用stop，确保不丢失进度（目前最多丢失5秒的下载进度）
    [[YYBLibAria2ServiceFacade sharedService] stopWithCompletion:^{
        YYBMacLogInfo(kTag, @"YYBLibAria2ServiceFacade stop success");
        // 处理完下载任务等数据后再退出 （延迟确保数据写入）
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            YYBMacLogInfo(kTag, @"applicationShouldTerminate end");
            [NSApp replyToApplicationShouldTerminate:YES];
        });
    }];
}

// 展示挽留弹窗（返回用户是否确认退出）
- (BOOL)showRetentionAlertForExit {
    NSString *appName = YYBAppConfig.appName;
    return [YYBAlert showConfirmAlertWithStyle:NSAlertStyleWarning
                                         title:[NSString stringWithFormat:@"退出%@", appName]
                                       message:[NSString stringWithFormat:@"安装即将完成，请勿退出%@", appName]
                                  confirmTitle:@"确定退出"
                                   cancelTitle:@"取消"];
}

#pragma mark - NSWindowDelegate

- (BOOL)windowShouldClose:(NSWindow *)sender {
    YYBMacLogInfo(kTag, @"windowShouldClose");
    if ((sender.styleMask & NSWindowStyleMaskFullScreen) == NSWindowStyleMaskFullScreen) {
        // 全屏状态下，先退出全屏，不要立即隐藏(也无法立即隐藏，因为是异步，先隐藏会黑屏)
        [sender toggleFullScreen:nil];
        self.shouldHideAfterFullScreen = YES;
        return NO;
    }
    [sender orderOut:nil]; // 隐藏窗口
    return NO; // 返回 NO，阻止窗口被关闭（释放）
}

- (void)windowDidExitFullScreen:(NSNotification *)notification {
    NSWindow *window = notification.object;
    if (self.shouldHideAfterFullScreen) {
        [window orderOut:nil];     // 现在全屏已退出，可以正常隐藏了
        self.shouldHideAfterFullScreen = NO;
    }
}

#pragma mark - Private Method
- (void)setupMainUI {
    // 创建主窗口
    self.window = [[MainWindow alloc] initWithContentRect:CGRectZero
                                                styleMask:NSWindowStyleMaskTitled | NSWindowStyleMaskClosable |   NSWindowStyleMaskMiniaturizable | NSWindowStyleMaskResizable
                                                  backing:NSBackingStoreBuffered
                                                    defer:NO];
    
    [self.window center];
    if (useNewUI) {
        self.mainViewController = [[MainViewController alloc] init];
        self.window.contentViewController = self.mainViewController;
        self.window.styleMask |= NSWindowStyleMaskFullSizeContentView;
        self.window.titlebarAppearsTransparent = YES;
        self.window.backgroundColor = [NSColor clearColor];
    } else {
        self.oldMainViewController = [OldMainViewController new];
        self.window.contentViewController = self.oldMainViewController;
        self.window.title = [NSString stringWithFormat:@"%@(%@.%@)", @"应用宝", [YYBAppConfig appVersion], [YYBAppConfig bundleVersion]];
    }
    self.window.delegate = self;
    NSScreen *screen = [NSScreen mainScreen];
    NSRect screenFrame = screen.frame;
    CGFloat windowX = 150;
    CGFloat windowY = 150;
    CGFloat windowWidth = screenFrame.size.width - windowX * 2;
    CGFloat windowHeight = screenFrame.size.height - windowY * 2;
    [self.window setFrame:CGRectMake(windowX, windowY, windowWidth, windowHeight) display:YES animate:NO];
    [self.window makeKeyAndOrderFront:nil];
    
    // 创建应用菜单
    [self setupMenu];
    // 创建状态栏按钮
    [self setupStatusItem];
    // 杀死其他商店进程，保证当前只有一个商店进程在运行中
    [self killOtherThread];
    
    YYBMacLogInfo(kTag, @"主界面创建完成");
}

- (void)requestNotificationPermission {
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    [center requestAuthorizationWithOptions: (UNAuthorizationOptionAlert + UNAuthorizationOptionSound + UNAuthorizationOptionBadge)
        completionHandler:^(BOOL granted, NSError * _Nullable error) {
            if (granted) {
                YYBMacLogInfo(kTag, @"通知权限已授权");
                DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
                    [[NSApplication sharedApplication] registerForRemoteNotifications];
                });
            } else if (error) {
                YYBMacLogInfo(kTag, @"通知权限申请失败: %@", error.localizedDescription);
            }
    }];
}

- (void)startService {
    NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *serverPath = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/%@/helper/YYBService", [[NSBundle mainBundle] bundleIdentifier]]];
    
    if ([YYBAppConfig isDebug] || [YYBAppConfig isFirstLaunchOfCurrentVersion] || ![[NSFileManager defaultManager] fileExistsAtPath:serverPath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:[serverPath stringByDeletingLastPathComponent]
                                  withIntermediateDirectories:YES
                                                   attributes:nil
                                                        error:nil];
        
        NSError *error;
        if ([[NSFileManager defaultManager] fileExistsAtPath:serverPath]) {
            if (![[NSFileManager defaultManager] removeItemAtPath:serverPath error:&error]) {
                YYBMacLogInfo(@"YYBService", @"remove file error：%@", error);
            }
        }
        NSString *oriServerPath = [[[[NSBundle mainBundle] executablePath] stringByDeletingLastPathComponent] stringByAppendingPathComponent:@"YYBService"];
        if (![[NSFileManager defaultManager] copyItemAtPath:oriServerPath toPath:serverPath error:&error]) {
            YYBMacLogInfo(@"YYBService", @"copy Item At Path error：%@", error);
        }

        {
            NSTask *chmodTask = [[NSTask alloc] init];
            [chmodTask setLaunchPath:@"/bin/chmod"];
            [chmodTask setArguments:@[@"+x", serverPath]];
            NSFileHandle *nullHandle = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
            chmodTask.standardError = nullHandle;
            chmodTask.standardOutput = nullHandle;;
            [chmodTask launch];
            [chmodTask waitUntilExit];
        }
    }

    NSString *plistPath = [[NSBundle mainBundle] pathForResource:@"yybService" ofType:@"plist"];
    YYBSocketEngine::instance().startServer(plistPath.UTF8String, serverPath.UTF8String);
    dispatch_async(dispatch_get_main_queue(), ^{
        bool ret = YYBSocketEngine::instance().connect(kProcessAppStore.UTF8String, (int)getpid());
        if (ret) {
            YYBSocketEngine::instance().registerMessageHandler(ipc::messageHandlers);
            fetchEngineInstalledApps();
        } else {
            YYBMacLogError(kTag, @"注册失败");
        }
    });
}

- (void)handleBackgroundToFront {
    YYBMacLogInfo(kTag, @"页面从后台模式到前台模式");
    self.isBackgroundMode = NO;
    [NSApp setActivationPolicy:NSApplicationActivationPolicyRegular];
    // 使用 TransformProcessType 强制系统更新进程类型
    ProcessSerialNumber psn = { 0, kCurrentProcess };
    TransformProcessType(&psn, kProcessTransformToForegroundApplication);
    // 延时激活，确保dock有时间反应过来刷新图标
    dispatch_async(dispatch_get_main_queue(), ^{
        [NSApp activateIgnoringOtherApps:YES];
    });
    
    
}

- (void)loadContent {
    NSArray *arguments = [[NSProcessInfo processInfo] arguments];
    // 判断是否后台启动(仅当ui未创建时允许后台模式启动）
    self.isBackgroundMode = [arguments containsObject: @"--runBackground"] && !self.window;
    YYBMacLogInfo(kTag, @"是否后台启动：%d", self.isBackgroundMode);
    if (self.isBackgroundMode) {
        [NSApp setActivationPolicy:NSApplicationActivationPolicyAccessory];
    } else {
        [self handleBackgroundToFront];
        [self requestNotificationPermission];
        // 创建主视图
        [self setupMainUI];
    }
    // 记录启动路径
    NSString *appPath = [[NSBundle mainBundle] bundlePath];
    YYBMacLogInfo(kTag, @"launchPath:%@", appPath);
    [[YYBMacMMKV sharedInstance] setString:appPath forKey:@"launchAppPath"];
}


- (void)setupStatusItem {
    NSImage *image = [NSImage imageNamed:@"StatusIcon"];
    self.statusItem = [[NSStatusBar systemStatusBar] statusItemWithLength:NSVariableStatusItemLength];
    self.statusItem.button.image = image;
    self.statusItem.button.action = @selector(showWindow:);
    self.statusItem.button.target = self;
}

- (void)showWindow:(id)sender {
    [NSApp activateIgnoringOtherApps:YES];
}

- (void)startSyncTasks {
    if (!self.syncTaskManager) {
        self.syncTaskManager = [[AsyncInitTaskManager alloc] init];
    }
    [self.syncTaskManager runTasks];
}

- (void)registerCallbacks {
    __weak typeof(self) weakSelf = self;
    [[BussinessMessageCenter sharedCenter] registerObserver:self forIdentifier:kBringAppToFront withHandler:^(BussinessMessage * _Nonnull message) {
        if (!weakSelf) {
            return;
        }
        YYBMacLogInfo(kTag, @"receive kBringAppToFront");
        if (weakSelf.isBackgroundMode) {
            [weakSelf orderWindowFront];
            if (message.payload && [message.payload isKindOfClass:NSClassFromString(@"NSBlock")]) {
                void (^block)(void) = (void (^)(void))message.payload;
                block();
            }
        }
    }];
    //展示引擎下载中Loading
    [[BussinessMessageCenter sharedCenter] registerObserver:self forIdentifier:kShowEnginLoadingView withHandler:^(BussinessMessage * _Nonnull message) {
        YYBMacLogInfo(kTag, @"showEnginLoadingView");
        if (weakSelf.hasShowingLoadingWindow == NO) {
            [weakSelf showLoading];
            weakSelf.hasShowingLoadingWindow = YES;
        }
        
    }];
    
    // 监听全屏退出事件
    [[NSNotificationCenter defaultCenter] addObserver:self
        selector:@selector(windowDidExitFullScreen:)
        name:NSWindowDidExitFullScreenNotification
        object:self.window];
}

// 杀死其他应用宝进程（启动某个应用宝后，杀死其他应用宝进程，保证同一时间只有一个进程在运行)
- (void)killOtherThread {
    NSString *currentBundleID = [NSBundle mainBundle].bundleIdentifier;
    if (!currentBundleID) {
        return;
    }
    NSRunningApplication *currentApp = [NSRunningApplication currentApplication];
    YYBMacLogInfo(kTag, @"当前应用进程号:%d", (int)currentApp.processIdentifier);
    NSArray<NSRunningApplication *> *runningApps = [NSRunningApplication runningApplicationsWithBundleIdentifier:currentBundleID];
    for (NSRunningApplication *app in runningApps) {
        if (app.processIdentifier != currentApp.processIdentifier) {
            BOOL res = [app terminate];
            YYBMacLogInfo(kTag, @"检测到正在运行的其他应用宝进程号:%d，杀死该进程.结果:%d", (int)app.processIdentifier, res);
        }
    }
}

- (void)dealloc
{
    [[YYBMacResHub sharedInstance] removeResRefreshListener:self.refreshListener];
    [[BussinessMessageCenter sharedCenter] unregisterObserver:self];
}

@end

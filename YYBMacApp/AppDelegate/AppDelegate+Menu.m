//
//  AppDelegate+Menu.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/7.
//

#import "AppDelegate+Menu.h"
#import "DebugWindowController.h"
#import "UninstallManager.h"
#import "YYBApkPackage.h"
#import "YYBRoutes.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacUpdateManager.h>
#import "YYBDiagnosticLog.h"
#import "YYBAlert.h"
#import "YYBUnInstallViewController.h"
#import "MacroUtils.h"
#import "YYBAppConfig.h"
#import "IdentifierConstants.h"
#import "YYBDefine.h"
#import "MainWindow.h"

static NSString *const kTag = @"AppDelegate+Menu";

@implementation AppDelegate (Menu)

- (void)setupMenu {
    [self setupFirstMenuItem];
    [self setupDebugMenuItem];  // TODO: halehuang 在release下屏蔽debug菜单
}

#pragma mark - 主菜单
- (void)setupFirstMenuItem {
    NSString *appName = YYBAppConfig.appName;
    
    NSMenu *mainMenu = [NSApp mainMenu];
    NSMenuItem *firstMenuItem = [mainMenu itemAtIndex:0];
    NSMenu *firstMenu = firstMenuItem.submenu;

    NSMenuItem *aboutMenuItem = [[NSMenuItem alloc] initWithTitle:[NSString stringWithFormat:@"关于%@", appName] action:@selector(aboutYYB:) keyEquivalent:@""];

    [firstMenu addItem:aboutMenuItem];

    // 添加更新子菜单项
    NSMenuItem *checkUpdateMenuItem = [[NSMenuItem alloc] initWithTitle:@"检查更新" action:@selector(checkUpdate:) keyEquivalent:@","];
    [firstMenu addItem:checkUpdateMenuItem];

    // 添加分隔线
    [firstMenu addItem:[NSMenuItem separatorItem]];
    NSMenuItem *uninstallAll = [[NSMenuItem alloc] initWithTitle:@"卸载全部" action:@selector(uninstallAll) keyEquivalent:@","];
    [firstMenu addItem:uninstallAll];

    // 添加分隔线
    [firstMenu addItem:[NSMenuItem separatorItem]];

    NSMenuItem *quitYYBItem = [[NSMenuItem alloc] initWithTitle:[NSString stringWithFormat:@"退出%@", appName] action:@selector(quitYYB:) keyEquivalent:@"q"];
    [firstMenu addItem:quitYYBItem];

    NSMenuItem *quitYYBEngineItem = [[NSMenuItem alloc] initWithTitle:[NSString stringWithFormat:@"退出%@移动应用引擎", appName] action:@selector(quitYYBEngine:) keyEquivalent:@""];
    [firstMenu addItem:quitYYBEngineItem];
}

- (void)aboutYYB:(id)sender {
    [YYBRoutes routeURLWithModule:yybRouteModuleApp cmd:yybRouteCmdRouteUrl params:@{yybRouteQueryUrl: @"https://betapc.yyb.qq.com/about"}];
}

- (void)checkUpdate:(id)sender {
    YYBMacLogInfo(kTag, @"checkUpdate");
    [[[YYBMacUpdateManager sharedInstance] getMarketUpdater] checkForUpdates];
//    [YYBAlert showConfirmAlertWithStyle:NSAlertStyleInformational title:@"更新新版本" message:@"检查版本更新功能待定" confirmTitle:@"确认" cancelTitle:@"取消" completion:^(BOOL confirmed) {
//
//    }];
}

// 退出应用宝引擎
- (void)quitYYBEngine:(id)sender {
    NSArray<NSRunningApplication *> *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];

    for (NSRunningApplication *app in runningApps) {
        NSString *bundleID = app.bundleIdentifier;
        if ([bundleID isEqualToString:YYBEngineBundleID]) {
            if (![app terminate]) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)),
                               dispatch_get_main_queue(), ^{
                    if (![app isTerminated]) {
                        [app forceTerminate];
                    }
                });
            }
            return;
        }
    }
}


// 退出应用宝
- (void)quitYYB:(id)sender {
    [self quitYYBEngine:sender];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"ApplicationWillTerminate" object:nil];
    [[NSRunLoop mainRunLoop] cancelPerformSelectorsWithTarget:self];
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [NSApp terminate:nil];
    });
}

// 卸载所有
- (void)uninstallAll {
    NSArray *installed = [[YYBApkPackage shared] installedApps];
    [YYBUnInstallViewController showWithApps:installed];
}

#pragma mark - Debug（只在非release下生效）
- (void)setupDebugMenuItem {
    NSMenu *mainMenu = [NSApp mainMenu];
    NSString *debugMenuTitle = @"调试";
    NSMenu *debugMenu = [[NSMenu alloc] initWithTitle:debugMenuTitle];
    NSMenuItem *debugMenuItem = [[NSMenuItem alloc] initWithTitle:debugMenuTitle action:nil keyEquivalent:@""];
    debugMenuItem.submenu = debugMenu;
    [mainMenu addItem:debugMenuItem];
    
    {
        // 调试面板
        NSMenuItem *item = [[NSMenuItem alloc] initWithTitle:@"打开调试面板" action:@selector(showDebugWindow:) keyEquivalent:@"d"];
        [debugMenu addItem:item];
        
    }
    
    {
        // 已安装app
        NSArray *lists = [[YYBApkPackage shared] installedApps];

        if (lists.count > 0) {
            NSMenu *appMenu = [[NSMenu alloc] initWithTitle:@"已安装app"];
            NSMenuItem *appMenuItem = [[NSMenuItem alloc] initWithTitle:@"已安装app" action:nil keyEquivalent:@""];
            appMenuItem.submenu = appMenu;
            [debugMenu addItem:appMenuItem];

            for (InstallApkInfo *info in lists) {
                NSMenuItem *item = [[NSMenuItem alloc] initWithTitle:info.name action:@selector(openApp:) keyEquivalent:@""];
                item.representedObject = info;
                [appMenu addItem:item];
            }
        }
    }
    
    [debugMenu addItem:[NSMenuItem separatorItem]];
    
    {
        // 诊断日志打包
        NSMenuItem *item = [[NSMenuItem alloc] initWithTitle:@"诊断日志打包" action:@selector(zipDiagnosticLog:) keyEquivalent:@""];
        [debugMenu addItem:item];
    }
    
    {
        // 日志上传
        NSMenuItem *item = [[NSMenuItem alloc] initWithTitle:@"日志上传（当天）" action:@selector(uploadDiagnosticLog:) keyEquivalent:@""];
        [debugMenu addItem:item];
    }
    
    // 添加分隔线
    [debugMenu addItem:[NSMenuItem separatorItem]];
    {
        NSMenuItem *item = [[NSMenuItem alloc] initWithTitle:@"打开新UI面板" action:@selector(openNewUI:) keyEquivalent:@"n"];
        [debugMenu addItem:item];
    }
}

- (void)showDebugWindow:(id)sender {
    [[DebugWindowController sharedInstance] showDebugWindow];
}

- (void)openApp:(id)sender {
    InstallApkInfo *info = [sender representedObject];

    [[YYBApkPackage shared] openApp:info.pkgName];
}

- (void)zipDiagnosticLog:(id)sender {
    [YYBDiagnosticLog zipDiagnosticLogWithWindwow:[NSApp mainWindow]
                          useDefaultProgressPanel:YES
                              useDefaultSavePanel:YES
                                    progressBlock:nil
                                       completion:^(BOOL success, NSString * _Nullable zipPath, NSError * _Nullable error) {
        if (success) {
            NSString *msg = [NSString stringWithFormat:@"诊断日志打包成功，文件路径：\n%@", zipPath ?: @""];
            [YYBAlert showAlert:NSAlertStyleInformational title:@"诊断日志保存成功" message:msg];
            YYBMacLogInfo(kTag, @"诊断日志保存成功，zip路径: %@", zipPath);
        } else {
            NSString *errMsg = error ? error.localizedDescription : @"未知错误";
            [YYBAlert showAlert:NSAlertStyleCritical title:@"诊断日志保存失败" message:errMsg];
            YYBMacLogError(kTag, @"诊断日志保存失败: %@", errMsg);
        }
    }];
}

- (void)uploadDiagnosticLog:(id)sender {
    [YYBDiagnosticLog showLogSummaryInputPanelAndUpload:[NSApp mainWindow]
                                             completion:^(BOOL result, NSString * _Nullable errMsg) {
        if (result) {
            [YYBAlert showAlert:@"上传成功" message:nil];
            YYBMacLogInfo(kTag, @"日志上传成功");
        } else {
            [YYBAlert showAlert:@"上传失败" message:errMsg ?: @"未知错误"];
            YYBMacLogError(kTag, @"日志上传失败: %@", errMsg ?: @"未知错误");
        }
    }];
}

- (void)openNewUI:(id)sender {
    if (self.glassUIWindow) {
        [self.glassUIWindow makeKeyAndOrderFront:self];
        return;
    }
    MainWindow *window = [[MainWindow alloc] initWithContentRect:CGRectZero
                                              styleMask:NSWindowStyleMaskTitled | NSWindowStyleMaskClosable | NSWindowStyleMaskMiniaturizable | NSWindowStyleMaskResizable
                                                backing:NSBackingStoreBuffered
                                                  defer:NO];
    
    MainViewController *mainViewController = [[MainViewController alloc] init];
    window.contentViewController = mainViewController;
    window.styleMask |= NSWindowStyleMaskFullSizeContentView;
    window.titlebarAppearsTransparent = YES;
    window.backgroundColor = [NSColor clearColor];
    
    NSScreen *screen = [NSScreen mainScreen];
    NSRect screenFrame = screen.frame;
    CGFloat windowX = 150;
    CGFloat windowY = 150;
    CGFloat windowWidth = screenFrame.size.width - windowX * 2;
    CGFloat windowHeight = screenFrame.size.height - windowY * 2;
    [window setFrame:CGRectMake(windowX, windowY, windowWidth, windowHeight) display:YES animate:NO];
    [window setReleasedWhenClosed:NO];
    [window makeKeyAndOrderFront:nil];
    self.glassUIWindow = window;
}

@end

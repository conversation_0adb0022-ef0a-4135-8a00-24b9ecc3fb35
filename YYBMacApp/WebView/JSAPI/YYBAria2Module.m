//
//  YYBAria2Module.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/22.
//

#import "YYBAria2Module.h"
#import "YYBJSAPIMacros.h"
#import "YYBLibAria2ServiceFacade.h"
#import "MacroUtils.h"
#import "YYBAria2DownloadManager.h"

static YYBJSAPIEventName const JSAPIAria2DownloadEvent = @"Aria2DownloadEvent";
static YYBJSAPIEventName const JSAPIAria2DownloadProgressEvent = @"Aria2DownloadProgressEvent";

@interface JSAria2DownloadEvent : NSObject

@property (nonatomic, assign) YYBLibAria2DownloadEvent event;
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, strong) NSDictionary *info;

@end

@interface JSAria2DownloadProgressEvent : NSObject

@property (nonatomic, copy) NSString *gid;
@property (nonatomic, strong, nullable) NSDictionary *info;

@end

@implementation JSAria2DownloadEvent

@end

@implementation JSAria2DownloadProgressEvent

@end

@interface YYBAria2Module ()

@property (nonatomic, copy) YYBLibAria2RunningStatusBlock runningStatusBlock;
@property (nonatomic, copy) YYBLibAria2DownloadEventBlock downloadEventBlock;
@property (nonatomic, strong) NSMutableDictionary<NSString *, YYBLibAria2SingleProgressBlock> *downloadProgressEventBlocks;

@end

@implementation YYBAria2Module

YYB_EXPORT_MODULE(aria2ForMac)

/**
 * @description 获取Aria2 WebSocket连接状态
 * @param 无
 * @return {String} 连接状态，"1"表示已连接，"2"表示未连接
 */
YYB_EXPORT_METHOD(GetAria2WebSocketConned) {
    BOOL result = [[YYBLibAria2ServiceFacade sharedService] isRunning];
    YYB_SEND_SUCCESS(result ? @"1" : @"2");
    if (!self.runningStatusBlock) {
        WEAKIFY(self);
        self.runningStatusBlock = ^(BOOL isRunning) {
            STRONGIFY(self);
            for (NSString * taskId in self.downloadProgressEventBlocks.allKeys) {
                NSString *eventName = [NSString stringWithFormat:@"%@_%@", JSAPIAria2DownloadProgressEvent, taskId];
                YYB_TRIGGER_EVENT_FAIL_LISTENER(eventName, [NSError errorWithDomain:kJSAPIErrorDomain code:ResponseCodeError userInfo:@{NSLocalizedDescriptionKey: @"Aria2服务已断开"}]);
            }
        };
        [[YYBLibAria2ServiceFacade sharedService] addRunningStatusListener:self.runningStatusBlock];
    }
}

/**
 * @description 调用Aria2的方法，直接透传名称和参数
 * @param {String} method 方法名称
 * @param {Array} param 方法参数
 * @return {Dictionary} 调用结果
 * @error {Dictionary} 可能返回错误
 */
YYB_EXPORT_METHOD(CallMethod) {
    YYB_DECLARE_PARAM(0, NSString, method);
    YYB_DECLARE_PARAM(1, NSArray, param);
    method = [NSString stringWithFormat:@"aria2.%@", method];
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:method
                                                                   params:param
                                                                   source:YYBAria2TaskSourceWeb
                                                               completion:^(NSDictionary * _Nullable result, NSData * _Nullable rawData, NSError * _Nullable error) {
        if (error) {
            YYB_SEND_ERROR(error);
            return;
        }
        YYB_SEND_SUCCESS(result);
    }];
}

/**
 * @description 添加Aria2事件监听器
 * @param {String} eventName 事件名称
 * @param {String} gid 下载任务ID
 * @return 无
 * @event Aria2DownloadEvent 下载事件触发时回调，不支持单taskId查询
 *   @property {Number} event 事件类型
 *   @property {String} gid 下载任务ID
 *   @property {Dictionary} info 下载任务信息
 * @event Aria2DownloadProgressEvent 下载进度变化时回调，支持taskId，当code == -1时，表示连接失败
 *   @property {String} gid 下载任务ID
 *   @property {Dictionary} info 下载任务进度信息
 */
YYB_EXPORT_METHOD(AddListener) {
    YYB_DECLARE_PARAM(0, NSString, eventName);
    if ([eventName isEqualToString:JSAPIAria2DownloadEvent]) {
        YYB_ADD_EVENT_LISTENER(eventName);
        if (!self.downloadEventBlock) {
            __weak typeof(self) weakSelf = self;
            self.downloadEventBlock = ^(YYBLibAria2DownloadEvent event, NSString * _Nonnull taskId, NSDictionary * _Nonnull info) {
                __strong typeof(weakSelf) self = weakSelf;
                JSAria2DownloadEvent *eventObj = [JSAria2DownloadEvent new];
                eventObj.event = event;
                eventObj.gid = taskId;
                eventObj.info = info;
                YYB_TRIGGER_EVENT_LISTENER(JSAPIAria2DownloadEvent, eventObj);
            };
            [[YYBLibAria2ServiceFacade sharedService] addDownloadEventListener:self.downloadEventBlock];
        }
    } else if ([eventName isEqualToString:JSAPIAria2DownloadProgressEvent]) {
        YYB_DECLARE_PARAM(1, NSString, taskId);
        if (taskId.length == 0) {
            return;
        }
        eventName = [NSString stringWithFormat:@"%@_%@", eventName, taskId];
        YYB_ADD_EVENT_LISTENER(eventName);
        if (!self.downloadProgressEventBlocks) {
            self.downloadProgressEventBlocks = [NSMutableDictionary new];
        }
        YYBLibAria2SingleProgressBlock block = self.downloadProgressEventBlocks[taskId];
        if (!block) {
            block = ^(NSString *taskId, NSDictionary *progress) {
                JSAria2DownloadProgressEvent *event = [JSAria2DownloadProgressEvent new];
                event.gid = taskId;
                event.info = progress;
                YYB_TRIGGER_EVENT_LISTENER(eventName, event);
            };
            [self.downloadProgressEventBlocks setObject:block forKey:taskId];
            [[YYBLibAria2ServiceFacade sharedService] addSingleProgressListenerForTaskId:taskId listener:block];
        }
    }
}

/**
 * @description 移除Aria2事件监听器
 * @param {String} eventName 事件名称
 * @param {String} removedCallbackId 要移除的回调ID
 * @return 无
 */
YYB_EXPORT_METHOD(RemoveListener) {
    YYB_DECLARE_PARAM(0, NSString, removedCallbackId);
    YYB_REMOVE_EVENT_LISTENER(removedCallbackId);
}

- (void)dealloc {
    if (self.downloadEventBlock) {
        [[YYBLibAria2ServiceFacade sharedService] removeDownloadEventListener:self.downloadEventBlock];
    }
    if (self.downloadProgressEventBlocks.count != 0) {
        for (NSString * taskId in self.downloadProgressEventBlocks.allKeys) {
            YYBLibAria2SingleProgressBlock block = self.downloadProgressEventBlocks[taskId];
            [[YYBLibAria2ServiceFacade sharedService] removeSingleProgressListenerForTaskId:taskId listener:block];
        }
    }
    if (self.runningStatusBlock) {
        [[YYBLibAria2ServiceFacade sharedService] removeRunningStatusListener:self.runningStatusBlock];
    }
}

@end

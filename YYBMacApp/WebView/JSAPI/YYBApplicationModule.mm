//
//  YYBApplicationModule.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/11.
//

#import "YYBApplicationModule.h"
#import "YYBJSAPIMacros.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBAppConfig.h"
#import "system_info.h"
#import "YYBPseudoProtocol.h"
#import "YYBDiagnosticLog.h"
#import "system_info.h"
#import "YYBAppConfig.h"
#import <YYBMacFusionSDK/YYBMacQimei.h>
#import "YYBApkStateInfo.h"
#import "YYBApkStateManager.h"
#import "YYBLoginManager.h"

static NSString *const kTag = @"YYBApplicationModule";
static YYBJSAPIEventName const JSAPIPseudoEvent = @"pseudoEvent";

typedef NS_ENUM(NSInteger, CpuVendor) {
    CpuVendorUnknown = 0,
    CpuVendorIntel = 1,
    CpuVendorAMD = 2,
    CpuVendorOther = 3,
    CpuVendorApple = 4,
};

@interface JSCPUInfo : NSObject

@property (nonatomic, assign) NSInteger coreNumber;
@property (nonatomic, assign) CpuVendor vendor;

@end

@implementation JSCPUInfo

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"coreNumber": @"core_number",
        @"filePath": @"file_path"
    };
}

@end

@interface JSPseudoProtocolData : NSObject

@property (nonatomic, assign) NSInteger identifier;
@property (nonatomic, copy) NSString *schema;
@property (nonatomic, assign) NSInteger timeStamp;
@end

@implementation JSPseudoProtocolData

- (instancetype)initWithSchema:(NSString *)schema {
    if (self = [super init]) {
        _identifier = [[NSUUID UUID] hash];
        _timeStamp = [[NSDate date] timeIntervalSince1970];
        _schema = schema;
    }
    return self;
}
    

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"timeStamp": @"time_stamp",
        @"identifier": @"id"
    };
}

@end

@interface YYBApplicationModule ()

@property (nonatomic, copy) YYBPseudoProtocolListenerBlock pseudoProtocolListenerBlock;

@end

@implementation YYBApplicationModule

YYB_EXPORT_MODULE(application)

/**
 * @description 获取客户端主版本号
 * @param 无
 * @return {String} 客户端版本号
 */
YYB_EXPORT_METHOD(GetMainVersion) {
    YYB_SEND_SUCCESS([YYBAppConfig appVersion]);
}

/**
 * @description 获取客户端类型
 * @param 无
 * @return {Number} 客户端类型码
 */
YYB_EXPORT_METHOD(GetClientType) {
    YYB_SEND_SUCCESS(@([YYBAppConfig clientType]));
}

/**
 * @description 设置客户端类型
 * @param {Number} type 客户端类型码
 * @return 无
 */
YYB_EXPORT_METHOD(SetClientType) {
    YYB_DECLARE_PARAM(0, NSNumber, type)
    [YYBAppConfig setClientType:[type toClientType]];
}

/**
 * @description 获取CPU信息
 * @param 无
 * @return {JSCPUInfo} CPU信息对象
 *   @property {Number} core_number CPU核心数
 *   @property {Number} vendor CPU厂商类型(0:未知, 1:Intel, 2:AMD, 3:其他, 4:Apple)
 */
YYB_EXPORT_METHOD(GetCpuInfo) {
    SystemInfo& sysInfo = SystemInfo::getInstance();
    SystemInfo::CPUInfo cpuInfo = sysInfo.getCPUInfo();
    JSCPUInfo *info = [[JSCPUInfo alloc] init];
    info.coreNumber = cpuInfo.coreCount;
    info.vendor = strcmp(cpuInfo.cpuType.c_str(), "Unknown") == 0 ? CpuVendorUnknown :
    cpuInfo.isAppleSilicon ? CpuVendorApple :
    CpuVendorIntel;
    YYB_SEND_SUCCESS(info);
}

/**
 * @description 添加伪协议监听器
 * @param 无
 * @return 无
 * @event pseudoEvent 当收到伪协议时触发
 *   @property {String|Array} 伪协议内容或伪协议数组
 */
YYB_EXPORT_METHOD(AddPseudoProtocolListener) {
    YYB_ADD_EVENT_LISTENER(JSAPIPseudoEvent);
    if (!self.pseudoProtocolListenerBlock) {
        __weak typeof(self) weakSelf = self;
        self.pseudoProtocolListenerBlock = ^void(NSArray<NSString *> *urls) {
            __strong typeof(weakSelf) self = weakSelf;
            if (urls.count == 1) {
                YYB_TRIGGER_EVENT_LISTENER(JSAPIPseudoEvent, urls[0]);
            } else if (urls.count > 1) {
                NSMutableArray<JSPseudoProtocolData *> *datas = [NSMutableArray arrayWithCapacity:urls.count];
                for (NSString *url in urls) {
                    JSPseudoProtocolData *data = [[JSPseudoProtocolData alloc] initWithSchema:url];
                    [datas addObject:data];
                }
                YYB_TRIGGER_EVENT_LISTENER(JSAPIPseudoEvent, datas);
            }
        };
        [YYBPseudoProtocol addListener:self.pseudoProtocolListenerBlock];
    }
}

/**
 * @description 生成诊断日志压缩包
 * @param 无
 * @return {String} 生成的诊断日志压缩包路径
 */
YYB_EXPORT_METHOD(GenerateDiagnosisZip) {
    [YYBDiagnosticLog zipDiagnosticLogWithWindwow:[NSApp mainWindow]
                          useDefaultProgressPanel:NO
                              useDefaultSavePanel:NO
                                    progressBlock:nil
                                       completion:^(BOOL success, NSString * _Nullable zipPath, NSError * _Nullable error) {
        YYB_SEND_SUCCESS(zipPath);
    }];
}

/**
 * @description 获取系统启动时间（UTC毫秒）
 * @param 无
 * @return {Number} 系统启动时间（UTC毫秒）
 */
YYB_EXPORT_METHOD(GetBootTimeUtcMsec) {
    SystemInfo& sysInfo = SystemInfo::getInstance();
    int64_t bootTime = sysInfo.getBootTimeUtcMsec();
    YYB_SEND_SUCCESS(@(bootTime));
}

/**
 * @description 获取应用渠道号
 * @param 无
 * @return {String} 应用渠道号
 */
YYB_EXPORT_METHOD(GetChannel) {
    YYB_SEND_SUCCESS([YYBAppConfig channel]);
}

/**
 * @description 获取设备GUID
 * @param 无
 * @return {String} 设备GUID（Qimei36）
 */
YYB_EXPORT_METHOD(GetGuid) {
    YYB_SEND_SUCCESS([[YYBMacQimei sharedInstance] getQimei36]);
}

/**
 * @description 通知应用状态变化
 * @param {YYBApkState} apkState 应用状态对象
 *   @property {String} pkgname 包名（主键）
 *   @property {String} actualPkgname 实际包名
 *   @property {String} percent 进度百分比
 *   @property {String} status 状态（如DownLoadAllStateBeginInstall等）
 *   @property {String} completedLength 已完成长度
 *   @property {String} totalLength 总长度
 *   @property {String} downloadSpeed 下载速度
 *   @property {Number} gameType 游戏类型(0:未知, 1:移动应用, 2:微信游戏, 3:微信小程序, 4:云游戏, 5:PC游戏, 7:PC应用, 16:QQ游戏, 18:AI代理, 19:AI工具)
 *   @property {Boolean} engineProgress 是否引擎进度
 *   @property {String} source 批量安装来源（可选）
 *   @property {String} recommend_id 广告推荐id（可选）
 *   @property {Number} code 异常状态码（可选）
 *   @property {Boolean} installing_market 是否商店安装中
 *   @property {Boolean} first_launch 是否首次启动
 * @return 无
 */
YYB_EXPORT_METHOD(NotifyAppState) {
    YYB_DECLARE_PARAM(0, YYBApkStateInfo, apkState);
    YYBMacLogInfo(kTag, @"notify app state with :%@", apkState);
    [[YYBApkStateManager sharedManager] insertOrUpdateApkState:apkState];
}

/**
 * @description 设置登录参数
 * @param {YYBLoginInfo} loginInfo 登录信息对象
 *   @property {String} openId 用户OpenID
 *   @property {String} accessToken 访问令牌
 *   @property {String} refreshToken 刷新令牌
 *   @property {String} loginType 登录类型（如LoginTypeQC、LoginTypeWX）
 *   @property {String} scope 授权范围
 *   @property {String} nickname 用户昵称
 *   @property {String} headImg 用户头像URL
 *   @property {Number} expireTime 令牌过期时间戳
 *   @property {String} wxappLoginType 微信小程序登录类型
 *   @property {String} wxappOpenId 微信小程序OpenID
 *   @property {String} wxappAccessToken 微信小程序访问令牌
 *   @property {String} wxappScope 微信小程序授权范围
 *   @property {String} wxappRefreshToken 微信小程序刷新令牌
 *   @property {Number} wxappExpireTime 微信小程序令牌过期时间戳
 * @return 无
 */
YYB_EXPORT_METHOD(SetLoginParam) {
    YYB_DECLARE_PARAM(0, YYBLoginInfo, loginInfo);
    [[YYBLoginManager sharedManager] setLoginInfoFromH5:loginInfo];
}

@end

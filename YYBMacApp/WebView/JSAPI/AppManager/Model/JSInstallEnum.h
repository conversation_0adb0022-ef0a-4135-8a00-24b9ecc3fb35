//
//  JSInstallEnum.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 安装事件的状态
typedef NS_ENUM(NSInteger, InstallState) {
    // 安装
    InstallStateInstallStart      = 1001,
    InstallStateInstalling        = 1002,
    InstallStateInstallSuccess    = 1003,
    InstallStateInstallError      = 1004,
    
    // 卸载
    InstallStateUnInstallStart    = 2001,
    InstallStateUnInstalling      = 2002,
    InstallStateUnInstallSuccess  = 2003,
    InstallStateUnInstallError    = 2004,
    InstallStateUninstallCancel   = 2006,
    
    // 运行
    InstallStatePreRunning        = 3001,
    InstallStateRunning           = 3002,
    InstallStateRunningSuccess    = 3003,
    InstallStateRunningError      = 3004,
    InstallStateLoginSuccess      = 3005,
    
    // 差量更新
    InstallStateDiffStart         = 4001,
    InstallStateDiffing           = 4002,
    InstallStateDiffSuccess       = 4003,
    InstallStateDiffError         = 4004,
    
    // 其他
    InstallStateExitApp           = 9001,
    InstallStateCrashApp          = 9002,
};

// 安装事件错误码
typedef NS_ENUM(NSInteger, InstallErrorCode) {
    InstallErrorCodeNoSpace = 130017,               // 磁盘空间不足
    InstallErrorCodeMd5VerifyMD5Fail = 130060,      // MD5检验失败
};

NS_ASSUME_NONNULL_END

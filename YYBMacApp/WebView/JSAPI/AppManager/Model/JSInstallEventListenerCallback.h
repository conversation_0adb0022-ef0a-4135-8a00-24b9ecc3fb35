//
//  JSInstallEventListenerCallback.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/8.
//

#import <Foundation/Foundation.h>
#import "JSInstallEnum.h"
#import "YYBApkPackageModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JSInstallEventListenerCallback : NSObject

@property (nonatomic, copy) NSString *packageName;
@property (nonatomic, assign) InstallState state;
@property (nonatomic, assign) InstallErrorCode code;
@property (nonatomic, copy) NSString *message;
@property (nonatomic, copy) InstallFrom installFrom;    // TODO: halehuang 后续需要补齐
@property (nonatomic, copy, nullable) NSString *name;

@end

NS_ASSUME_NONNULL_END

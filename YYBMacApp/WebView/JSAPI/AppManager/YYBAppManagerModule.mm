//
//  YYBAppManagerModule.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/10.
//

#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBAppManagerModule.h"
#import "YYBJSAPIMacros.h"
#import "YYModel.h"
#import "YYBApkPackage.h"
#import "YYBFile.h"
#import "JSInstallEventListenerCallback.h"
#import "JSGetInstalledListInfo.h"
#import "system_info.h"

static NSString *const kTag = @"YYBAppManagerModule";
static NSInteger const GB = 1024 * 1024 * 1024;

// 安装事件
static YYBJSAPIEventName const JSAPIInstallEvent = @"installEvent";

@implementation YYBAppManagerModule

YYB_EXPORT_MODULE(appManager)

/**
 * @description 安装APK应用
 * @param {InstallApkInfo} info 安装信息对象
 *   @property {String} pkg_name 包名（前端传入的字段名）
 *   @property {String} file_path 文件路径（前端传入的字段名）
 *   @property {String} name 应用名称
 *   @property {String} icon 图标URL（前端传入的字段名）
 *   @property {String} version_name 版本名称（可选，前端传入的字段名）
 *   @property {String} md5 文件MD5值（可选）
 * @return 无
 * @event installEvent 安装状态变化时触发
 *   @property {String} package_name 包名
 *   @property {Number} state 安装状态码
 */
YYB_EXPORT_METHOD(InstallApk) {
    YYB_DECLARE_PARAM(1, InstallApkInfo, info);
    NSString *pkgName = info.pkgName;
    YYBMacLogInfo(kTag, @"开始安装：%@", pkgName);
    JSInstallEventListenerCallback *listenCallback = [JSInstallEventListenerCallback new];
    listenCallback.packageName = pkgName;
    listenCallback.name = info.name;
    // TODO: halehuang 这里start和installing先合并在一起
    listenCallback.state = InstallStateInstallStart;
    [self triggerInstallEvent:listenCallback];
    
    // md5检查
    NSString *md5 = [YYBFile getFileMD5AtPath:info.filePath].lowercaseString;
    if (![md5 isEqualToString:info.md5.lowercaseString]) {
        YYBMacLogError(kTag, @"安装失败：%@", @"md5不一致");
        listenCallback.state = InstallStateInstallError;
        listenCallback.message = @"md5不一致";
        listenCallback.code = InstallErrorCodeMd5VerifyMD5Fail;
        [self triggerInstallEvent:listenCallback];
        return;
    }
    
    // 磁盘空间检查
    SystemInfo& sysInfo = SystemInfo::getInstance();
    SystemInfo::DiskInfo diskInfo = sysInfo.getDiskInfo();
    if (diskInfo.free < 40 * GB) {
        YYBMacLogError(kTag, @"安装失败：%@", @"磁盘空间不足");
        listenCallback.state = InstallStateInstallError;
        listenCallback.message = @"磁盘空间不足";
        listenCallback.code = InstallErrorCodeNoSpace;
        [self triggerInstallEvent:listenCallback];
        return;
    }
    
    // 开始安装
    listenCallback.state = InstallStateInstalling;
    [self triggerInstallEvent:listenCallback];
    [[YYBApkPackage shared] installApp:info
                            completion:^(InstallApkInfo * apkInfo, NSInteger retCode, NSString * _Nullable msg) {
        if (retCode == 0) {
            YYBMacLogInfo(kTag, @"安装成功：%@", msg);
            listenCallback.state = InstallStateInstallSuccess;
            [self triggerInstallEvent:listenCallback];
        } else {
            YYBMacLogError(kTag, @"安装失败：%@", msg);
            listenCallback.state = InstallStateInstallError;
            [self triggerInstallEvent:listenCallback];
        }
    }];
}

/**
 * @description 卸载APK应用
 * @param {String} pkgName 要卸载的应用包名
 * @return 无
 * @event installEvent 卸载状态变化时触发
 */
YYB_EXPORT_METHOD(UnInstall) {
    YYB_DECLARE_PARAM(0, NSString, pkgName);
    // TODO: halehuang 这里start和installing先合并在一起
    JSInstallEventListenerCallback *listenCallback = [JSInstallEventListenerCallback new];
    listenCallback.packageName = pkgName;
    listenCallback.state = InstallStateUnInstallStart;
    [self triggerInstallEvent:listenCallback];
    listenCallback.state = InstallStateUnInstalling;
    [self triggerInstallEvent:listenCallback];
    [[YYBApkPackage shared] uninstallApp:pkgName completion:^(InstallApkInfo* _Nullable info, NSInteger retCode, NSString* _Nullable  msg) {
        if (retCode == 0) {
            YYBMacLogInfo(kTag, @"卸载成功：%@", info.pkgName);
            listenCallback.state = InstallStateUnInstallSuccess;
            [self triggerInstallEvent:listenCallback];
        } else {
            YYBMacLogError(kTag, @"卸载失败：%@", info.pkgName);
            listenCallback.state = InstallStateUnInstallError;
            [self triggerInstallEvent:listenCallback];
        }
    }];
}

/**
 * @description 获取已安装的应用列表
 * @param 无
 * @return {JSGetInstalledListInfo} 已安装应用列表
 *   @property {Array} list 应用列表，每个元素为InstallApkInfo对象
 *     @property {String} pkg_name 包名（前端接收到的字段名）
 *     @property {String} name 应用名称
 *     @property {String} icon 图标URL（前端接收到的字段名）
 *     @property {String} file_path 文件路径（前端接收到的字段名）
 *     @property {String} appPath 应用路径
 *     @property {String} iconPath 图标路径
 *     @property {String} version_name 版本名称（前端接收到的字段名）
 *     @property {String} md5 文件MD5值
 *     @property {InstallApkExtendInfo}  扩展信息
 */
YYB_EXPORT_METHOD(GetInstalledList) {
    NSArray<InstallApkInfo *> *installedApps = [[YYBApkPackage shared] installedApps];
    for (InstallApkInfo *info in installedApps) {
        InstallApkExtendInfo *extendInfo = [InstallApkExtendInfo new];
        extendInfo.installFrom = InstallFromFromStore;
        info.extendInfo = extendInfo;
    }
    JSGetInstalledListInfo *info = [JSGetInstalledListInfo new];
    info.list = installedApps;
    YYB_SEND_SUCCESS(info);
    
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:kYYBAppsChangedNotification
                                                  object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleAppsChanged:)
                                                 name:kYYBAppsChangedNotification
                                               object:nil];
}

/**
 * @description 启动APK应用
 * @param {String} pkgName 要启动的应用包名
 * @return 无
 */
YYB_EXPORT_METHOD(LaunchApk) {
    YYB_DECLARE_PARAM(0, NSString, pkgName);
    YYBMacLogInfo(kTag, @"launch app: %@", pkgName);
    [[YYBApkPackage shared] openApp:pkgName];
}

/**
 * @description 添加安装事件监听器
 * @param 无
 * @return 无
 * @event installEvent 安装状态变化时触发
 *   @property {String} package_name 包名
 *   @property {Number} state 安装状态码，见InstallState
 *   @property {Number} code 错误码，见InstallErrorCode
 *   @property {String} message 错误详情
 *   @property {String} install_rom 安装来源，见InstallFrom
 *   @property {String} name app名
 */
YYB_EXPORT_METHOD(AddInstallEventListener) {
    YYB_ADD_EVENT_LISTENER(JSAPIInstallEvent);
}

/**
 * @description 创建快捷方式
 * @param {String} pkgName 要启动的应用包名
 * @return 无
 */
YYB_EXPORT_METHOD(CreateAppShortcut) {
    YYB_DECLARE_PARAM(0, NSString, pkgName);
    YYBMacLogInfo(kTag, @"Create app shortcut: %@", pkgName);
    [[YYBApkPackage shared] installShotcut:pkgName completion:^(InstallApkInfo * _Nullable apkInfo, NSInteger retCode, NSString * _Nullable errorMessage) {
        YYBMacLogInfo(kTag, @"Create app shortcut: %@, error message: %@", @(retCode), errorMessage);
    }];
}

/**
 * @description 删除快捷方式
 * @param {String} pkgName 要启动的应用包名
 * @return 无
 */
YYB_EXPORT_METHOD(DeleteAppShortcut) {
    YYB_DECLARE_PARAM(0, NSString, pkgName);
    YYBMacLogInfo(kTag, @"Delete app shortcut: %@", pkgName);
    [[YYBApkPackage shared] uninstallShotcut:pkgName completion:^(InstallApkInfo * _Nullable apkInfo, NSInteger retCode, NSString * _Nullable errorMessage) {
        YYBMacLogInfo(kTag, @"Delete app shortcut: %@, error message: %@", @(retCode), errorMessage);
    }];
}

/**
 * @description 检查快捷方式
 * @param {String} pkgName 要启动的应用包名
 * @return 无
 */
YYB_EXPORT_METHOD(CheckAppShortcut) {
    YYB_DECLARE_PARAM(0, NSString, pkgName);
    BOOL exist = [[YYBApkPackage shared] checkAppShotcut:pkgName];
    YYBMacLogInfo(kTag, @"check app shortcut: %@, exist: %@", pkgName, @(exist));
    YYB_SEND_SUCCESS(exist ? @(1) : @(0));
}

- (void)triggerInstallEvent:(JSInstallEventListenerCallback *)listenerCallback {
    YYB_TRIGGER_EVENT_LISTENER(JSAPIInstallEvent, listenerCallback);
}

- (void)handleAppsChanged:(NSNotification *)notification {
    NSArray *addedApps = notification.userInfo[kYYBAddedAppsKey];
    NSArray *removedApps = notification.userInfo[kYYBRemovedAppsKey];
    for (InstallApkInfo *apkInfo in addedApps) {
        JSInstallEventListenerCallback *listenCallback = [JSInstallEventListenerCallback new];
        listenCallback.packageName = apkInfo.pkgName;
        listenCallback.name = apkInfo.name;
        listenCallback.state = InstallStateInstallSuccess;
        [self triggerInstallEvent:listenCallback];
    }
    for (InstallApkInfo *apkInfo in removedApps) {
        JSInstallEventListenerCallback *listenCallback = [JSInstallEventListenerCallback new];
        listenCallback.packageName = apkInfo.pkgName;
        listenCallback.name = apkInfo.name;
        listenCallback.state = InstallStateUnInstallSuccess;
        [self triggerInstallEvent:listenCallback];
    }
}

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
}

@end

//
//  IdentifierConstants.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import <Foundation/Foundation.h>
#import "SocketDataSender.h"

/**
 * 进程间通讯消息定义
 */
 
// 引擎下载结果
FOUNDATION_EXPORT SocketMessageAction const kEngineDownloadResult;

// vms下载结果
FOUNDATION_EXPORT SocketMessageAction const kVMSDownloadResult;

// 打开调试面板
FOUNDATION_EXPORT SocketMessageAction const kOpenDebugView;

// 打开强制删除快捷方式弹窗
FOUNDATION_EXPORT SocketMessageAction const kShowForceKillPkgDialog;

// 如果商店在后台运行，拉起商店前台进程
FOUNDATION_EXPORT SocketMessageAction const kBringAppToFront;

// 拉取到reshub配置
FOUNDATION_EXPORT SocketMessageAction const kPullReshubConfigFinished;

// reshub资源有更新
FOUNDATION_EXPORT SocketMessageAction const kReshubResRefreshed;

// 引擎或vms下载中，展示下载loading
extern NSString* const kShowEnginLoadingView;

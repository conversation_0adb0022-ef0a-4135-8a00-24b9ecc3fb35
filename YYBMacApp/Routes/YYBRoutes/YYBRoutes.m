//
//  YYBRoutes.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/8.
//

#import "YYBRoutes.h"
#import <JLRoutes/JLRoutes.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "AppDelegate.h"

static NSString *const kTag = @"YYBRoutes";
static NSString *const yybRouteScheme = @"yyb";
static NSString *JLRouteCmd(NSString *module, NSString *cmd) {
    return [NSString stringWithFormat:@"%@/%@", module, cmd];
}

@implementation YYBRoutes

+ (JLRoutes *)routes {
    return [JLRoutes routesForScheme:yybRouteScheme];;
}

+ (void)registerRoutes {
    [YYBRoutes routes][JLRouteCmd(yybRouteModuleApp, yybRouteCmdRouteUrl)] = ^BOOL(NSDictionary *parameters) {
        NSString *urlString = parameters[yybRouteQueryUrl];
        BOOL root = [parameters[yybRouteQueryIsRoot] boolValue];
        AppDelegate *delegate = (AppDelegate *)[NSApplication sharedApplication].delegate;
        if (root) {
            [delegate.oldMainViewController replaceWebViewWithUrl:urlString];
        } else {
            [delegate.oldMainViewController loadWebViewWithUrl:urlString];
        }
        return YES;
    };
}

+ (BOOL)routeURL:(NSURL *)url {
    BOOL canOpen = [[YYBRoutes routes] routeURL:url];
    YYBMacLogInfo(kTag, @"open %@ with url: %@", @(canOpen), url.absoluteString);
    return canOpen;
}

+ (void)routeURLWithModule:(YYBModuleString)host cmd:(YYBCmdString)cmd params:(NSDictionary *)params {
    NSURLComponents *components = [[NSURLComponents alloc] init];
    components.scheme = yybRouteScheme;
    components.host = host;
    components.path = [NSString stringWithFormat:@"/%@", cmd];
    NSMutableArray<NSURLQueryItem *> *queryItems = [NSMutableArray array];
    [params enumerateKeysAndObjectsUsingBlock:^(NSString *key, id value, BOOL *stop) {
        NSString *valueString = nil;
        if ([value isKindOfClass:[NSString class]]) {
            valueString = value;
        } else {
            valueString = [value description];
        }
        NSURLQueryItem *item = [NSURLQueryItem queryItemWithName:key value:valueString];
        [queryItems addObject:item];
    }];
    components.queryItems = queryItems;
    NSURL *url = [components URL];
    [[NSWorkspace sharedWorkspace] openURL:url];
}


@end

//
//  YYBRoutes.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/8.
//

#import <Foundation/Foundation.h>
#import "YYBRoutesDefine.h"

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSString *const yybRoutesScheme;

/**
 * 应用内路由
 *
 * 用于实现应用内不同模块间的导航和通信
 */
@interface YYBRoutes : NSObject

// 注册路由
+ (void)registerRoutes;

// 路由一个url
+ (BOOL)routeURL:(NSURL *)url;

// 路由具体的业务场景（不需要自己构造url）
+ (void)routeURLWithModule:(YYBModuleString)host cmd:(YYBCmdString)cmd params:(NSDictionary *)params;

@end

NS_ASSUME_NONNULL_END

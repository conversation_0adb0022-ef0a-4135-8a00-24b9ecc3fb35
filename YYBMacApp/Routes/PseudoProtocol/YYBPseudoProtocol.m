//
//  YYBPseudoProtocol.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/21.
//

#import "YYBPseudoProtocol.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

NSString *const yybPseudoProtocolScheme = @"androws";
static NSString *const kTag = @"YYBPseudoProtocol";
static NSHashTable *_routeListeners = nil;
static NSMutableArray<NSString *> *_pendingURLs = nil;

@implementation YYBPseudoProtocol

+ (void)pendingURLs:(NSArray<NSString *> *)urls {
    YYBMacLogInfo(kTag, @"pending urls: %@", urls);
    if (urls.count == 0) {
        return;
    };
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _pendingURLs = [NSMutableArray array];
    });
    @synchronized (_pendingURLs) {
        [_pendingURLs addObjectsFromArray:urls];
    }
    YYBMacLogInfo(kTag, @"pending urls end: %@", _pendingURLs);
}

+ (void)addListener:(YYBPseudoProtocolListenerBlock)listener {
    YYBMacLogInfo(kTag, @"add listener");
    if (!listener) {
        return;
    }
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _routeListeners = [NSHashTable weakObjectsHashTable];
    });
    BOOL isFirstListener = NO;
    @synchronized (_routeListeners) {
        if (_routeListeners.count == 0) {
            isFirstListener = YES;
        }
        [_routeListeners addObject:[listener copy]];
    }
    if (isFirstListener) {
        NSArray *pending = nil;
        @synchronized (_pendingURLs) {
            pending = [_pendingURLs copy];
            [_pendingURLs removeAllObjects];
        }
        YYBMacLogInfo(kTag, @"send message: %@", _pendingURLs);
        if (pending.count > 0) {
            listener(pending);
        }
    }
}

+ (BOOL)canHandleURL:(NSURL *)url {
    return [url.scheme isEqualToString:yybPseudoProtocolScheme];
}

+ (void)routeURLs:(NSArray<NSURL *> *)urls {
    NSMutableArray<NSString *> *canOpenURLs = [NSMutableArray array];
    for (NSURL *url in urls) {
        if ([self canHandleURL:url]) {
            [canOpenURLs addObject:url.absoluteString];
        }
    }
    YYBMacLogInfo(kTag, @"opened urls: %@", canOpenURLs);
    @synchronized (_routeListeners) {
        if (_routeListeners.count == 0) {
            [self pendingURLs:canOpenURLs];
            return;
        }
        for (id obj in _routeListeners) {
            YYBPseudoProtocolListenerBlock listener = (YYBPseudoProtocolListenerBlock)obj;
            if (listener && canOpenURLs.count > 0) {
                listener(canOpenURLs);
            }
        }
    }
}

@end

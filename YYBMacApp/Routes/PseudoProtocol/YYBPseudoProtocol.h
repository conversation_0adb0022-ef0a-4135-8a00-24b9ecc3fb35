//
//  YYBPseudoProtocol.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSString *const yybPseudoProtocolScheme;

typedef void (^YYBPseudoProtocolListenerBlock)(NSArray<NSString *> *urls);

/**
 * 伪协议
 *
 * 用于转发伪协议到前端，客户端只负责透传，不负责处理
 */
@interface YYBPseudoProtocol : NSObject

+ (BOOL)canHandleURL:(NSURL *)url;
+ (void)routeURLs:(NSArray<NSURL *> *)urls;

+ (void)addListener:(YYBPseudoProtocolListenerBlock)listener;

@end

NS_ASSUME_NONNULL_END

//
//  TimeUtils.m
//  YYBMacApp
//
//  Created by bethahua<PERSON> on 2025/7/10.
//

#import "TimeUtils.h"

NS_ASSUME_NONNULL_BEGIN

@implementation TimeUtils

+ (NSString *)getCurrentTimeFormat {
    NSDate *now = [NSDate date];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyyMMddHHmmss"]; // 设置时间格式
    NSString *nowString = [formatter stringFromDate:now];
    return nowString;
}

@end

NS_ASSUME_NONNULL_END

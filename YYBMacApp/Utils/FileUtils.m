//
//  FileUtils.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/11.
//

#import "FileUtils.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <SSZipArchive/SSZipArchive.h>
#import <CommonCrypto/CommonDigest.h>


static NSString* const kTag = @"FileUtils";

@implementation FileUtils

+ (nullable NSString *)getApplicationSupportSubDir: (NSString*)subDirName {
    if (!subDirName || [subDirName length] == 0) {
        YYBMacLogInfo(kTag, @"subDirName 不能为空");
        return nil;
    }
    NSString *myAppSupportDir = [FileUtils getMarketApplicationSupportDir];
    if (!myAppSupportDir) {
        return nil;
    }
    NSString *dir = [FileUtils getDirPathSafely:[myAppSupportDir stringByAppendingPathComponent:subDirName]];
    YYBMacLogDebug(kTag, @"getApplicationSupportSubDir: %@", dir);
    return dir;
}

+ (nonnull NSString *)getMarketApplicationSupportDir {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES);
    NSString *appSupportPath = [paths firstObject];
    NSString *bundleID = [[NSBundle mainBundle] bundleIdentifier] ?: @"com.tencent.yybmac";
    return [appSupportPath stringByAppendingPathComponent:bundleID];
}

+ (BOOL)unZipFile:(NSString *)sourcePath toDestination:(NSString *)destPath {
    if (![FileUtils isFileExist:sourcePath] || !destPath) {
        return NO;
    }
    BOOL success = [SSZipArchive unzipFileAtPath:sourcePath toDestination:destPath];
    return success;
}

+ (BOOL)moveFile:(NSString *)sourceFilePath to:(NSString *)targetFilePath {
    if (![FileUtils isFileExist:sourceFilePath] || !targetFilePath) {
        YYBMacLogError(kTag, @"moveFile file path not exist.path:%@ target path:%@", sourceFilePath, targetFilePath);
        return NO;
    }
    NSString *targetParentDir = [targetFilePath stringByDeletingLastPathComponent];
    if (![FileUtils isFileExist:targetParentDir]) {
        YYBMacLogInfo(kTag, @"%@不存在，创建目录", targetParentDir);
        NSError *dirError = nil;
        BOOL dirCreated = [[NSFileManager defaultManager] createDirectoryAtPath:targetParentDir
                                 withIntermediateDirectories:YES
                                                  attributes:nil
                                                       error:&dirError];
        if (!dirCreated) {
            YYBMacLogError(kTag, @"目录创建失败。目录路径：%@. 错误：%@", targetParentDir, dirError);
            return NO;
        }
    }
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    BOOL success = [fileManager moveItemAtPath:sourceFilePath toPath:targetFilePath error:&error];
    if (success) {
        YYBMacLogInfo(kTag, @"moveFile success.source:%@ target:%@", sourceFilePath, targetFilePath);
    } else {
        YYBMacLogError(kTag, @"moveFile failed. source:%@ target:%@ error:%@", sourceFilePath, targetFilePath, error);
    }
    return success;
}

+ (NSString *)md5ForBigFile:(NSString *)filePath {
    NSFileHandle *handle = [NSFileHandle fileHandleForReadingAtPath:filePath];
    if (!handle) return nil;
    
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    CC_MD5_CTX md5;
    CC_MD5_Init(&md5);
    
    while (true) {
        @autoreleasepool {
            NSData *fileData = [handle readDataOfLength:1024 * 8];
            if (fileData.length == 0) break;
            CC_MD5_Update(&md5, [fileData bytes], (CC_LONG)fileData.length);
        }
    }
    [handle closeFile];
    
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5_Final(digest, &md5);
    
    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", digest[i]];
    }
    return [md5String copy];
#pragma clang diagnostic pop
    
}

+ (BOOL)isFileExist:(NSString *)filePath {
    return [[NSFileManager defaultManager] fileExistsAtPath:filePath];
}


+ (NSString*)getDirPathSafely: (NSString*)dirPath {
    NSFileManager *fm = [NSFileManager defaultManager];
        BOOL isDir = NO;
        BOOL exists = [fm fileExistsAtPath:dirPath isDirectory:&isDir];
        if (!exists) {
            NSError *error = nil;
            BOOL created = [fm createDirectoryAtPath:dirPath
                         withIntermediateDirectories:YES
                                          attributes:nil
                                               error:&error];
            if (!created) {
                YYBMacLogError(kTag, @"创建子目录失败: %@ 路径:%@", error, dirPath);
                if (error && error.code == NSFileWriteFileExistsError) {
                    return dirPath;
                }
                return nil;
            }
        }
    return dirPath;
}

+ (BOOL)deleteFile:(NSString *)filePath {
    if (![FileUtils isFileExist:filePath]) {
        YYBMacLogInfo(kTag, @"文件不存在，无需删除。文件路径：%@", filePath);
        return YES;
    }
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    BOOL success = [fileManager removeItemAtPath:filePath error:&error];
    if (success) {
        YYBMacLogInfo(kTag, @"文件删除成功。文件路径：%@", filePath);
    } else {
        YYBMacLogError(kTag, @"文件删除失败。文件路径：%@ 失败原因:%@", filePath, error);
    }
    return success;
}

+ (nonnull NSString *)getEngineApplicationSupportDir {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES);
    NSString *appSupportPath = [paths firstObject];
    NSString *myAppSupportDir = [appSupportPath stringByAppendingPathComponent:@"com.tencent.yybmac.engine"];
    return myAppSupportDir;
}

+ (BOOL)copyFile:(NSString *)sourceFilePath to:(NSString *)targetFilePath needForce:(BOOL)force error:(NSError **)error {
    if (!sourceFilePath || !targetFilePath || ![FileUtils isFileExist:sourceFilePath]) {
        YYBMacLogInfo(kTag, @"传入路径为空或目标路径为空或原路径不存在.原路径:%@", sourceFilePath);
        return NO;
    }
    NSFileManager *fileManager = [NSFileManager defaultManager];

    // 1. 如果目标目录已存在，先删除
    if ([fileManager fileExistsAtPath:targetFilePath]) {
        BOOL removeSuccess = [fileManager removeItemAtPath:targetFilePath error:error];
        if (!removeSuccess) {
            YYBMacLogError(kTag, @"删除目标目录失败: %@ 源路径:%@ 目标路径:%@", *error, sourceFilePath, targetFilePath);
            return NO;
        }
    }
    
    // 2.检查父目录是否存在，如果不存在，则创建父目录
    NSString *targetParentDir = [targetFilePath stringByDeletingLastPathComponent];
    if (![FileUtils isFileExist:targetParentDir]) {
        YYBMacLogInfo(kTag, @"%@不存在，创建目录", targetParentDir);
        NSError *dirError = nil;
        BOOL dirCreated = [fileManager createDirectoryAtPath:targetParentDir
                                 withIntermediateDirectories:YES
                                                  attributes:nil
                                                       error:&dirError];
        if (!dirCreated) {
            YYBMacLogError(kTag, @"目录创建失败。目录路径：%@. 错误：%@", targetParentDir, dirError);
            return NO;
        }
    }

    // 3. 拷贝目录
    BOOL copySuccess = [fileManager copyItemAtPath:sourceFilePath toPath:targetFilePath error:error];
    if (!copySuccess) {
        YYBMacLogError(kTag, @"拷贝目录失败: %@ 源路径:%@ 目标路径:%@", *error, sourceFilePath, targetFilePath);
        return NO;
    }
    return YES;
}

+ (NSNumber *)getFreeSpace {
    NSError *error = nil;
    NSDictionary *attrs = [[NSFileManager defaultManager] attributesOfFileSystemForPath:@"/" error:&error];
    if (attrs) {
        NSNumber *freeSpace = [attrs objectForKey:NSFileSystemFreeSize];
        return freeSpace;
    }
    YYBMacLogWarn(kTag, @"getFreeSpace failed. error:%@", error);
    return nil;    
}

+ (NSMutableDictionary *)createDirectoryJsonObject:(NSString *)path {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDirectory = NO;
    
    // 检查路径是否存在并且判断它是否是一个目录
    if (![fileManager fileExistsAtPath:path isDirectory:&isDirectory]) {
        YYBMacLogInfo(kTag, @"getDictoryStructureJsonStr.path '%@' is not dir.", path);
        return nil;
    }
    NSError *error = nil;
    if (!isDirectory) {
        NSDictionary<NSFileAttributeKey, id> *attributes = [fileManager attributesOfItemAtPath:path error:&error];
        if (!attributes) {
            YYBMacLogInfo(kTag, @"getDictoryStructureJsonStr.attributes==null");
            return nil;
        }
        NSDate *creationDate = attributes[NSFileCreationDate];
        NSISO8601DateFormatter *formatter = [[NSISO8601DateFormatter alloc] init];
        NSString *dateString = [formatter stringFromDate:creationDate];
        
        return [NSMutableDictionary dictionaryWithDictionary:@{
            @"size": attributes[NSFileSize],
            @"createTime": dateString ?: @""
        }];
    }
    
    
    NSArray<NSString *> *contents = [fileManager contentsOfDirectoryAtPath:path error:&error];
    if (contents == nil) {
        // 如果无法读取目录内容，返回nil
        YYBMacLogInfo(kTag, @"contents == nil");
        return nil;
    }
    NSMutableDictionary *directoryDict = [NSMutableDictionary dictionary];
    
    for (NSString *itemName in contents) {
        // 忽略隐藏文件，例如 .DS_Store
        if ([itemName hasPrefix:@"."]) {
            continue;
        }
        NSString *fullPath = [path stringByAppendingPathComponent:itemName];
        NSMutableDictionary * subItemObject = [self createDirectoryJsonObject: fullPath];
        if (subItemObject) {
            directoryDict[itemName] = subItemObject;
        } else {
            return nil;
        }
    }
    return [directoryDict copy];
}

+ (NSString *)generateDirectoryJsonStringFromPath:(NSString *)directoryPath {
    NSDictionary *directoryJsonAsDict = [self createDirectoryJsonObject:directoryPath];
    if (!directoryJsonAsDict) {
        YYBMacLogInfo(kTag, @"directoryJsonAsDict == nil");
        return nil;
    }
    
    NSError* error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:directoryJsonAsDict
                                                       options:NSJSONWritingPrettyPrinted
                                                         error:&error];
    if (!jsonData) {
        YYBMacLogInfo(kTag, @"jsonData == nil");
        return nil;
    }
    
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

@end


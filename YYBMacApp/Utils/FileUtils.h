//
//  FileUtils.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FileUtils : NSObject


/**
 获取Application Support 下的子目录路径。
 如果不存在，则自动创建
 目录位置: /Users/<USER>/Library/Application Support/com.tencent.yybmac/subDirName
 */
+ (nullable NSString*) getApplicationSupportSubDir: (NSString*) subDirName;

// 获取商店Application Support 目录路径:/Users/<USER>/Library/Application Support/com.tencent.yybmac
+ (nonnull NSString*)getMarketApplicationSupportDir;

// 获取引擎Application Support 目录路径:/Users/<USER>/Library/Application Support/com.tencent.yybmac.engine
+ (NSString*)getEngineApplicationSupportDir;

// 文件解压
+ (BOOL)unZipFile:(nullable NSString *)sourcePath toDestination:(nullable NSString *)destPath;

// 判断文件/目录是否存在
+ (BOOL)isFileExist:(nullable NSString*)filePath;


// 采用分块读取计算大文件md5值（不要在主线程使用）
+ (NSString *)md5ForBigFile:(NSString *)filePath;

// 删除文件
+ (BOOL)deleteFile:(nullable NSString*)filePath;

// 文件移动（也可用于文件重命名)。目录不存在时会自动创建目录
+ (BOOL)moveFile:(nullable NSString*)sourceFilePath to:(nullable NSString*)targetFilePath;

/// 文件拷贝
/// @param sourceFilePath 文件源路径
/// @param targetFilePath 文件拷贝目标路径
/// @param force 是否强制覆盖
/// @param error 异常
/// @return 拷贝是否成功
+ (BOOL)copyFile:(NSString*)sourceFilePath to:(NSString*)targetFilePath needForce:(BOOL)force error:(NSError **)error;

/// 获取根目录磁盘可用空间
/// @return 可用空间。如果无法获取，则返回nil
+ (nullable NSNumber*)getFreeSpace;


/// 读取某个目录下的所有文件结构，生成json字符串。可用于目录（仅适用于只读目录）完整性校验
/// @return 目录json。目录结构如下：
/// {
///     "dirName": {
///        "fileName": {
///         "size": "",
///         "createTime": ""
///         }
///         ...
///      }
///      ...
///}
+ (nullable NSMutableDictionary*)createDirectoryJsonObject:(nullable NSString*)path;

/**
 * 读取某个目录下的所有文件结构，生成JSON字符串。
 * @param directoryPath 要读取的目录的完整路径。
 * @return 描述目录结构的JSON字符串，如果出错则返回nil。
 */
+ (NSString *)generateDirectoryJsonStringFromPath:(NSString *)directoryPath;

@end

NS_ASSUME_NONNULL_END

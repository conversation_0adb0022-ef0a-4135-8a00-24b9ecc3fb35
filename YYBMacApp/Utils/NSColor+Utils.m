//
//  NSColor+Utils.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/24.
//

#import "NSColor+Utils.h"

@implementation NSColor (Utils)

+ (NSColor *)colorWithHexString:(NSString *)hexString {
    // 去除前导的#
    if ([hexString hasPrefix:@"#"]) {
        hexString = [hexString substringFromIndex:1];
    }

    // 只接受6位或8位
    NSUInteger length = hexString.length;

    if (length != 6 && length != 8) {
        return nil;
    }

    unsigned int r = 0, g = 0, b = 0, a = 0xFF;
    NSScanner *scanner = [NSScanner scannerWithString:hexString];
    unsigned int hexValue = 0;

    if (![scanner scanHexInt:&hexValue]) {
        return nil;
    }

    if (length == 8) {
        // RRGGBBAA
        r = (hexValue & 0xFF000000) >> 24;
        g = (hexValue & 0x00FF0000) >> 16;
        b = (hexValue & 0x0000FF00) >> 8;
        a = (hexValue & 0x000000FF);
    } else if (length == 6) {
        // RRGGBB
        r = (hexValue & 0xFF0000) >> 16;
        g = (hexValue & 0x00FF00) >> 8;
        b = (hexValue & 0x0000FF);
        a = 0xFF;
    }

    return [NSColor colorWithRed:r / 255.0
                           green:g / 255.0
                            blue:b / 255.0
                           alpha:a / 255.0];
}

@end

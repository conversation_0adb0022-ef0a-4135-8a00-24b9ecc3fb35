//
//  UninstallManager.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/17.
//

#import <AppKit/AppKit.h>
#import <ScriptingBridge/ScriptingBridge.h>
#import <UserNotifications/UserNotifications.h>
#import "UninstallManager.h"
#import "YYBMacFusionSDK/YYBMacLog.h"

static BOOL _isUninstall = NO;

@implementation UninstallManager
+ (void)setup {
    NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *targetPath = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/%@/helper/YYBUninstaller", [[NSBundle mainBundle] bundleIdentifier]]];

    NSString *oriPath = [[[[NSBundle mainBundle] executablePath] stringByDeletingLastPathComponent] stringByAppendingPathComponent:@"YYBUninstaller"];


    [[NSFileManager defaultManager] createDirectoryAtPath:[targetPath stringByDeletingLastPathComponent]
                              withIntermediateDirectories:YES
                                               attributes:nil
                                                    error:nil];

    NSError *error;

    if ([[NSFileManager defaultManager] fileExistsAtPath:targetPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:targetPath error:&error];
    }

    [[NSFileManager defaultManager] copyItemAtPath:oriPath toPath:targetPath error:&error];

    [self removeQuarantineAttribute:targetPath];

    NSTask *chmodTask = [[NSTask alloc] init];
    [chmodTask setLaunchPath:@"/bin/chmod"];
    [chmodTask setArguments:@[@"+x", targetPath]];
    [chmodTask launch];
    [chmodTask waitUntilExit];
}

+ (void)removeQuarantineAttribute:(NSString *)appPath {
    NSTask *task = [[NSTask alloc] init];

    task.launchPath = @"/usr/bin/xattr";
    task.arguments = @[@"-d", @"com.apple.quarantine", appPath];
    NSPipe *pipe = [NSPipe pipe];
    task.standardError = pipe;
    task.standardOutput = [NSFileHandle fileHandleForWritingAtPath:@"/dev/null"];
    [task launch];
    [task waitUntilExit];
    int status = [task terminationStatus];
    NSData *data = [[pipe fileHandleForReading] readDataToEndOfFile];
    NSString *output = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    if (status != 0) {
        YYBMacLogError(@"Uninstall", @"Failed to remove quarantine:%@", output ?: @"Unknown error");
    }
}

+ (void)performUninstall {
    _isUninstall = YES;
    NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *scriptPath = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/%@/helper/YYBUninstaller", [[NSBundle mainBundle] bundleIdentifier]]];
    NSTask *chmodTask = [[NSTask alloc] init];

    chmodTask.launchPath = @"/bin/chmod";
    chmodTask.arguments = @[@"+x", scriptPath];
    [chmodTask launch];
    [chmodTask waitUntilExit];

    NSTask *uninstallTask = [[NSTask alloc] init];
    uninstallTask.launchPath = scriptPath;
    [uninstallTask launch];

    [NSApp terminate:nil];
}

+ (BOOL)isUninstall {
    return _isUninstall;
}

@end

//
//  YYBUnInstallViewController.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/24.
//

#import "SDWebImage.h"
#import "UninstallManager.h"
#import "YYBRoutes.h"
#import "YYBUnInstallViewController.h"
#import "YYBAppConfig.h"
#import "YYBWKWebView.h"
#import "InstallApkInfo.h"

@interface YYBUnInstallViewController ()
@property (nonatomic, strong) NSArray<InstallApkInfo *> *apps;
@end

@implementation YYBUnInstallViewController

+ (void)showWithApps:(NSArray *_Nullable)apps {
    YYBUnInstallViewController *alertController = [[YYBUnInstallViewController alloc] initWithApps:apps];
    NSWindow *panel = [NSWindow windowWithContentViewController:alertController];

    panel.styleMask = NSWindowStyleMaskTitled | NSWindowStyleMaskClosable;
    panel.backingType = NSBackingStoreBuffered;

    panel.titlebarAppearsTransparent = YES;
    panel.titleVisibility = NSWindowTitleHidden;
    [panel standardWindowButton:NSWindowMiniaturizeButton].hidden = YES;
    [panel standardWindowButton:NSWindowZoomButton].hidden = YES;

    NSWindow *parentWindow = [NSApplication sharedApplication].windows.firstObject;
    [parentWindow beginSheet:panel
           completionHandler:^(NSModalResponse code) {
        // 关闭回调
    }];
}

- (instancetype)initWithApps:(NSArray *)apps {
    self = [super init];

    if (self) {
        self.apps = apps;
    }

    return self;
}

- (void)loadView {
    NSString *appName = [YYBAppConfig appName];
    NSStackView *stackView = [[NSStackView alloc] init];

    stackView.orientation = NSUserInterfaceLayoutOrientationVertical;
    stackView.alignment = NSLayoutAttributeLeft;
    stackView.edgeInsets = NSEdgeInsetsMake(20, 24, 24, 24);
    stackView.spacing = 24;
    self.view = stackView;

    // 添加宽度约束
    [stackView.widthAnchor constraintEqualToConstant:480].active = YES;

    NSTextField *titleLabel = [self createLabel:[NSString stringWithFormat:@"确定完全卸载%@吗？", appName]
                                       fontSize:24
                                         weight:NSFontWeightMedium
                                       maxWidth:440];
    [stackView addArrangedSubview:titleLabel];
    [stackView setCustomSpacing:12 afterView:titleLabel];

    NSString *msg = @"此操作将会为您完全卸载应用宝市场，若您有任何问题和建议，请点击左下角问题反馈进行处理。";

    if (self.apps.count > 0) {
        msg =  [NSString stringWithFormat:@"移动应用运行能力由腾讯移动引擎支持，卸载%@后，已安装的%ld款软件将一并卸载。", appName, self.apps.count];
    }

    NSTextField *descriptionLabel = [self createLabel:msg
                                             fontSize:16
                                               weight:NSFontWeightRegular
                                             maxWidth:432];
    [stackView addArrangedSubview:descriptionLabel];

    NSView *appsView = [self setupAppsView];

    if (appsView) {
        [stackView addArrangedSubview:appsView];
    }

    NSStackView *buttonsStackView = [[NSStackView alloc] init];
    buttonsStackView.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    buttonsStackView.distribution = NSStackViewDistributionFill;
    buttonsStackView.spacing = 12;
    [stackView addArrangedSubview:buttonsStackView];

    // 问题反馈按钮
    NSString *feedbackTitle = @"问题反馈";
    NSColor *feedbackColor = [NSColor colorNamed:@"Uninstall/blue"];
    NSButton *feedbackButton = [NSButton buttonWithTitle:@"" target:self action:@selector(feedbackAction)];
    [feedbackButton setAttributedTitle:({
        NSDictionary *attributes = @{
                NSForegroundColorAttributeName: feedbackColor,
                NSFontAttributeName: [NSFont systemFontOfSize:14
                                                       weight:NSFontWeightRegular]
        };
        NSAttributedString *attrTitle = [[NSAttributedString alloc] initWithString:feedbackTitle
                                                                        attributes:attributes];
        attrTitle;
    })];
    [feedbackButton setBordered:NO];
    [feedbackButton setBezelStyle:NSBezelStyleRecessed];
    feedbackButton.image = [NSImage imageWithSystemSymbolName:@"questionmark.circle" accessibilityDescription:feedbackTitle];
    feedbackButton.contentTintColor = feedbackColor;
    feedbackButton.imagePosition = NSImageLeading;
    [buttonsStackView addArrangedSubview:feedbackButton];

    NSView *spacingView = [[NSView alloc] init];
    [spacingView setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [buttonsStackView addArrangedSubview:spacingView];



    // 取消按钮
    NSButton *cancelButton = [NSButton buttonWithTitle:@"" target:self action:@selector(cancelAction)];
    cancelButton.keyEquivalent = @"\e"; // ESC键
    [cancelButton.widthAnchor constraintEqualToConstant:104].active = YES;
    [cancelButton.heightAnchor constraintEqualToConstant:40].active = YES;
    [cancelButton setBordered:NO];
    cancelButton.wantsLayer = YES;
    cancelButton.layer.cornerRadius = 20;
    cancelButton.layer.backgroundColor = [NSColor colorNamed:@"black05"].CGColor;
    [cancelButton setAttributedTitle:({
        NSDictionary *attributes = @{
                NSForegroundColorAttributeName: [NSColor colorNamed:@"black85"],
                NSFontAttributeName: [NSFont systemFontOfSize:16
                                                       weight:NSFontWeightRegular]
        };
        NSAttributedString *attrTitle = [[NSAttributedString alloc] initWithString:@"取消"
                                                                        attributes:attributes];
        attrTitle;
    })];
    [buttonsStackView addArrangedSubview:cancelButton];

    // 卸载按钮
    NSButton *uninstallButton = [NSButton buttonWithTitle:@"" target:self action:@selector(uninstallAction)];
    [uninstallButton.widthAnchor constraintEqualToConstant:104].active = YES;
    [uninstallButton.heightAnchor constraintEqualToConstant:40].active = YES;
    [uninstallButton setBordered:NO];
    uninstallButton.wantsLayer = YES;
    uninstallButton.layer.cornerRadius = 20;
    uninstallButton.layer.backgroundColor = [NSColor colorNamed:@"Uninstall/blue"].CGColor;

    [uninstallButton setAttributedTitle:({
        NSDictionary *attributes = @{
                NSForegroundColorAttributeName: [NSColor colorNamed:@"white"],
                NSFontAttributeName: [NSFont systemFontOfSize:16
                                                       weight:NSFontWeightMedium]
        };
        NSAttributedString *attrTitle = [[NSAttributedString alloc] initWithString:@"确定卸载"
                                                                        attributes:attributes];
        attrTitle;
    })];

    [buttonsStackView addArrangedSubview:uninstallButton];

    // 添加按钮堆栈视图的约束
    buttonsStackView.translatesAutoresizingMaskIntoConstraints = NO;
    [buttonsStackView.leadingAnchor constraintEqualToAnchor:stackView.leadingAnchor constant:24].active = YES;
    [buttonsStackView.trailingAnchor constraintEqualToAnchor:stackView.trailingAnchor constant:-24].active = YES;
}

- (NSTextField *)createLabel:(NSString *)text fontSize:(CGFloat)size weight:(NSFontWeight)weight
                    maxWidth:(CGFloat)width {
    NSTextField *label = [NSTextField labelWithString:text];

    label.font = [NSFont systemFontOfSize:size weight:weight];
    label.lineBreakMode = NSLineBreakByWordWrapping;
    label.maximumNumberOfLines = 0;
    label.preferredMaxLayoutWidth = width;
    label.alignment = NSTextAlignmentLeft;
    return label;
}

- (void)feedbackAction {
    NSLog(@"Feedback button clicked");
    [self.view.window close];
    [YYBRoutes routeURLWithModule:yybRouteModuleApp cmd:yybRouteCmdRouteUrl params:@{yybRouteQueryUrl: @"https://betapc.yyb.qq.com/about"}];
}

- (void)cancelAction {
    [self.view.window close];
}

- (void)uninstallAction {
    NSLog(@"Uninstall confirmed");
    [YYBWKWebView clearAllCookies];
    [self.view.window close];
    [UninstallManager performUninstall];
}

- (NSView *)setupAppsView {
    if (self.apps.count == 0) {
        return nil;
    } else {
        NSView *containerView = [[NSView alloc] init];

        NSStackView *stackView = [[NSStackView alloc] init];
        stackView.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        stackView.distribution = NSStackViewDistributionFillEqually;
        stackView.alignment = NSLayoutAttributeCenterY;
        stackView.spacing = 16;

        NSInteger count = MIN(self.apps.count, 4); // 最多显示4个

        if (self.apps.count > 4) {
            count = 3;
        }

        for (int i = 0; i < count; i++) {
            InstallApkInfo *info = self.apps[i];
            [stackView addArrangedSubview:[self appViewInfoView:info]];
        }

        if (self.apps.count > 4) {
            [stackView addArrangedSubview:[self moreView:self.apps.count - 3]];
        }

        [containerView addSubview:stackView];
        stackView.translatesAutoresizingMaskIntoConstraints = NO;

        // 居中约束
        [NSLayoutConstraint activateConstraints:@[
             [stackView.centerXAnchor constraintEqualToAnchor:containerView.centerXAnchor],
             [stackView.topAnchor constraintEqualToAnchor:containerView.topAnchor],
             [stackView.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor]
        ]];

        if (count == 1) {
            stackView.distribution = NSStackViewDistributionFill;
        }

        return containerView;
    }
}

- (NSView *)appViewInfoView:(InstallApkInfo *)info {
    NSView *contentView = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 90.5, 104)];

    contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView.widthAnchor constraintEqualToConstant:90.5].active = YES;
    [contentView.heightAnchor constraintEqualToConstant:104].active = YES;

    // 名称
    NSTextField *nameLabel = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 90.5, 24)];
    nameLabel.stringValue = info.name;
    nameLabel.alignment = NSTextAlignmentCenter;
    nameLabel.font = [NSFont systemFontOfSize:16];
    nameLabel.textColor = [NSColor colorNamed:@"black70"];
    nameLabel.bezeled = NO;
    nameLabel.drawsBackground = NO;
    nameLabel.editable = NO;
    nameLabel.cell.lineBreakMode = NSLineBreakByTruncatingTail;
    [contentView addSubview:nameLabel];

    // 图标
    NSImageView *iconView = [[NSImageView alloc] initWithFrame:CGRectMake(10, 24 + 8, 72, 72)];
    iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    iconView.wantsLayer = YES;
    iconView.layer.cornerRadius = 16;
    iconView.layer.masksToBounds = YES;
    iconView.layer.borderWidth = 1.0;
    iconView.layer.borderColor = [NSColor colorNamed:@"black12"].CGColor;
    [iconView sd_setImageWithURL:[NSURL URLWithString:info.iconUrl]];
    [contentView addSubview:iconView];
    return contentView;
}

- (NSView *)moreView:(NSInteger)count {
    NSView *contentView = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 90.5, 104)];

    contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView.widthAnchor constraintEqualToConstant:90.5].active = YES;
    [contentView.heightAnchor constraintEqualToConstant:104].active = YES;

    // 名称
    NSTextField *nameLabel = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 90.5, 24)];
    nameLabel.stringValue = [NSString stringWithFormat:@"其余%ld款", count];
    nameLabel.alignment = NSTextAlignmentCenter;
    nameLabel.font = [NSFont systemFontOfSize:16];
    nameLabel.textColor = [NSColor colorNamed:@"black70"];
    nameLabel.bezeled = NO;
    nameLabel.drawsBackground = NO;
    nameLabel.editable = NO;
    [contentView addSubview:nameLabel];

    // 图标
    NSView *iconView = [[NSView alloc] initWithFrame:CGRectMake(10, 24 + 8, 72, 72)];
    iconView.wantsLayer = YES;
    iconView.layer.cornerRadius = 16;
    iconView.layer.masksToBounds = YES;
    iconView.layer.borderWidth = 1.0;
    iconView.layer.borderColor = [NSColor colorNamed:@"black12"].CGColor;
    iconView.layer.backgroundColor = [NSColor colorNamed:@"Uninstall/moreBG"].CGColor;
    [contentView addSubview:iconView];

    NSTextField *countLabel = [[NSTextField alloc] initWithFrame:CGRectZero];
    countLabel.alignment = NSTextAlignmentCenter;
    countLabel.font = [NSFont systemFontOfSize:24 weight:NSFontWeightBold];
    countLabel.textColor = [NSColor colorNamed:@"Uninstall/blue"];
    countLabel.bezeled = NO;
    countLabel.drawsBackground = NO;
    countLabel.editable = NO;
    countLabel.stringValue = [NSString stringWithFormat:@"+%ld", (long)count];
    [countLabel sizeToFit];
    [iconView addSubview:countLabel];
    countLabel.translatesAutoresizingMaskIntoConstraints = NO;

    [NSLayoutConstraint activateConstraints:@[
         [countLabel.centerXAnchor constraintEqualToAnchor:iconView.centerXAnchor],
         [countLabel.centerYAnchor constraintEqualToAnchor:iconView.centerYAnchor],
    ]];
    return contentView;
}

@end

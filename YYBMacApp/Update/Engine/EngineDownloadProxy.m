//
//  EngineDownloadProxy.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/10.
//

#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <AppKit/AppKit.h>
#include <sys/xattr.h>
#import "EngineDownloadProxy.h"
#import "TimeUtils.h"
#import "FileUtils.h"
#import "EngineDownloadHelper.h"
#import "YYBAria2DownloadManager.h"
#import "YYBAria2Task.h"
#import "BussinessMessageCenter.h"
#import "IdentifierConstants.h"
#import "EngineInfoCenter.h"

@interface EngineDownloadProxy()

@property (nonatomic, strong) YYBAria2Task* task;

@property (nonatomic, copy) DownloadResult resultCallback;
@end


NSString* const kEngineZipMD5Key = @"yybMacEngineZipMD5";
NSString* const kEngineDownloadStatusKey = @"yybMacEngineDownloadStatus";
NSString* const kTempEngineDownloadUrl = @"https://cms.myapp.com/wupload/xy/yybtech/saEdIak3.zip";
NSString* const kTempMd5 = @"000b84325afe31d4dfb72852182226f1";
static NSString * const kTAG = @"EngineDownloadProxy";

// todo: 代码待整理
@implementation EngineDownloadProxy



- (BOOL)enableUseEngine {
    // 当且仅当engine路径存在且状态记录未UNZIPPED时无需启动下载
    NSString* status = [[YYBMacMMKV sharedInstance] getStringForKey:kEngineDownloadStatusKey defaultValue:@"NOT_DOWNLOAD"];
    
    // todo: 临时代码
    NSString* tmpUrl = [[YYBMacMMKV sharedInstance] getStringForKey:@"tmpEngineDownloadUrl"];
    return [[EngineInfoCenter shareInstance] isEngineExist] && [[EngineDownloadHelper sharedInstance] stringToEngineDownloadStatus:status] == UNZIPPED && [tmpUrl isEqualToString:kTempEngineDownloadUrl];
}


// 清除文件夹下所有前缀为${kEngineSaveName}的文件
- (BOOL)clean {
    NSString *prefix = [kEngineSaveName copy];
    NSString *directory = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    NSArray *files = [fileManager contentsOfDirectoryAtPath:directory error:&error];
    if (error) {
        YYBMacLogError(kTAG, @"目录读取失败。目录名：%@", directory);
        return NO;
    } else {
        BOOL isSuccess = YES;
        for (NSString *fileName in files) {
            if ([fileName hasPrefix:prefix]) {
                NSString *filePath = [directory stringByAppendingPathComponent:fileName];
                NSError *removeError = nil;
                BOOL success = [fileManager removeItemAtPath:filePath error:&removeError];
                if (success) {
                    YYBMacLogInfo(kTAG, @"已删除文件：%@", filePath);
                } else {
                    YYBMacLogError(kTAG, @"删除文件失败。文件路径：%@ 错误：%@", filePath, removeError);
                    isSuccess = NO;
                }
            }
        }
        return isSuccess;
    }
}

- (BOOL)unzipEnginePackage:(NSString*)path {
    // 解压前先清除之前已解压过的文件
    BOOL success = [FileUtils deleteFile:[[EngineInfoCenter shareInstance]getEnginePath]];
    if (!success) {
        YYBMacLogError(kTAG, @"unzipEnginePackage 清除engine包失败");
        return NO;
    }
    return [FileUtils unZipFile:path toDestination:[FileUtils getApplicationSupportSubDir:kEngineDownloadDirName]];
}

- (BOOL)handleUnzip:(NSString*)filePath {
    // 如果目录下存在app文件，会导致解压失败。解压前先把app
    
    BOOL success = [self unzipEnginePackage:filePath];
    if (success) {
        YYBMacLogInfo(kTAG, @"downloadEngine 解压成功。");
        NSString* appPath = [[EngineInfoCenter shareInstance]getEnginePath];
        removexattr([appPath fileSystemRepresentation], "com.apple.quarantine", 0);
        // 解压完成后删除zip包
        [[YYBAria2DownloadManager sharedManager] cancelTask:self.task deleteFile:YES];
        
        [[YYBMacMMKV sharedInstance] setString:[[EngineDownloadHelper sharedInstance] engineDownloadStatusToString:UNZIPPED] forKey:kEngineDownloadStatusKey];
        
        // todo: 临时代码
        [[YYBMacMMKV sharedInstance] setString:kTempEngineDownloadUrl forKey:@"tmpEngineDownloadUrl"];
        NSString* tmpEngineUrl = [[YYBMacMMKV sharedInstance] getStringForKey:@"tmpEngineDownloadUrl"];
        YYBMacLogInfo(kTAG, @"tmpEngineUrl:%@", tmpEngineUrl);
        [[EngineDownloadHelper sharedInstance] notifyDownloadStatus: UNZIPPED andProgress:@1];
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kEngineDownloadResult payload:@1]];
        self.resultCallback(YES);
        return YES;
    } else {
        YYBMacLogInfo(kTAG, @"downloadEngine 解压失败。");
        [[EngineDownloadHelper sharedInstance] notifyDownloadStatus: NOT_DOWNLOADED andProgress:@-1];
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kEngineDownloadResult payload:@0]];
        self.resultCallback(NO);
        return NO;
    }
}


- (void)downloadEngine:(DownloadResult) callback {
    YYBMacLogInfo(kTAG, @"启动引擎下载");
    self.resultCallback = callback;
    // todo: 临时代码，启动下载时先删除已有的app文件
    id showDialogBlock = ^{
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kShowForceKillPkgDialog payload:@""]];
    };
    [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kBringAppToFront payload:showDialogBlock]];
    YYBMacLogInfo(kTAG, @"send kBringAppToFront. engine");
    BOOL needExistEngine = NO;
    for (NSRunningApplication *app in [[NSWorkspace sharedWorkspace] runningApplications]) {
        if ([app.bundleIdentifier hasPrefix:@"com.tencent.yybmac.app"]) {
            [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kShowForceKillPkgDialog payload:@""]];
        }
        if ([app.bundleIdentifier isEqualToString:@"com.tencent.yybmac.engine"]) {
            needExistEngine = YES;
            BOOL res = [app terminate];
            YYBMacLogInfo(kTAG, @"普通退出引擎结果: %d", res);
            if (!res) {
                res = [app forceTerminate];
            }
            if (res) {
                [FileUtils deleteFile:[[EngineDownloadHelper sharedInstance] getEngineFilePath:@"app"]];
            }
            YYBMacLogInfo(kTAG, @"退出引擎结果: %d", res);
        }
    }
    // 如果不需要退出引擎，还需要检查是否有快捷方式启动中，如果有，杀死快捷方式
    if (!needExistEngine) {
        for (NSRunningApplication *app in [[NSWorkspace sharedWorkspace] runningApplications]) {
            if ([app.bundleIdentifier hasPrefix:@"com.tencent.yybmac.app"]) {
                BOOL res = [app terminate];
                if (!res) {
                    res = [app forceTerminate];
                }
                YYBMacLogInfo(kTAG, @"退出快捷方式结果: %d", res);
            }
        }
    }
    
    [self startDownload];
}


- (void)startDownload {
    YYBMacLogInfo(kTAG, @"startDownload");
    NSString* downloadDir = [FileUtils getApplicationSupportSubDir:kEngineDownloadDirName];
    // todo 传递md5校验
    YYBAria2Task *task = [[YYBAria2DownloadManager sharedManager] createDownloadTaskWithURL:kTempEngineDownloadUrl
                                                                                    destDir:downloadDir
                                                                                   fileName:nil
                                                                                        md5:nil
                                                                                 visibility:YYBAria2TaskVisibilitySilent        //静默任务(不占用感知任务额度)
                                                                                   priority:YYBAria2TaskPriorityHigh            // 高优
    ];
    if (!task) {
        YYBMacLogInfo(kTAG, @"engine download task create failed.");
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kEngineDownloadResult payload:@0]];
        [[EngineDownloadHelper sharedInstance] notifyDownloadStatus: ENGINE_DOWNLOAD_FAILED andProgress:@-1];
        self.resultCallback(NO);
        return;
    }
    YYBMacLogInfo(kTAG, @"real startDownload");
    self.task = task;
    if (task.status == YYBAria2TaskStatusComplete) {
        if ([FileUtils isFileExist:task.finalFilePath]) {
            [self handleUnzip:task.finalFilePath];
            return;
        }        
    }
    if (task.status == YYBAria2TaskStatusErrorFatal || task.status == YYBAria2TaskStatusRemoved) {
        YYBMacLogInfo(kTAG, @"vms下载失败，清除任务");
        [[YYBAria2DownloadManager sharedManager] cancelTask:self.task deleteFile:YES];
    }
    __weak typeof(self) weakSelf = self;
    task.progressBlock = ^(YYBAria2Task * _Nonnull task, double progress) {
        if (!weakSelf) {
            return;
        }
        [[EngineDownloadHelper sharedInstance] notifyDownloadStatus: NOT_DOWNLOADED andProgress:[NSNumber numberWithDouble:progress]];
        
    };
    task.statusBlock = ^(YYBAria2Task * _Nonnull task, YYBAria2TaskStatus status, NSError * _Nullable error) {
        if (!weakSelf) {
            return;
        }
        YYBMacLogInfo(kTAG, @"status:%d", (int)status);
        if (status == YYBAria2TaskStatusErrorFatal) {
            self.resultCallback(NO);
        }
        if (status == YYBAria2TaskStatusComplete) {
            [[EngineDownloadHelper sharedInstance] notifyDownloadStatus: DOWNLOADED andProgress:@1];
            if (!weakSelf) {
                return;
            }
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                [[YYBMacMMKV sharedInstance] setString:[[EngineDownloadHelper sharedInstance] engineDownloadStatusToString:DOWNLOADED] forKey:kEngineDownloadStatusKey];
                
                YYBMacLogInfo(kTAG, @"是否是主进程:%d", [NSThread isMainThread]);
                [weakSelf handleUnzip: task.finalFilePath];
            });
        } else if (status == YYBAria2TaskStatusErrorFatal) {
            [[EngineDownloadHelper sharedInstance] notifyDownloadStatus: ENGINE_DOWNLOAD_FAILED andProgress:@-1];
            [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kEngineDownloadResult payload:@0]];
            self.resultCallback(NO);
        }
    };
    [[YYBAria2DownloadManager sharedManager] startTask:task];
    [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc]initWithIdentifier:kShowEnginLoadingView payload:@""]];
}


@end

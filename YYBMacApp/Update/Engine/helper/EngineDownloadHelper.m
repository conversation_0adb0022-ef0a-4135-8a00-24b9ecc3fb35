//
//  EngineDownloadHelper.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/12.
//

#import "EngineDownloadHelper.h"
#import "FileUtils.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import "MacroUtils.h"

NS_ASSUME_NONNULL_BEGIN

NSString* const kEngineDownloadDirName = @"YYBEngineDownload";
NSString* const kEngineSaveName = @"yyb_mac";

static NSString* const kTag = @"EngineDownloadHelper";

@interface EngineDownloadHelper()

@property (nonatomic, strong) NSMutableArray* callbacks;

@property (nonatomic, strong) NSMutableArray* vmsCallback;

@end

@implementation EngineDownloadHelper


+ (nonnull instancetype)sharedInstance {
    static EngineDownloadHelper *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[EngineDownloadHelper alloc] init];
    });
    return instance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.callbacks = [NSMutableArray array];
        self.vmsCallback = [NSMutableArray array];
    }
    return self;
}

- (NSString *)engineDownloadStatusToString:(EngineDownloadStatus)status {
    switch (status) {
        case NOT_DOWNLOADED:
            return @"NOT_DOWNLOAD";
        case DOWNLOADED:
            return @"DOENLOADED";
        case UNZIPPED:
            return @"UNZIPPED";
        case ENGINE_DOWNLOAD_FAILED:
            return @"ENGINE_DOWNLOAD_FAILED";
        default:
            return @"NOT_DOWNLOAD";
    }
}

- (EngineDownloadStatus)stringToEngineDownloadStatus:(NSString *)str {
    if ([str isEqualToString:@"NOT_DOWNLOAD"]) {
        return NOT_DOWNLOADED;
    } else if ([str isEqualToString:@"DOENLOADED"]) {
        return DOWNLOADED;
    } else if ([str isEqualToString:@"UNZIPPED"]) {
        return UNZIPPED;
    } else if ([str isEqualToString:@"ENGINE_DOWNLOAD_FAILED"]) {
        return ENGINE_DOWNLOAD_FAILED;
    }
    else {
        return NOT_DOWNLOADED;
    }
}

- (NSString*)getEngineFilePath:(NSString*)suffix {
    NSString* engineSaveDir = [FileUtils getApplicationSupportSubDir:kEngineDownloadDirName];
    if (!engineSaveDir) {
        return nil;
    }
    NSString* enginePath = [NSString stringWithFormat:@"%@.%@", [engineSaveDir stringByAppendingPathComponent:kEngineSaveName], suffix];
    return enginePath;
}



- (void)registerDownloadCallback:(EngineDownlodCallback)callback {
    @synchronized (self.callbacks) {
        [self.callbacks addObject:callback];
    }
    
}

- (void)unregisterDownloadCallback:(EngineDownlodCallback)callback {
    @synchronized (self.callbacks) {
        [self.callbacks removeObject:callback];
    }
    
}

- (void)clearCallbacks {
    [self.callbacks removeAllObjects];
    [self.vmsCallback removeAllObjects];
}

- (void)notifyDownloadStatus:(EngineDownloadStatus)status andProgress:(NSNumber *)number {
    DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
        NSMutableArray* tmpCallbacks = [self.callbacks copy];
        for (EngineDownlodCallback callback in tmpCallbacks) {
            if (callback) {
                callback(number, status);
            }
            
        }
    })
    
}

- (void)notifyVmsDownloadStatus:(NSString*)status andProgress:(NSNumber*)number resHubStatus:(ResHubLocalResStatus)resHubStatus {
    DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
        NSMutableArray* tmpCallbacks = [self.vmsCallback copy];
        for (VmsDownlodCallback callback in tmpCallbacks) {
            if (callback) {
                callback(number, status, resHubStatus);
            }
            
        }
    })
    
}

- (void)unregisterVmsDownloadCallback:(nonnull VmsDownlodCallback)callback {
    @synchronized (self.vmsCallback) {
        [self.vmsCallback removeObject:callback];
    }
    
}

- (void)registerVmsDownloadCallback:(nonnull VmsDownlodCallback)callback {
    @synchronized (self.vmsCallback) {
        [self.vmsCallback addObject:callback];
    }
    
}

@end

NS_ASSUME_NONNULL_END

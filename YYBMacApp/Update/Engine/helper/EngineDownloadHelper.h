//
//  EngineDownloadHelper.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/12.
//

#import <Foundation/Foundation.h>
#import <YYBMacFusionSDK/ResHubCommonDefines.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, EngineDownloadStatus) {
    // 未下载完成(未启动下载，下载中都属于未下载完成）
    NOT_DOWNLOADED,
    // 已下载（zip包存在时，说明已下载）
    DOWNLOADED,
    // 已解压（此时模拟器引擎为可用状态）
    UNZIPPED,
    // 下载失败
    ENGINE_DOWNLOAD_FAILED
};

typedef void(^EngineDownlodCallback)(NSNumber* progress, EngineDownloadStatus status);
typedef void(^VmsDownlodCallback)(NSNumber* progress, NSString* downloadStatus, ResHubLocalResStatus reshubStatus);

@interface EngineDownloadHelper : NSObject

+ (instancetype)sharedInstance;

extern NSString* const kEngineDownloadDirName;
extern NSString* const kEngineSaveName;

- (nonnull NSString*)engineDownloadStatusToString: (EngineDownloadStatus)status;

- (EngineDownloadStatus)stringToEngineDownloadStatus: (NSString*)str;

- (NSString*)getEngineFilePath:(NSString*)suffix;


- (void)registerDownloadCallback:(EngineDownlodCallback)callback;

- (void)unregisterDownloadCallback:(EngineDownlodCallback)callback;

- (void)registerVmsDownloadCallback:(VmsDownlodCallback)callback;

- (void)unregisterVmsDownloadCallback:(VmsDownlodCallback)callback;

- (void)clearCallbacks;

- (void)notifyDownloadStatus:(EngineDownloadStatus)status andProgress:(NSNumber*)number;

- (void)notifyVmsDownloadStatus:(NSString*)status andProgress:(NSNumber*)number resHubStatus:(ResHubLocalResStatus)resHubStatus;

@end

NS_ASSUME_NONNULL_END

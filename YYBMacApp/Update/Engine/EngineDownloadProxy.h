//
//  EngineDownloadProxy.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^DownloadResult)(BOOL success);


@interface EngineDownloadProxy : NSObject
extern NSString* const kEngineZipMD5Key;
extern NSString* const kEngineDownloadStatusKey;
// 临时mock数据，待删除
extern NSString* const kTempMd5;
extern NSString* const kTempEngineDownloadUrl;


- (void)downloadEngine:(DownloadResult) callback;

- (BOOL)enableUseEngine;



@end

NS_ASSUME_NONNULL_END

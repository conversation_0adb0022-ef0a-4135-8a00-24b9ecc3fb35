//
//  DebugWindowController.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/7.
//

#import "DebugWindowController.h"
#import "DebugPanelController.h"
#import "MacroUtils.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

@interface DebugWindowController ()

@property (strong, nonatomic) DebugPanelController *debugViewController;
@property (assign, nonatomic) BOOL isAlertShowing;

@end

@implementation DebugWindowController

+ (instancetype)sharedInstance {
    static DebugWindowController *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[DebugWindowController alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupWindow];
        [self setupViewController];
        self.isAlertShowing = NO;
    }
    return self;
}

- (void)setupWindow {
    NSWindow *window = [[NSWindow alloc] initWithContentRect:NSMakeRect(100, 100, 600, 400)
                                                   styleMask:NSWindowStyleMaskTitled | NSWindowStyleMaskClosable | NSWindowStyleMaskMiniaturizable | NSWindowStyleMaskResizable
                                                     backing:NSBackingStoreBuffered
                                                       defer:NO];
    
    window.title = @"调试面板";
    window.minSize = NSMakeSize(400, 300);
    self.window = window;
}

- (void)setupViewController {
    self.debugViewController = [[DebugPanelController alloc] init];
    self.window.contentViewController = self.debugViewController;
}

- (void)showDebugWindow {
    if (!self.window.isVisible) {
        YYBMacLogInfo(@"DebugWindowController", @"showDebugWindow");
        [self.window makeKeyAndOrderFront:nil];
    } else {
        [self.window orderFront:nil];
    }
}

- (void)showAlert {
    if (self.isAlertShowing) {
        YYBMacLogInfo(@"DebugWindowController",@"弹窗已在展示，不再重复弹出。");
        return;
    }
    
    self.isAlertShowing = YES;
    
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = @"当前强制更新中。检测到本地有快捷方式正在运行中，已强制杀除。";
    alert.informativeText = @"弹窗提醒。";
    [alert addButtonWithTitle:@"确定"];
    
    [alert beginSheetModalForWindow:self.window completionHandler:^(NSModalResponse returnCode) {
        self.isAlertShowing = NO;
        YYBMacLogInfo(@"DebugWindowController", @"弹窗已关闭。");
    }];
    
}

@end 

//
//  DebugAria2TestVeiwController.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/6/23.
//

#import "DebugAria2TestVeiwController.h"
#import "YYBLibAria2ServiceFacade.h"
#import "YYBApkPackage.h"
#import "YYBAria2DownloadManager.h"
#import "MacroUtils.h"
#import "YYBAria2DownloadManager.h"
#import "ApkInfoGenerater.h"

@interface DebugAria2TestVeiwController () <NSTableViewDataSource, NSTableViewDelegate, NSSplitViewDelegate>

@property (nonatomic, strong) NSSplitView *mainSplitView;
@property (nonatomic, strong) NSView *topPanel;
@property (nonatomic, strong) NSView *bottomPanel;

// 顶部
@property (nonatomic, strong) NSTextField *topTitleLabel;
@property (nonatomic, strong) NSButton *startButton;
@property (nonatomic, strong) NSButton *stopButton;
@property (nonatomic, strong) NSTextField *serviceStatusLabel;
@property (nonatomic, strong) NSButton *refreshButton;
@property (nonatomic, strong) NSButton *purgeAllButton; // 清除全部任务按钮
@property (nonatomic, strong) NSTextField *recentCountField;
@property (nonatomic, strong) NSTextField *recentCountLabel;
@property (nonatomic, strong) NSTableView *tableView;
@property (nonatomic, strong) NSScrollView *tableScrollView;
@property (nonatomic, strong) NSTextField *addUrlField;
@property (nonatomic, strong) NSButton *addTaskButton;
@property (nonatomic, strong) NSButton *openFolderButton;
@property (nonatomic, strong) NSButton *openApkFolderButton;
@property (nonatomic, strong) NSButton *pasteClipboardButton;

// 大文件URL输入框及按钮
@property (nonatomic, strong) NSTextField *addBigUrlField;
@property (nonatomic, strong) NSButton *addBigTaskButton;
@property (nonatomic, strong) NSButton *pasteBigClipboardButton;

// 下部
@property (nonatomic, strong) NSTextField *bottomTitleLabel;
@property (nonatomic, strong) NSView *debugPanel;
@property (nonatomic, strong) NSView *jsonrpcButtonPanel;
@property (nonatomic, strong) NSSplitView *responseLogSplitView;
@property (nonatomic, strong) NSView *responsePanel;
@property (nonatomic, strong) NSTextField *responseTitleLabel;
@property (nonatomic, strong) NSButton *rpcResponseCopyButton;
@property (nonatomic, strong) NSScrollView *jsonrpcResponseScroll;
@property (nonatomic, strong) NSTextView *jsonrpcResponseView;
@property (nonatomic, strong) NSView *logPanel;
@property (nonatomic, strong) NSTextField *logTitleLabel;
@property (nonatomic, strong) NSScrollView *logScroll;
@property (nonatomic, strong) NSTextView *logTextView;
@property (nonatomic, strong) NSButton *clearLogButton;

// 自定义JSON-RPC输入
@property (nonatomic, strong) NSTextField *customJsonrpcField;
@property (nonatomic, strong) NSButton *sendCustomJsonrpcButton;

// 数据

@property (nonatomic, strong) NSMutableArray<NSMutableDictionary *> *tasks; // 展示用任务列表
@property (nonatomic, copy) NSString *downloadDir;
@property (nonatomic, strong) NSArray *rpcTests;

// 配置
@property (nonatomic, assign) NSInteger recentTaskCount;

// 状态
@property (nonatomic, assign) BOOL serviceRunning;

// 定时器
@property (nonatomic, strong) NSTimer *autoRefreshTimer;

@property (nonatomic, strong) NSMutableArray<NSMutableDictionary *> *localCompletedTasks;

@end

@implementation DebugAria2TestVeiwController

@synthesize navigationController;

#pragma mark - 生命周期

- (void)dealloc {
    [self stopAutoRefreshTimer];
    self.autoRefreshTimer = nil;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)loadView {
    self.view = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 1280, 980)];
}

- (void)viewDidAppear {
    [super viewDidAppear];
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [self.mainSplitView setPosition:420 ofDividerAtIndex:0];
        CGFloat totalWidth = self.responseLogSplitView.bounds.size.width;
        CGFloat leftWidth = totalWidth * 0.66; // 左2/3，右1/3
        [self.responseLogSplitView setPosition:leftWidth ofDividerAtIndex:0];
    });
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.tasks = [NSMutableArray array];
    self.localCompletedTasks = [NSMutableArray array];
    self.downloadDir = [[YYBLibAria2ServiceFacade sharedService] currentDownloadDir];
    self.rpcTests = @[
        @{@"title":@"查询活动任务", @"method":@"aria2.tellActive", @"params":@[]},
        @{@"title":@"查询等待任务", @"method":@"aria2.tellWaiting", @"params":@[@0, @100]},
        @{@"title":@"查询已完成任务", @"method":@"aria2.tellStopped", @"params":@[@0, @100]},
        @{@"title":@"全局状态", @"method":@"aria2.getGlobalStat", @"params":@[]},
        @{@"title":@"暂停全部", @"method":@"aria2.pauseAll", @"params":@[]},
        @{@"title":@"恢复全部", @"method":@"aria2.unpauseAll", @"params":@[]}
    ];
    self.recentTaskCount = 20;
    [self setupMainSplitView];
    [self updateServiceStatus:NO];
    [self log:@"[App] 测试页面已加载"];
    [self checkServiceAndAutoRefresh];
}


#pragma mark - 主分栏布局（上下分栏）

- (void)setupMainSplitView {
    // 1. 主分栏
    self.mainSplitView = [[NSSplitView alloc] initWithFrame:self.view.bounds];
    self.mainSplitView.dividerStyle = NSSplitViewDividerStyleThin;
    self.mainSplitView.vertical = NO;
    self.mainSplitView.delegate = self;
    self.mainSplitView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.mainSplitView];

    // 2. 顶部面板
    self.topPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.topPanel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.mainSplitView addSubview:self.topPanel];

    // 3. 底部面板
    self.bottomPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.bottomPanel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.mainSplitView addSubview:self.bottomPanel];

    // 4. 主分栏约束
    [NSLayoutConstraint activateConstraints:@[
        [self.mainSplitView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.mainSplitView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.mainSplitView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.mainSplitView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    // 5. 顶部/底部面板高度约束（关键：必须给定最小高度，避免高度为0导致冲突）
    NSLayoutConstraint *minHeight = [self.topPanel.heightAnchor constraintGreaterThanOrEqualToConstant:320];
    minHeight.priority = NSLayoutPriorityDefaultLow; // 250
    minHeight.active = YES;
    [self.bottomPanel.heightAnchor constraintGreaterThanOrEqualToConstant:320].active = YES;

    // 6. 子面板内容
    [self setupTopPanel];
    [self setupBottomPanel];

    // 7. 分栏初始位置
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [self.mainSplitView setPosition:420 ofDividerAtIndex:0];
        [self.responseLogSplitView setPosition:700 ofDividerAtIndex:0];
    });
}

#pragma mark - 顶部面板（任务管理区）

- (void)setupTopPanel {
    self.topTitleLabel = [self sectionTitleLabel:@"任务管理区"];
    NSStackView *serviceStack = [self createServiceControlStack];
    NSStackView *recentCountStack = [self createRecentCountStack];
    [self setupTableView];
    [self.tableScrollView.heightAnchor constraintEqualToConstant:220].active = YES;
    NSStackView *addTaskStack = [self createAddTaskStack];
    NSStackView *addBigTaskStack = [self createAddBigTaskStack];

    // 主stackView
    NSStackView *topStack = [NSStackView stackViewWithViews:@[
        self.topTitleLabel, serviceStack, recentCountStack, self.tableScrollView, addTaskStack, addBigTaskStack
    ]];
    topStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    topStack.spacing = 12;
    topStack.edgeInsets = NSEdgeInsetsMake(16, 16, 16, 16);
    topStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.topPanel addSubview:topStack];

    // 关键：顶部主stackView约束
    [NSLayoutConstraint activateConstraints:@[
        [topStack.leadingAnchor constraintEqualToAnchor:self.topPanel.leadingAnchor],
        [topStack.trailingAnchor constraintEqualToAnchor:self.topPanel.trailingAnchor],
        [topStack.topAnchor constraintEqualToAnchor:self.topPanel.topAnchor],
        [topStack.bottomAnchor constraintLessThanOrEqualToAnchor:self.topPanel.bottomAnchor]
    ]];
}

- (NSTextField *)sectionTitleLabel:(NSString *)title {
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    label.stringValue = title;
    label.font = [NSFont boldSystemFontOfSize:18];
    label.textColor = [NSColor labelColor];
    label.editable = NO;
    label.bezeled = NO;
    label.drawsBackground = NO;
    label.selectable = NO;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (NSStackView *)createServiceControlStack {
    self.startButton = [self buttonWithTitle:@"启动服务" action:@selector(startService:)];
    self.stopButton = [self buttonWithTitle:@"停止服务" action:@selector(stopService:)];
    self.serviceStatusLabel = [self labelWithString:@"服务未启动" fontSize:14 color:[NSColor systemRedColor]];
    self.refreshButton = [self buttonWithTitle:@"全部刷新" action:@selector(onRefreshAllTasks:)];
    self.purgeAllButton = [self buttonWithTitle:@"清除全部任务" action:@selector(onPurgeAllTasks:)];
    NSStackView *serviceStack = [NSStackView stackViewWithViews:@[
        self.startButton, self.stopButton, self.serviceStatusLabel, self.refreshButton, self.purgeAllButton
    ]];
    serviceStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    serviceStack.spacing = 16;
    serviceStack.alignment = NSLayoutAttributeCenterY;
    serviceStack.translatesAutoresizingMaskIntoConstraints = NO;
    return serviceStack;
}

- (NSStackView *)createRecentCountStack {
    self.recentCountLabel = [self labelWithString:@"最近任务数" fontSize:13 color:[NSColor secondaryLabelColor]];
    self.recentCountField = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 40, 24)];
    self.recentCountField.stringValue = [NSString stringWithFormat:@"%ld", (long)self.recentTaskCount];
    self.recentCountField.font = [NSFont systemFontOfSize:13];
    self.recentCountField.alignment = NSTextAlignmentCenter;
    self.recentCountField.bezelStyle = NSTextFieldRoundedBezel;
    self.recentCountField.editable = YES;
    self.recentCountField.target = self;
    self.recentCountField.action = @selector(onRecentCountChanged:);
    self.recentCountField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.recentCountField.widthAnchor constraintEqualToConstant:50].active = YES;
    NSStackView *recentCountStack = [NSStackView stackViewWithViews:@[self.recentCountLabel, self.recentCountField]];
    recentCountStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    recentCountStack.spacing = 8;
    recentCountStack.alignment = NSLayoutAttributeCenterY;
    recentCountStack.translatesAutoresizingMaskIntoConstraints = NO;
    return recentCountStack;
}

- (void)setupTableView {
    self.tableView = [[NSTableView alloc] initWithFrame:NSZeroRect];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.usesAlternatingRowBackgroundColors = YES;
    [self addTableColumnWithIdentifier:@"gid" title:@"GID" width:160];
    [self addTableColumnWithIdentifier:@"url" title:@"URL" width:260];
    [self addTableColumnWithIdentifier:@"status" title:@"下载状态" width:65];
    [self addTableColumnWithIdentifier:@"progress" title:@"下载进度" width:65];
    [self addTableColumnWithIdentifier:@"fileStatus" title:@"文件状态" width:85];
    [self addTableColumnWithIdentifier:@"fileName" title:@"文件名" width:85];
    [self addTableColumnWithIdentifier:@"fileSize" title:@"文件大小" width:100];
    [self addTableColumnWithIdentifier:@"action" title:@"操作" width:240];

    self.tableScrollView = [[NSScrollView alloc] initWithFrame:NSZeroRect];
    self.tableScrollView.documentView = self.tableView;
    self.tableScrollView.hasVerticalScroller = YES;
    self.tableScrollView.autohidesScrollers = YES;
    self.tableScrollView.borderType = NSBezelBorder;
    self.tableScrollView.translatesAutoresizingMaskIntoConstraints = NO;

    // tableView填满scrollView
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.tableView.leadingAnchor constraintEqualToAnchor:self.tableScrollView.contentView.leadingAnchor].active = YES;
    [self.tableView.trailingAnchor constraintEqualToAnchor:self.tableScrollView.contentView.trailingAnchor].active = YES;
    [self.tableView.topAnchor constraintEqualToAnchor:self.tableScrollView.contentView.topAnchor].active = YES;
    [self.tableView.bottomAnchor constraintEqualToAnchor:self.tableScrollView.contentView.bottomAnchor].active = YES;
    
    // 桥接双击事件
    self.tableView.target = self;
    self.tableView.doubleAction = @selector(onTableViewDoubleClick:);
}

- (NSStackView *)createAddTaskStack {
    self.addUrlField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addUrlField.placeholderString = @"输入下载URL";
    self.addUrlField.stringValue = @"https://ehe-1258344701.shiply-cdn.qq.com/apprelease/gray/29378ad6d8/prod/1739516899/com.tencent.ehe_v2.2.3_289_release-signed.apk";
    self.addUrlField.font = [NSFont systemFontOfSize:13];
    self.addUrlField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addUrlField setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addUrlField setContentCompressionResistancePriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.pasteClipboardButton = [self buttonWithTitle:@"粘贴剪贴板" action:@selector(pasteClipboard:)];
    [self.pasteClipboardButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.pasteClipboardButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.addTaskButton = [self buttonWithTitle:@"添加任务" action:@selector(addTask:)];
    [self.addTaskButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addTaskButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.openFolderButton = [self buttonWithTitle:@"打开下载目录" action:@selector(openFolder:)];
    [self.openFolderButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.openFolderButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];

    NSStackView *addTaskStack = [NSStackView stackViewWithViews:@[
        self.addUrlField, self.pasteClipboardButton, self.addTaskButton, self.openFolderButton
    ]];
    addTaskStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    addTaskStack.spacing = 12;
    addTaskStack.alignment = NSLayoutAttributeCenterY;
    addTaskStack.translatesAutoresizingMaskIntoConstraints = NO;
    return addTaskStack;
}

- (NSStackView *)createAddBigTaskStack {
    self.addBigUrlField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addBigUrlField.placeholderString = @"输入大文件下载URL";
    self.addBigUrlField.stringValue = @"https://downali.wandoujia.com/s1/4/6/2025070309121524_com.tencent.tmgp.sgame.apk?nrd=0&fname=%E7%8E%8B%E8%80%85%E8%8D%A3%E8%80%80&productid=2011&packageid=603894747&pkg=com.tencent.tmgp.sgame&vcode=1004010607&yingid=wdj_web&minSDK=22&size=2070985367&pos=wdj_web%2Fdetail_normal_dl%2F0&shortMd5=391bbcf65835a613ae92a11d72f46882&appid=6648837&apprd=6648837&crc32=632131349&iconUrl=http%3A%2F%2Fandroid-artworks.25pp.com%2Ffs08%2F2025%2F07%2F03%2F4%2F109_1f8a220af109b1bfaab874957ede4cd6_con.png&did=22e4598d49204b78a09b3dd0aa320d64&md5=65278df5e5c0ab4efbab9e6b9256d589";
    self.addBigUrlField.font = [NSFont systemFontOfSize:13];
    self.addBigUrlField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addBigUrlField setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addBigUrlField setContentCompressionResistancePriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.pasteBigClipboardButton = [self buttonWithTitle:@"粘贴剪贴板" action:@selector(pasteBigClipboard:)];
    [self.pasteBigClipboardButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.pasteBigClipboardButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.addBigTaskButton = [self buttonWithTitle:@"添加大文件任务" action:@selector(addBigTask:)];
    [self.addBigTaskButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addBigTaskButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    
    self.openApkFolderButton = [self buttonWithTitle:@"打开Apk下载目录" action:@selector(openApkFolder:)];
    [self.openApkFolderButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.openApkFolderButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];

    NSStackView *addBigTaskStack = [NSStackView stackViewWithViews:@[
        self.addBigUrlField, self.pasteBigClipboardButton, self.addBigTaskButton, self.openApkFolderButton,
    ]];
    addBigTaskStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    addBigTaskStack.spacing = 12;
    addBigTaskStack.alignment = NSLayoutAttributeCenterY;
    addBigTaskStack.translatesAutoresizingMaskIntoConstraints = NO;
    return addBigTaskStack;
}

#pragma mark - 下半区（调试与日志区）

- (void)setupBottomPanel {
    self.bottomTitleLabel = [self sectionTitleLabel:@"调试与日志区"];
    self.bottomTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.bottomPanel addSubview:self.bottomTitleLabel];
    [self.bottomTitleLabel.topAnchor constraintEqualToAnchor:self.bottomPanel.topAnchor constant:8].active = YES;
    [self.bottomTitleLabel.leadingAnchor constraintEqualToAnchor:self.bottomPanel.leadingAnchor constant:16].active = YES;

    self.debugPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.debugPanel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.bottomPanel addSubview:self.debugPanel];
    [NSLayoutConstraint activateConstraints:@[
        [self.debugPanel.leadingAnchor constraintEqualToAnchor:self.bottomPanel.leadingAnchor],
        [self.debugPanel.trailingAnchor constraintEqualToAnchor:self.bottomPanel.trailingAnchor],
        [self.debugPanel.topAnchor constraintEqualToAnchor:self.bottomTitleLabel.bottomAnchor constant:8],
        [self.debugPanel.bottomAnchor constraintEqualToAnchor:self.bottomPanel.bottomAnchor]
    ]];

    [self setupJsonrpcButtonPanel];
    [self setupResponseLogSplitView];

    NSStackView *debugStack = [NSStackView stackViewWithViews:@[
        self.jsonrpcButtonPanel, [self customJsonrpcInputPanel], self.responseLogSplitView
    ]];
    debugStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    debugStack.spacing = 16;
    debugStack.edgeInsets = NSEdgeInsetsMake(0, 16, 16, 16);
    debugStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.debugPanel addSubview:debugStack];
    [NSLayoutConstraint activateConstraints:@[
        [debugStack.leadingAnchor constraintEqualToAnchor:self.debugPanel.leadingAnchor],
        [debugStack.trailingAnchor constraintEqualToAnchor:self.debugPanel.trailingAnchor],
        [debugStack.topAnchor constraintEqualToAnchor:self.debugPanel.topAnchor],
        [debugStack.bottomAnchor constraintEqualToAnchor:self.debugPanel.bottomAnchor]
    ]];
}

- (void)setupJsonrpcButtonPanel {
    NSInteger maxPerRow = 3;
    NSMutableArray *rows = [NSMutableArray array];
    NSMutableArray *currentRow = [NSMutableArray array];
    for (NSInteger i = 0; i < self.rpcTests.count; i++) {
        NSDictionary *item = self.rpcTests[i];
        NSButton *btn = [self buttonWithTitle:item[@"title"] action:@selector(onJsonrpcTestButton:)];
        btn.tag = i;
        [btn setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
        [btn setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
        [currentRow addObject:btn];
        if (currentRow.count == maxPerRow || i == self.rpcTests.count - 1) {
            [rows addObject:[currentRow copy]];
            [currentRow removeAllObjects];
        }
    }
    NSMutableArray *rowViews = [NSMutableArray array];
    for (NSArray *row in rows) {
        NSStackView *rowStack = [NSStackView stackViewWithViews:row];
        rowStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        rowStack.spacing = 12;
        rowStack.alignment = NSLayoutAttributeCenterY;
        rowStack.translatesAutoresizingMaskIntoConstraints = NO;
        [rowViews addObject:rowStack];
    }
    self.jsonrpcButtonPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    NSStackView *buttonStack = [NSStackView stackViewWithViews:rowViews];
    buttonStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    buttonStack.spacing = 8;
    buttonStack.alignment = NSLayoutAttributeLeading;
    buttonStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.jsonrpcButtonPanel addSubview:buttonStack];
    [NSLayoutConstraint activateConstraints:@[
        [buttonStack.leadingAnchor constraintEqualToAnchor:self.jsonrpcButtonPanel.leadingAnchor],
        [buttonStack.trailingAnchor constraintLessThanOrEqualToAnchor:self.jsonrpcButtonPanel.trailingAnchor],
        [buttonStack.topAnchor constraintEqualToAnchor:self.jsonrpcButtonPanel.topAnchor],
        [buttonStack.bottomAnchor constraintEqualToAnchor:self.jsonrpcButtonPanel.bottomAnchor]
    ]];
}

- (void)setupResponseLogSplitView {
    self.responseLogSplitView = [[NSSplitView alloc] initWithFrame:NSZeroRect];
    self.responseLogSplitView.dividerStyle = NSSplitViewDividerStyleThin;
    self.responseLogSplitView.vertical = YES;
    self.responseLogSplitView.delegate = self;
    self.responseLogSplitView.translatesAutoresizingMaskIntoConstraints = NO;

    // 响应区
    self.responsePanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.responsePanel.translatesAutoresizingMaskIntoConstraints = NO;
    self.responseTitleLabel = [self sectionTitleLabel:@"JSON-RPC响应区"];
    self.rpcResponseCopyButton = [self buttonWithTitle:@"复制响应" action:@selector(onCopyResponse:)];
    NSStackView *responseTitleStack = [NSStackView stackViewWithViews:@[self.responseTitleLabel, self.rpcResponseCopyButton]];
    responseTitleStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    responseTitleStack.spacing = 16;
    responseTitleStack.alignment = NSLayoutAttributeCenterY;
    responseTitleStack.translatesAutoresizingMaskIntoConstraints = NO;

    self.jsonrpcResponseView = [[NSTextView alloc] initWithFrame:NSZeroRect];
    self.jsonrpcResponseView.editable = NO;
    self.jsonrpcResponseView.font = [NSFont userFixedPitchFontOfSize:12];
    self.jsonrpcResponseView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

    self.jsonrpcResponseScroll = [[NSScrollView alloc] initWithFrame:NSZeroRect];
    self.jsonrpcResponseScroll.documentView = self.jsonrpcResponseView;
    self.jsonrpcResponseScroll.hasVerticalScroller = YES;
    self.jsonrpcResponseScroll.autohidesScrollers = YES;
    self.jsonrpcResponseScroll.borderType = NSBezelBorder;
    self.jsonrpcResponseScroll.translatesAutoresizingMaskIntoConstraints = NO;

    NSStackView *responseStack = [NSStackView stackViewWithViews:@[responseTitleStack, self.jsonrpcResponseScroll]];
    responseStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    responseStack.spacing = 8;
    responseStack.edgeInsets = NSEdgeInsetsMake(8, 8, 8, 8);
    responseStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.responsePanel addSubview:responseStack];
    [NSLayoutConstraint activateConstraints:@[
        [responseStack.leadingAnchor constraintEqualToAnchor:self.responsePanel.leadingAnchor],
        [responseStack.trailingAnchor constraintEqualToAnchor:self.responsePanel.trailingAnchor],
        [responseStack.topAnchor constraintEqualToAnchor:self.responsePanel.topAnchor],
        [responseStack.bottomAnchor constraintEqualToAnchor:self.responsePanel.bottomAnchor]
    ]];

    // 日志区
    self.logPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.logPanel.translatesAutoresizingMaskIntoConstraints = NO;
    self.logTitleLabel = [self sectionTitleLabel:@"日志区"];
    self.logTextView = [[NSTextView alloc] initWithFrame:NSZeroRect];
    self.logTextView.editable = NO;
    self.logTextView.font = [NSFont userFixedPitchFontOfSize:12];
    self.logTextView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

    self.logScroll = [[NSScrollView alloc] initWithFrame:NSZeroRect];
    self.logScroll.documentView = self.logTextView;
    self.logScroll.hasVerticalScroller = YES;
    self.logScroll.autohidesScrollers = YES;
    self.logScroll.borderType = NSBezelBorder;
    self.logScroll.translatesAutoresizingMaskIntoConstraints = NO;

    self.clearLogButton = [self buttonWithTitle:@"清空日志" action:@selector(onClearLog:)];
    NSStackView *logTitleStack = [NSStackView stackViewWithViews:@[self.logTitleLabel, self.clearLogButton]];
    logTitleStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    logTitleStack.spacing = 16;
    logTitleStack.alignment = NSLayoutAttributeCenterY;
    logTitleStack.translatesAutoresizingMaskIntoConstraints = NO;

    NSStackView *logStack = [NSStackView stackViewWithViews:@[logTitleStack, self.logScroll]];
    logStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    logStack.spacing = 8;
    logStack.edgeInsets = NSEdgeInsetsMake(8, 8, 8, 8);
    logStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.logPanel addSubview:logStack];
    [NSLayoutConstraint activateConstraints:@[
        [logStack.leadingAnchor constraintEqualToAnchor:self.logPanel.leadingAnchor],
        [logStack.trailingAnchor constraintEqualToAnchor:self.logPanel.trailingAnchor],
        [logStack.topAnchor constraintEqualToAnchor:self.logPanel.topAnchor],
        [logStack.bottomAnchor constraintEqualToAnchor:self.logPanel.bottomAnchor]
    ]];

    // NSSplitView子view必须有明确宽度约束，否则拉伸时会冲突
    [self.responsePanel.widthAnchor constraintGreaterThanOrEqualToConstant:300].active = YES;
    [self.logPanel.widthAnchor constraintGreaterThanOrEqualToConstant:300].active = YES;

    [self.responseLogSplitView addSubview:self.responsePanel];
    [self.responseLogSplitView addSubview:self.logPanel];
}

- (NSView *)customJsonrpcInputPanel {
    NSTextField *label = [self labelWithString:@"自定义JSON-RPC请求：" fontSize:13 color:[NSColor labelColor]];
    self.customJsonrpcField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.customJsonrpcField.placeholderString = @"{\"method\":\"aria2.getVersion\",\"params\":[]}";
    self.customJsonrpcField.stringValue = @"{\"method\":\"aria2.getVersion\",\"params\":[]}";
    self.customJsonrpcField.font = [NSFont userFixedPitchFontOfSize:12];
    self.customJsonrpcField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.customJsonrpcField setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.customJsonrpcField setContentCompressionResistancePriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.customJsonrpcField.widthAnchor constraintGreaterThanOrEqualToConstant:800].active = YES;

    NSStackView *inputStack = [NSStackView stackViewWithViews:@[label, self.customJsonrpcField]];
    inputStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    inputStack.spacing = 8;
    inputStack.alignment = NSLayoutAttributeCenterY;
    inputStack.translatesAutoresizingMaskIntoConstraints = NO;

    self.sendCustomJsonrpcButton = [self buttonWithTitle:@"发送" action:@selector(onSendCustomJsonrpc:)];
    [self.sendCustomJsonrpcButton setContentHuggingPriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.sendCustomJsonrpcButton setContentCompressionResistancePriority:NSLayoutPriorityDefaultHigh forOrientation:NSLayoutConstraintOrientationHorizontal];

    NSStackView *buttonStack = [NSStackView stackViewWithViews:@[self.sendCustomJsonrpcButton]];
    buttonStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    buttonStack.alignment = NSLayoutAttributeCenterX;
    buttonStack.translatesAutoresizingMaskIntoConstraints = NO;
    buttonStack.distribution = NSStackViewDistributionFill;

    NSStackView *mainStack = [NSStackView stackViewWithViews:@[inputStack, buttonStack]];
    mainStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    mainStack.spacing = 8;
    mainStack.translatesAutoresizingMaskIntoConstraints = NO;
    return mainStack;
}

- (void)onSendCustomJsonrpc:(id)sender {
    
    [self showToast:@"不再支持自定义RPC发送"];
//    NSString *jsonStr = self.customJsonrpcField.stringValue;
//    if (jsonStr.length == 0) return;
//    NSData *data = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
//    NSError *err = nil;
//    NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&err];
//    if (err || ![json isKindOfClass:[NSDictionary class]]) {
//        [self log:[NSString stringWithFormat:@"[自定义JSON-RPC] 解析失败: %@", err ?: @"格式错误"]];
//        [self showJsonrpcResponse:nil error:err ?: [NSError errorWithDomain:@"DebugAria2" code:-1 userInfo:@{NSLocalizedDescriptionKey:@"JSON格式错误"}]];
//        return;
//    }
//    [self log:[NSString stringWithFormat:@"[自定义JSON-RPC] 发送: %@", jsonStr]];
//    [[YYBLibAria2ServiceFacade sharedService] sendCustomJSONRPCRequest:json completion:^(NSDictionary * _Nullable result, NSData *rawData, NSError * _Nullable error) {
//        [self log:[NSString stringWithFormat:@"[自定义JSON-RPC] 响应: %@", error ? error : @"OK"]];
//        [self showJsonrpcResponse:result error:error];
//    }];
}

#pragma mark - 自动刷新定时器


- (void)startAutoRefreshTimer {
    [self stopAutoRefreshTimer];
    __weak typeof(self) weakSelf = self;
    // 确保NSTimer在主线程创建，否则不会触发
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        weakSelf.autoRefreshTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 repeats:YES block:^(NSTimer * _Nonnull timer) {
            if (!weakSelf) return;
            [weakSelf refreshAllTasks];
        }];
    });
}

- (void)stopAutoRefreshTimer {
    if (self.autoRefreshTimer) {
        [self.autoRefreshTimer invalidate];
        self.autoRefreshTimer = nil;
    }
}

#pragma mark - 服务控制

- (void)startService:(id)sender {
    [self log:@"[UI] 启动aria2c服务..."];
    YYBLibAria2ServiceFacade *mgr = [YYBLibAria2ServiceFacade sharedService];
     // 可选：设置下载目录
     // mgr.downloadDir = self.downloadDir;
    
    [mgr startWithCompletion:^(BOOL success, NSError * _Nullable error) {
        [self updateServiceStatus:success];
        [self log:success ? @"[aria2c] 启动成功" : [NSString stringWithFormat:@"[aria2c] 启动失败: %@", error]];
        if (success) {
            [self startAutoRefreshTimer];
            [self refreshAllTasks];
        }
    }];
}

- (void)stopService:(id)sender {
    [self log:@"[UI] 停止aria2c服务"];
    [[YYBLibAria2ServiceFacade sharedService] stopWithCompletion:nil];
    [self updateServiceStatus:NO];
    [self stopAutoRefreshTimer];
    [self.tasks removeAllObjects];
    [self refreshTableView];
}

- (void)updateServiceStatus:(BOOL)running {
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        self.serviceStatusLabel.stringValue = running ? @"服务已启动" : @"服务未启动";
        self.serviceStatusLabel.textColor = running ? [NSColor systemGreenColor] : [NSColor systemRedColor];
        self.startButton.enabled = !running;
        self.stopButton.enabled = running;
        self.refreshButton.enabled = running;
        self.purgeAllButton.enabled = running;
    });
    self.serviceRunning = running;
}

#pragma mark - 最近任务数配置

- (void)onRecentCountChanged:(id)sender {
    NSInteger val = [self.recentCountField.stringValue integerValue];
    if (val < 1) val = 1;
    if (val > 100) val = 100;
    self.recentTaskCount = val;
    self.recentCountField.stringValue = [NSString stringWithFormat:@"%ld", (long)val];
    [self log:[NSString stringWithFormat:@"[UI] 最近任务数已设置为: %ld", (long)val]];
    [self refreshAllTasks];
}

#pragma mark - 刷新

- (void)onRefreshAllTasks:(id)sender {
    [self log:@"[UI] 手动刷新全部任务"];
    [self refreshAllTasks];
}

- (void)refreshTableView {
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        if (!self.tableView || !self.view.window) return; // 防御页面已销毁
        [self.tableView reloadData];
    });
}

#pragma mark - 任务列表同步与状态合并（核心优化）

/// 合并active/waiting/stopped任务，按gid去重，优先级：active > waiting > stopped
- (void)refreshAllTasks {
    if (!self.serviceRunning) {
        [self.tasks removeAllObjects];
        [self.tasks addObjectsFromArray:self.localCompletedTasks]; // 离线任务也显示
        [self refreshTableView];
        return;
    }
    // 1. 查询active
    __weak typeof(self) weakSelf = self;
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.tellActive" params:@[] source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable resultActive, NSData *rawData, NSError * _Nullable errorActive) {
        NSArray *activeList = (resultActive[@"result"] && [resultActive[@"result"] isKindOfClass:[NSArray class]]) ? resultActive[@"result"] : @[];
        // 2. 查询waiting
        [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.tellWaiting" params:@[@0, @(self.recentTaskCount)] source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable resultWaiting, NSData *rawData, NSError * _Nullable errorWaiting) {
            NSArray *waitingList = (resultWaiting[@"result"] && [resultWaiting[@"result"] isKindOfClass:[NSArray class]]) ? resultWaiting[@"result"] : @[];
            // 3. 查询stopped
            [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.tellStopped" params:@[@0, @(self.recentTaskCount)] source:YYBAria2TaskSourceDebug  completion:^(NSDictionary * _Nullable resultStopped, NSData *rawData, NSError * _Nullable errorStopped) {
                __strong typeof(weakSelf) self = weakSelf;
                NSArray *stoppedList = (resultStopped[@"result"] && [resultStopped[@"result"] isKindOfClass:[NSArray class]]) ? resultStopped[@"result"] : @[];
                // 合并三类任务，按gid去重，优先级：active > waiting > stopped
                NSMutableDictionary<NSString *, NSDictionary *> *gidMap = [NSMutableDictionary dictionary];
                NSMutableSet *fileNameSet = [NSMutableSet set];
                for (NSDictionary *info in stoppedList) {
                    NSString *gid = info[@"gid"];
                    if (gid) gidMap[gid] = info;
                    // 记录文件名
                    NSArray *files = info[@"files"];
                    if ([files isKindOfClass:[NSArray class]] && files.count > 0) {
                        NSString *fn = [files[0][@"path"] lastPathComponent];
                        if (fn.length > 0) [fileNameSet addObject:fn];
                    }
                }
                for (NSDictionary *info in waitingList) {
                    NSString *gid = info[@"gid"];
                    if (gid) gidMap[gid] = info;
                    NSArray *files = info[@"files"];
                    if ([files isKindOfClass:[NSArray class]] && files.count > 0) {
                        NSString *fn = [files[0][@"path"] lastPathComponent];
                        if (fn.length > 0) [fileNameSet addObject:fn];
                    }
                }
                for (NSDictionary *info in activeList) {
                    NSString *gid = info[@"gid"];
                    if (gid) gidMap[gid] = info;
                    NSArray *files = info[@"files"];
                    if ([files isKindOfClass:[NSArray class]] && files.count > 0) {
                        NSString *fn = [files[0][@"path"] lastPathComponent];
                        if (fn.length > 0) [fileNameSet addObject:fn];
                    }
                }
                // 按active > waiting > stopped顺序排序
                NSMutableArray *allTasks = [NSMutableArray array];
                NSMutableSet *addedGids = [NSMutableSet set];
                for (NSDictionary *info in activeList) {
                    NSString *gid = info[@"gid"];
                    if (gid && ![addedGids containsObject:gid]) {
                        [allTasks addObject:[self taskDictFromAria2Info:info]];
                        [addedGids addObject:gid];
                    }
                }
                for (NSDictionary *info in waitingList) {
                    NSString *gid = info[@"gid"];
                    if (gid && ![addedGids containsObject:gid]) {
                        [allTasks addObject:[self taskDictFromAria2Info:info]];
                        [addedGids addObject:gid];
                    }
                }
                for (NSDictionary *info in stoppedList) {
                    NSString *gid = info[@"gid"];
                    if (gid && ![addedGids containsObject:gid]) {
                        [allTasks addObject:[self taskDictFromAria2Info:info]];
                        [addedGids addObject:gid];
                    }
                }
                // 合并本地已完成任务（fileName未出现在Aria2任务中的才加）
                for (NSDictionary *localTask in self.localCompletedTasks) {
                    NSString *fileName = localTask[@"fileName"];
                    if (fileName.length > 0 && ![fileNameSet containsObject:fileName]) {
                        [allTasks addObject:[localTask mutableCopy]];
                    }
                }
                // 更新已安装状态
                for (NSMutableDictionary *task in allTasks) {
                    NSString *appName = [self appNameFromTask:task];
                    
                    BOOL installed = [[YYBApkPackage shared] checkAppShotcut:[NSString stringWithFormat:@"%@.app", appName]];
                    task[@"isInstalled"] = @(installed);
                }
                self.tasks = allTasks;
                [self refreshTableView];
            }];
        }];
    }];
}

#pragma mark - 清除全部任务

- (void)onPurgeAllTasks:(id)sender {
    [self log:@"[UI] 清除全部历史任务"];
    /// 清除所有Aria2下载任务和文件
    [[YYBAria2DownloadManager sharedManager] clearAllTasksDeleteFiles:YES];
    [[YYBLibAria2ServiceFacade sharedService] cleanAllAria2Task:^{
        [self.tasks removeAllObjects];
        [self.localCompletedTasks removeAllObjects];
        [self refreshAllTasks];
        [[YYBLibAria2ServiceFacade sharedService] startWithCompletion:^(BOOL success, NSError * _Nullable error) {
            [self refreshAllTasks];
            [self showToast:@"清除成功（服务已重启）"];
        }];
    }];
}

#pragma mark - 日志清空

- (void)onClearLog:(id)sender {
    self.logTextView.string = @"";
}

#pragma mark - 响应复制

- (void)onCopyResponse:(id)sender {
    NSString *resp = self.jsonrpcResponseView.string ?: @"";
    if (resp.length > 0) {
        NSPasteboard *pb = [NSPasteboard generalPasteboard];
        [pb clearContents];
        [pb setString:resp forType:NSPasteboardTypeString];
        [self showToast:@"响应已复制"];
    }
}

#pragma mark - 进入页面时自动检测服务状态

- (void)checkServiceAndAutoRefresh {
    // 查询当前服务是否运行
    BOOL running = [YYBLibAria2ServiceFacade sharedService].isRunning;
    NSString *log = [NSString stringWithFormat:@"[App] 检测到aria2c服务启动状态, running = %@", @(running)];
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [self updateServiceStatus:running];
        [self log:log];
        if (running) {
            [self startAutoRefreshTimer];
            [self refreshAllTasks];
        }
    });
    
    // 监听服务状态变化
    [[NSNotificationCenter defaultCenter] addObserverForName:kYYBLibAria2ServiceStatusChangedNotification object:nil queue:[NSOperationQueue mainQueue] usingBlock:^(NSNotification * _Nonnull note) {
        BOOL running = [note.userInfo[@"running"] boolValue];
        [self updateServiceStatus:running];
        NSString *log = [NSString stringWithFormat:@"[App] aria2c服务状态发生变化, running = %@", @(running)];
        [self log:log];
        if (running) {
            [self startAutoRefreshTimer];
            [self refreshAllTasks];
        }
    }];
}

#pragma mark - 任务操作

- (void)onPauseClicked:(NSButton *)btn {
    [self pauseTaskAtRow:btn.tag];
}
- (void)onResumeClicked:(NSButton *)btn {
    [self resumeTaskAtRow:btn.tag];
}
- (void)onRemoveClicked:(NSButton *)btn {
    [self removeTaskAtRow:btn.tag];
}

- (void)pauseTaskAtRow:(NSInteger)row {
    NSString *gid = self.tasks[row][@"gid"];
    [self log:[NSString stringWithFormat:@"[UI] 暂停任务: %@", gid]];
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.pause" params:@[gid]  source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable result, NSData *rawData, NSError * _Nullable error) {
        [self log:error ? [NSString stringWithFormat:@"[aria2c] 暂停失败: %@", error] : @"[aria2c] 暂停成功"];
        [self showJsonrpcResponse:result error:error];
        [self refreshAllTasks];
    }];
}

- (void)resumeTaskAtRow:(NSInteger)row {
    NSString *gid = self.tasks[row][@"gid"];
    [self log:[NSString stringWithFormat:@"[UI] 恢复任务: %@", gid]];
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.unpause" params:@[gid] source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable result, NSData *rawData, NSError * _Nullable error) {
        [self log:error ? [NSString stringWithFormat:@"[aria2c] 恢复失败: %@", error] : @"[aria2c] 恢复成功"];
        [self showJsonrpcResponse:result error:error];
        [self refreshAllTasks];
    }];
}

- (void)removeTaskAtRow:(NSInteger)row {
    NSString *gid = self.tasks[row][@"gid"];
    [self log:[NSString stringWithFormat:@"[UI] 删除任务: %@", gid]];
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.remove" params:@[gid] source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable result, NSData *rawData, NSError * _Nullable error) {
        [self log:error ? [NSString stringWithFormat:@"[aria2c] 删除失败: %@", error] : @"[aria2c] 删除成功"];
        [self showJsonrpcResponse:result error:error];
        [self refreshAllTasks];
    }];
}

#pragma mark - 任务状态显示

- (NSMutableDictionary *)taskDictFromAria2Info:(NSDictionary *)info {
    NSString *gid = info[@"gid"] ?: @"";
    NSString *url = [self urlFromInfo:info];
    NSString *status = info[@"status"] ?: @"未知";
    long long total = [info[@"totalLength"] longLongValue];
    long long completed = [info[@"completedLength"] longLongValue];
    NSString *progress = total > 0 ? [NSString stringWithFormat:@"%.1f%%", completed*100.0/total] : @"0%";
    NSString *fileStatus = @"未知";
    NSString *fileName = @"";
    NSString *fileSize = @"";
    NSArray *files = info[@"files"];
    if ([files isKindOfClass:[NSArray class]] && files.count > 0) {
        NSDictionary *fileInfo = files[0];
        NSString *path = fileInfo[@"path"];
        if (path) {
            fileName = [path lastPathComponent];
            NSDictionary *attr = [[NSFileManager defaultManager] attributesOfItemAtPath:path error:nil];
            NSNumber *sizeNum = attr[NSFileSize];
            unsigned long long size = sizeNum ? [sizeNum unsignedLongLongValue] : 0;
            fileSize = size > 0 ? [NSByteCountFormatter stringFromByteCount:size countStyle:NSByteCountFormatterCountStyleFile] : @"";
            if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
                fileStatus = @"存在";
            } else {
                fileStatus = @"文件已删除";
            }
        }
    }
    return [@{@"gid":gid, @"url":url, @"status":[self statusDisplayString:status], @"progress":progress, @"fileStatus":fileStatus, @"fileName":fileName, @"fileSize":fileSize, @"rawStatus":status} mutableCopy];
}

- (NSString *)urlFromInfo:(NSDictionary *)info {
    NSArray *files = info[@"files"];
    if (files.count > 0) {
        NSArray *uris = files[0][@"uris"];
        if ([uris isKindOfClass:[NSArray class]] && uris.count > 0) {
            return uris[0][@"uri"] ?: @"";
        }
    }
    return @"";
}

- (NSString *)statusDisplayString:(NSString *)status {
    if ([status isEqualToString:@"active"]) return @"下载中";
    if ([status isEqualToString:@"waiting"]) return @"等待中";
    if ([status isEqualToString:@"paused"]) return @"已暂停";
    if ([status isEqualToString:@"complete"]) return @"已完成";
    if ([status isEqualToString:@"error"]) return @"出错";
    if ([status isEqualToString:@"removed"]) return @"已删除";
    return status ?: @"未知";
}

#pragma mark - 添加任务

- (void)addTask:(id)sender {
    NSString *url = self.addUrlField.stringValue;
    if (url.length == 0) return;
    NSString *fileName = [self safeFileNameFromUrl:url];
    NSString *filePath = [self.downloadDir stringByAppendingPathComponent:fileName];
    
    [self getRemoteFileSizeForURL:url completion:^(long long remoteSize, NSError *error) {
        [self log:[NSString stringWithFormat:@"[HEAD] 获取文件大小失败: %@", error]];
        DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
            if (error) {
                [self startAria2DownloadWithUrl:url];
                return;
            }
        });
        if ([self isLocalFileComplete:filePath expectedSize:remoteSize]) {
            [self log:@"[UI] 本地已存在，不再重复添加任务"];
            NSMutableDictionary *task = [@{
                @"gid": [NSString stringWithFormat:@"local_%@", fileName],
                @"url": url,
                @"status": @"已完成",
                @"progress": @"100%",
                @"fileStatus": @"存在",
                @"fileName": fileName,
                @"fileSize": [NSByteCountFormatter stringFromByteCount:remoteSize countStyle:NSByteCountFormatterCountStyleFile],
                @"rawStatus": @"complete"
            } mutableCopy];
            // 标记为已安装（如有）
            NSString *appName = [self appNameFromTask:task];
            BOOL installed =  [[YYBApkPackage shared] checkAppShotcut:[NSString stringWithFormat:@"%@.app", appName]];
            task[@"isInstalled"] = @(installed);
            
            // 检查是否已存在于本地已完成任务，避免重复
            BOOL alreadyInLocal = NO;
            for (NSDictionary *t in self.localCompletedTasks) {
                if ([t[@"fileName"] isEqualToString:fileName]) {
                    alreadyInLocal = YES;
                    break;
                }
            }
            if (!alreadyInLocal) {
                [self.localCompletedTasks addObject:task];
            }
            [self refreshAllTasks];
            [self showToast:@"本地已存在，不再重复添加此任务"];
        } else {
            DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
                [self startAria2DownloadWithUrl:url];
            });
        }
    }];
}

- (void)addBigTask:(id)sender {
    NSString *url = self.addBigUrlField.stringValue;
    if (url.length == 0) return;
    NSString *fileName = [self safeFileNameFromUrl:url];
    NSString *filePath = [self.downloadDir stringByAppendingPathComponent:fileName];
    [self getRemoteFileSizeForURL:url completion:^(long long remoteSize, NSError *error) {
            if (error) {
                [self log:[NSString stringWithFormat:@"[HEAD] 获取大文件大小失败: %@", error]];
                DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
                    [self startAria2DownloadWithUrl:url];
                });
                return;
            }
            if ([self isLocalFileComplete:filePath expectedSize:remoteSize]) {
                [self log:@"[UI] 本地已存在，不再重复添加任务"];
                NSMutableDictionary *task = [@{
                    @"gid": [NSString stringWithFormat:@"local_%@", fileName],
                    @"url": url,
                    @"status": @"已完成",
                    @"progress": @"100%",
                    @"fileStatus": @"存在",
                    @"fileName": fileName,
                    @"fileSize": [NSByteCountFormatter stringFromByteCount:remoteSize countStyle:NSByteCountFormatterCountStyleFile],
                    @"rawStatus": @"complete"
                } mutableCopy];
                NSString *appName = [self appNameFromTask:task];
                BOOL installed =  [[YYBApkPackage shared] checkAppShotcut:[NSString stringWithFormat:@"%@.app", appName]];
                task[@"isInstalled"] = @(installed);
                
                BOOL alreadyInLocal = NO;
                for (NSDictionary *t in self.localCompletedTasks) {
                    if ([t[@"fileName"] isEqualToString:fileName]) {
                        alreadyInLocal = YES;
                        break;
                    }
                }
                if (!alreadyInLocal) {
                    [self.localCompletedTasks addObject:task];
                }
                [self refreshAllTasks];
                [self showToast:@"本地已存在，不再重复添加任务"];
            } else {
                DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
                    [self startAria2DownloadWithUrl:url];
                });
            }
        
    }];
}

- (void)startAria2DownloadWithUrl:(NSString *)url {
    NSString *fileName = [self safeFileNameFromUrl:url];
    NSArray *params = @[
        @[url],
        @{@"dir": self.downloadDir, @"out": fileName}
    ];
    [self log:[NSString stringWithFormat:@"[UI] 添加任务: %@ (out=%@)", url, fileName]];
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:@"aria2.addUri" params:params source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable result, NSData *rawData, NSError * _Nullable error) {
        if (error) {
            [self log:[NSString stringWithFormat:@"[aria2c] 添加任务失败: %@", error]];
        } else {
            NSString *gid = result[@"result"];
            if (gid) {
                [self log:[NSString stringWithFormat:@"[aria2c] 添加任务成功，gid: %@", gid]];
                [self refreshAllTasks];
            }
        }
    }];
}

// 工具方法，去掉?及后面参数
- (NSString *)safeFileNameFromUrl:(NSString *)url {
    NSString *fileName = [url lastPathComponent];
    NSRange q = [fileName rangeOfString:@"?"];
    if (q.location != NSNotFound) {
        fileName = [fileName substringToIndex:q.location];
    }
    return fileName;
}

- (void)pasteClipboard:(id)sender {
    NSPasteboard *pb = [NSPasteboard generalPasteboard];
    NSString *str = [pb stringForType:NSPasteboardTypeString];
    if (str.length > 0) {
        self.addUrlField.stringValue = str;
    }
}

// 大文件输入框粘贴
- (void)pasteBigClipboard:(id)sender {
    NSPasteboard *pb = [NSPasteboard generalPasteboard];
    NSString *str = [pb stringForType:NSPasteboardTypeString];
    if (str.length > 0) {
        self.addBigUrlField.stringValue = str;
    }
}

#pragma mark - TableView

- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.tasks.count;
}

- (NSView *)tableView:(NSTableView *)tableView viewForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    NSDictionary *task = self.tasks[row];
    NSString *identifier = tableColumn.identifier;
    if ([identifier isEqualToString:@"action"]) {
        NSString *rawStatus = task[@"rawStatus"];
        // 操作按钮显示逻辑优化：只要不是已完成/已删除/出错，都可操作
        BOOL canPause = [rawStatus isEqualToString:@"active"] || [rawStatus isEqualToString:@"waiting"];
        BOOL canResume = [rawStatus isEqualToString:@"paused"];
        BOOL canRemove = ![rawStatus isEqualToString:@"removed"] && ![rawStatus isEqualToString:@"complete"];
        BOOL canInstall = [rawStatus isEqualToString:@"complete"] && ![task[@"isInstalled"] boolValue];
        BOOL isInstalled = [task[@"isInstalled"] boolValue];
        NSMutableArray *btns = [NSMutableArray array];
        if (canPause) {
            NSButton *pauseBtn = [self buttonWithTitle:@"暂停" action:@selector(onPauseClicked:)];
            pauseBtn.tag = row;
            [pauseBtn.widthAnchor constraintEqualToConstant:70].active = YES;
            [btns addObject:pauseBtn];
        }
        if (canResume) {
            NSButton *resumeBtn = [self buttonWithTitle:@"恢复" action:@selector(onResumeClicked:)];
            resumeBtn.tag = row;
            [resumeBtn.widthAnchor constraintEqualToConstant:70].active = YES;
            [btns addObject:resumeBtn];
        }
        if (canRemove) {
            NSButton *removeBtn = [self buttonWithTitle:@"删除" action:@selector(onRemoveClicked:)];
            removeBtn.tag = row;
            [removeBtn.widthAnchor constraintEqualToConstant:70].active = YES;
            [btns addObject:removeBtn];
        }
        // 安装按钮
        if (canInstall) {
            NSButton *installBtn = [self buttonWithTitle:@"安装" action:@selector(onInstallClicked:)];
            installBtn.tag = row;
            [installBtn.widthAnchor constraintEqualToConstant:70].active = YES;
            [btns addObject:installBtn];
        }
        // 安装后显示“打开”和“删除”
        if (isInstalled) {
            NSButton *openBtn = [self buttonWithTitle:@"打开" action:@selector(onOpenAppClicked:)];
            openBtn.tag = row;
            [openBtn.widthAnchor constraintEqualToConstant:70].active = YES;
            [btns addObject:openBtn];
            
            NSButton *delBtn = [self buttonWithTitle:@"删除" action:@selector(onDeleteAppClicked:)];
            delBtn.tag = row;
            [delBtn.widthAnchor constraintEqualToConstant:70].active = YES;
            [btns addObject:delBtn];
        }
        if (btns.count == 0) return nil;
        NSStackView *actionView = [NSStackView stackViewWithViews:btns];
        actionView.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        actionView.spacing = 16;
        actionView.alignment = NSLayoutAttributeCenterY;
        return actionView;
    } else {
        NSTextField *cell = [tableView makeViewWithIdentifier:identifier owner:self];
        if (!cell) {
            cell = [[NSTextField alloc] initWithFrame:NSZeroRect];
            cell.identifier = identifier;
            cell.editable = NO;
            cell.bezeled = NO;
            cell.drawsBackground = NO;
            cell.lineBreakMode = NSLineBreakByTruncatingTail;
        }
        if ([identifier isEqualToString:@"gid"]) {
            cell.stringValue = task[@"gid"];
        } else if ([identifier isEqualToString:@"url"]) {
            cell.stringValue = task[@"url"];
        } else if ([identifier isEqualToString:@"status"]) {
            cell.stringValue = task[@"status"];
        } else if ([identifier isEqualToString:@"progress"]) {
            cell.stringValue = task[@"progress"];
        } else if ([identifier isEqualToString:@"fileStatus"]) {
            cell.stringValue = task[@"fileStatus"];
        } else if ([identifier isEqualToString:@"fileName"]) {
            cell.stringValue = task[@"fileName"];
        } else if ([identifier isEqualToString:@"fileSize"]) {
            cell.stringValue = task[@"fileSize"];
        }
        return cell;
    }
}

// 双击拷贝
- (void)onTableViewDoubleClick:(id)sender {
    NSInteger row = [self.tableView clickedRow];
    NSInteger col = [self.tableView clickedColumn];
    if (row < 0 || col < 0) return;
    NSTableColumn *column = self.tableView.tableColumns[col];
    NSString *identifier = column.identifier;
    NSDictionary *task = self.tasks[row];
    NSString *copyText = nil;
    NSString *tip = nil;
    if ([identifier isEqualToString:@"fileName"]) {
        copyText = task[@"fileName"];
        tip = @"文件名已复制";
    } else if ([identifier isEqualToString:@"url"]) {
        copyText = task[@"url"];
        tip = @"URL已复制";
    } else if ([identifier isEqualToString:@"gid"]) {
        copyText = task[@"gid"];
        tip = @"GID已复制";
    } else if ([identifier isEqualToString:@"fileStatus"]) {
        copyText = task[@"fileStatus"];
        tip = @"文件状态已复制";
    }
    if (copyText.length > 0) {
        NSPasteboard *pb = [NSPasteboard generalPasteboard];
        [pb clearContents];
        [pb setString:copyText forType:NSPasteboardTypeString];
        [self showToast:tip ?: @"已复制"];
    }
}

#pragma mark - 日志与回包展示

static inline void dispatch_async_on_main_queue(dispatch_block_t block) {
    if ([NSThread isMainThread]) {
        block();
    } else {
        dispatch_async(dispatch_get_main_queue(), block);
    }
}

- (void)log:(NSString *)msg {
    dispatch_async_on_main_queue( ^{
        NSString *old = self.logTextView.string ?: @"";
        NSString *logString = [old stringByAppendingFormat:@"%@\n", msg];
        self.logTextView.string = logString;
        [self.logTextView scrollRangeToVisible:NSMakeRange(self.logTextView.string.length, 0)];
    });
}

- (void)showJsonrpcResponse:(NSDictionary *)result error:(NSError *)error {
    dispatch_async_on_main_queue( ^{
        if (error) {
            self.jsonrpcResponseView.string = [NSString stringWithFormat:@"Error: %@", error];
        } else if (result) {
            NSData *respData = [NSJSONSerialization dataWithJSONObject:result options:NSJSONWritingPrettyPrinted error:nil];
            self.jsonrpcResponseView.string = [[NSString alloc] initWithData:respData encoding:NSUTF8StringEncoding];
        } else {
            self.jsonrpcResponseView.string = @"";
        }
        [self.jsonrpcResponseView scrollRangeToVisible:NSMakeRange(self.jsonrpcResponseView.string.length, 0)];
    });
}

#pragma mark - 预设JSON-RPC测试

- (void)onJsonrpcTestButton:(NSButton *)btn {
    NSDictionary *item = self.rpcTests[btn.tag];
    NSString *method = item[@"method"];
    id params = item[@"params"];
    [[YYBAria2DownloadManager sharedManager] sendJSONRPCRequestWithMethod:method params:params source:YYBAria2TaskSourceDebug completion:^(NSDictionary * _Nullable result, NSData *rawData, NSError * _Nullable error) {
        [self log:[NSString stringWithFormat:@"[JSON-RPC] %@: %@", method, error ? error : @"OK"]];
        [self showJsonrpcResponse:result error:error];
        // 发送完后，也刷新列表
        [self refreshAllTasks];
    }];
}

#pragma mark - NSSplitViewDelegate

- (CGFloat)splitView:(NSSplitView *)splitView constrainMinCoordinate:(CGFloat)proposedMin ofSubviewAt:(NSInteger)dividerIndex {
    if (splitView == self.mainSplitView) {
        return 120;
    } else if (splitView == self.responseLogSplitView) {
        return 200;
    }
    return proposedMin;
}
- (CGFloat)splitView:(NSSplitView *)splitView constrainMaxCoordinate:(CGFloat)proposedMax ofSubviewAt:(NSInteger)dividerIndex {
    if (splitView == self.mainSplitView) {
        return splitView.bounds.size.height - 80;
    } else if (splitView == self.responseLogSplitView) {
        return splitView.bounds.size.width - 200;
    }
    return proposedMax;
}

#pragma mark - Toast

- (void)showToast:(NSString *)msg {
    if (!msg.length) return;
    NSView *parent = self.view;
    NSTextField *toast = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 200, 36)];
    toast.stringValue = msg;
    toast.alignment = NSTextAlignmentCenter;
    toast.font = [NSFont boldSystemFontOfSize:15];
    toast.textColor = [NSColor whiteColor];
    toast.backgroundColor = [[NSColor blackColor] colorWithAlphaComponent:0.8];
    toast.bordered = NO;
    toast.editable = NO;
    toast.selectable = NO;
    toast.wantsLayer = YES;
    toast.layer.cornerRadius = 8;
    toast.layer.masksToBounds = YES;
    
    NSArray<NSLayoutConstraint *> *constraints = @[
        [toast.centerXAnchor constraintEqualToAnchor:parent.centerXAnchor],
        [toast.bottomAnchor constraintEqualToAnchor:parent.bottomAnchor constant:-80],
        [toast.widthAnchor constraintGreaterThanOrEqualToConstant:120],
        [toast.heightAnchor constraintEqualToConstant:36]
    ];
    
    // 确保在主线程
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [parent addSubview:toast];
        toast.translatesAutoresizingMaskIntoConstraints = NO;
        [NSLayoutConstraint activateConstraints:constraints];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [toast removeFromSuperview];
    });
}

#pragma mark - 工具方法

- (NSButton *)buttonWithTitle:(NSString *)title action:(SEL)action {
    NSButton *btn = [NSButton buttonWithTitle:title target:self action:action];
    btn.bezelStyle = NSBezelStyleRounded;
    btn.translatesAutoresizingMaskIntoConstraints = NO;
    [btn.widthAnchor constraintGreaterThanOrEqualToConstant:70].active = YES;
    return btn;
}

- (NSTextField *)labelWithString:(NSString *)str fontSize:(CGFloat)size color:(NSColor *)color {
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    label.stringValue = str;
    label.font = [NSFont systemFontOfSize:size];
    label.textColor = color;
    label.editable = NO;
    label.bezeled = NO;
    label.drawsBackground = NO;
    label.selectable = NO;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (void)addTableColumnWithIdentifier:(NSString *)identifier title:(NSString *)title width:(CGFloat)width {
    NSTableColumn *col = [[NSTableColumn alloc] initWithIdentifier:identifier];
    col.title = title;
    col.width = width;
    col.resizingMask = NSTableColumnUserResizingMask;
    [self.tableView addTableColumn:col];
}

#pragma mark - 查看下载目录

- (void)openFolder:(id)sender {
    NSURL *url = [NSURL fileURLWithPath:self.downloadDir];
    [[NSWorkspace sharedWorkspace] openURL:url];
}

- (void)openApkFolder:(id)sender {
    NSURL *url = [NSURL fileURLWithPath:[[YYBLibAria2ServiceFacade sharedService] sharedDownloadDir]];
    [[NSWorkspace sharedWorkspace] openURL:url];
}


#pragma mark - 安装APK

- (void)onInstallClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    NSMutableDictionary *task = self.tasks[row];

    NSString *apkPath = [self apkPathFromTask:task];
    if (!apkPath.length || ![[NSFileManager defaultManager] fileExistsAtPath:apkPath]) {
        [self showToast:@"APK文件不存在"];
        return;
    }
    btn.enabled = NO;
    [self showToast:@"正在安装..."];

    InstallApkInfo* apkInfo = [[InstallApkInfo alloc] init];
    apkInfo.filePath = apkPath;
    [[YYBApkPackage shared] installLocalApp:apkInfo
                             completion:^(InstallApkInfo* _Nullable info, NSInteger retCode, NSString* _Nullable  msg) {

            btn.enabled = YES;
            if (retCode == 0) {
                [self showToast:@"安装成功"];
                [self log:[NSString stringWithFormat:@"[安装] %@ 安装成功", info.name ?: apkPath]];
                // 标记已安装
                task[@"isInstalled"] = @(YES);
                [self refreshTableView];
            } else {
                [self showToast:[NSString stringWithFormat:@"安装失败: %@", info.name ?: @"未知错误"]];
                [self log:[NSString stringWithFormat:@"[安装] %@ 安装失败: %@", info.name ?: apkPath, info.filePath ?: @"未知错误"]];
            }
        }
    ];
}

#pragma mark - 打开App

- (void)onOpenAppClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    NSDictionary *task = self.tasks[row];
    NSString *appName = [self appNameFromTask:task];
    
    if (![[YYBApkPackage shared] checkAppShotcut:[NSString stringWithFormat:@"%@.app", appName]]) {
        [self showToast:@"App未找到"];
        return;
    }
    
    NSURL *appUrl = [NSURL fileURLWithPath:[ApkInfoGenerater launchAppsPathFromName:appName]];
    [[NSWorkspace sharedWorkspace] openApplicationAtURL:appUrl
                                         configuration:[NSWorkspaceOpenConfiguration configuration]
                                     completionHandler:^(NSRunningApplication *runningApp, NSError *error) {
        if (error) {
            [self showToast:[NSString stringWithFormat:@"启动失败: %@", error.localizedDescription]];
            NSLog(@"启动失败: %@", error.localizedDescription);
        }
    }];
}

#pragma mark - 删除App

- (void)onDeleteAppClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    NSMutableDictionary *task = self.tasks[row];
    NSString *appName = [self appNameFromTask:task];
    if (!appName.length) {
        [self showToast:@"App名为空"];
        return;
    }
    [[YYBApkPackage shared] uninstallShotcut:appName completion:^(InstallApkInfo* _Nullable info, NSInteger retCode, NSString* _Nullable  msg) {
        
    }];
    [self showToast:@"已删除"];
    [self log:[NSString stringWithFormat:@"[删除] %@ 已删除", appName]];
    // 标记未安装
    task[@"isInstalled"] = @(NO);
    [self refreshTableView];
}


#pragma mark - 获取远程文件大小
- (void)getRemoteFileSizeForURL:(NSString *)urlString completion:(void(^)(long long size, NSError *error))completion {
    NSURL *url = [NSURL URLWithString:urlString];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.HTTPMethod = @"HEAD";
    NSURLSession *session = [NSURLSession sharedSession];
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request
                                            completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        long long size = 0;
        if ([response isKindOfClass:[NSHTTPURLResponse class]]) {
            NSHTTPURLResponse *httpResp = (NSHTTPURLResponse *)response;
            NSString *contentLength = httpResp.allHeaderFields[@"Content-Length"];
            if (contentLength) {
                size = [contentLength longLongValue];
            }
        }
        if (completion) {
            completion(size, error);
        }
    }];
    [task resume];
}

#pragma mark - 判断本地文件是否完整
- (BOOL)isLocalFileComplete:(NSString *)filePath expectedSize:(long long)expectedSize {
    if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]) return NO;
    NSString *aria2File = [filePath stringByAppendingString:@".aria2"];
    if ([[NSFileManager defaultManager] fileExistsAtPath:aria2File]) {
        NSLog(@"任务在下载中。不完整");
        return NO;
    }
    NSDictionary *attr = [[NSFileManager defaultManager] attributesOfItemAtPath:filePath error:nil];
    unsigned long long fileSize = [attr[NSFileSize] unsignedLongLongValue];
    return (expectedSize > 0 && fileSize == expectedSize);
}


#pragma mark - APK信息提取

- (NSString *)apkPathFromTask:(NSDictionary *)task {
    // 优先用files[0].path
    NSArray *files = task[@"files"];
    if ([files isKindOfClass:[NSArray class]] && files.count > 0) {
        NSString *path = files[0][@"path"];
        if (path.length > 0) return path;
    }
    // 兼容旧数据
    NSString *fileName = task[@"fileName"];
    if (fileName.length > 0 && self.downloadDir.length > 0) {
        return [self.downloadDir stringByAppendingPathComponent:fileName];
    }
    return nil;
}

- (NSString *)appIconFromTask:(NSDictionary *)task {
    // 你可以根据实际情况从task中提取icon路径
    return task[@"appIcon"] ?: @"";
}

// 兜底提取pkgName（如 com.baidu.searchbox）和appName（如 baidu）
- (NSString *)pkgNameFromTask:(NSDictionary *)task {
    NSString *pkgName = task[@"pkgName"];
    if (pkgName.length > 0) return pkgName;
    NSString *fileName = task[@"fileName"];
    if (fileName.length == 0) return @"unknown.pkg";

    // 1. 正则匹配 com.xxx.yyy(.zzz) 只要在.apk前
    NSError *error = nil;
    // 匹配 com.开头，后面跟字母数字下划线点，直到遇到非这些字符或.apk
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"(com\\.[a-zA-Z0-9_\\.]+)" options:0 error:&error];
    NSTextCheckingResult *match = [regex firstMatchInString:fileName options:0 range:NSMakeRange(0, fileName.length)];
    if (match && match.numberOfRanges > 1) {
        NSRange pkgRange = [match rangeAtIndex:1];
        if (pkgRange.location != NSNotFound) {
            NSString *pkg = [fileName substringWithRange:pkgRange];
            // 2. 如果后面跟着下划线或其他分隔符，去掉
            // 比如 com.tencent.ehe_v2.2.3_289_release-signed.apk
            NSRange dotRange = [pkg rangeOfString:@".apk"];
            if (dotRange.location != NSNotFound) {
                pkg = [pkg substringToIndex:dotRange.location];
            }
            // 3. 如果pkg后面还有下划线等，去掉
            NSCharacterSet *invalidSet = [NSCharacterSet characterSetWithCharactersInString:@"_ -"];
            NSRange invalidRange = [pkg rangeOfCharacterFromSet:invalidSet];
            if (invalidRange.location != NSNotFound) {
                pkg = [pkg substringToIndex:invalidRange.location];
            }
            return pkg;
        }
    }

    // 4. 兜底：找第一个com.开头到.apk之间的内容
    NSRange comRange = [fileName rangeOfString:@"com."];
    NSRange apkRange = [fileName rangeOfString:@".apk"];
    if (comRange.location != NSNotFound && apkRange.location != NSNotFound && apkRange.location > comRange.location) {
        NSString *pkg = [fileName substringWithRange:NSMakeRange(comRange.location, apkRange.location - comRange.location)];
        // 去掉后缀下划线等
        NSCharacterSet *invalidSet = [NSCharacterSet characterSetWithCharactersInString:@"_ -"];
        NSRange invalidRange = [pkg rangeOfCharacterFromSet:invalidSet];
        if (invalidRange.location != NSNotFound) {
            pkg = [pkg substringToIndex:invalidRange.location];
        }
        return pkg;
    }

    // 5. 兜底：去后缀
    NSString *name = [fileName stringByDeletingPathExtension];
    return name.length > 0 ? name : @"unknown.pkg";
}

// 兼容多种文件名格式的appName提取
- (NSString *)appNameFromTask:(NSDictionary *)task {
    NSString *appName = task[@"appName"];
    if (appName.length > 0) return appName;
    NSString *pkgName = [self pkgNameFromTask:task];
    if (pkgName.length == 0) return @"App";
    NSArray *parts = [pkgName componentsSeparatedByString:@"."];
    if (parts.count > 0) {
        NSString *last = parts.lastObject;
        if (last.length > 0) return last;
    }
    return pkgName.length > 0 ? pkgName : @"App";
}

@end

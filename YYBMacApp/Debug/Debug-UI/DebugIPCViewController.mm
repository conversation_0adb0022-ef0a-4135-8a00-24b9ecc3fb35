//
//  DebugIPCViewController.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/6/26.
//

#import "DebugIPCViewController.h"
#include "YYBMessageSender.h"
#import "YYBApkPackage.h"
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import "LoadingWindowController.h"
#import "MacroUtils.h"
#import "EngineModule.h"

@interface DebugIPCViewController () <NSTableViewDelegate, NSTableViewDataSource>

@property (nonatomic, strong) NSTableView *tableView;
@property (nonatomic, strong) NSArray *items;

@end

@implementation DebugIPCViewController

@synthesize navigationController;

- (void)viewDidLoad {
    [super viewDidLoad];

    _items = @[
        @"手机应用宝",
        @"设置",
        @"日历",
        @"安装本地apk",
        @"aosp_reset", @"shut_down", @"cpu_stop", @"cpu_cont", @"测试并发", @"engineCloseApp", @"卸载（随机）", @"由商店启动引擎"];
    
    // 创建滚动视图容器
    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:self.view.bounds];
    scrollView.hasVerticalScroller = YES;
    scrollView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    
    // 创建表格视图
    _tableView = [[NSTableView alloc] init];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.headerView = nil; // 隐藏表头
    
    // 创建自适应列
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:@"FuncDebugColumn"];
    column.resizingMask = NSTableColumnAutoresizingMask;
    [_tableView addTableColumn:column];
    
    scrollView.documentView = _tableView;
    [self.view addSubview:scrollView];
    
    // 自动调整表格大小
    _tableView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [_tableView sizeLastColumnToFit];
}

- (void)tableViewSelectionDidChange:(NSNotification *)notification {
    NSInteger row = _tableView.selectedRow;
    if (row >= 0 && row < _items.count) {
        NSString *item = _items[row];
        if ([item isEqualToString:@"aosp_reset"]) {
            aospRest();
        } else if ([item isEqualToString:@"shut_down"]) {
            shutDown();
        } else if ([item isEqualToString:@"cpu_stop"]) {
            cpuStop();
        } else if ([item isEqualToString:@"cpu_cont"]) {
            cpuCont();
        } else if ([item isEqualToString:@"测试并发"]) {
            debugAsynLog();
        } else if ([item isEqualToString:@"engineCloseApp"]) {
            engineCloseApp();
        } else if ([item isEqualToString:@"手机应用宝"]) {
            [self appInatall:@"com.tencent.android.qqdownloader" appName:item];
        } else if ([item isEqualToString:@"设置"]) {
            [self appInatall:@"com.android.settings" appName:item];
        } else if ([item isEqualToString:@"日历"]) {
            [self appInatall:@"com.android.calendar" appName:item];
        } else if ([item isEqualToString:@"安装本地apk"]) {
            [self selectAPKFile];
        } else if ([item isEqualToString:@"卸载（随机）"]) {
            [self uninstall];
        } else if ([item isEqualToString:@"由商店启动引擎"]) {
            [self startEngine];
        }
    }
    [_tableView deselectAll:nil];
}

#pragma mark - NSTableViewDataSource
- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return _items.count;
}

- (id)tableView:(NSTableView *)tableView objectValueForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    return _items[row];
}

- (CGFloat)tableView:(NSTableView *)tableView heightOfRow:(NSInteger)row {
    return 45;
}

- (void)appInatall:(NSString *)pkgName appName:(NSString *)appName {
    InstallApkInfo* apkInfo = [[InstallApkInfo alloc] init];
    apkInfo.pkgName = pkgName;
    apkInfo.name = appName;
    [[YYBApkPackage shared] installShotcutWithInfo:apkInfo completion:^(InstallApkInfo * _Nullable apkInfo, NSInteger retCode, NSString * _Nullable errorMessage) {
        if (retCode == 0) {
            [self openApp:apkInfo];
        }
    }];
}

- (void)openApp:(InstallApkInfo *)apkInfo {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSAlert *alert = [[NSAlert alloc] init];
        alert.messageText = [NSString stringWithFormat:@"%@已安装完成", apkInfo.name];
        alert.informativeText = [NSString stringWithFormat:@"在启动台可查看，要打开 %@", apkInfo.name];
        [alert addButtonWithTitle:@"打开"];
        [alert addButtonWithTitle:@"取消"];
        NSModalResponse response =  [alert runModal];
        if (response == NSAlertFirstButtonReturn) {
            [[YYBApkPackage shared] openApp:apkInfo.pkgName];
        }
    });
}

- (void)selectAPKFile {
    NSOpenPanel *panel = [NSOpenPanel openPanel];
    [panel setTitle:@"选择一个 APK 文件"];
    [panel setAllowsMultipleSelection:NO];
    [panel setCanChooseDirectories:NO];
    [panel setCanChooseFiles:YES];
    
    UTType *apkType = [UTType typeWithFilenameExtension:@"apk"];
    panel.allowedContentTypes = @[apkType];

    if ([panel runModal] == NSModalResponseOK) {
        NSURL *fileURL = [[panel URLs] firstObject];
        InstallApkInfo* apkInfo = [[InstallApkInfo alloc] init];
        apkInfo.filePath = fileURL.path;
        [[YYBApkPackage shared] installApp:apkInfo completion:^(InstallApkInfo* _Nullable info, NSInteger retCode, NSString* _Nullable  msg) {
            if (retCode == 0) {
                [[LoadingWindowController sharedController] hide];
                [[YYBApkPackage shared] openApp:info.pkgName];
            } else {
                [[LoadingWindowController sharedController] show:@"安装失败"];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [[LoadingWindowController sharedController] hide];
                });
            }
        }];
        [[LoadingWindowController sharedController] show:@"安装中...."];
    } else {
        NSLog(@"没有选择文件 ");
    }
}

- (void)uninstall {
    NSArray *installed = [[YYBApkPackage shared] installedApps];
    for (InstallApkInfo *info in installed) {
        if ([@[@"手机应用宝", @"日历", @"设置"] containsObject:info.name]) {
            // 避免系统app被删掉
            continue;
        }
        [[LoadingWindowController sharedController] show:@"卸载中"];
        [[YYBApkPackage shared] uninstallApp:info.pkgName completion:^(InstallApkInfo* _Nullable info, NSInteger retCode, NSString* _Nullable  msg) {
            [[LoadingWindowController sharedController] hide];
        }];
    }
}

- (void)startEngine {
    [[EngineModule sharedInstance] startEngine:^(StartEngineResult res, NSString * _Nonnull msg) {
        NSLog(@"启动结果：%d 错误信息：%@", (int)res, msg);
    }];
    
}

- (NSString *)enginePath {
    // Applications下商店路径
    NSString *appStoreEngine = @"/Applications/YYBMacApp.app/Contents/MacOS/yyb_mac.app/Contents/MacOS/yyb_mac";

    if ([[NSFileManager defaultManager] fileExistsAtPath:appStoreEngine]) {
        return appStoreEngine;
    }

    NSString *appSupportDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *enginePath = [appSupportDir stringByAppendingString:[NSString stringWithFormat:@"/com.tencent.yybmac/YYBEngineDownload/yyb_mac.app/Contents/MacOS/yyb_mac"]];
    NSLog(@"enginePath:%@", enginePath);

    if ([[NSFileManager defaultManager] fileExistsAtPath:enginePath]) {
        return enginePath;
    }

    return nil;
}


@end

//
//  DebugAria2DownloadVeiwController.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/12.
//

#import "DebugAria2DownloadVeiwController.h"
#import "YYBAria2DownloadManager.h"
#import "YYBLibAria2ServiceFacade.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "MacroUtils.h"

static NSString *const kLogTag = @"DebugAria2DownloadVeiwController";

@interface DebugAria2DownloadVeiwController () <NSTableViewDataSource, NSTableViewDelegate, YYBAria2TaskDelegate>

@property (nonatomic, strong) NSView *topPanel;
@property (nonatomic, strong) NSTextField *topTitleLabel;
@property (nonatomic, strong) NSTextField *addUrlField;
@property (nonatomic, strong) NSTextField *addFileNameField;
@property (nonatomic, strong) NSTextField *addMd5Field;
@property (nonatomic, strong) NSButton *addTaskButton;
@property (nonatomic, strong) NSButton *pasteClipboardButton;

@property (nonatomic, strong) NSTextField *addUrlField2;
@property (nonatomic, strong) NSTextField *addFileNameField2;
@property (nonatomic, strong) NSTextField *addMd52Field;
@property (nonatomic, strong) NSButton *addTaskButton2;

@property (nonatomic, strong) NSTextField *downloadDirLabel;

@property (nonatomic, strong) NSView *actionPanel;
@property (nonatomic, strong) NSButton *openFolderButton;
@property (nonatomic, strong) NSButton *openApkFolderButton;
@property (nonatomic, strong) NSButton *clearAllButton;
@property (nonatomic, strong) NSButton *clearAllWithFilesButton;

@property (nonatomic, strong) NSTableView *tableView;
@property (nonatomic, strong) NSScrollView *tableScrollView;

@property (nonatomic, strong) NSTextView *logTextView;
@property (nonatomic, strong) NSScrollView *logScroll;
@property (nonatomic, strong) NSButton *clearLogButton;

@property (nonatomic, strong) NSMutableArray<YYBAria2Task *> *tasks;
@property (nonatomic, copy) NSString *downloadDir;

@end

@implementation DebugAria2DownloadVeiwController

@synthesize navigationController;

#pragma mark - 生命周期

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)loadView {
    self.view = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 1280, 760)];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.downloadDir = [[YYBLibAria2ServiceFacade sharedService] currentDownloadDir];
    self.tasks = [NSMutableArray array];
    [self setupTopPanel];
    [self setupActionPanel];
    [self setupTableView];
    [self setupLogPanel];
    [self refreshAllTasks];
    [self registerDelegateForAllTasks];
    [self log:@"[App] DebugAria2DownloadVeiwController页面已加载"];
}

#pragma mark - UI搭建
- (void)setupTopPanel {

    self.topPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.topPanel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.topPanel];
    self.topTitleLabel = [self sectionTitleLabel:@"Aria2下载任务管理区"];
    self.downloadDirLabel = [self labelWithString:[NSString stringWithFormat:@"下载目录: %@", self.downloadDir] fontSize:13 color:[NSColor secondaryLabelColor]];


    // 第一行任务添加栏
    self.addUrlField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addUrlField.placeholderString = @"输入下载URL";
    self.addUrlField.stringValue = @"https://dldir1v6.qq.com/weixin/android/weixin8061android2880_0x28003d34_arm64.apk";
    self.addUrlField.font = [NSFont systemFontOfSize:13];
    self.addUrlField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addUrlField setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.addFileNameField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addFileNameField.placeholderString = @"文件名(可选)";
    self.addFileNameField.font = [NSFont systemFontOfSize:13];
    self.addFileNameField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addFileNameField setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addFileNameField.widthAnchor constraintEqualToConstant:160].active = YES;

    self.addMd5Field = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addMd5Field.placeholderString = @"md5(可选)";
    self.addMd5Field.font = [NSFont systemFontOfSize:13];
    self.addMd5Field.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addMd5Field setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addMd5Field.widthAnchor constraintEqualToConstant:180].active = YES;

    self.pasteClipboardButton = [self buttonWithTitle:@"粘贴剪贴板" action:@selector(pasteClipboard:)];
    self.addTaskButton = [self buttonWithTitle:@"添加任务" action:@selector(addTask:)];
    [self.addTaskButton.widthAnchor constraintEqualToConstant:80].active = YES;

    NSStackView *addTaskStack = [NSStackView stackViewWithViews:@[
        self.addUrlField, self.addFileNameField, self.addMd5Field, self.pasteClipboardButton, self.addTaskButton
    ]];

    addTaskStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    addTaskStack.spacing = 12;
    addTaskStack.alignment = NSLayoutAttributeCenterY;
    addTaskStack.translatesAutoresizingMaskIntoConstraints = NO;

    // 第二行任务添加栏（大文件）
    self.addUrlField2 = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addUrlField2.placeholderString = @"输入大文件URL";
    self.addUrlField2.stringValue = @"https://dl.google.com/developers/android/tm/images/gsi/aosp_arm64-exp-T3B3.230413.003-9957835-d7b72a80.zip?hl=zh-cn";
    self.addUrlField2.font = [NSFont systemFontOfSize:13];
    self.addUrlField2.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addUrlField2 setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];

    self.addFileNameField2 = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addFileNameField2.placeholderString = @"文件名(可选)";
    self.addFileNameField2.font = [NSFont systemFontOfSize:13];
    self.addFileNameField2.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addFileNameField2 setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addFileNameField2.widthAnchor constraintEqualToConstant:160].active = YES;

    self.addMd52Field = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.addMd52Field.placeholderString = @"md5(可选)";
    self.addMd52Field.font = [NSFont systemFontOfSize:13];
    self.addMd52Field.translatesAutoresizingMaskIntoConstraints = NO;
    [self.addMd52Field setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
    [self.addMd52Field.widthAnchor constraintEqualToConstant:180].active = YES;

    self.addTaskButton2 = [self buttonWithTitle:@"添加大文件任务" action:@selector(addTask2:)];
    [self.addTaskButton2.widthAnchor constraintEqualToConstant:120].active = YES;

    NSStackView *addTaskStack2 = [NSStackView stackViewWithViews:@[
        self.addUrlField2, self.addFileNameField2, self.addMd52Field, self.addTaskButton2
    ]];

    addTaskStack2.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    addTaskStack2.spacing = 12;
    addTaskStack2.alignment = NSLayoutAttributeCenterY;
    addTaskStack2.translatesAutoresizingMaskIntoConstraints = NO;

    // 顶部整体Stack
    NSStackView *topStack = [NSStackView stackViewWithViews:@[
        self.topTitleLabel, self.downloadDirLabel, addTaskStack, addTaskStack2
    ]];

    topStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    topStack.spacing = 12;
    topStack.edgeInsets = NSEdgeInsetsMake(16, 16, 8, 16);
    topStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.topPanel addSubview:topStack];

    [NSLayoutConstraint activateConstraints:@[
        [self.topPanel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.topPanel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.topPanel.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [topStack.leadingAnchor constraintEqualToAnchor:self.topPanel.leadingAnchor],
        [topStack.trailingAnchor constraintEqualToAnchor:self.topPanel.trailingAnchor],
        [topStack.topAnchor constraintEqualToAnchor:self.topPanel.topAnchor],
        [topStack.bottomAnchor constraintEqualToAnchor:self.topPanel.bottomAnchor]
    ]];
}

/// 操作区：打开下载目录、清空任务
- (void)setupActionPanel {
    self.actionPanel = [[NSView alloc] initWithFrame:NSZeroRect];
    self.actionPanel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.actionPanel];

    self.openFolderButton = [self buttonWithTitle:@"打开下载目录" action:@selector(openFolder:)];
    self.openApkFolderButton = [self buttonWithTitle:@"打开APK下载目录" action:@selector(openApkFolder:)];
    self.clearAllButton = [self buttonWithTitle:@"清空所有任务" action:@selector(onClearAllTasks:)];
    self.clearAllWithFilesButton = [self buttonWithTitle:@"清空任务及文件" action:@selector(onClearAllTasksWithFiles:)];

    NSStackView *actionStack = [NSStackView stackViewWithViews:@[
        self.openFolderButton, self.openApkFolderButton, self.clearAllButton, self.clearAllWithFilesButton
    ]];
    
    actionStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    actionStack.spacing = 20;
    actionStack.alignment = NSLayoutAttributeCenterY;
    actionStack.edgeInsets = NSEdgeInsetsMake(0, 16, 0, 16);
    actionStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.actionPanel addSubview:actionStack];

    [NSLayoutConstraint activateConstraints:@[
        [self.actionPanel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.actionPanel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.actionPanel.topAnchor constraintEqualToAnchor:self.topPanel.bottomAnchor],
        [actionStack.leadingAnchor constraintEqualToAnchor:self.actionPanel.leadingAnchor],
        [actionStack.trailingAnchor constraintEqualToAnchor:self.actionPanel.trailingAnchor],
        [actionStack.topAnchor constraintEqualToAnchor:self.actionPanel.topAnchor],
        [actionStack.bottomAnchor constraintEqualToAnchor:self.actionPanel.bottomAnchor],
        [self.actionPanel.heightAnchor constraintEqualToConstant:48]
    ]];
}


/// 任务列表

- (void)setupTableView {
    self.tableView = [[NSTableView alloc] initWithFrame:NSZeroRect];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.usesAlternatingRowBackgroundColors = YES;
    [self addTableColumnWithIdentifier:@"taskId" title:@"任务ID" width:100];
    [self addTableColumnWithIdentifier:@"gid" title:@"任务gid" width:80];
    [self addTableColumnWithIdentifier:@"url" title:@"URL" width:120];
    [self addTableColumnWithIdentifier:@"status" title:@"状态" width:100];
    [self addTableColumnWithIdentifier:@"progress" title:@"进度" width:70];
    [self addTableColumnWithIdentifier:@"fileName" title:@"文件名" width:120];
    [self addTableColumnWithIdentifier:@"fileSize" title:@"文件大小" width:80];
    [self addTableColumnWithIdentifier:@"source" title:@"来源" width:40];
    [self addTableColumnWithIdentifier:@"visibility" title:@"感知" width:70];
    [self addTableColumnWithIdentifier:@"priority" title:@"优先级" width:40];
    [self addTableColumnWithIdentifier:@"scheduleStatus" title:@"调度状态" width:80];
    [self addTableColumnWithIdentifier:@"action" title:@"操作" width:320];

    self.tableScrollView = [[NSScrollView alloc] initWithFrame:NSZeroRect];
    self.tableScrollView.documentView = self.tableView;
    self.tableScrollView.hasVerticalScroller = YES;
    self.tableScrollView.autohidesScrollers = YES;
    self.tableScrollView.borderType = NSBezelBorder;
    self.tableScrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.tableScrollView];

    [NSLayoutConstraint activateConstraints:@[
        [self.tableScrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [self.tableScrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],
        [self.tableScrollView.topAnchor constraintEqualToAnchor:self.actionPanel.bottomAnchor constant:8],
        [self.tableScrollView.heightAnchor constraintEqualToConstant:260]
    ]];

    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.tableView.leadingAnchor constraintEqualToAnchor:self.tableScrollView.contentView.leadingAnchor].active = YES;
    [self.tableView.trailingAnchor constraintEqualToAnchor:self.tableScrollView.contentView.trailingAnchor].active = YES;
    [self.tableView.topAnchor constraintEqualToAnchor:self.tableScrollView.contentView.topAnchor].active = YES;
    [self.tableView.bottomAnchor constraintEqualToAnchor:self.tableScrollView.contentView.bottomAnchor].active = YES;

    self.tableView.target = self;
    self.tableView.doubleAction = @selector(onTableViewDoubleClick:);
}


/// 日志区

- (void)setupLogPanel {
    self.logTextView = [[NSTextView alloc] initWithFrame:NSZeroRect];
    self.logTextView.editable = NO;
    self.logTextView.font = [NSFont userFixedPitchFontOfSize:12];
    self.logTextView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

    self.logScroll = [[NSScrollView alloc] initWithFrame:NSZeroRect];
    self.logScroll.documentView = self.logTextView;
    self.logScroll.hasVerticalScroller = YES;
    self.logScroll.autohidesScrollers = YES;
    self.logScroll.borderType = NSBezelBorder;
    self.logScroll.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.logScroll];
    self.clearLogButton = [self buttonWithTitle:@"清空日志" action:@selector(onClearLog:)];

    NSTextField *logTitleLabel = [self sectionTitleLabel:@"日志区"];
    NSStackView *logTitleStack = [NSStackView stackViewWithViews:@[logTitleLabel, self.clearLogButton]];
    logTitleStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    logTitleStack.spacing = 16;
    logTitleStack.alignment = NSLayoutAttributeCenterY;
    logTitleStack.translatesAutoresizingMaskIntoConstraints = NO;

    NSStackView *logStack = [NSStackView stackViewWithViews:@[logTitleStack, self.logScroll]];
    logStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    logStack.spacing = 8;
    logStack.edgeInsets = NSEdgeInsetsMake(8, 8, 8, 8);
    logStack.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:logStack];

    [NSLayoutConstraint activateConstraints:@[
        [logStack.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [logStack.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],
        [logStack.topAnchor constraintEqualToAnchor:self.tableScrollView.bottomAnchor constant:8],
        [logStack.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor constant:-16]
    ]];
}

#pragma mark - TableView

- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.tasks.count;
}

- (NSView *)tableView:(NSTableView *)tableView viewForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    if (row >= self.tasks.count) {
        return nil;
    }
    YYBAria2Task *task = self.tasks[row];
    NSString *identifier = tableColumn.identifier;
    if ([identifier isEqualToString:@"action"]) {
        // 操作按钮：启动、暂停、恢复、取消、打开文件、查找本地文件
        NSMutableArray *btns = [NSMutableArray array];
        if (!task.isStarted) {
            // 未启动时显示“启动”按钮
            NSButton *startBtn = [self buttonWithTitle:@"启动" action:@selector(onStartClicked:)];
            startBtn.tag = row;
            [startBtn.widthAnchor constraintEqualToConstant:60].active = YES;
            [btns addObject:startBtn];
        }
        if (task.isStarted && task.status == YYBAria2TaskStatusActive) {
            NSButton *pauseBtn = [self buttonWithTitle:@"暂停" action:@selector(onPauseClicked:)];
            pauseBtn.tag = row;
            [pauseBtn.widthAnchor constraintEqualToConstant:60].active = YES;
            [btns addObject:pauseBtn];
        }
        if (task.isStarted && task.status == YYBAria2TaskStatusPaused) {
            NSButton *resumeBtn = [self buttonWithTitle:@"恢复" action:@selector(onResumeClicked:)];
            resumeBtn.tag = row;
            [resumeBtn.widthAnchor constraintEqualToConstant:60].active = YES;
            [btns addObject:resumeBtn];
        }
        if (task.status != YYBAria2TaskStatusComplete && task.status != YYBAria2TaskStatusRemoved) {
            NSButton *cancelBtn = [self buttonWithTitle:@"取消" action:@selector(onCancelClicked:)];
            cancelBtn.tag = row;
            [cancelBtn.widthAnchor constraintEqualToConstant:60].active = YES;
            [btns addObject:cancelBtn];
        }
        if (task.status == YYBAria2TaskStatusComplete) {
            NSButton *openBtn = [self buttonWithTitle:@"打开文件" action:@selector(onOpenFileClicked:)];
            openBtn.tag = row;
            [openBtn.widthAnchor constraintEqualToConstant:80].active = YES;
            [btns addObject:openBtn];
        }
        if (task.md5.length > 0) {
            NSButton *findBtn = [self buttonWithTitle:@"查找本地文件" action:@selector(onFindLocalFileClicked:)];
            findBtn.tag = row;
            [findBtn.widthAnchor constraintEqualToConstant:100].active = YES;
            [btns addObject:findBtn];
        }
        if (btns.count == 0) return nil;
        NSStackView *actionView = [NSStackView stackViewWithViews:btns];
        actionView.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        actionView.spacing = 12;
        actionView.alignment = NSLayoutAttributeCenterY;
        return actionView;
    } else {
        NSTextField *cell = [tableView makeViewWithIdentifier:identifier owner:self];
        if (!cell) {
            cell = [[NSTextField alloc] initWithFrame:NSZeroRect];
            cell.identifier = identifier;
            cell.editable = NO;
            cell.bezeled = NO;
            cell.drawsBackground = NO;
            cell.lineBreakMode = NSLineBreakByTruncatingTail;
        }
        if ([identifier isEqualToString:@"taskId"]) {
            cell.stringValue = task.taskId;
        } else if ([identifier isEqualToString:@"gid"]) {
            cell.stringValue = task.gid ?: @"";
        } else if ([identifier isEqualToString:@"url"]) {
            cell.stringValue = task.url;
        } else if ([identifier isEqualToString:@"status"]) {
            cell.stringValue = [self statusDisplayString:task.status isStarted:task.isStarted];
        } else if ([identifier isEqualToString:@"progress"]) {
            cell.stringValue = [NSString stringWithFormat:@"%.1f%%", task.progress * 100];
        } else if ([identifier isEqualToString:@"fileName"]) {
            cell.stringValue = task.fileName;
        } else if ([identifier isEqualToString:@"fileSize"]) {
            cell.stringValue = [self fileSizeString:task.finalFilePath];
        } else if ([identifier isEqualToString:@"source"]) {
            cell.stringValue = [self sourceDisplayString:task.source];
        } else if ([identifier isEqualToString:@"visibility"]) {
            cell.stringValue = [self visibilityDisplayString:task.visibility];
        } else if ([identifier isEqualToString:@"priority"]) {
            cell.stringValue = [self priorityDisplayString:task.priority];
        } else if ([identifier isEqualToString:@"scheduleStatus"]) {
            cell.stringValue = [task scheduleStatusString];
        }
        return cell;
    }
}

- (void)onTableViewDoubleClick:(id)sender {
    NSInteger row = [self.tableView clickedRow];
    NSInteger col = [self.tableView clickedColumn];
    if (row < 0 || col < 0) return;
    NSTableColumn *column = self.tableView.tableColumns[col];
    NSString *identifier = column.identifier;
    YYBAria2Task *task = self.tasks[row];
    NSString *copyText = nil;
    NSString *tip = nil;
    if ([identifier isEqualToString:@"fileName"]) {
        copyText = task.fileName;
        tip = @"文件名已复制";
    } else if ([identifier isEqualToString:@"url"]) {
        copyText = task.url;
        tip = @"URL已复制";
    } else if ([identifier isEqualToString:@"taskId"]) {
        copyText = task.taskId;
        tip = @"任务ID已复制";
    } else if ([identifier isEqualToString:@"gid"]) {
        copyText = task.gid;
        tip = @"任务GID已复制";
    }
    if (copyText.length > 0) {
        NSPasteboard *pb = [NSPasteboard generalPasteboard];
        [pb clearContents];
        [pb setString:copyText forType:NSPasteboardTypeString];
        [self showToast:tip ?: @"已复制"];
    }
}

#pragma mark - 操作按钮事件

// 启动任务
- (void)onStartClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    YYBAria2Task *task = self.tasks[row];
    if (task.isStarted) {
        [self showToast:@"任务已启动"];
        return;
    }
    [self log:[NSString stringWithFormat:@"[UI] 启动任务: %@", task.taskId]];
    __weak typeof(self) weakSelf = self;
    
    task.progressBlock = ^(YYBAria2Task * _Nonnull task, double progress) {
        [weakSelf log:[NSString stringWithFormat:@"[进度] %@: %.1f%%", task.fileName, progress * 100]];
        [weakSelf refreshAllTasks];
    };
    
    task.statusBlock = ^(YYBAria2Task * _Nonnull task, YYBAria2TaskStatus status, NSError * _Nullable error) {
        if (status == YYBAria2TaskStatusComplete) {
            [weakSelf log:[NSString stringWithFormat:@"[完成] %@ 下载完成", task.fileName]];
            [weakSelf showToast:@"任务已下载完成"];
        } else {
            [weakSelf log:[NSString stringWithFormat:@"[状态变化] %@ 状态: %@  error: %@", task.fileName, task.statusString, error]];
            if (status == YYBAria2TaskStatusErrorFatal || status == YYBAria2TaskStatusErrorNetwork) {
                [weakSelf showToast:@"下载失败"];
            }
        }
        [weakSelf refreshAllTasks];
    };
    [[YYBAria2DownloadManager sharedManager] startTask:task];
    [self refreshAllTasks];
}

- (void)onPauseClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    YYBAria2Task *task = self.tasks[row];
    [self log:[NSString stringWithFormat:@"[UI] 暂停任务: %@", task.taskId]];
    [[YYBAria2DownloadManager sharedManager] pauseTask:task completion:^(BOOL success, NSError * _Nullable error) {
        [self log:success ? @"[Aria2] 暂停成功" : [NSString stringWithFormat:@"[Aria2] 暂停失败: %@", error]];
        [self refreshAllTasks];
    }];
}

- (void)onResumeClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    YYBAria2Task *task = self.tasks[row];
    [self log:[NSString stringWithFormat:@"[UI] 恢复任务: %@", task.taskId]];
    [[YYBAria2DownloadManager sharedManager] resumeTask:task completion:^(BOOL success, NSError * _Nullable error) {
        [self log:success ? @"[Aria2] 恢复成功" : [NSString stringWithFormat:@"[Aria2] 恢复失败: %@", error]];
        [self refreshAllTasks];
    }];
}

- (void)onCancelClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    YYBAria2Task *task = self.tasks[row];
    [self log:[NSString stringWithFormat:@"[UI] 取消任务: %@", task.taskId]];
    [[YYBAria2DownloadManager sharedManager] cancelTask:task deleteFile:YES];
    [self refreshAllTasks];
}

- (void)onOpenFileClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    YYBAria2Task *task = self.tasks[row];
    if (!task.finalFilePath.length || ![[NSFileManager defaultManager] fileExistsAtPath:task.finalFilePath]) {
        [self showToast:@"文件不存在"];
        return;
    }
    NSURL *fileUrl = [NSURL fileURLWithPath:task.finalFilePath];
    [[NSWorkspace sharedWorkspace] activateFileViewerSelectingURLs:@[fileUrl]];
    [self log:[NSString stringWithFormat:@"[UI] 打开文件所在目录: %@", task.finalFilePath]];
}

- (void)onFindLocalFileClicked:(NSButton *)btn {
    NSInteger row = btn.tag;
    if (row < 0 || row >= self.tasks.count) return;
    YYBAria2Task *task = self.tasks[row];
    if (!task.md5.length) {
        [self showToast:@"md5为空"];
        return;
    }
    YYBAria2Task *localTask = [[YYBAria2DownloadManager sharedManager] findLocalCompleteFileWithMD5:task.md5];
    if (localTask.finalFilePath.length > 0) {
        [self showToast:@"本地文件已找到"];
        [self log:[NSString stringWithFormat:@"[Aria2] 通过md5查找到本地文件: %@", localTask.finalFilePath]];
        [[NSWorkspace sharedWorkspace] openURL:[NSURL fileURLWithPath:localTask.finalFilePath]];
    } else {
        [self showToast:@"未找到本地文件"];
        [self log:@"[Aria2] 未找到本地文件"];
    }
}

#pragma mark - 注册代理

- (void)registerDelegateForAllTasks {
    for (YYBAria2Task *task in self.tasks) {
        task.delegate = self;
    }
}

#pragma mark - 任务添加

- (void)addTask:(id)sender {
    NSString *url = self.addUrlField.stringValue;
    NSString *fileName = self.addFileNameField.stringValue;
    NSString *md5 = self.addMd5Field.stringValue;
    if (url.length == 0) {
        [self showToast:@"URL不能为空"];
        return;
    }
    [self log:[NSString stringWithFormat:@"[UI] 添加任务: %@, fileName=%@, md5=%@", url, fileName, md5]];
    YYBAria2Task *task = [[YYBAria2DownloadManager sharedManager] createDownloadTaskWithURL:url
                                                                                    destDir:nil
                                                                                   fileName:fileName.length > 0 ? fileName : nil
                                                                                        md5:md5.length > 0 ? md5 : nil];
    task.source = YYBAria2TaskSourceDebug;
    if (!task) {
        [self showToast:@"任务创建失败"];
        [self log:@"[Aria2] 任务创建失败"];
        return;
    }
    // 判断是否已存在
    BOOL alreadyExists = NO;
    for (YYBAria2Task *t in self.tasks) {
        if ([t.taskId isEqualToString:task.taskId]) {
            alreadyExists = YES;
            break;
        }
    }
    if (alreadyExists) {
        [self showToast:@"任务已存在"];
        [self log:[NSString stringWithFormat:@"[Aria2] 任务已存在: %@", task.taskId]];
        return;
    }
    [self.tasks addObject:task];
    task.delegate = self;
    [self log:[NSString stringWithFormat:@"[Aria2] 任务已创建: %@", task.taskId]];
    [self refreshAllTasks];
}

- (void)addTask2:(id)sender {
    NSString *url = self.addUrlField2.stringValue;
    NSString *fileName = self.addFileNameField2.stringValue;
    NSString *md5 = self.addMd52Field.stringValue;
    if (url.length == 0) {
        [self showToast:@"URL不能为空"];
        return;
    }
    [self log:[NSString stringWithFormat:@"[UI] 添加大文件任务: %@, fileName=%@, md5=%@", url, fileName, md5]];
    YYBAria2Task *task = [[YYBAria2DownloadManager sharedManager] createDownloadTaskWithURL:url
                                                                                    destDir:nil
                                                                                   fileName:fileName.length > 0 ? fileName : nil
                                                                                        md5:md5.length > 0 ? md5 : nil];
    task.source = YYBAria2TaskSourceDebug;
    if (!task) {
        [self showToast:@"大文件-任务创建失败"];
        [self log:@"[Aria2] 大文件-任务创建失败"];
        return;
    }
    // 判断是否已存在
    BOOL alreadyExists = NO;
    for (YYBAria2Task *t in self.tasks) {
        if ([t.taskId isEqualToString:task.taskId]) {
            alreadyExists = YES;
            break;
        }
    }
    if (alreadyExists) {
        [self showToast:@"大文件-任务已存在"];
        [self log:[NSString stringWithFormat:@"[Aria2] 大文件-任务已存在: %@", task.taskId]];
        return;
    }
    [self.tasks addObject:task];
    task.delegate = self;
    [self log:[NSString stringWithFormat:@"[Aria2] 大文件-任务已创建: %@", task.taskId]];
    [self refreshAllTasks];
}

- (void)pasteClipboard:(id)sender {
    NSPasteboard *pb = [NSPasteboard generalPasteboard];
    NSString *str = [pb stringForType:NSPasteboardTypeString];
    if (str.length > 0) {
        self.addUrlField.stringValue = str;
    }
}

- (void)openFolder:(id)sender {
    NSURL *url = [NSURL fileURLWithPath:self.downloadDir];
    [[NSWorkspace sharedWorkspace] openURL:url];
}

- (void)openApkFolder:(id)sender {
    NSURL *url = [NSURL fileURLWithPath:[[YYBLibAria2ServiceFacade sharedService] sharedDownloadDir]];
    [[NSWorkspace sharedWorkspace] openURL:url];
}

#pragma mark - 清空任务相关

- (void)onClearAllTasks:(id)sender {
    [self showConfirmAlertWithTitle:@"清空所有任务"
                            message:@"确定要清空所有任务吗？（不会删除本地文件）"
                        confirmText:@"清空"
                          onConfirm:^{
        [self log:@"[UI] 清空所有任务"];
        [[YYBAria2DownloadManager sharedManager] clearAllTasksDeleteFiles:NO];
        [self.tasks removeAllObjects];
        [self refreshAllTasks];
        [self showToast:@"所有任务已清空"];
    }];
}

- (void)onClearAllTasksWithFiles:(id)sender {
    [self showConfirmAlertWithTitle:@"清空所有任务及文件"
                            message:@"确定要清空所有任务及其本地文件吗？此操作不可恢复！"
                        confirmText:@"清空并删除文件"
                          onConfirm:^{
        [self log:@"[UI] 清空所有任务及文件"];
        [[YYBAria2DownloadManager sharedManager] clearAllTasksDeleteFiles:YES];
        [self.tasks removeAllObjects];
        [self refreshAllTasks];
        [self showToast:@"所有任务及文件已清空"];
    }];
}

- (void)showConfirmAlertWithTitle:(NSString *)title
                          message:(NSString *)message
                      confirmText:(NSString *)confirmText
                        onConfirm:(void(^)(void))onConfirm
{
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = title ?: @"确认操作";
    alert.informativeText = message ?: @"";
    [alert addButtonWithTitle:confirmText ?: @"确定"];
    [alert addButtonWithTitle:@"取消"];
    [alert beginSheetModalForWindow:self.view.window completionHandler:^(NSModalResponse returnCode) {
        if (returnCode == NSAlertFirstButtonReturn) {
            if (onConfirm) onConfirm();
        }
    }];
}

#pragma mark - 日志

static inline void dispatch_async_on_main_queue(dispatch_block_t block) {
    if ([NSThread isMainThread]) {
        block();
    } else {
        dispatch_async(dispatch_get_main_queue(), block);
    }
}

- (void)onClearLog:(id)sender {
    self.logTextView.string = @"";
}

- (void)log:(NSString *)msg {
    dispatch_async_on_main_queue( ^{
        NSString *old = self.logTextView.string ?: @"";
        NSString *logString = [old stringByAppendingFormat:@"%@\n", msg];
        self.logTextView.string = logString;
        [self.logTextView scrollRangeToVisible:NSMakeRange(self.logTextView.string.length, 0)];
    });
}

#pragma mark - 工具方法

- (NSTextField *)sectionTitleLabel:(NSString *)title {
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    label.stringValue = title;
    label.font = [NSFont boldSystemFontOfSize:18];
    label.textColor = [NSColor labelColor];
    label.editable = NO;
    label.bezeled = NO;
    label.drawsBackground = NO;
    label.selectable = NO;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (NSTextField *)labelWithString:(NSString *)str fontSize:(CGFloat)size color:(NSColor *)color {
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    label.stringValue = str;
    label.font = [NSFont systemFontOfSize:size];
    label.textColor = color;
    label.editable = NO;
    label.bezeled = NO;
    label.drawsBackground = NO;
    label.selectable = NO;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (NSButton *)buttonWithTitle:(NSString *)title action:(SEL)action {
    NSButton *btn = [NSButton buttonWithTitle:title target:self action:action];
    btn.bezelStyle = NSBezelStyleRounded;
    btn.translatesAutoresizingMaskIntoConstraints = NO;
    [btn.widthAnchor constraintGreaterThanOrEqualToConstant:60].active = YES;
    return btn;
}

- (void)addTableColumnWithIdentifier:(NSString *)identifier title:(NSString *)title width:(CGFloat)width {
    NSTableColumn *col = [[NSTableColumn alloc] initWithIdentifier:identifier];
    col.title = title;
    col.width = width;
    col.resizingMask = NSTableColumnUserResizingMask;
    [self.tableView addTableColumn:col];
}

// 支持显示“未启动”状态
- (NSString *)statusDisplayString:(YYBAria2TaskStatus)status isStarted:(BOOL)isStarted {
    NSString *currentStatus = [self statusDisplayString:status];
    if (isStarted) {
        return currentStatus;
    }
    
    return [NSString stringWithFormat:@"未启动(%@)", currentStatus];
}

- (NSString *)statusDisplayString:(YYBAria2TaskStatus)status {
    switch (status) {
        case YYBAria2TaskStatusPending: return @"待下发";
        case YYBAria2TaskStatusActive: return @"下载中";
        case YYBAria2TaskStatusPaused: return @"已暂停";
        case YYBAria2TaskStatusComplete: return @"已完成";
        case YYBAria2TaskStatusErrorNetwork: return @"网络出错";
        case YYBAria2TaskStatusErrorFatal: return @"下载出错";
        case YYBAria2TaskStatusRemoved: return @"已删除";
        case YYBAria2TaskStatusWaitingForNetwork: return @"等网络";
        case YYBAria2TaskStatusWaitingForService: return @"等服务";
        case YYBAria2TaskStatusRetrying: return @"重试中";
        default: return @"未知";
    }
}

- (NSString *)sourceDisplayString:(YYBAria2TaskSource)source {
    switch (source) {
        case YYBAria2TaskSourceStore: return @"商店";
        case YYBAria2TaskSourceWeb: return @"Web";
        case YYBAria2TaskSourceDebug: return @"Debug";
        default: return @"未知";
    }
}

- (NSString *)visibilityDisplayString:(YYBAria2TaskVisibility)visibility {
    switch (visibility) {
        case YYBAria2TaskVisibilityUser: return @"用户感知";
        case YYBAria2TaskVisibilitySilent: return @"静默";
        default: return @"未知";
    }
}

- (NSString *)priorityDisplayString:(YYBAria2TaskPriority)priority {
    switch (priority) {
        case YYBAria2TaskPriorityHigh: return @"高";
        case YYBAria2TaskPriorityNormal: return @"中";
        case YYBAria2TaskPriorityLow: return @"低";
        default: return @"未知";
    }
}

- (NSString *)fileSizeString:(NSString *)filePath {
    if (!filePath.length) return @"";
    NSDictionary *attr = [[NSFileManager defaultManager] attributesOfItemAtPath:filePath error:nil];
    unsigned long long size = [attr[NSFileSize] unsignedLongLongValue];
    if (size == 0) return @"";
    return [NSByteCountFormatter stringFromByteCount:size countStyle:NSByteCountFormatterCountStyleFile];
}

- (void)showToast:(NSString *)msg {
    if (!msg.length) return;
    NSView *parent = self.view;
    NSTextField *toast = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 200, 36)];
    toast.stringValue = msg;
    toast.alignment = NSTextAlignmentCenter;
    toast.font = [NSFont boldSystemFontOfSize:15];
    toast.textColor = [NSColor whiteColor];
    toast.backgroundColor = [[NSColor blackColor] colorWithAlphaComponent:0.8];
    toast.bordered = NO;
    toast.editable = NO;
    toast.selectable = NO;
    toast.wantsLayer = YES;
    toast.layer.cornerRadius = 8;
    toast.layer.masksToBounds = YES;
    NSArray<NSLayoutConstraint *> * constraints = @[
        [toast.centerXAnchor constraintEqualToAnchor:parent.centerXAnchor],
        [toast.bottomAnchor constraintEqualToAnchor:parent.bottomAnchor constant:-80],
        [toast.widthAnchor constraintGreaterThanOrEqualToConstant:120],
        [toast.heightAnchor constraintEqualToConstant:36]
    ];
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [parent addSubview:toast];
        toast.translatesAutoresizingMaskIntoConstraints = NO;
        [NSLayoutConstraint activateConstraints:constraints];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [toast removeFromSuperview];
    });
}

#pragma mark - 任务刷新

- (void)refreshAllTasks {
    // 直接用 manager 的 allTasks，避免页面和后台不同步
    self.tasks = [[[YYBAria2DownloadManager sharedManager] allTasks] mutableCopy];
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [self.tableView reloadData];
    });
}

#pragma mark - YYBAria2TaskDelegate

- (void)yybAria2Task:(YYBAria2Task *)task didUpdateProgress:(double)progress {
    [self log:[NSString stringWithFormat:@"[Delegate] 进度: %@ %.1f%%", task.fileName, progress * 100]];
    [self refreshAllTasks];
}

- (void)yybAria2Task:(YYBAria2Task *)task didChangeStatus:(NSInteger)status error:(NSError *)error {
    if (status == YYBAria2TaskStatusComplete) {
        [self log:[NSString stringWithFormat:@"[完成] %@ 下载完成", task.fileName]];
        [self showToast:@"任务已下载完成"];
    } else {
        [self log:[NSString stringWithFormat:@"[状态变化] %@ 状态: %@  error: %@", task.fileName, task.statusString, error]];
        if (status == YYBAria2TaskStatusErrorFatal || status == YYBAria2TaskStatusErrorNetwork) {
            [self showToast:@"下载失败"];
        }
    }
    [self refreshAllTasks];
    
}

@end

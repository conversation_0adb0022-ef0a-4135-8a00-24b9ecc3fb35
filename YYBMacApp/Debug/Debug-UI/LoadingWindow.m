//
//  LoadingWindow.m
//  YYBMacApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/5.
//

#import <Foundation/Foundation.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/ResHubCommonDefines.h>
#import "LoadingWindow.h"
#import "EngineDownloadHelper.h"
#import "EngineInfoCenter.h"
#import "EngineDownloadProxy.h"


@interface LoadingWindow()
@property (strong) NSProgressIndicator *progressBar;
@property (strong) NSImageView *iconView;
@property (strong) NSTextField *textField;
@property (nonatomic, strong) NSTextField *descTextFiled;
@property (nonatomic) Boolean isEngingDownloadCompleted;
@property (nonatomic) Boolean hasVmsDownloadProgress;
@property (nonatomic) Boolean hasEnginDownloadProgress;
@property (nonatomic) Boolean isVmsDownloadCompleted;
@property (nonatomic, strong) NSNumber* vmsProgress;
@property (nonatomic, strong) NSNumber* engineProgress;
@end

@implementation LoadingWindow

- (instancetype)initWithContentRect:(NSRect)contentRect {
    self = [super initWithContentRect:CGRectMake(500,  500, 300, 220)
                          styleMask:NSWindowStyleMaskTitled | NSWindowStyleMaskClosable
                            backing:NSBackingStoreBuffered
                              defer:NO];
    
    if (self) {
        // 强制设置窗口尺寸
       [self setMovableByWindowBackground:YES]; // 允许通过背景拖动窗口
       [self setupUI];
    }
    [self setTitleVisibility:NSWindowTitleHidden];
    [self setTitlebarAppearsTransparent:YES];
    [[self standardWindowButton:NSWindowCloseButton] setHidden:YES];
    [[self standardWindowButton:NSWindowMiniaturizeButton] setHidden:YES];
    [[self standardWindowButton:NSWindowZoomButton] setHidden:YES];
    [self center];
    [self initParams];
    [self registerVmsDownloadCallback];
    [self registerEngineDownloadCallback];
    [self setProgress:0];
    return self;
}

- (void)setupUI{
    // 窗口基础配置
    self.level = NSStatusWindowLevel;
    
    // 创建顶部图标区域 (100x100)
    _iconView = [[NSImageView alloc] initWithFrame:NSMakeRect(100, 110, 100, 100)];
    _iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    
    // 创建顶部文案 (宽度匹配窗口)
    _textField = [[NSTextField alloc] initWithFrame:NSMakeRect(50, 70, 200, 20)];
    _textField.stringValue = @"Processing...";
    _textField.editable = NO;
    _textField.bezeled = NO;
    _textField.drawsBackground = NO;
    _textField.alignment = NSTextAlignmentCenter;
    
    // 创建描述文案
    _descTextFiled = [[NSTextField alloc] initWithFrame:NSMakeRect(50, 55, 200, 20)];
    _descTextFiled.editable = NO;
    _descTextFiled.bezeled = NO;
    _descTextFiled.drawsBackground = NO;
    _descTextFiled.font = [NSFont systemFontOfSize:10];
    _descTextFiled.alignment = NSTextAlignmentCenter;
    
    // 创建进度条 (宽度匹配窗口)
    _progressBar = [[NSProgressIndicator alloc] initWithFrame:NSMakeRect(50, 30, 200, 20)];
    _progressBar.style = NSProgressIndicatorStyleBar;
    _progressBar.indeterminate = NO;
    _progressBar.minValue = 0;
    _progressBar.maxValue = 100;
    _progressBar.doubleValue = 30; // 默认进度
    
    // 添加到内容视图
    [self.contentView addSubview:_iconView];
    [self.contentView addSubview:_textField];
    [self.contentView addSubview:_descTextFiled];
    [self.contentView addSubview:_progressBar];
}

#pragma mark - 配置方法
- (void)setProgressColor:(NSColor *)color {
    _progressBar.wantsLayer = YES;
    _progressBar.layer.backgroundColor = [[NSColor clearColor] CGColor];
    // 设置进度条前景色
    if (color) {
        [_progressBar setControlTint:NSDefaultControlTint]; // 使用默认色调
        [_progressBar setNeedsDisplay:YES];
    }
    // 自定义绘制前景色（如果需要更灵活的颜色控制）
    [_progressBar setNeedsDisplay:YES];

}

- (void)setTopIcon:(NSImage *)icon {
    _iconView.image = icon;
}

- (void)setTopText:(NSString *)text {
    _textField.stringValue = text;
}

- (void)setProgress:(NSNumber *)progress {
    _progressBar.doubleValue = progress.doubleValue;
}

- (void)setDescText:(NSString *)descText {
    _descTextFiled.stringValue = descText;
}

#pragma mark - 控制方法
- (void)showWindow {
    // 强制刷新窗口属性
    [self setFrame:[self frame] display:YES animate:NO];
    [self.contentView setFrameSize:self.frame.size];
    [self.contentView setNeedsDisplay:YES];
    [self setLevel:NSNormalWindowLevel]; // 恢复标准窗口层级
    [self makeKeyAndOrderFront:nil];
}

- (void)hideWindow {
    [self orderOut:nil];
}

- (void)close {
    [super close];

}

- (void)initParams {
    self.vmsProgress = @(0);
    self.engineProgress = @(0);
    self.isEngingDownloadCompleted = [[EngineInfoCenter shareInstance] enableUseEngine];
    self.hasEnginDownloadProgress = !self.isEngingDownloadCompleted;
    self.hasVmsDownloadProgress = ![[EngineInfoCenter shareInstance] enableUseVms];
    if (_isEngingDownloadCompleted) {
        self.engineProgress = @(100);
    }
    if (!_hasVmsDownloadProgress) {
        self.vmsProgress = @(100);
    }
}

- (void)updateUI:(NSNumber *)multipliedProgress statusTips:(NSString *)statusTips {
    NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
    formatter.minimumFractionDigits = 1;
    formatter.maximumFractionDigits = 1;
    formatter.numberStyle = NSNumberFormatterDecimalStyle;
    
    NSString *formatted = [formatter stringFromNumber:multipliedProgress];
    NSString* text = [NSString stringWithFormat:@"%@ %@%%",statusTips,formatted];
    [self setTopText:text];
    [self setProgress:multipliedProgress];
}

-(void)registerEngineDownloadCallback {
    [[EngineDownloadHelper sharedInstance] registerDownloadCallback:^(NSNumber * _Nonnull progress, EngineDownloadStatus status) {
        
        NSString* statusTips;
        switch (status) {
            case NOT_DOWNLOADED:
                statusTips = @"引擎下载中...";
                break;
            case DOWNLOADED:
                statusTips = @"引擎解压中...";
                break;
            case UNZIPPED:
                statusTips = @"引擎已就绪";
                self.isEngingDownloadCompleted = YES;
                break;
            case ENGINE_DOWNLOAD_FAILED:
                statusTips = @"引擎下载失败";
            default:
                statusTips = @"引擎未下载";
                break;
        }
        NSNumber *multipliedProgress = @(progress.doubleValue * 100);
        if (multipliedProgress >= 0) {
            self.engineProgress = multipliedProgress;
            if (!self.hasVmsDownloadProgress || [self.engineProgress intValue] < [self.vmsProgress intValue]) {
                [self updateUI:multipliedProgress statusTips:statusTips];
            }
        }
        
        if (self.isEngingDownloadCompleted == true && [[EngineInfoCenter shareInstance] enableUseVms]) {
            [self hideWindow];
        }
       
    }];
}

-(void)registerVmsDownloadCallback {
    [[EngineDownloadHelper sharedInstance] registerVmsDownloadCallback:^(NSNumber * _Nonnull progress, NSString * _Nonnull downloadStatus, ResHubLocalResStatus reshubStatus) {
        
        NSNumber *multipliedProgress = @(progress.doubleValue * 100);
            
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        formatter.minimumFractionDigits = 1;
        formatter.maximumFractionDigits = 1;
        formatter.numberStyle = NSNumberFormatterDecimalStyle;

        
        if (multipliedProgress >= 0) {
            self.vmsProgress = multipliedProgress;
            if (!self.hasEnginDownloadProgress || [self.vmsProgress intValue] < [self.engineProgress intValue]) {
                [self updateUI:multipliedProgress statusTips:[NSString stringWithFormat:@"引擎%@...", downloadStatus]];
            }
        }
        [self setDescText:[self getReshubStr:reshubStatus]];
        if (self.isEngingDownloadCompleted == true && [downloadStatus isEqual:@"已就绪"]) {
            [self hideWindow];
        }
        
    }];
}



- (NSString*)getReshubStr:(ResHubLocalResStatus)reshubStatus {
    switch (reshubStatus) {
        case ResHubLocalResStatusGOOD:
            return @"vms状态:正常";
        case ResHubLocalResStatusNeedDownload:
            return @"vms状态:待下载";
        case ResHubLocalResStatusNeedUpdate:
            return @"vms状态:待更新";
        case ResHubLocalResStatusDisabled:
            return @"vms状态:资源禁用";
        case ResHubLocalResStatusFileInvalid:
            return @"vms状态:文件不可用，重新下载";
        case ResHubLocalResStatusNotExist:
            return @"vms状态:未找到资源";
        case ResHubLocalResStatusLocalOnly:
            return @"vms状态:本地资源有效，远端异常";
        case ResHubLocalResStatusLocalUnknow:
            return @"vms状态:未知错误";
        default:
            return @"vms状态:未知状态";
    }
}

@end

//
//  YYBMacApp
//
//  Created by 凌刚 on 2025/5/29.
//
#import "DebugNotificationTestViewController.h"
#import "NotificationCenter.h"

@interface DebugNotificationTestViewController ()

@property (nonatomic, strong) NSView *contentView;
@property (nonatomic, strong) NSButton *testButton;
@property (nonatomic, strong) NSTextField *countLabel;
@property (nonatomic, strong) NSTextField *clickInfoLabel;

@end

@implementation DebugNotificationTestViewController

@synthesize navigationController;

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // 创建安全区域容器视图
    _contentView = [[NSView alloc] initWithFrame:self.view.bounds];
    _contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:_contentView];
    
    // 添加自动布局约束（考虑安全区域）
    [NSLayoutConstraint activateConstraints:@[
        [_contentView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [_contentView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],
        [_contentView.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor],
        [_contentView.trailingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.trailingAnchor]
    ]];
    
    // 创建垂直堆栈视图
    NSStackView *stackView = [[NSStackView alloc] init];
    stackView.orientation = NSUserInterfaceLayoutOrientationVertical;
    stackView.alignment = NSLayoutAttributeCenterX; // 水平居中对齐
    stackView.distribution = NSStackViewDistributionEqualSpacing;
    stackView.spacing = 30;
    stackView.translatesAutoresizingMaskIntoConstraints = NO;
    [_contentView addSubview:stackView];
    
    // 添加约束：堆栈视图在容器中居中
    [NSLayoutConstraint activateConstraints:@[
        [stackView.leadingAnchor constraintEqualToAnchor:_contentView.leadingAnchor constant:20], // 添加左边距
        [stackView.trailingAnchor constraintEqualToAnchor:_contentView.trailingAnchor constant:-20], // 添加右边距
        [stackView.centerYAnchor constraintEqualToAnchor:_contentView.centerYAnchor] // 保持垂直居中
    ]];
    
    // 计次标签
    _countLabel = [NSTextField labelWithString:@"已发送：0次"];
    _countLabel.font = [NSFont systemFontOfSize:18 weight:NSFontWeightMedium];
    [stackView addArrangedSubview:_countLabel];
    
    // 测试按钮
    _testButton = [NSButton buttonWithTitle:@"发送测试通知"
                                     target:self
                                     action:@selector(handleTestClick)];
    _testButton.bezelStyle = NSBezelStyleRounded;
    _testButton.font = [NSFont systemFontOfSize:16];
    
    // 设置按钮尺寸约束
    [_testButton.widthAnchor constraintEqualToConstant:220].active = YES;
    [_testButton.heightAnchor constraintEqualToConstant:45].active = YES;
    [stackView addArrangedSubview:_testButton];
    
    // 添加消息提示区域（显示点击信息）
    _clickInfoLabel = [NSTextField labelWithString:@"用户点击消息将显示在此处"];
    _clickInfoLabel.font = [NSFont systemFontOfSize:14];
    _clickInfoLabel.textColor = [NSColor secondaryLabelColor];
    _clickInfoLabel.alignment = NSTextAlignmentCenter;
    _clickInfoLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [_contentView addSubview:_clickInfoLabel];
    
    // 消息提示标签约束（置于底部）
    [NSLayoutConstraint activateConstraints:@[
        [_clickInfoLabel.leadingAnchor constraintEqualToAnchor:_contentView.leadingAnchor constant:20],
        [_clickInfoLabel.trailingAnchor constraintEqualToAnchor:_contentView.trailingAnchor constant:-20],
        [_clickInfoLabel.bottomAnchor constraintEqualToAnchor:_contentView.bottomAnchor constant:-20],
        [_clickInfoLabel.heightAnchor constraintEqualToConstant:68]
    ]];
    
    [self setupNotifications];
}

- (void)viewDidAppear {
    [super viewDidAppear];
    [self.view layoutSubtreeIfNeeded]; // 强制立即更新布局
}


- (void)handleTestClick {
    self.notificationCount++;
    _countLabel.stringValue = [NSString stringWithFormat:@"已发送：%ld次", self.notificationCount];
    [self sendDebugNotification];
    if (self.onTestButtonClick) self.onTestButtonClick();
}

#pragma mark - 通知发送
- (void)sendDebugNotification {
    NSString *title = [NSString stringWithFormat:@"调试通知%ld", self.notificationCount];
    NSString *payload = [NSString stringWithFormat:@"{\"debug_id\":%ld}", self.notificationCount];
    
    showQtNotification(title.UTF8String,
                       "调试通知内容",
                       0,
                       payload.UTF8String);
}


- (void)setupNotifications {
    __weak typeof(self) weakSelf = self;
    [NotificationCenter shared].notificationClickHandler = ^(NSDictionary *info) {
        NSLog(@"用户点击了消息: %@", info);
        __strong typeof(weakSelf) strongSelf = weakSelf;
        strongSelf->_clickInfoLabel.stringValue = [NSString stringWithFormat:@"%@", info];
    };
}


// 移除监听
- (void)dealloc {
    [NotificationCenter shared].notificationClickHandler = NULL;
}


@end

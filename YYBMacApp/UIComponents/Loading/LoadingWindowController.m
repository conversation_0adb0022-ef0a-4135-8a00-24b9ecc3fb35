//
//  LoadingWindowController.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/7.
//

#import "LoadingWindowController.h"
#import "MacroUtils.h"

@interface LoadingWindowController ()
@property (nonatomic, strong) NSProgressIndicator *indicator;
@property (nonatomic, strong) NSTextField *messageInfo;
@end

@implementation LoadingWindowController

+ (instancetype)sharedController {
    static LoadingWindowController *sharedController = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedController = [[self alloc] init];
    });
    return sharedController;
}

- (instancetype)init {

    NSWindow *window = [[NSWindow alloc] initWithContentRect:NSMakeRect(0, 0, 160, 160)
                                                   styleMask:NSWindowStyleMaskTitled
                                                     backing:NSBackingStoreBuffered
                                                       defer:NO];
    window.level = NSFloatingWindowLevel;
    window.opaque = NO;
    window.title = @"GlobalLoadingWindow";
    window.releasedWhenClosed = NO;
    window.styleMask = NSWindowStyleMaskTitled | NSWindowStyleMaskFullSizeContentView;
    window.titleVisibility = NSWindowTitleHidden;
    window.titlebarAppearsTransparent = YES;
    window.movable = NO;
    window.movableByWindowBackground = NO;
    window.hasShadow = YES;
    self = [super initWithWindow:window];
    if (self) {
        [self setupContent];
    }
    return self;
}

- (void)setupContent {
    NSView *contentView = self.window.contentView;
    CGFloat windowWidth = contentView.bounds.size.width;
    CGFloat windowHeight = contentView.bounds.size.height;
    
    CGFloat indicatorWidth = 50;
    CGFloat indicatorHeight = 50;
    CGFloat labelHeight = 40;
    CGFloat labelWidth = windowWidth;
    CGFloat spacing = 10;
    
    CGFloat totalHeight = indicatorHeight + spacing + labelHeight;
    CGFloat startY = (windowHeight - totalHeight) / 2;
    
    self.indicator = [[NSProgressIndicator alloc] initWithFrame:NSMakeRect((windowWidth-indicatorWidth)/2, startY + labelHeight + spacing, indicatorWidth, indicatorHeight)];
    self.indicator.style = NSProgressIndicatorStyleBar;
    self.indicator.controlSize = NSControlSizeRegular;
    self.indicator.displayedWhenStopped = NO;
    [contentView addSubview:self.indicator];
    
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSMakeRect(0, startY, labelWidth, labelHeight)];
    [label setStringValue:@"Loading..."];
    [label setFont:[NSFont systemFontOfSize:15]];
    [label setAlignment:NSTextAlignmentCenter];
    [label setBezeled:NO];
    [label setDrawsBackground:NO];
    [label setEditable:NO];
    [label setSelectable:NO];
    self.messageInfo = label;
    [contentView addSubview:label];
}
- (void)show:(NSString *)msg {
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [self.window center];
        [self showWindow:nil];
        [self.indicator startAnimation:nil];
        self.messageInfo.stringValue = msg;
        [NSApp activateIgnoringOtherApps:YES];
    });
}

- (void)hide {
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        [self.indicator stopAnimation:nil];
        self.messageInfo.stringValue = @"";
        [self close];
    });
}

@end

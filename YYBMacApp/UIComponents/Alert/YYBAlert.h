//
//  YYBAlert.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/21.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBAlert : NSObject

+ (void)showAlert:(NSString *)title message:(nullable NSString *)message;
+ (void)showAlert:(NSAlertStyle)style title:(nullable NSString *)title message:(nullable NSString *)message;

/// 通用确认/取消弹窗（主线程回调，返回YES表示用户选择confirm，NO表示取消）- 异步弹窗
+ (void)showConfirmAlertWithStyle:(NSAlertStyle)style
                            title:(NSString *)title
                          message:(nullable NSString *)message
                     confirmTitle:(NSString *)confirmTitle
                      cancelTitle:(NSString *)cancelTitle
                       completion:(void(^)(BOOL confirmed))completion;

/// 通用确认/取消弹窗（主线程回调，返回YES表示用户选择confirm，NO表示取消）- 同步弹窗
+ (BOOL)showConfirmAlertWithStyle:(NSAlertStyle)style
                            title:(NSString *)title
                          message:(nullable NSString *)message
                     confirmTitle:(NSString *)confirmTitle
                      cancelTitle:(NSString *)cancelTitle;

@end

NS_ASSUME_NONNULL_END

//
//  YYBAlert.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/21.
//

#import "YYBAlert.h"
#import "MacroUtils.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kLogTag = @"YYBAria2TaskSQLStore";


@implementation YYBAlert

+ (void)showAlert:(NSString *)title message:(NSString *)message {
    [self showAlert:NSAlertStyleInformational title:title message:message];
}

+ (void)showAlert:(NSAlertStyle)style title:(NSString *)title message:(NSString *)message {
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        NSAlert *alert = [[NSAlert alloc] init];
        alert.messageText = title ?: @"";
        alert.alertStyle = style;
        alert.informativeText = message ?: @"";
        [alert addButtonWithTitle:@"确定"];
        [alert runModal];
    });
}

/// 通用确认/取消弹窗（主线程回调，返回YES表示用户选择confirm，NO表示取消）
+ (void)showConfirmAlertWithStyle:(NSAlertStyle)style
                            title:(NSString *)title
                          message:(nullable NSString *)message
                     confirmTitle:(NSString *)confirmTitle
                      cancelTitle:(NSString *)cancelTitle
                       completion:(void(^)(BOOL confirmed))completion {
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        NSAlert *alert = [[NSAlert alloc] init];
        alert.messageText = title ?: @"";
        alert.informativeText = message ?: @"";
        alert.alertStyle = style;
        [alert addButtonWithTitle:confirmTitle ?: @"确定"];
        [alert addButtonWithTitle:cancelTitle ?: @"取消"];
        YYBMacLogInfo(kLogTag, @"showConfirmAlert: title=%@, message=%@", title, message);
        NSModalResponse response = [alert runModal];
        BOOL confirmed = (response == NSAlertFirstButtonReturn);
        YYBMacLogInfo(kLogTag, @"showConfirmAlert: userSelected=%@", confirmed ? @"YES" : @"NO");
        if (completion) {
            completion(confirmed);
        }
    });
}


/// 通用确认/取消弹窗（主线程回调，返回YES表示用户选择confirm，NO表示取消）- 同步弹窗
+ (BOOL)showConfirmAlertWithStyle:(NSAlertStyle)style
                            title:(NSString *)title
                          message:(nullable NSString *)message
                     confirmTitle:(NSString *)confirmTitle
                      cancelTitle:(NSString *)cancelTitle {
    __block BOOL confirmed = NO;
     void (^alertBlock)(void) = ^{
         NSAlert *alert = [[NSAlert alloc] init];
         alert.messageText = title ?: @"";
         alert.informativeText = message ?: @"";
         alert.alertStyle = style;
         [alert addButtonWithTitle:confirmTitle ?: @"确定"];
         [alert addButtonWithTitle:cancelTitle ?: @"取消"];
         YYBMacLogInfo(kLogTag, @"showConfirmAlert: title=%@, message=%@", title, message);
         NSModalResponse response = [alert runModal];
         confirmed = (response == NSAlertFirstButtonReturn);
         YYBMacLogInfo(kLogTag, @"showConfirmAlert: userSelected=%@", confirmed ? @"YES" : @"NO");
     };
     if ([NSThread isMainThread]) {
         alertBlock();
     } else {
         // 这里必须是同步
         dispatch_sync(dispatch_get_main_queue(), alertBlock);
     }
     return confirmed;
}

@end

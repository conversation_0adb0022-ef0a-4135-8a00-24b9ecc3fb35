//
//  YYBSystemDialog.m
//  YYBMacApp
//
//  Created by halehuang on 2025/6/20.
//

#import "YYBSystemDialog.h"

@implementation YYBSystemDialog

// 打开系统文件选择对话框
+ (nullable NSURL *)openFileSelectionDialog {
    NSOpenPanel *openPanel = [NSOpenPanel openPanel];
    [openPanel setCanChooseFiles:YES];
    [openPanel setCanChooseDirectories:NO];
    [openPanel setAllowsMultipleSelection:NO];
    
    if ([openPanel runModal] == NSModalResponseOK) {
        return [openPanel URL];
    }
    return nil;
}

// 打开系统文件夹选择对话框
+ (nullable NSURL *)openFolderSelectionDialog {
    NSOpenPanel *openPanel = [NSOpenPanel openPanel];
    [openPanel setCanChooseFiles:NO];
    [openPanel setCanChooseDirectories:YES];
    [openPanel setAllowsMultipleSelection:NO];
    
    if ([openPanel runModal] == NSModalResponseOK) {
        return [openPanel URL];
    }
    return nil;
}

@end

//
//  NotificationCenter.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/5/29.
// NotificationCenter.mm

#import "NotificationCenter.h"
#import <AppKit/AppKit.h>
#import "MacroUtils.h"

@implementation NotificationCenter {
    NSMutableDictionary *_pendingNotifications; // 存储未触发的通知
}

+ (instancetype)shared {
    static NotificationCenter *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
        [instance setupNotificationCenter];
    });
    return instance;
}

#pragma mark - 初始化配置

- (void)setupNotificationCenter {
    _pendingNotifications = [NSMutableDictionary new];
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    center.delegate = self;
    [self requestAuthorization];
}

#pragma mark - 权限管理

- (void)requestAuthorization {
    // 主动请求通知权限
    [[UNUserNotificationCenter currentNotificationCenter]
     requestAuthorizationWithOptions:(UNAuthorizationOptionAlert|UNAuthorizationOptionSound)
     completionHandler:^(BOOL granted, NSError * _Nullable error) {
        if (!granted) {
            NSLog(@"[通知中心] 通知权限未开启，将无法显示系统通知");
            // 主线程弹窗引导用户去系统设置开启通知权限
            DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
                NSAlert *alert = [[NSAlert alloc] init];
                alert.messageText = @"通知权限未开启";
                alert.informativeText = @"请在“系统设置-通知”中为本应用手动开启通知权限，否则无法收到系统通知。";
                [alert addButtonWithTitle:@"知道了"];
                [alert runModal];
            });
        } else {
            NSLog(@"[通知中心] 通知权限已授权");
        }
    }];
}

#pragma mark - 通知发送

- (void)showNotificationWithTitle:(NSString*)title
                      content:(NSString*)content
                        delay:(NSTimeInterval)delay
                     userInfo:(NSDictionary *_Nullable)userInfo {
    // 构建通知内容
    UNMutableNotificationContent *notificationContent = [[UNMutableNotificationContent alloc] init];
    notificationContent.title = title ?: @"";
    notificationContent.body = content ?: @"";
    notificationContent.sound = [UNNotificationSound defaultSound];
    if (userInfo) {
        notificationContent.userInfo = userInfo;
    }
    // 可选：设置categoryIdentifier以支持更多交互
    // notificationContent.categoryIdentifier = @"defaultCategory";

    NSTimeInterval checkDelay = delay > 0 ? delay : 0.5;
    UNTimeIntervalNotificationTrigger *trigger =
        [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:checkDelay repeats:NO];

    // 生成唯一标识符（可根据业务自定义）
    NSString *identifier = [NSString stringWithFormat:@"notify_%.0f_%d",
                            [[NSDate date] timeIntervalSince1970], arc4random()%1000];

    UNNotificationRequest *request =
        [UNNotificationRequest requestWithIdentifier:identifier
                                             content:notificationContent
                                             trigger:trigger];

    [[UNUserNotificationCenter currentNotificationCenter]
     addNotificationRequest:request
     withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            NSLog(@"[通知中心] 通知注册失败：%@", error.localizedDescription);
        } else {
            // 存储待触发通知的payload
            if (userInfo) {
                self->_pendingNotifications[identifier] = userInfo;
            }
            NSLog(@"[通知中心] 通知已添加，identifier: %@", identifier);
        }
    }];
}

#pragma mark - 通知点击响应

// 处理通知点击事件
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void (^)(void))completionHandler {
    NSDictionary *userInfo = response.notification.request.content.userInfo;
    DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
        if (self.notificationClickHandler) {
            self.notificationClickHandler(userInfo ?: @{});
        }
        // 清理已触发的通知缓存
        [self->_pendingNotifications removeObjectForKey:response.notification.request.identifier];
    });
    completionHandler();
}

#pragma mark - Qt接口实现

- (void)showNotificationForQt:(const char*)title
                      content:(const char*)content
                   identifier:(const char*)identifier
                    delaySecs:(NSTimeInterval)delay
                      payload:(const char*)payload {
    // 转换C字符串到NSString（处理空指针）
    NSString *nsTitle = title ? [NSString stringWithUTF8String:title] : @"";
    NSString *nsContent = content ? [NSString stringWithUTF8String:content] : @"";
    NSString *nsIdentifier = identifier ? [NSString stringWithUTF8String:identifier] : nil;
    NSString *nsPayload = payload ? [NSString stringWithUTF8String:payload] : nil;

    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    if (nsIdentifier) {
        userInfo[@"identifier"] = nsIdentifier;
    }
    if (nsPayload) {
        userInfo[@"payload"] = nsPayload;
    }

    [self showNotificationWithTitle:nsTitle
                            content:nsContent
                              delay:delay
                           userInfo:userInfo];
}

@end


// C接口实现
void showQtNotification(const char* title,
                        const char* content,
                        int delaySecs,
                        const char* payload) {
    [[NotificationCenter shared] showNotificationForQt:title
                                               content:content
                                            identifier:"qt_notification"
                                             delaySecs:delaySecs
                                               payload:payload];
}

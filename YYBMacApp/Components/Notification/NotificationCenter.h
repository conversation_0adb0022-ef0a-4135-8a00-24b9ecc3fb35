//
//  NotificationCenter.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/5/29.
//
// NotificationCenter.h
#import <Foundation/Foundation.h>
#import <UserNotifications/UserNotifications.h>

NS_ASSUME_NONNULL_BEGIN

/**
 跨平台系统通知中心（支持Qt/C++调用）
 
 关键特性：
 1. 使用UNUserNotifications框架（macOS 10.14+）
 2. 单例模式管理权限和通知生命周期
 3. 支持延迟触发和点击回调
 4. 提供C接口供Qt工程调用
 */
@interface NotificationCenter : NSObject <UNUserNotificationCenterDelegate>

/// 用户点击通知时的回调Block
@property (nonatomic, copy, nullable) void (^notificationClickHandler)(NSDictionary *userInfo);

+ (instancetype)shared;

/**
 基础通知接口
 @param title 通知标题（支持多语言）
 @param content 通知正文内容
 @param delay 延迟触发时间（秒）
 @param userInfo 自定义透传数据（点击通知时带回）
 */
- (void)showNotificationWithTitle:(NSString*)title
                         content:(NSString*)content
                      delay:(NSTimeInterval)delay
                     userInfo:(NSDictionary *_Nullable)userInfo;

@end

// C接口声明（供纯Qt工程调用）
#ifdef __cplusplus
extern "C" {
#endif
/// Qt调用接口（需保证字符串为UTF8编码）
void showQtNotification(const char* title,
                        const char* content,
                        int delaySecs,
                        const char* payload);
#ifdef __cplusplus
}
#endif

NS_ASSUME_NONNULL_END

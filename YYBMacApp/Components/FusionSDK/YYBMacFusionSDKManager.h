//
//  YYBMacFusionSDKManager.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/6/19.
//

#import <Foundation/Foundation.h>

// YYBMacFusionSDK集成了bugly、大同上报、日志（打印及上传/导出）、qimei、shiply->资源管理、shiply->配置开关、mmkv等核心库并提供C++接口给外部使用
// 其中YYBMacFusionSDK.h： 统一所有sdk的初始化及后台环境、账号切换方法，通过YYBMacFusionSDKManager可以调用
// 其余功能需要使用时，单独引入对应的头文件，详见：YYBMacFusion.h
#import "YYBMacFusionSDK/YYBMacFusion.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacFusionSDKManager : NSObject

/// 统一初始化（日志）
+ (void)setupFustionSDK:(void(^_Nullable)(BOOL success, NSError * _Nullable error))completion;

/// 切换用户
+ (void)switchUser:(nullable NSString *)userId;

/// 切换后台环境
+ (void)switchEnvironment:(BOOL)isRelease;

// 获取reshub资源路径
+ (NSString*)getResHubResourcePath;

// 是否已初始化。注意，使用FusionSDK前，建议先判断sdk是否初始化
+ (BOOL)isInitialied;

@end

NS_ASSUME_NONNULL_END

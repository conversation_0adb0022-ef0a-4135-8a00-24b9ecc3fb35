//
//  ResHubDownloadTask.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/8.
//

#import <Foundation/Foundation.h>
#import <YYBMacFusionSDK/RAFTDownloadProtocol.h>
#import "YYBAria2Task.h"

@protocol RAFTDownloadTaskProtocol;

typedef NS_ENUM(NSUInteger, ResHubResourceDownloadErrorCode) {
    ResHubResourceDownloadErrorCodeInValidUrl, // url不合法
    ResHubResourceDownloadErrorCodeCodeDownloadFail, // 下载失败（不可恢复错误）
    ResHubResourceDownloadErrorCodeCodeWriteFail, // 文件拷贝失败
    ResHubResourceDownloadErrorCodeCodeCancel, // 取消下载
    ResHubResourceDownloadErrorCodeCreateTaskFail, // 创建任务失败
    ResHubResourceDownloadErrorCodeTaskIsDestroyed, // 任务已销毁
    ResHubResourceDownloadErrorCodeNotEnoughDiskSpace // 磁盘空间不足
};

NS_ASSUME_NONNULL_BEGIN

@interface ResHubDownloadTask : NSObject<RAFTDownloadTaskProtocol>

FOUNDATION_EXPORT const NSInteger kDefaultHTTPStatusCode;
FOUNDATION_EXPORT NSString* const kResHubDownloadErrorDomain;

/// 任务初始化
- (instancetype)initWithAria2Task:(nonnull YYBAria2Task*)task andTargetPath:(NSString*)targetPath;

/// 任务启动
- (void)startTaskWithProcessBlock:(nullable RAFTDownloadProgressBlock)progressBlock andCompletedBlock:(nullable RAFTDownloadCompletedBlock)completedBlock;

/// 任务取消
- (BOOL)cancel;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END

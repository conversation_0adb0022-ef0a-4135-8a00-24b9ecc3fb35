//
//  ResRefreshListener.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/13.
//

#import <YYBMacFusionSDK/YYBMacLog.h>
#import "ResRefreshListener.h"
#import "ResHubDownloader.h"
#import "BussinessMessageCenter.h"
#import "IdentifierConstants.h"

static NSString* const kTag = @"ResRefreshListener";

@implementation ResRefreshListener

- (void)onResRefreshed:(NSString *)resId model:(ResHubModel *)resHubModel {
    YYBMacLogInfo(kTag, @"onResRefreshed. resId:%@ model:%@", resId, resHubModel);
    BussinessMessage* msg = [[BussinessMessage alloc] initWithIdentifier:kReshubResRefreshed payload:resHubModel];
    [[BussinessMessageCenter sharedCenter] pushMessage:msg];
}


- (void)onResConfigInfoPullTypeAllFinished:(NSDictionary<NSString *,ResHubModel *> *)resHubModelDict {
    YYBMacLogInfo(kTag, @"onResConfigInfoPullTypeAllFinished.dic:%@", resHubModelDict);
    [[ResHubDownloader sharedInstance] setIsReshubConfigPullFinished:YES];
    BussinessMessage* msg = [[BussinessMessage alloc] initWithIdentifier:kPullReshubConfigFinished payload:resHubModelDict];
    [[BussinessMessageCenter sharedCenter] pushMessage:msg];
}
@end

//
//  ResHubDownloadTask.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/8.
//
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "ResHubDownloadTask.h"
#import "YYBAria2DownloadManager.h"

static NSString* const kTag = @"ResHubDownloadTask";

NSString* const kResHubDownloadErrorDomain = @"ResHubDownloadErrorDomain";
const NSInteger kDefaultHTTPStatusCode = 200;
@interface ResHubDownloadTask ()

@property(nonatomic, strong) YYBAria2Task* task;
@property(nonatomic, nonnull, copy) NSString* targetPath;

@end

@implementation ResHubDownloadTask


- (instancetype)initWithAria2Task:(nonnull YYBAria2Task *)task andTargetPath:(NSString*)targetPath {
    self = [super init];
    if (self) {
        self.task = task;
        self.targetPath = targetPath;
    }
    return self;
}

- (BOOL)cancel {
    YYBMacLogInfo(kTag, @"cancel.");
    // 默认都是取消成功
    [[YYBAria2DownloadManager sharedManager] cancelTask:self.task deleteFile:YES];
    return YES;
}

- (void)startTaskWithProcessBlock:(nullable RAFTDownloadProgressBlock)progressBlock andCompletedBlock:(nullable RAFTDownloadCompletedBlock)completedBlock {
    YYBMacLogInfo(kTag, @"startTaskWithPriority. tasInfo:%@", self.task);
    self.task.progressBlock = ^(YYBAria2Task * _Nonnull task, double progress) {
        progressBlock(task.completedLength, task.totalLength);
    };
    
    
    __weak typeof(self) weakSelf = self;
    self.task.statusBlock = ^(YYBAria2Task * _Nonnull task, YYBAria2TaskStatus status, NSError * _Nullable error) {
        if (status == YYBAria2TaskStatusComplete) {
            if (weakSelf) {
                [weakSelf handleTaskDownloadComplete:task withBlock:completedBlock];
            } else {
                completedBlock(kDefaultHTTPStatusCode, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeTaskIsDestroyed userInfo:@{NSLocalizedDescriptionKey: @"current task is null."}]);
            }
            
        } else if (status == YYBAria2TaskStatusErrorFatal) {
            YYBMacLogError(kTag, @"reshub resource download failed. error:%@ task:%@", error, task);
            NSDictionary *userInfo = error ? error.userInfo : @{NSLocalizedDescriptionKey: @"download failed."};
            completedBlock(kDefaultHTTPStatusCode, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeCodeDownloadFail userInfo:userInfo]);
        } else if (status == YYBAria2TaskStatusRemoved) {
            YYBMacLogInfo(kTag, @"reshub resource download task is canceled. task:%@", task);
            completedBlock(kDefaultHTTPStatusCode, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeCodeCancel userInfo:@{NSLocalizedDescriptionKey: @"download task is canceled."}]);
        } else {
            YYBMacLogInfo(kTag, @"reshub resource status changed. status: %@ task:%@", task.statusString, task);
        }
    };
    
    
    
    [[YYBAria2DownloadManager sharedManager] startTask:self.task];
}

-(void)handleTaskDownloadComplete:(nonnull YYBAria2Task*)task withBlock:(nullable RAFTDownloadCompletedBlock)completedBlock {
    NSError *error = nil;
    BOOL success = [[NSFileManager defaultManager] moveItemAtPath:task.finalFilePath toPath:self.targetPath error:&error];
    if (success) {
        YYBMacLogInfo(kTag, @"reshub resource download success. task:%@", task);
        completedBlock(kDefaultHTTPStatusCode, nil);
        return;
    }
    
    YYBMacLogInfo(kTag, @"reshub resource move failed. task:%@ error:%@ sourcePath:%@ targetPath:%@", task, error, task.finalFilePath, self.targetPath);
    if (error) {
        completedBlock(kDefaultHTTPStatusCode, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeCodeWriteFail userInfo:error.userInfo]);
        return;
    }
    
    NSString* errorMsg = [NSString stringWithFormat:@"copy failed.sourcePath:%@ targetPath:%@", task.finalFilePath, self.targetPath];
    completedBlock(kDefaultHTTPStatusCode, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeCodeWriteFail userInfo:@{NSLocalizedDescriptionKey: errorMsg}]);
}


@end


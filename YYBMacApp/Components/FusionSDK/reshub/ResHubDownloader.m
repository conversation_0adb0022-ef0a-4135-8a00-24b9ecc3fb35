//
//  ResHubDownloader.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/8.
//

#import "Reachability.h"
#import "ResHubDownloader.h"
#import "YYBAria2DownloadManager.h"
#import "YYBAria2Task.h"
#import "ResHubDownloadTask.h"

NS_ASSUME_NONNULL_BEGIN


static NSString* const kTag = @"ResHubDownloader";

@interface ResHubDownloader()

@end


@implementation ResHubDownloader

+ (instancetype)sharedInstance {
    static ResHubDownloader* downloader = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        downloader = [[ResHubDownloader alloc] init];
    });
    return downloader;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.isReshubConfigPullFinished = NO;
    }
    return self;
}

- (nullable id<RAFTDownloadTaskProtocol>)downloadWithUrl:(nonnull NSString *)urlString filePath:(nonnull NSString *)filePath priority:(RAFTDownloadPriority)priority progress:(nullable RAFTDownloadProgressBlock)progressBlock completed:(nullable RAFTDownloadCompletedBlock)completedBlock {
    // 1. url 合法性检验
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        if (completedBlock) {
            NSError *error = [self buildErrorWithCode:ResHubResourceDownloadErrorCodeInValidUrl message:@"invalid url"];
            completedBlock(kDefaultHTTPStatusCode, error);
        }
        return nil;
    }
    
    // 2. 创建下载任务
    YYBAria2Task* task = [[YYBAria2DownloadManager sharedManager] createDownloadTaskWithURL:urlString
                                                                                    destDir:nil
                                                                                   fileName:nil
                                                                                        md5:nil
                                                                                 visibility:YYBAria2TaskVisibilitySilent
                                                                                   priority:[self convertTaskPriority:priority]];
    if (!task) {
        NSError *error = [self buildErrorWithCode:ResHubResourceDownloadErrorCodeCreateTaskFail message:@"create task failed."];
        completedBlock(kDefaultHTTPStatusCode, error);
        return nil;
    }
    ResHubDownloadTask* reshubDownloadTask = [[ResHubDownloadTask alloc] initWithAria2Task:task andTargetPath:filePath];
    
    // 3. 启动下载
    [reshubDownloadTask startTaskWithProcessBlock:progressBlock andCompletedBlock:completedBlock];
    return reshubDownloadTask;
}

- (RAFTNetworkStatus)networkStatus {
    Reachability *reach = [Reachability reachabilityForInternetConnection];
    NetworkStatus status = [reach currentReachabilityStatus];
    if (status == NotReachable) {
        return RAFTNetworkStatusNone;
    } else {
        return RAFTNetworkStatusWIFI;
    }
}

/// 优先级转换。高优先级和最高优先级都会立刻启动。低优先级和默认优先级任务需要等待可见任务（用户可感知任务）完成后才会启动
- (YYBAria2TaskPriority)convertTaskPriority:(RAFTDownloadPriority)priority {
    switch (priority) {
        case RAFTDownloadPriorityLow:
            return YYBAria2TaskPriorityLow;
        case RAFTDownloadPriorityNormal:
            return YYBAria2TaskPriorityNormal;
        case RAFTDownloadPriorityHigh:
        case RAFTDownloadPriorityHighest:
            return YYBAria2TaskPriorityHigh;
    }
}

- (NSError *)buildErrorWithCode:(ResHubResourceDownloadErrorCode)code message:(NSString*)message {
    NSDictionary *userInfo = @{NSLocalizedDescriptionKey: message ?: @""};
    return [NSError errorWithDomain:kResHubDownloadErrorDomain code:code userInfo:userInfo];
}



@end

NS_ASSUME_NONNULL_END

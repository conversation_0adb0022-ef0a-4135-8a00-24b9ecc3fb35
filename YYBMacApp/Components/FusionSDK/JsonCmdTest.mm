//
//  HttpTest.m
//  YYBMacFusionSDK
//
//  Created by g<PERSON><PERSON><PERSON> on 2025/8/15.
//  Copyright © 2025 CocoaPods. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "JsonCmdTest.h"

@implementation JsonCmdTest

-(void)testJsonCmd {
    // json命令字请求url格式：https://yybadaccess.sparta.html5.qq.com/v2/dynamicard_pcyyb?scene=discovery
    // 目前基础库中写死了这个域名，后续支持环境切换后再支持其他域名
    // 创建一个请求对象
    YYBMacJsonCmdRequest *request = [[YYBMacJsonCmdRequest alloc] init];
    // api，如dynamicard_pcyyb
    request.api = @"dynamicard_pcyyb";
    // scene，url参数，如discovery
    request.scene = @"discovery";
    // bodyValue，作为请求体中的body的值
    request.bodyValue = @{
        @"bid": @"yybmac",
        @"preview": @NO,
        @"layout": @"",
        @"listS": @{
            @"region": @{
                @"repStr": @[@"CN"]
            },
            @"supplyId": @{
                @"repStr": @[@""]
            },
            @"wx_sdk_version": @{
                @"repStr": @[@""]
            },
            @"installed_list": @{
                @"repStr": @[]
            },
            @"architecture": @{
                @"repStr": @[@"Unknown"]
            },
            @"trace_id": @{
                @"repStr": @[@""]
            }
        },
        @"listI": @{
            @"international": @{
                @"repInt": @[@0]
            },
            @"client_type": @{
                @"repInt": @[@0]
            },
            @"oem_type": @{
                @"repInt": @[@0]
            },
            @"installed_appid_list": @{
                @"repInt": @[]
            },
            @"multi_oaid_switch": @{
                @"repInt": @[@2]
            }
        },
        @"offset": @0,
        @"size": @15,
        @"trace": @NO
    };

    // 发送请求
    [[YYBMacHttp sharedInstance] sendYYBJsonCmd:request listener:self];
}

# pragma mark - IYYBMacHttpListener

// 收到响应，但是response中的ret字段可能不为0，需要业务自行根据ret字段实现业务逻辑
- (void)onJsonCmdResponse:(YYBMacJsonCmdResponse *)response request:(YYBMacJsonCmdRequest *)request {
    NSLog(@"HttpTest onJsonCmdResponse ret:%ld, msg:%@", response.ret, response.msg);
}

// 发生错误
- (void)onJsonCmdFail:(nullable NSError *)error request:(YYBMacJsonCmdRequest *)request {
    NSLog(@"HttpTest onJsonCmdFail: errCode:%ld, error:%@", error.code, error);
}

@end

//
//  ClientType.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/24.
//

#import "ClientType.h"

@implementation NSNumber (ClientType)

- (ClientType)toClientType {
    NSInteger value = [self integerValue];
    switch (value) {
        case ClientTypeUnset:
        case ClientTypeMain:
        case ClientTypeLite:
        case ClientTypeMainWithoutEngine:
        case ClientTypeMac:
        case ClientTypeLiteGame:
        case ClientTypeWeb:
            return (ClientType)value;
        default:
            return ClientTypeUnset;
    }
}

@end

//
//  ClientType.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/24.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, ClientType) {
    ClientTypeUnset = -1,               // 未设置
    ClientTypeMain = 0,                 // pc
    ClientTypeLite = 1,                 // 极速版
    ClientTypeMainWithoutEngine = 2,    // 普通版未安装引擎
    ClientTypeMac = 3,                  // mac
    ClientTypeLiteGame = 4,             // 小游戏版
    ClientTypeWeb = 1000,               // 外渠中心化场景 web
};

@interface NSNumber (ClientType)

- (ClientType)toClientType;

@end

NS_ASSUME_NONNULL_END

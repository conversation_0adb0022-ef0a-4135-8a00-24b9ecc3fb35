//
//  YYBAppConfig.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/22.
//

#import <Foundation/Foundation.h>
#import "ClientType.h"

NS_ASSUME_NONNULL_BEGIN

// 应用配置管理
@interface YYBAppConfig : NSObject

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

// 加载配置
+ (void)loadConfig;

// 渠道号
@property (class, nonatomic, copy, nullable) NSString *channel;
// 客户端类型
@property (class, nonatomic, assign) ClientType clientType;
// 获取App版本号
+ (NSString *)appVersion;
// 获取CFBundleVersion
+ (NSString *)bundleVersion;
// 获取App名称
+ (NSString *)appName;
// 获取App Bundle ID
+ (NSString *)bundleID;
// 是否首次启动（版本维度）
+ (BOOL)isFirstLaunchOfCurrentVersion;
// 是否是Debug模式
+ (BOOL)isDebug;

@end

NS_ASSUME_NONNULL_END

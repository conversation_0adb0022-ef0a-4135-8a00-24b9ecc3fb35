//
//  YYBAppConfig.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/7/22.
//

#import "YYBAppConfig.h"

@implementation YYBAppConfig

static BOOL _isFirstLaunchOfCurrentVersion = NO;
static NSString *_channel = nil;
static ClientType _clientType = ClientTypeMain;

+ (void)loadConfig {
    _isFirstLaunchOfCurrentVersion = [self loadAndSetIsFirstLaunchOfCurrentVersion];
}

+ (NSString *)appVersion {
    return [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
}

+ (NSString *)bundleVersion {
    return [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleVersion"];
}

+ (NSString *)appName {
    return [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleName"];
}

+ (NSString *)bundleID {
    return [[NSBundle mainBundle] bundleIdentifier];
}

+ (BOOL)isFirstLaunchOfCurrentVersion {
    return _isFirstLaunchOfCurrentVersion;
}

+ (BOOL)loadAndSetIsFirstLaunchOfCurrentVersion {
    NSString *currentVersion = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
    NSString *currentBuild = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleVersion"];
    NSString *currentVersionBuild = [NSString stringWithFormat:@"%@.%@", currentVersion, currentBuild];

    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *lastVersionBuild = [defaults stringForKey:@"LastVersionBuild"];

    BOOL first = ![currentVersionBuild isEqualToString:lastVersionBuild];
    if (first) {
        [defaults setObject:currentVersionBuild forKey:@"LastVersionBuild"];
        [defaults synchronize];
    }
    return first;
}

+ (BOOL)isDebug {
#ifdef DEBUG
    return true;
#else
    return false;
#endif
}

+ (NSString *)channel {
    return _channel;
}

+ (void)setChannel:(NSString *)channel {
    _channel = [channel copy];
}

+ (ClientType)clientType {
    return _clientType;
}

+ (void)setClientType:(ClientType)clientType {
    _clientType = clientType;
}

@end

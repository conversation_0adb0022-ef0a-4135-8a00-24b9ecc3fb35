//
//  MessageCenter.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import <Foundation/Foundation.h>
#import "BussinessMessageQueue.h"

NS_ASSUME_NONNULL_BEGIN
typedef void(^MessageHandlerBlock)(BussinessMessage *message);
@interface BussinessMessageCenter : NSObject


+ (instancetype)sharedCenter;

/**
 * 向消息中心推送一条消息。
 * MessageCenter 会将此消息分发给所有订阅了该消息 identifier 的观察者。
 * @param message 要推送的消息对象
 */
- (void)pushMessage:(BussinessMessage *)message;

/**
 * 注册一个观察者来处理特定标识符的消息。
 *
 * @param observer 观察者对象。通常是 self (比如一个 UIViewController)。当该对象被销毁时，其注册的回调会自动被移除。
 * @param identifier 要监听的消息的唯一标识符。
 * @param handler 收到消息后执行的回调 Block。block必须使用weak引用
 */
- (void)registerObserver:(id)observer
          forIdentifier:(NSString *)identifier
            withHandler:(MessageHandlerBlock)handler;

/**
 * 手动为观察者移除其订阅的所有消息回调。
 * 通常不需要手动调用，因为观察者销毁时会自动移除。
 * @param observer 要移除订阅的观察者对象。
 */
- (void)unregisterObserver:(id)observer;


- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END

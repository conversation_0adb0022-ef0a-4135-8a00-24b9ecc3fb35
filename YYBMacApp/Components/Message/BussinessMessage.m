//
//  Message.m
//  YYBMacApp
//
//  Created by bethahua<PERSON> on 2025/8/1.
//

#import "BussinessMessage.h"

@implementation BussinessMessage

- (instancetype)initWithIdentifier:(NSString *)identifier payload:(id)payload {
    if (self = [super init]) {
        _identifier = [identifier copy];
        _payload = payload;
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<Message: %p, identifier: '%@', payload: %@>", self, self.identifier, self.payload];
}

@end

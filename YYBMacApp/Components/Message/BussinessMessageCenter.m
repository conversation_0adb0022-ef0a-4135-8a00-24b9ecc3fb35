//
//  MessageCenter.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import <objc/runtime.h>
#import "BussinessMessageCenter.h"
#import "YYBMacFusionSDK/YYBMacLog.h"

static NSString* const kTag = @"MessageCenter";

@interface _MessageObserverDeallocator : NSObject
@property (nonatomic, weak) id observer;
@property (nonatomic, weak) BussinessMessageCenter *center;
@end

@implementation _MessageObserverDeallocator
- (void)dealloc {
    if (_observer && _center) {
        [_center unregisterObserver:_observer];
        YYBMacLogInfo(kTag, @"[MessageCenter] Observer %@ deallocated. Auto-unregistered.", _observer);
    }
}
@end


@interface BussinessMessageCenter()


@property (nonatomic, strong) BussinessMessageQueue *messageQueue;

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMapTable<id, MessageHandlerBlock> *> *observers;

@end


@implementation BussinessMessageCenter

#pragma mark - Singleton

+ (instancetype)sharedCenter {
    static BussinessMessageCenter *center = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        center = [[super alloc] initInternal];
    });
    return center;
}

- (instancetype)initInternal {
    if (self = [super init]) {
        self.observers = [NSMutableDictionary dictionary];
        self.messageQueue = [[BussinessMessageQueue alloc] init];
        __weak typeof(self) weakSelf = self;
        [self.messageQueue startListeningWithHandler:^(BussinessMessage *msg) {
            [weakSelf dispatchMessage:msg];
        }];
    }
    return self;
}

#pragma mark - Public API

- (void)pushMessage:(BussinessMessage *)message {
    if (message) {
        YYBMacLogInfo(kTag, @"pushMessage:%@", message);
        [self.messageQueue pushMessage:message];
    }
    
}

- (void)registerObserver:(id)observer forIdentifier:(NSString *)identifier withHandler:(MessageHandlerBlock)handler {
    if (!observer || !identifier || !handler) {
        return;
    }
    
    @synchronized (self) {
        NSMapTable<id, MessageHandlerBlock> *handlersForIdentifier = self.observers[identifier];
        if (!handlersForIdentifier) {
            handlersForIdentifier = [NSMapTable mapTableWithKeyOptions:NSPointerFunctionsWeakMemory valueOptions:NSPointerFunctionsStrongMemory];
            self.observers[identifier] = handlersForIdentifier;
        }
        
        [handlersForIdentifier setObject:handler forKey:observer];
        YYBMacLogInfo(kTag, @"registerObserver identifier: %@ observer: %@", identifier, observer);
        
        [self setupDeallocatorForObserver:observer];
    }
}

- (void)unregisterObserver:(id)observer {
    if (!observer) return;
    YYBMacLogInfo(kTag, @"unregisterObserver: %@", observer);
    @synchronized (self) {
        for (NSString *identifier in self.observers.allKeys) {
            NSMapTable *handlers = self.observers[identifier];
            [handlers removeObjectForKey:observer];
        }
    }
}

#pragma mark - Private Methods

- (void)dispatchMessage:(BussinessMessage *)message {
    if (!message.identifier) return;

    NSMapTable<id, MessageHandlerBlock> *handlers;
    @synchronized (self) {
        handlers = [self.observers[message.identifier] copy];
    }
    
    if (handlers.count == 0) return;
    
    // 在主线程分发给所有订阅者
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id observer in handlers) {
            MessageHandlerBlock handler = [handlers objectForKey:observer];
            if (handler) {
                handler(message);
            }
        }
    });
}


- (void)setupDeallocatorForObserver:(id)observer {
    // 使用 observer 的地址作为 key，确保唯一性
    const void *key = (__bridge const void *)observer;
    
    if (objc_getAssociatedObject(observer, key)) {
        // 如果已经关联过，就不再重复关联
        return;
    }
    
    _MessageObserverDeallocator *deallocator = [[_MessageObserverDeallocator alloc] init];
    deallocator.observer = observer;
    deallocator.center = self;
    
    objc_setAssociatedObject(observer, key, deallocator, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
@end


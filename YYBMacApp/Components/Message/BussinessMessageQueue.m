//
//  MessageQueue.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import "BussinessMessageQueue.h"


@interface BussinessMessageQueue()
@property (nonatomic, strong) NSMutableArray *queue;
@property (nonatomic, strong) dispatch_queue_t serialQueue;
@property (nonatomic, strong) dispatch_source_t source;
@property (nonatomic, copy) void(^handler)(BussinessMessage *msg);
@end

@implementation BussinessMessageQueue

- (instancetype)init {
    if (self = [super init]) {
        self.queue = [NSMutableArray array];
        self.serialQueue = dispatch_queue_create("com.handler.messagequeue", DISPATCH_QUEUE_SERIAL);
        
        self.source = dispatch_source_create(DISPATCH_SOURCE_TYPE_DATA_ADD, 0, 0, self.serialQueue);
        dispatch_source_set_event_handler(self.source, ^{
            while (self.queue.count > 0) {
                BussinessMessage *msg = self.queue.firstObject;
                [self.queue removeObjectAtIndex:0];
                // 消息回调
                if (self.handler) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        self.handler(msg);
                    });
                }
            }
        });
    }
    return self;
}


- (void)pushMessage:(BussinessMessage *)msg {
    dispatch_async(self.serialQueue, ^{
        [self.queue addObject:msg];
        // 通知 source 数据已增加
        dispatch_source_merge_data(self.source, 1);
    });
}

- (void)startListeningWithHandler:(void(^)(BussinessMessage *msg))handler {
    self.handler = [handler copy];
    // dispatch_source 创建后默认是挂起状态，必须 resume 后才能接收和处理事件
    dispatch_resume(self.source);
}

- (void)stopListening {
    dispatch_async(self.serialQueue, ^{
        dispatch_source_cancel(self.source);
        self.handler = nil;
    });
}

- (void)dealloc {
    [self stopListening];
}

@end

//
//  YYBSocketDataSender.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import <Foundation/Foundation.h>
#import "SocketDataSender.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 商店为Client的进程间通讯
 *
 * 用于实现商店为Client的进程间通讯的OC类
 */
@interface YYBSocketDataSender : NSObject

/**
 * 进程通信接口。
 * 注意：info参数的NSMutableDictionary仅支持NSString和NSNumber对象。
 *
 * @param action 指令（SocketDataSender类型）。
 * @param info 额外信息（NSMutableDictionary，仅支持NSString和NSNumber）。
 * @param to 接收对象（NSString类型）。
 * @return 是否成功发送请求。
 */
+ (BOOL)sendMessage:(SocketMessageAction)action with:(nullable NSMutableDictionary*)info to:(nullable NSString*)to;


/**
 * 进程通信请求-响应接口（使用默认超时时间）。
 *
 * @param action 指令（SocketDataSender类型）。
 * @param info 额外信息（NSMutableDictionary，仅支持NSString和NSNumber）。
 * @param to 接收对象（NSString类型）。
 * @param callback 回调函数，参数为响应数据和错误码。
 * @return 是否成功发送请求。
 */
+ (BOOL)sendRequest:(SocketMessageAction)action
              with:(nullable NSMutableDictionary*)info
                to:(nullable NSString*)to
          callback:(SocketResponseCallback)callback;

/**
 * 进程通信请求-响应接口。
 * 注意：info参数的NSMutableDictionary仅支持NSString和NSNumber对象。
 *
 * @param action 指令（SocketDataSender类型）。
 * @param info 额外信息（NSMutableDictionary，仅支持NSString和NSNumber）。
 * @param to 接收对象（NSString类型）。
 * @param callback 回调函数，参数为响应数据和错误码。
 * @param timeout 超时时间（毫秒），默认为30000毫秒。
 * @return 是否成功发送请求。
 */
+ (BOOL)sendRequest:(SocketMessageAction)action
              with:(nullable NSMutableDictionary*)info
                to:(nullable NSString*)to 
          callback:(SocketResponseCallback)callback
           timeout:(NSInteger)timeout;

@end

NS_ASSUME_NONNULL_END


#include <atomic>
#include <chrono>
#include <iostream>
#include <map>
#include <thread>
#include <vector>
#include "YYBMessageSender.h"
#include "YYBSocketEngine.h"
#include "YYBDefine.hpp"

namespace SocketMessageAction {
constexpr const char *installApp = "installApp";
constexpr const char *uninstallApp = "uninstallApp";
constexpr const char *fetchEngineInstalledApps = "fetchInstalledApps";
}  // namespace SocketMessageAction

/// @brief  安装app
void installAppToEngine(const std::string& pkgPath) {
    YYBSocketMsgData msg;
    msg.to = YYB::ProcessEngine;
    msg.action = SocketMessageAction::installApp;
    msg.info = { { "pkgPath", pkgPath } };
    YYBSocketEngine::instance().sendMessage(msg);
}

/// @brief  卸载app
void uninstallAppToEngine(const std::string& pkgName) {
    YYBSocketMsgData msg;
    msg.to = YYB::ProcessEngine;
    msg.action = SocketMessageAction::uninstallApp;
    msg.info = { { "pkgName", pkgName } };
    YYBSocketEngine::instance().sendMessage(msg);
}

void fetchEngineInstalledApps() {
    YYBSocketMsgData msg;
    msg.to = YYB::ProcessEngine;
    msg.action = SocketMessageAction::fetchEngineInstalledApps;
    
    YYBSocketEngine::instance().sendMessage(msg);
}


void aospRest() {
    YYBSocketMsgData msg;
    msg.to = YYB::ProcessEngine;
    msg.action = "aosp_reset";
    YYBSocketEngine::instance().sendMessage(msg);
}

void shutDown() {
    YYBSocketMsgData msg;
    msg.action = "shut_down";
    msg.to = YYB::ProcessEngine;
    YYBSocketEngine::instance().sendMessage(msg);
}

void cpuStop() {
    YYBSocketMsgData msg;
    msg.action = "cpu_stop";
    msg.to = YYB::ProcessEngine;
    YYBSocketEngine::instance().sendMessage(msg);
}

void cpuCont() {
    YYBSocketMsgData msg;
    msg.action = "cpu_cont";
    msg.to = YYB::ProcessEngine;
    YYBSocketEngine::instance().sendMessage(msg);
}

// 测试参数
const int NUM_THREADS = 10;         // 并发线程数
const int MESSAGES_PER_THREAD = 1000; // 每个线程发送的消息数
const int MESSAGE_SIZE = 100;      // 每条消息的大小

std::atomic<int> messages_sent(0);
std::atomic<int> messages_failed(0);
void test_send_thread(int thread_id) {
    // 创建消息数据
    YYBSocketMsgData msg;

    msg.action = "log";

    std::string event_data(MESSAGE_SIZE, 'A' + thread_id);
    msg.info = { { "event", event_data } };

    for (int i = 0; i < MESSAGES_PER_THREAD; i++) {
        // 更新事件信息
        msg.info.value()["event"] = event_data + "_" + std::to_string(i);

        if (YYBSocketEngine::instance().sendMessage(msg)) {
            messages_sent++;
        } else {
            messages_failed++;
            std::cerr << "Thread " << thread_id << " failed to send message " << i << std::endl;
        }

        // 添加微小延迟模拟真实场景
        std::this_thread::sleep_for(std::chrono::microseconds(10));
    }
}

void debugAsynLog() {
    auto start_time = std::chrono::high_resolution_clock::now();

    std::vector<std::thread> threads;

    for (int i = 0; i < NUM_THREADS; i++) {
        threads.emplace_back(test_send_thread, i);
    }

    for (auto& t : threads) {
        t.join();
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    std::cout << "\n===== Test Results =====" << std::endl;
    std::cout << "Total messages sent: " << messages_sent << std::endl;
    std::cout << "Messages failed: " << messages_failed << std::endl;
    std::cout << "Total time: " << duration.count() << " ms" << std::endl;
    std::cout << "Throughput: " << (messages_sent * 1000.0 / duration.count()) << " msg/s" << std::endl;
}

void engineCloseApp() {
    YYBSocketMsgData msg;
    msg.action = "engineCloseApp";
    msg.to = "com.tencent.android.qqdownloader";
    YYBSocketEngine::instance().sendMessage(msg);
}


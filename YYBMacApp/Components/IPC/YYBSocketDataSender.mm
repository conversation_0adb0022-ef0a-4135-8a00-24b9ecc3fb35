//
//  YYBSocketDataSender.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import "YYBSocketDataSender.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBDefine.h"

static NSString *const kTag = @"YYBSocketDataSender";
static NSUInteger defaultTimeOutMS = 30000;

@implementation YYBSocketDataSender

+ (BOOL)sendMessage:(SocketMessageAction)action with:(nullable NSMutableDictionary*)info to:(nullable NSString*)to {
    YYBMacLogInfo(kTag, @"sendMessage: %@ info:%@", action, info);
    return [SocketDataSender sendMessage:action
                                    from:kProcessAppStore
                                    with:info
                                      to:to];
}

+ (BOOL)sendRequest:(SocketMessageAction)action
              with:(nullable NSMutableDictionary*)info
                to:(nullable NSString*)to
          callback:(SocketResponseCallback)callback {
    return [self sendRequest:action
                        with:info
                          to:to
                    callback:callback
                     timeout:defaultTimeOutMS];
}

+ (BOOL)sendRequest:(SocketMessageAction)action
              with:(nullable NSMutableDictionary*)info
                to:(nullable NSString*)to 
          callback:(SocketResponseCallback)callback 
           timeout:(NSInteger)timeout {
    YYBMacLogInfo(kTag, @"sendRequest: %@ info:%@ timeout:%ld", action, info, (long)timeout);
    return [SocketDataSender sendRequest:action
                                    from:kProcessAppStore
                                    with:info
                                      to:to
                                callback:callback
                                 timeout:timeout];
}

@end

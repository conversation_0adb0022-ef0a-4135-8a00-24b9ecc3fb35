
#include <string>
#include <functional>
#include <unordered_map>
#include <map>
#include "YYBSocketMsgData.h"

namespace ipc {
    using MessageHandler = std::function<void(const YYBSocketMsgData&)>;

    void engineInstallAppHandle(const YYBSocketMsgData&);
    void engineUninstallAppHandle(const YYBSocketMsgData&);
    void receiveStartApp(const YYBSocketMsgData&);
    void engineIsReadyHandle(const YYBSocketMsgData&);
    void fetchInstalledAppsCallBack(const YYBSocketMsgData&);
    void handleStartEngine(const YYBSocketMsgData&);
    void handleDisconnect(const YYBSocketMsgData&);

    static std::unordered_map<std::string, MessageHandler> messageHandlers = {
        {"engineInstallApp", engineInstallAppHandle},
        {"engineUninstallApp", engineUninstallAppHandle},
        {"engineIsReady", engineIsReadyHandle},
        {"fetchInstalledAppsCallBack", fetchInstalledAppsCallBack},
        {"startEngine", handleStartEngine},
        {"disconnect", handleDisconnect}
    };  // 消息处理器映射
}


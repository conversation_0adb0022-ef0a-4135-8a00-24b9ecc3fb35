
#import <YYBMacFusionSDK/YYBMacLog.h>
#include "YYBMessageReceiver.h"
#import "YYBApkPackage.h"
#import "EngineModule.h"
#import "YYBMessageSender.h"

namespace ipc {

void engineInstallAppHandle(const YYBSocketMsgData& msg) {
    YYBMacLogInfo(@"YYB_IPC", @"receive Engine install app");
    if (!msg.info.has_value()) {
        return;
    }
    std::string pkgName = msg.info->at("packageName");
    if (!pkgName.empty()) {
        [YYBApkPackage.shared updateEngineInstalledApp:[NSString stringWithUTF8String:pkgName.c_str()]];
    }

}

void engineUninstallAppHandle(const YYBSocketMsgData& msg) {
    YYBMacLogInfo(@"YYB_IPC", @"receive Engine uninstall app");
    if (!msg.info.has_value()) {
        return;
    }
    std::string pkgName = msg.info->at("packageName");
    if (!pkgName.empty()) {
        [YYBApkPackage.shared updateEngineUninstallApp:[NSString stringWithUTF8String:pkgName.c_str()]];
    }
}

void engineIsReadyHandle(const YYBSocketMsgData&) {
    YYBMacLogInfo(@"YYB_IPC", @"engineIsReady");
    fetchEngineInstalledApps();
}

void handleStartEngine(const YYBSocketMsgData&data) {

    [[EngineModule sharedInstance] startEngine:^(StartEngineResult res, NSString * _Nonnull msg) {
        YYBMacLogInfo(@"YYB_IPC", @"handleStartEngine res:%d msg:%@", (int)res, msg);
    }];
}

void handleDisconnect(const YYBSocketMsgData& data) {
    std::string from = data.from;
    YYBMacLogInfo(@"YYB_IPC", @"handleDisconnect:%@", [NSString stringWithUTF8String:from.c_str()]);
}


void fetchInstalledAppsCallBack(const YYBSocketMsgData& msg) {
    if (!msg.info.has_value()) {
        return;
    }
    const std::string retCode = msg.info->at("retCode");
    const std::string appListStr = msg.info->at("appList");
    if (retCode != "0" || appListStr.empty()) {
        YYBMacLogInfo(@"YYB_IPC", @"fetchInstalledApps: retCode=%s", retCode.c_str());
        return;
    }
    
    NSString *appListJson = [NSString stringWithUTF8String:appListStr.c_str()];
    NSData *jsonData = [appListJson dataUsingEncoding:NSUTF8StringEncoding];
    if (!jsonData) {
        YYBMacLogInfo(@"YYB_IPC", @"appList字符串转NSData失败");
        return;
    }
    
    NSError *err = nil;
    NSArray *appList = [NSJSONSerialization JSONObjectWithData:jsonData
                                                       options:NSJSONReadingMutableContainers
                                                         error:&err];
    if (err) {
        YYBMacLogInfo(@"YYB_IPC", @"appList JSON解析失败：%@", err);
        return;
    }
    [[YYBApkPackage shared] updateInstalledInEngineStatus:appList];
}

}

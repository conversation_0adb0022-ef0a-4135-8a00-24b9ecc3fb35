//
//  YYBLoginManager.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/30.
//

#import "YYBLoginManager.h"

@interface YYBLoginManager ()

@property (nonatomic, strong) YYBLoginInfo *loginInfo;

@end

@implementation YYBLoginManager

+ (instancetype)sharedManager {
    static YYBLoginManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[YYBLoginManager alloc] init];
    });
    return instance;
}

- (void)setLoginInfoFromH5:(YYBLoginInfo *)loginInfo {
    self.loginInfo = loginInfo;
}

@end

//
//  YYBLoginInfo.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/30.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NSString * LoginType NS_TYPED_ENUM;
FOUNDATION_EXPORT LoginType const LoginTypeQC;
FOUNDATION_EXPORT LoginType const LoginTypeWX;

@interface YYBLoginInfo : NSObject

@property (nonatomic, copy) NSString *openId;
@property (nonatomic, copy) NSString *accessToken;
@property (nonatomic, copy) NSString *refreshToken;
@property (nonatomic, copy) LoginType loginType;
@property (nonatomic, copy) NSString *scope;
@property (nonatomic, copy) NSString *nickname;
@property (nonatomic, copy) NSString *headImg;
@property (nonatomic, assign) NSTimeInterval expireTime;

// wx
@property (nonatomic, copy) NSString *wxappLoginType;
@property (nonatomic, copy) NSString *wxappOpenId;
@property (nonatomic, copy) NSString *wxappAccessToken;
@property (nonatomic, copy) NSString *wxappScope;
@property (nonatomic, copy) NSString *wxappRefreshToken;
@property (nonatomic, assign) NSTimeInterval wxappExpireTime;

@end

NS_ASSUME_NONNULL_END

//
//  SystemMonitor.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SystemMonitor : NSObject

/**
 * 获取系统总体的CPU使用率。
 * @discussion 由于CPU使用率需要通过比较两次采样计算得出，首次调用此方法将返回0.0，并建立一个基准。
 * 从第二次调用开始，将返回两次调用之间的时间段内的CPU使用率。
 * @return 一个 0.0 到 100.0 之间的浮点数。
 */
- (float)getCPUUsage;

/**
 * 获取用户最后一次操作（键盘、鼠标等）至今的空闲时间。
 * @return 空闲的秒数 (NSTimeInterval/double)。
 */
- (NSTimeInterval)getSecondsSinceLastUserActivity;

@end

NS_ASSUME_NONNULL_END

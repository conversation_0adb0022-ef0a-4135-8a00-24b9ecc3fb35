//
//  SystemMonitor.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/14.
//

#import "SystemMonitor.h"
#import <AppKit/AppKit.h>
#import <mach/mach.h>
#import <YYBMacFusionSDK/YYBMacLog.h>


static NSString* const kTag = @"SystemMonitor";
@interface SystemMonitor() {
    processor_info_array_t _previousCpuInfo;
    mach_msg_type_number_t _previousCpuInfoCount;
}
@end

@implementation SystemMonitor

- (void)dealloc {
    if (_previousCpuInfo != nil) {
        vm_size_t prev_cpu_info_size = sizeof(integer_t) * _previousCpuInfoCount;
        vm_deallocate(mach_task_self(), (vm_address_t)_previousCpuInfo, prev_cpu_info_size);
    }
}

#pragma mark - 用户活动检测

- (NSTimeInterval)getSecondsSinceLastUserActivity {
    // kCGEventSourceStateHIDSystemState 表示检查系统级别的硬件输入事件
    // kCGAnyInputEventType 表示任何类型的输入（鼠标移动、点击、键盘按键等）
    return CGEventSourceSecondsSinceLastEventType(kCGEventSourceStateHIDSystemState, kCGAnyInputEventType);
}

#pragma mark - CPU占用率检测

- (float)getCPUUsage {
    natural_t numProcessors;
    processor_info_array_t currentCpuInfo;
    mach_msg_type_number_t currentCpuInfoCount;
    host_t host = mach_host_self();

    // 获取关于处理器核心的信息
    kern_return_t kr = host_processor_info(host, PROCESSOR_CPU_LOAD_INFO, &numProcessors, &currentCpuInfo, &currentCpuInfoCount);
    if (kr != KERN_SUCCESS) {
        YYBMacLogError(kTag, @"Error: host_processor_info() failed: %d", kr);
        return -1.0f; // 返回一个错误值
    }

    // 如果没有上一次的数据，就保存当前数据作为基准，并返回0
    if (_previousCpuInfo == nil) {
        _previousCpuInfo = currentCpuInfo;
        _previousCpuInfoCount = currentCpuInfoCount;
        return 0.0f;
    }
    
    float totalUsage = 0.0f;

    for (natural_t i = 0; i < numProcessors; i++) {
        // 计算两次采样之间的时间片（ticks）增量
        // 每个核心有4个状态: User, System, Idle, Nice
        unsigned long long prevUser = _previousCpuInfo[i * CPU_STATE_MAX + CPU_STATE_USER];
        unsigned long long prevSystem = _previousCpuInfo[i * CPU_STATE_MAX + CPU_STATE_SYSTEM];
        unsigned long long prevIdle = _previousCpuInfo[i * CPU_STATE_MAX + CPU_STATE_IDLE];
        unsigned long long prevNice = _previousCpuInfo[i * CPU_STATE_MAX + CPU_STATE_NICE];

        unsigned long long currentUser = currentCpuInfo[i * CPU_STATE_MAX + CPU_STATE_USER];
        unsigned long long currentSystem = currentCpuInfo[i * CPU_STATE_MAX + CPU_STATE_SYSTEM];
        unsigned long long currentIdle = currentCpuInfo[i * CPU_STATE_MAX + CPU_STATE_IDLE];
        unsigned long long currentNice = currentCpuInfo[i * CPU_STATE_MAX + CPU_STATE_NICE];
        
        // 总增量 = (活动时间增量) + (空闲时间增量)
        unsigned long long totalTicksDelta = (currentUser - prevUser) + (currentSystem - prevSystem) + (currentNice - prevNice) + (currentIdle - prevIdle);
        
        // 活动时间增量
        unsigned long long busyTicksDelta = (currentUser - prevUser) + (currentSystem - prevSystem) + (currentNice - prevNice);
        
        if (totalTicksDelta > 0) {
            totalUsage += (float)busyTicksDelta / totalTicksDelta;
        }
    }
    
    // 释放上一次的CPU信息内存
    vm_size_t prev_cpu_info_size = sizeof(integer_t) * _previousCpuInfoCount;
    vm_deallocate(mach_task_self(), (vm_address_t)_previousCpuInfo, prev_cpu_info_size);

    _previousCpuInfo = currentCpuInfo;
    _previousCpuInfoCount = currentCpuInfoCount;
    
    // 返回所有核心的平均使用率
    return (totalUsage / numProcessors) * 100.0f;
}

@end

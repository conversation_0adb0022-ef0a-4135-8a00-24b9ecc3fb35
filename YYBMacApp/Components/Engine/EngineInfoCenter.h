//
//  EngineInfoCenter.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface EngineInfoCenter : NSObject

+(instancetype)shareInstance;


/**
 * 获取引擎启动路径
 */
- (nonnull NSString*)getEnginePath;

/**
 * 获取VMS启动路径
 */
- (nonnull NSString*)getVMSPath;

/**
 * 引擎是否存在
 */
- (BOOL)isEngineExist;

/**
 * VMS是否存在
 */
- (BOOL)isVMSExist;

/**
 * 引擎是否可用
 */
- (BOOL)enableUseEngine;

/**
 * vms是否可用
 */
- (BOOL)enableUseVms;


/**
 * 获取vms相关mmkv 的key
 */
- (NSString*)getVmsUniqueKey:(NSString*)key;

@end

NS_ASSUME_NONNULL_END

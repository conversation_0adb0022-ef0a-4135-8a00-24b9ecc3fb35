//
//  EngineInfoCenter.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/5.
//

#import <YYBMacFusionSDK/YYBMacResHub.h>
#import <YYBMacFusionSDK/ResHubModel.h>
#import <YYBMacFusionSDK/ResHubCommonDefines.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "EngineInfoCenter.h"
#import "FileUtils.h"
#import "YYBDefine.h"
#import "EngineDownloadProxy.h"

NS_ASSUME_NONNULL_BEGIN

static NSString* const kTag = @"EngineInfoCenter";

@interface EngineInfoCenter()
@property (assign, nonatomic) BOOL useCustomVms;
@property (assign, nonatomic) BOOL useCustomEngine;
@end

@implementation EngineInfoCenter

+ (instancetype)shareInstance {
    static EngineInfoCenter *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.useCustomVms = NO;
        self.useCustomEngine = NO;
    }
    return self;
}

- (nonnull NSString *)getEnginePath {
    NSString* applicationSupportMarketDir = [FileUtils getMarketApplicationSupportDir];
    return [applicationSupportMarketDir stringByAppendingFormat:@"/YYBEngineDownload/yyb_mac.app"];
}

- (nonnull NSString *)getVMSPath {
    NSString* applicationSupportEngineDir = [FileUtils getEngineApplicationSupportDir];
    return [applicationSupportEngineDir stringByAppendingPathComponent:@"vms"];
}

- (BOOL)isEngineExist {
    return [FileUtils isFileExist:[self getEnginePath]];
}

- (BOOL)isVMSExist {
    return [FileUtils isFileExist:[self getVMSPath]];
}


- (BOOL)enableUseEngine {
    BOOL isEngineExist = [[EngineInfoCenter shareInstance]isEngineExist];
    NSString* tmpEngineUrl = [[YYBMacMMKV sharedInstance] getStringForKey:@"tmpEngineDownloadUrl"];
    YYBMacLogInfo(kTag, @"enableUseEngine tmpEngineUrl:%@", tmpEngineUrl);
    return self.useCustomEngine ||(isEngineExist && [tmpEngineUrl isEqualToString:kTempEngineDownloadUrl]);
}

- (BOOL)enableUseVms {
    if (self.useCustomVms) {
        YYBMacLogInfo(kTag, @"使用本地vms");
        return YES;
    }
    // 文件存在性和资源状态校验
    BOOL isVMSExist = [[EngineInfoCenter shareInstance]isVMSExist];
    ResHubLocalResStatus status = [[YYBMacResHub sharedInstance] getResStatusWithId:kVmsResId];
    if (!isVMSExist || (status != ResHubLocalResStatusGOOD && status != ResHubLocalResStatusLocalOnly)) {
        YYBMacLogInfo(kTag, @"enableUseVms:%d status:%d", isVMSExist, (int)status);
        return NO;
    }
    
    // 文件目录结构校验（包括文件大小和时间戳校验）
    ResHubModel* model = [[YYBMacResHub sharedInstance] resourceWithId:kVmsResId preferLocal:NO];
    NSString* info = [[YYBMacMMKV sharedInstance]getStringForKey:[self getVmsUniqueKey:model.md5]];
    NSString* dirInfo = [FileUtils generateDirectoryJsonStringFromPath:[[EngineInfoCenter shareInstance] getVMSPath]];
    BOOL res = (info != nil && dirInfo != nil && [info isEqualToString:dirInfo]);

    if (!res) {
        YYBMacLogInfo(kTag, @"enableUseVms is false. info:%@ dirInfo:%@", info, dirInfo);
    }
    return res;
}

- (NSString*)getVmsUniqueKey:(NSString*)key {
    return [NSString stringWithFormat:@"vmskey_%@", key];
}

@end

NS_ASSUME_NONNULL_END

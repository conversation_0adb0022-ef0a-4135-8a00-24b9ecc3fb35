//
//  EngineModule.h
//  YYBMacApp
//
//  Created by bethahua<PERSON> on 2025/8/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, StartEngineResult) {
    // 启动成功
    ENGINE_LAUNCH_SUCCESS,
    // 启动失败
    ENGINE_LAUNCH_FAILED,
    // 引擎已启动
    ENGINE_IS_RUNNING,
    // 引擎/aosp未找到
    FILE_NOT_FOUND,
    // 商店、引擎、aosp版本不匹配
    VERSION_MISMACH,
    // 引擎/aosp强制更新中
    FORCE_UPDATE_IN_PROGRESS,
    // reshub请求未返回
    SDK_NOT_INITIALIED
};

extern NSString* const kEngineBundleId;

typedef void (^StartEngineCallback)(StartEngineResult res, NSString* msg);

@interface EngineModule : NSObject

+ (instancetype)sharedInstance;
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

/**
 * 启动引擎。建议通过StartEngineTask启动。直接调用该方法前需保证fusionsdk已初始化成功
 * @param callback 启动结果回调，参数为启动是否成功（BOOL）和错误信息（NSString）。
 */
- (void)startEngine:(nullable StartEngineCallback)callback;



@end

NS_ASSUME_NONNULL_END

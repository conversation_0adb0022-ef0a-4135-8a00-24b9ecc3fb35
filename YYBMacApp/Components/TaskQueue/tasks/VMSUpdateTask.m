//
//  VMSUpdateTask.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/3.
//


#import "YYBMacFusionSDK/YYBMacLog.h"
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/YYBMacResHub.h>
#import <YYBMacFusionSDK/ResHubModel.h>
#import <YYBMacFusionSDK/ResHubCommonDefines.h>
#import <AppKit/AppKit.h>
#import "VMSUpdateTask.h"
#import "YYBDefine.h"
#import "EngineDownloadHelper.h"
#import "ResHubDownloadTask.h"
#import "FileUtils.h"
#import "EngineInfoCenter.h"
#import "BussinessMessageCenter.h"
#import "IdentifierConstants.h"

NS_ASSUME_NONNULL_BEGIN

static NSString* const kTag = @"VMSUpdateTask";

@interface VMSUpdateTask()
@end

@implementation VMSUpdateTask

- (instancetype)initWithIdentifier:(NSString *)identifier {
    self = [super initWithIdentifier:identifier];
    if (self) {
    }
    return self;
}

- (void)executeTaskWithCompletion:(void (^)(BOOL, NSError * _Nullable))completion {
    
    ResHubLocalResStatus status = [[YYBMacResHub sharedInstance] getResStatusWithId:kVmsResId];
    YYBMacLogInfo(kTag, @"当前本地vms状态:%d", (int)status);
    ResHubModel* model = [[YYBMacResHub sharedInstance] resourceWithId:kVmsResId preferLocal:NO needValidate:NO];
    
    switch (status) {
        case ResHubLocalResStatusGOOD: {
            YYBMacLogInfo(kTag, @"当前vms资源无需更新。资源信息:%@ 资源路径:%@ 资源版本:%d 任务id:%@ md5:%@", model, model.localPath, (int)model.version, model.taskId, model.md5);
            [self handleVMSCanUse:model withCompletion:completion];
            break;
        }
        case ResHubLocalResStatusLocalOnly: {
            YYBMacLogWarn(kTag, @"当前资源仅本地可用。资源信息:%@ 资源路径:%@ 资源版本:%d 任务id:%@ md5:%@", model, model.localPath, (int)model.version, model.taskId, model.md5);
            [self handleVMSCanUse:model withCompletion:completion];
            break;
        }
        case ResHubLocalResStatusNeedDownload:
        case ResHubLocalResStatusNeedUpdate:
            YYBMacLogInfo(kTag, @"当前vms资源需要下载/更新。当前资源信息:%@ 资源路径:%@ 资源版本:%d 任务id:%@", model, model.localPath, (int)model.version, model.taskId);
            [self handleVMSDownload: completion];
            break;
        case ResHubLocalResStatusFileInvalid:
            // 本地文件不存在或md5校验失败
            YYBMacLogWarn(kTag, @"当前vms资源文件不可用。当前资源信息:%@ 资源路径:%@ 资源版本:%d 任务id:%@", model, model.localPath, (int)model.version, model.taskId);
            [[YYBMacResHub sharedInstance] deleteResWithId:kVmsResId];
            [self handleVMSDownload: completion];
            break;
        case ResHubLocalResStatusDisabled:
            // 资源被禁用，资源版本号不在可用区间
            YYBMacLogWarn(kTag, @"当前资源不可用");
            [[YYBMacResHub sharedInstance] deleteResWithId:kVmsResId];
            [self handleVMSForceUpdate:completion];
            break;
        case ResHubLocalResStatusNotExist:
            // 资源不存在（远端没有找到这个资源）
            YYBMacLogWarn(kTag, @"当前资源不存在");
            [self handleVMSForceUpdate:completion];
            break;
        case ResHubLocalResStatusLocalUnknow:
            YYBMacLogError(kTag, @"未知错误。");
            completion(NO, [NSError errorWithDomain:kResHubDownloadErrorDomain code:-1 userInfo:@{NSLocalizedDescriptionKey: @"vms download failed.unknown error."}]);
            break;
    }
}

- (AsyncTaskType)getTaskType {
    return VMS_UPDATE_TASK;
}

- (void)executeCompletionHandler:(BOOL)success error:(nullable NSError *)error {
    YYBMacLogInfo(kTag, @"engineUpdateTask end. taskId:%@ sucess:%d error:%@", self.taskIdentifier, success, error);
    ResHubLocalResStatus status = [[YYBMacResHub sharedInstance] getResStatusWithId:kVmsResId];
    if (success) {
        [[EngineDownloadHelper sharedInstance] notifyVmsDownloadStatus:@"已就绪" andProgress:@(1.0) resHubStatus:status];
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kVMSDownloadResult payload:@1]];
    } else {
        [[EngineDownloadHelper sharedInstance] notifyVmsDownloadStatus:@"失败" andProgress:@(-1.0) resHubStatus:status];
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kVMSDownloadResult payload:@0]];
    }
}

- (void)handleVMSDownload:(void (^)(BOOL, NSError * _Nullable))completion {
    YYBMacLogInfo(kTag, @"start vms download.");
    
    // 磁盘空间检查
    NSNumber* freeSpace = [FileUtils getFreeSpace];
    ResHubModel* fetchModel = [[YYBMacResHub sharedInstance] fetchedResConfigWithId:kVmsResId];
    if (freeSpace && [freeSpace compare:@(fetchModel.size * 2)] == NSOrderedAscending) {
        YYBMacLogError(kTag, @"free space is not enough. current space: %@ model size:%ld", freeSpace, (long)fetchModel.size);
        completion(NO, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeNotEnoughDiskSpace userInfo:@{NSLocalizedDescriptionKey: @"free space is not enough."}]);
        return;
    }
    
    
    [self killThread];
    [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc]initWithIdentifier:kShowEnginLoadingView payload:@""]];
    ResHubLocalResStatus status = [[YYBMacResHub sharedInstance] getResStatusWithId:kVmsResId];
    
    // 执行下载
    __weak typeof(self) weakSelf = self;
    [[YYBMacResHub sharedInstance] loadResourceWithId:kVmsResId preferLocal:NO progress:^(CGFloat progress) {
        // YYBMacLogInfo(kTag, @"handleVMSDownload progress:%f", (double)progress);
        [[EngineDownloadHelper sharedInstance] notifyVmsDownloadStatus:@"下载中" andProgress:@(progress) resHubStatus:status];
    } completed:^(BOOL success, NSError * _Nullable error, ResHubModel * _Nullable resModel) {
        YYBMacLogInfo(kTag, @"download vms result: %d error:%@ resModel:%@", success, error, resModel);
        if (!weakSelf) {
            completion(NO, [NSError errorWithDomain:kResHubDownloadErrorDomain code:-1 userInfo:@{NSLocalizedDescriptionKey: @"current task not exist."}]);
            return;
        }
        if (success) {
            [self copyVMS:resModel withCompletion:completion];
        } else {
            completion(NO, error);
        }
    }];
}

- (void)handleVMSForceUpdate:(void (^)(BOOL, NSError * _Nullable))completion {
    YYBMacLogInfo(kTag, @"handleVMSForceUpdate");
    [[YYBMacResHub sharedInstance] updateAllConfigs:^(NSError * _Nullable error) {
        ResHubLocalResStatus status = [[YYBMacResHub sharedInstance] getResStatusWithId:kVmsResId];
        YYBMacLogInfo(kTag, @"handleVMSForceUpdate. current status: %d", (int)status);
        if (status == ResHubLocalResStatusGOOD || status == ResHubLocalResStatusLocalOnly) {
            completion(YES, nil);
        } else if (status == ResHubLocalResStatusNeedDownload || status == ResHubLocalResStatusNeedUpdate) {
            [self handleVMSDownload:completion];
        } else {
            completion(NO, [NSError errorWithDomain:kResHubDownloadErrorDomain code:-1 userInfo:@{NSLocalizedDescriptionKey: @"vms force download failed.unknown error."}]);
        }
    }];
}

- (void)handleVMSCanUse:(ResHubModel*)model withCompletion:(void (^)(BOOL, NSError * _Nullable))completion {
    YYBMacLogInfo(kTag, @"handleVMSCanUse.model:%@", model);
    if ([[EngineInfoCenter shareInstance] enableUseVms]) {
        completion(YES, nil);
        return;
    }
    
    [self copyVMS:model withCompletion:completion];
    
}

- (BOOL)copyVMS:(ResHubModel *)model withCompletion:(void (^)(BOOL, NSError * _Nullable))completion{
    NSError* error = nil;
    NSString* vmsPath = [model.localPath stringByAppendingPathComponent:@"vms"];
    NSString* targetVmsPath = [[EngineInfoCenter shareInstance] getVMSPath];
    BOOL copyRes = [FileUtils copyFile:vmsPath to:targetVmsPath needForce:YES error:&error];
    if (copyRes) {
        [[YYBMacMMKV sharedInstance] setString:[FileUtils generateDirectoryJsonStringFromPath:targetVmsPath]
                                        forKey:[[EngineInfoCenter shareInstance] getVmsUniqueKey:model.md5]];
        completion(YES, nil);
    } else if (error.domain == NSCocoaErrorDomain && error.code == NSFileWriteOutOfSpaceError) {
        completion(NO, [NSError errorWithDomain:kResHubDownloadErrorDomain code:ResHubResourceDownloadErrorCodeNotEnoughDiskSpace userInfo:@{NSLocalizedDescriptionKey: @"copy failed. current free space is not enough."}]);
    } else {
        completion(NO, [NSError errorWithDomain:kResHubDownloadErrorDomain code:-1 userInfo:@{NSLocalizedDescriptionKey: @"copy vms failed."}]);
    }
    YYBMacLogInfo(kTag, @"copyVMS res:%d", copyRes);
    return copyRes;
}



// 临时逻辑，下载vms之前把快捷方式和引擎都干掉
- (void)killThread {
    YYBMacLogInfo(kTag, @"killThread");
    [FileUtils deleteFile:[[EngineInfoCenter shareInstance]getVMSPath]];
    id showDialogBlock = ^{
        [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kShowForceKillPkgDialog payload:@""]];
    };
    YYBMacLogInfo(kTag, @"send kBringAppToFront. vms");
    [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kBringAppToFront payload:showDialogBlock]];
    BOOL needExistEngine = NO;
    for (NSRunningApplication *app in [[NSWorkspace sharedWorkspace] runningApplications]) {
        if ([app.bundleIdentifier hasPrefix:@"com.tencent.yybmac.app"]) {
            [[BussinessMessageCenter sharedCenter] pushMessage:[[BussinessMessage alloc] initWithIdentifier:kShowForceKillPkgDialog payload:@""]];
        }
        if ([app.bundleIdentifier isEqualToString:@"com.tencent.yybmac.engine"]) {
            needExistEngine = YES;
            BOOL res = [app terminate];
            YYBMacLogInfo(kTag, @"普通退出引擎结果: %d", res);
            if (!res) {
                res = [app forceTerminate];
            }
            YYBMacLogInfo(kTag, @"退出引擎结果: %d", res);
        }
    }
    // 如果不需要退出引擎，还需要检查是否有快捷方式启动中，如果有，杀死快捷方式
    if (!needExistEngine) {
        for (NSRunningApplication *app in [[NSWorkspace sharedWorkspace] runningApplications]) {
            if ([app.bundleIdentifier hasPrefix:@"com.tencent.yybmac.app"]) {
                BOOL res = [app terminate];
                if (!res) {
                    res = [app forceTerminate];
                }
                YYBMacLogInfo(kTag, @"退出快捷方式结果: %d", res);
            }
        }
    }
}


@end

NS_ASSUME_NONNULL_END

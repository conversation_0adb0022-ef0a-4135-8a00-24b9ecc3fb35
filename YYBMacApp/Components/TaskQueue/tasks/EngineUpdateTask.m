//
//  EngineUpdateTask.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/11.
//

#import "EngineUpdateTask.h"
#import "EngineDownloadProxy.h"
#import "FileUtils.h"
#import "YYBMacFusionSDK/YYBMacLog.h"
#import "EngineInfoCenter.h"
#import <YYBMacFusionSDK/YYBMacMMKV.h>

NS_ASSUME_NONNULL_BEGIN

@interface EngineUpdateTask()
@property (strong, nonatomic) EngineDownloadProxy *downloadProxy;

@end

static NSString* const kTag = @"EngineUpdateTask";

@implementation EngineUpdateTask


- (instancetype)initWithIdentifier:(NSString *)identifier {
    self = [super initWithIdentifier:identifier];
    if (self) {
        self.downloadProxy = [[EngineDownloadProxy alloc] init];

    }
    return self;
}

-(void)executeTaskWithCompletion:(void (^)(BOOL, NSError * _Nullable))completion {
    if ([[EngineInfoCenter shareInstance] enableUseEngine]) {
        YYBMacLogInfo(kTag, @"[start]enableUseEngine true.");
        completion(YES, nil);
    } else {
        [self.downloadProxy downloadEngine:^(BOOL success) {
            self.completionHandler(success, nil, self.taskIdentifier);
            completion(success, nil);
        }];
    }
}



- (AsyncTaskType)getTaskType {
    return ENGINE_UPDATE_TASK;
}

- (void)executeCompletionHandler:(BOOL)success error:(nullable NSError *)error {
    YYBMacLogInfo(kTag, @"engineUpdateTask end. taskId:%@ sucess:%d", self.taskIdentifier, success);
}



@end

NS_ASSUME_NONNULL_END

//
//  StartEngineTask.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/4.
//

#import <YYBMacFusionSDK/YYBMacLog.h>
#import "StartEngineTask.h"
#import "EngineModule.h"
#import "ResHubDownloader.h"
#import "BussinessMessageCenter.h"
#import "IdentifierConstants.h"

static NSString* const kTag = @"StartEngineTask";

@implementation StartEngineTask

- (void)realStartEngineTask:(void (^ _Nonnull)(BOOL, NSError * _Nullable))completion {
    YYBMacLogInfo(kTag, @"realStartEngineTask");
    [[EngineModule sharedInstance] startEngine:^(StartEngineResult res, NSString * _Nonnull msg) {
        BOOL success = (res == ENGINE_LAUNCH_SUCCESS || res == ENGINE_IS_RUNNING);
        completion(success, nil);
    }];
}

- (void)executeTaskWithCompletion:(void (^)(BOOL, NSError * _Nullable))completion {
    __weak typeof(self) weakSelf = self;
    if (![[ResHubDownloader sharedInstance] isReshubConfigPullFinished]) {
        if (!weakSelf) {
            return;
        }
        YYBMacLogInfo(kTag, @"isReshubConfigPullFinished is false. register callback.");
        // 如果启动引擎前reshub配置还没有返回，注册监听。
        [[BussinessMessageCenter sharedCenter] registerObserver:self
                                                  forIdentifier:kPullReshubConfigFinished
                                                    withHandler:^(BussinessMessage * _Nonnull message) {
            
            [weakSelf realStartEngineTask:completion];
            [[BussinessMessageCenter sharedCenter] unregisterObserver:self];
        }];
        return;
    }
    [weakSelf realStartEngineTask:completion];
}

- (AsyncTaskType)getTaskType {
    return START_ENGINE_TASK;
}

- (void)dealloc
{
    [[BussinessMessageCenter sharedCenter] unregisterObserver:self];
}

@end


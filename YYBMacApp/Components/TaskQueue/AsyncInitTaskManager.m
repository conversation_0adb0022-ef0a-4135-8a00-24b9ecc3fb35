//
//  AsyncInitTaskManager.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/12.
//

#import <YYBMacFusionSDK/YYBMacResHub.h>
#import "AsyncInitTaskManager.h"
#import "AsyncTaskManager.h"
#import "StartEngineTask.h"
#import "VMSUpdateTask.h"
#import "ResRefreshListener.h"

NS_ASSUME_NONNULL_BEGIN

@interface AsyncInitTaskManager ()

@property(nonatomic, strong) ResRefreshListener* refreshListener;


@end

@implementation AsyncInitTaskManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self initQueue];
    }
    return self;
}

- (void)initQueue {
    
    StartEngineTask* startEngineTask = [[StartEngineTask alloc] initWithIdentifier:[self generateId]];
    [[AsyncTaskManager sharedInstance] addTask:startEngineTask];
}

- (void)runTasks {
    [[AsyncTaskManager sharedInstance] startAllTasks];
}

- (NSString*)generateId {
    NSString *uuid = [[NSUUID UUID] UUIDString];
    return [uuid copy];
}


@end

NS_ASSUME_NONNULL_END

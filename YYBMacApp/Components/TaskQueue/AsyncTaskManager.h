//
//  AsyncTaskManager.h
//  YYBMacApp
//
//  Created by bethahua<PERSON> on 2025/8/2.
//

#import <Foundation/Foundation.h>
#import "BaseAsyncInitTask.h"

NS_ASSUME_NONNULL_BEGIN

@interface AsyncTaskManager : NSObject

+ (instancetype)sharedInstance;

- (void)addTask:(BaseAsyncInitTask *)task;
- (void)removeTaskByIdentifier:(NSString *)identifier;
- (void)runTasksSequentiallyWithIdentifiers:(NSArray<NSString *> *)identifiers;
- (void)startTaskWithIdentifier:(NSString *)identifier;
- (void)startAllTasks;
- (NSMutableDictionary*)getTasks;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END

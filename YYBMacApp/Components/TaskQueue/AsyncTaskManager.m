//
//  AsyncTaskManager.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/2.
//

#import "AsyncTaskManager.h"
#import <YYBMacFusionSDK/YYBMacLog.h>


static NSString* const kTag = @"AsyncTaskManager";

@interface AsyncTaskManager()
@property (nonatomic, strong) NSOperationQueue* queue;
@property (nonatomic, strong) NSMutableDictionary<NSString *, BaseAsyncInitTask *> *tasks;
@property (nonatomic, strong) dispatch_queue_t tasksAccessQueue;
@end

@implementation AsyncTaskManager

+ (instancetype)sharedInstance {
    static AsyncTaskManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[AsyncTaskManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.queue = [[NSOperationQueue alloc] init];
        self.tasks = [NSMutableDictionary dictionary];
        self.tasksAccessQueue = dispatch_queue_create("com.asyncTaskManager.tasksQueue", DISPATCH_QUEUE_CONCURRENT);
    }
    return self;
}

- (void)addTask:(BaseAsyncInitTask *)task {
    if (task.taskIdentifier) {
        __weak typeof(self) weakSelf = self;
        task.completionHandler = ^(BOOL success, NSError * _Nullable error, NSString * _Nonnull taskIdentifier) {
            if (weakSelf) {
                YYBMacLogInfo(kTag, @"task '%@' is completed. status:%d. remove task from dic.", taskIdentifier, success);
                [weakSelf removeTaskByIdentifier:taskIdentifier];
            }
        };
        dispatch_barrier_async(self.tasksAccessQueue, ^{
            self.tasks[task.taskIdentifier] = task;
            YYBMacLogInfo(kTag, @"addTask. taskIdentifier:%@", task.taskIdentifier);
        });
        
    }
}

- (void)removeTaskByIdentifier:(NSString *)identifier {
    if (!identifier) return;
    
    dispatch_barrier_async(self.tasksAccessQueue, ^{
        if (self.tasks[identifier]) {
            [self.tasks removeObjectForKey:identifier];
            YYBMacLogInfo(kTag, @"remove task. taskIdentifier:%@", identifier);
        }
    });
}

- (NSMutableDictionary *)getTasks {
    return [self.tasks copy];
}

- (void)runTasksSequentiallyWithIdentifiers:(NSArray<NSString *> *)identifiers {
    __block BaseAsyncInitTask *previousTask = nil;
    
    dispatch_sync(self.tasksAccessQueue, ^{
        for (NSString *identifier in identifiers) {
            BaseAsyncInitTask *currentTask = self.tasks[identifier];
            if (currentTask) {
                if (previousTask) {
                    [currentTask addDependency:previousTask];
                }
                previousTask = currentTask;
            }
        }
    });
    for (NSString *identifier in identifiers) {
        [self startTaskWithIdentifier:identifier];
    }
}

- (void)startTaskWithIdentifier:(NSString *)identifier{
    __block BaseAsyncInitTask *task = nil;
    dispatch_sync(self.tasksAccessQueue, ^{
        task = self.tasks[identifier];
    });
    
    if (task && !task.isExecuting && !task.isFinished) {
        [self.queue addOperation:task];
    }
}

- (void)startAllTasks {
    __block NSArray *allTasks = nil;
    dispatch_sync(self.tasksAccessQueue, ^{
        allTasks = self.tasks.allValues;
    });
    
    for (BaseAsyncInitTask *task in allTasks) {
        if (!task.isExecuting && !task.isFinished) {
            [self.queue addOperation:task];
        }
    }
}

@end


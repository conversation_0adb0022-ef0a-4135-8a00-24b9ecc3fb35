//
//  BaseSyncInitTask.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/11.
//

#import <Foundation/Foundation.h>
#import "AsyncTaskType.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^AsyncTaskCompletionBlock)(BOOL success, NSError * _Nullable error, NSString *taskIdentifier);

@interface BaseAsyncInitTask : NSOperation

@property (nonatomic, copy, readonly) NSString *taskIdentifier;
@property (nonatomic, copy, nullable) AsyncTaskCompletionBlock completionHandler;

- (instancetype)initWithIdentifier:(NSString *)identifier;
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

/// 注意！！在任务完成（无论失败或成功）后一定一定要调用completion方法，否则已完成的任务不会被删除，会出现内存泄漏问题
- (void)executeTaskWithCompletion:(void(^)(BOOL success, NSError * _Nullable error))completion;

- (NSInteger)getMaxRetryAttempts;

- (NSTimeInterval)getRetryDelayTime;

- (void)executeCompletionHandler:(BOOL)success error:(nullable NSError *)error;




// 任务启动（非主线程）
-(void)start;


// 方法类型
-(AsyncTaskType)getTaskType;

@end

NS_ASSUME_NONNULL_END

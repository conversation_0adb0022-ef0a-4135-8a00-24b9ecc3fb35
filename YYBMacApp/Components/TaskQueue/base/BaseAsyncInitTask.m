//
//  BaseSyncInitTask.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/7/11.
//

#import "BaseAsyncInitTask.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString* const kTag = @"BaseAsyncInitTask";


@interface BaseAsyncInitTask()

@property (nonatomic, assign) BOOL isExecuting;
@property (nonatomic, assign) BOOL isFinished;
@property (nonatomic, assign) NSInteger currentAttempt;

@end

@implementation BaseAsyncInitTask

- (instancetype)initWithIdentifier:(NSString *)identifier {
    self = [super init];
    if (self) {
        _taskIdentifier = [identifier copy];
        _currentAttempt = 0;
    }
    return self;
}



- (AsyncTaskType)getTaskType {
    return BASE_ASYN_TASK;
}

- (void)start {
    if ([self isCancelled]) {
        self.isFinished = YES;
        YYBMacLogInfo(kTag, @"current task is canceled. taskId: %@", self.taskIdentifier);
        return;
    }
    if (self.isExecuting) {
        YYBMacLogInfo(kTag, @"current task is isExecuting. taskId:%@", self.taskIdentifier);
        return;
    }
    self.isExecuting = YES;
    [self attemptToExecute];
}

- (void)attemptToExecute {
    if ([self isCancelled]) {
        [self finishWithSuccess:NO error:nil];
        return;
    }
    
    self.currentAttempt++;
    YYBMacLogInfo(kTag, @"task type:%d id:%@ currentAttempt:%ld", (int)[self getTaskType], self.taskIdentifier, (long)self.currentAttempt);
    
    [self executeTaskWithCompletion:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            YYBMacLogInfo(kTag, @"Task '%@' execute success. task type:%d", self.taskIdentifier, (int)[self getTaskType]);
            [self finishWithSuccess:YES error:nil];
        } else {
            YYBMacLogInfo(kTag, @"Task '%@ execute failed. task type:%d error:%@", self.taskIdentifier, (int)[self getTaskType], error.localizedDescription);
            if (self.currentAttempt < [self getMaxRetryAttempts]) {
                NSTimeInterval delayTime = [self getRetryDelayTime];
                YYBMacLogInfo(kTag, @"currentAttempt:%d maxRetryAttempts:%d retryDelay:%f. retry.", (int)self.currentAttempt, (int)[self getMaxRetryAttempts], delayTime);
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                    [self attemptToExecute];
                });
            } else {
                YYBMacLogInfo(kTag, @"currentAttempt:%d maxRetryAttempts:%d. stop.", (int)self.currentAttempt, (int)[self getMaxRetryAttempts]);
                [self finishWithSuccess:NO error:error];
            }
        }
    }];
}

- (void)finishWithSuccess:(BOOL)success error:(nullable NSError *)error {
    [self executeCompletionHandler:success error:error];
    self.isExecuting = NO;
    self.isFinished = YES;
}

- (void)executeTaskWithCompletion:(void (^)(BOOL, NSError * _Nullable))completion {
    // 子类必须重写该方法
    [NSException raise:NSInternalInconsistencyException
                format:@"You must override %@ in a subclass", NSStringFromSelector(_cmd)];
}

#pragma mark - KVO

- (void)setIsExecuting:(BOOL)isExecuting {
    [self willChangeValueForKey:@"isExecuting"];
    _isExecuting = isExecuting;
    [self didChangeValueForKey:@"isExecuting"];
}

- (void)setIsFinished:(BOOL)isFinished {
    [self willChangeValueForKey:@"isFinished"];
    _isFinished = isFinished;
    [self didChangeValueForKey:@"isFinished"];
}


- (NSTimeInterval)getRetryDelayTime {
    return 2.0;
}

// 默认任务失败不重试
- (NSInteger)getMaxRetryAttempts {
    return -1;
}

- (NSString *)description
{
    NSDictionary* dic = @{
        @"taskIdentifier": self.taskIdentifier,
        @"taskType": @([self getTaskType]),
        @"retryDelayTime": @([self getRetryDelayTime]),
        @"getMaxRetryAttempts": @([self getMaxRetryAttempts]),
        @"currentAttempt": @(self.currentAttempt),
        @"isExecuting": @(self.isExecuting),
        @"isFinished": @(self.isFinished),
        @"isCanceled": @(self.isCancelled)
    };
    return [NSString stringWithFormat:@"async task info:%@", dic];
}

- (void)executeCompletionHandler:(BOOL)success error:(nullable NSError *)error {
    if (self.completionHandler) {
        self.completionHandler(success, error, self.taskIdentifier);
    }
}

@end


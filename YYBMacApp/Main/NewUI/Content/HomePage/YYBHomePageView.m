//
//  YYBHomePageView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import "YYBHomePageView.h"
#import "Masonry.h"

@implementation YYBHomePageView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupView];
        [self addDetailContent];
    }
    return self;
}

- (void)setupView {
    self.wantsLayer = YES;
}

- (void)addDetailContent {
    // 添加一些示例内容区块
    NSInteger columns = 3;
    NSInteger rows = 2;
    CGFloat padding = 15;
    
    for (NSInteger row = 0; row < rows; row++) {
        for (NSInteger col = 0; col < columns; col++) {
            NSView *contentBlockView = [[NSView alloc] init];
            contentBlockView.wantsLayer = YES;
            
            // 使用不同的颜色来区分内容块
            CGFloat hue = (CGFloat)((row * columns + col) % 6) / 6.0;
            NSColor *blockColor = [NSColor colorWithHue:hue saturation:0.3 brightness:0.3 alpha:1.0];
            contentBlockView.layer.backgroundColor = blockColor.CGColor;
            contentBlockView.layer.cornerRadius = 8;
            
            [self addSubview:contentBlockView];
            
            [contentBlockView mas_makeConstraints:^(MASConstraintMaker *make) {
                CGFloat width = (100.0 / columns);
                CGFloat height = (100.0 / rows);
                
                make.left.equalTo(self).offset(padding + col * (width + padding));
                make.top.equalTo(self).offset(padding + row * (height + padding));
                make.width.equalTo(self).multipliedBy(width / 100.0).offset(-padding * 2);
                make.height.equalTo(self).multipliedBy(height / 100.0).offset(-padding * 2);
            }];
        }
    }
}

@end

//
//  YYBMainContentView.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import <Cocoa/Cocoa.h>
#import "YYBMainNavigationItem.h"
#import "YYBMainNavigationModel.h"
#import "YYBHomePageView.h"
#import "YYBWKWebView.h"

NS_ASSUME_NONNULL_BEGIN

@class YYBMainContentView;

/**
 * YYBMainContentView的代理协议
 */
@protocol YYBMainContentViewDelegate <NSObject>

@optional
/**
 * 内容视图切换时调用
 * @param contentView 内容视图
 */
- (void)contentViewDidSwitchContent:(YYBMainContentView *)contentView;

@end

@interface YYBMainContentView : NSView

/**
 * 代理对象
 */
@property (nonatomic, weak) id<YYBMainContentViewDelegate> delegate;

@property (nonatomic, strong, readonly) YYBHomePageView *homePageView;
@property (nonatomic, strong, readonly) YYBWKWebView *webView;

/**
 * 加载新的内容视图
 * @param item 导航项
 * @param animated 是否使用动画
 */
- (void)loadNavigationItem:(YYBMainNavigationItem *)item animated:(BOOL)animated;

/**
 * 返回到上一个导航项
 */
- (void)goBack;

/**
 * 前进到下一个导航项
 */
- (void)goForward;

/**
 * 刷新当前页面
 */
- (void)reload;

/**
 * 判断当前是否可以返回
 * @return 如果可以返回上一个导航项返回YES，否则返回NO
 */
- (BOOL)canGoBack;

/**
 * 获取当前导航项
 * @return 当前导航项
 */
- (nullable YYBMainNavigationItem *)currentNavigationItem;

@end

NS_ASSUME_NONNULL_END

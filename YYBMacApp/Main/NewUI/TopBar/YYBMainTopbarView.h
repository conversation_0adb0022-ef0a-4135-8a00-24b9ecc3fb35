//
//  YYBMainTopbarView.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import <Cocoa/Cocoa.h>
#import "YYBTopbarModel.h"
#import "YYBTopbarItem.h"
#import "YYBTopbarBackButton.h"
#import "YYBTopbarSearchBar.h"

NS_ASSUME_NONNULL_BEGIN

@class YYBMainTopbarView;

/**
 * 顶部工具栏代理协议
 */
@protocol YYBMainTopbarViewDelegate <NSObject>

@optional
/**
 * 点击返回按钮时调用
 */
- (void)topbarViewDidClickBackButton:(YYBMainTopbarView *)topbarView;

/**
 * 点击工具栏项时调用
 */
- (void)topbarView:(YYBMainTopbarView *)topbarView didClickItem:(YYBTopbarItem *)item;

/**
 * 搜索框内容变化时调用
 */
- (void)topbarView:(YYBMainTopbarView *)topbarView searchTextDidChange:(NSString *)searchText;

/**
 * 搜索框提交搜索时调用
 */
- (void)topbarView:(YYBMainTopbarView *)topbarView didSubmitSearchText:(NSString *)searchText;

@end

/**
 * 顶部工具栏数据源协议
 */
@protocol YYBMainTopbarViewDataSource <NSObject>

@optional
/**
 * 获取顶部工具栏的数据模型
 */
- (nullable YYBTopbarModel *)topbarModelForTopbarView:(YYBMainTopbarView *)topbarView;

@end

/**
 * 应用顶部工具栏视图
 */
@interface YYBMainTopbarView : NSView

/**
 * 代理对象
 */
@property (nonatomic, weak) id<YYBMainTopbarViewDelegate> delegate;

/**
 * 数据源对象
 */
@property (nonatomic, weak) id<YYBMainTopbarViewDataSource> dataSource;

/**
 * 启用或禁用返回按钮
 */
- (void)setBackButtonEnabled:(BOOL)enabled;

/**
 * 重新加载工具栏数据
 */
- (void)reloadData;

@end

NS_ASSUME_NONNULL_END

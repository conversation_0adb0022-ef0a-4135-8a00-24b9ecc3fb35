//
//  YYBTopbarItem.h
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import <Foundation/Foundation.h>
#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBTopbarItem : NSObject

/**
 * 工具栏项的唯一标识符
 */
@property (nonatomic, copy, readonly) NSString *identifier;

/**
 * 工具栏项的标题
 */
@property (nonatomic, copy, readonly) NSString *title;

/**
 * 工具栏项的图标
 */
@property (nonatomic, strong, readonly) NSImage *icon;

/**
 * 工具栏项是否可见
 */
@property (nonatomic, assign) BOOL visible;

/**
 * 工具栏项是否可用
 */
@property (nonatomic, assign) BOOL enabled;

/**
 * 使用标识符和图标初始化工具栏项
 */
- (instancetype)initWithIdentifier:(NSString *)identifier icon:(nullable NSImage *)icon;

/**
 * 使用标识符、标题和图标初始化工具栏项
 */
- (instancetype)initWithIdentifier:(NSString *)identifier title:(nullable NSString *)title icon:(nullable NSImage *)icon;

@end

NS_ASSUME_NONNULL_END

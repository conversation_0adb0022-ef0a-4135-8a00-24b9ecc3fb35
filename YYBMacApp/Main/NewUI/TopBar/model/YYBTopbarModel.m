//
//  YYBTopbarModel.m
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import "YYBTopbarModel.h"

@implementation YYBTopbarModel

- (instancetype)init {
    self = [super init];
    if (self) {
        self.items = [NSMutableArray array];
        self.showBackButton = YES;
        self.showSearchField = YES;
    }
    return self;
}

- (instancetype)initWithItems:(NSArray<YYBTopbarItem *> *)items {
    self = [self init];
    if (self) {
        [self addItems:items];
    }
    return self;
}

- (void)addItem:(YYBTopbarItem *)item {
    if (item) {
        [self.items addObject:item];
    }
}

- (void)addItems:(NSArray<YYBTopbarItem *> *)items {
    if (items.count > 0) {
        [self.items addObjectsFromArray:items];
    }
}

- (nullable YYBTopbarItem *)itemWithIdentifier:(NSString *)identifier {
    for (YYBTopbarItem *item in self.items) {
        if ([item.identifier isEqualToString:identifier]) {
            return item;
        }
    }
    return nil;
}

- (void)removeItemWithIdentifier:(NSString *)identifier {
    YYBTopbarItem *itemToRemove = [self itemWithIdentifier:identifier];
    if (itemToRemove) {
        [self.items removeObject:itemToRemove];
    }
}

@end
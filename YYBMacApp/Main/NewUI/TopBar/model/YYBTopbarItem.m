//
//  YYBTopbarItem.m
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import "YYBTopbarItem.h"

@interface YYBTopbarItem ()

@property (nonatomic, copy) NSString *identifier;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, strong) NSImage *icon;

@end

@implementation YYBTopbarItem

- (instancetype)initWithIdentifier:(NSString *)identifier icon:(nullable NSImage *)icon {
    return [self initWithIdentifier:identifier title:nil icon:icon];
}

- (instancetype)initWithIdentifier:(NSString *)identifier title:(nullable NSString *)title icon:(nullable NSImage *)icon {
    self = [super init];
    if (self) {
        _identifier = [identifier copy];
        _title = [title copy];
        _icon = icon;
        _visible = YES;
        _enabled = YES;
    }
    return self;
}

@end

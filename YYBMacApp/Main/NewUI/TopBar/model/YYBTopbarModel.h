//
//  YYBTopbarModel.h
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import <Foundation/Foundation.h>
#import "YYBTopbarItem.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBTopbarModel : NSObject

/**
 * 工具栏项数组
 */
@property (nonatomic, strong) NSMutableArray<YYBTopbarItem *> *items;

/**
 * 是否显示返回按钮
 */
@property (nonatomic, assign) BOOL showBackButton;

/**
 * 是否显示搜索框
 */
@property (nonatomic, assign) BOOL showSearchField;

/**
 * 初始化工具栏模型
 */
- (instancetype)init;

/**
 * 使用工具栏项数组初始化工具栏模型
 */
- (instancetype)initWithItems:(NSArray<YYBTopbarItem *> *)items;

/**
 * 添加工具栏项
 */
- (void)addItem:(YYBTopbarItem *)item;

/**
 * 添加多个工具栏项
 */
- (void)addItems:(NSArray<YYBTopbarItem *> *)items;

/**
 * 根据标识符获取工具栏项
 */
- (nullable YYBTopbarItem *)itemWithIdentifier:(NSString *)identifier;

/**
 * 根据标识符移除工具栏项
 */
- (void)removeItemWithIdentifier:(NSString *)identifier;

@end

NS_ASSUME_NONNULL_END
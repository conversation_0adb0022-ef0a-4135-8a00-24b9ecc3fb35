//
//  YYBTopbarBackButton.h
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@class YYBTopbarBackButton;

@protocol YYBTopbarBackButtonDelegate <NSObject>

@optional
- (void)topbarBackButtonDidClick:(YYBTopbarBackButton *)backButton;

@end

@interface YYBTopbarBackButton : NSVisualEffectView

@property (nonatomic, weak) id<YYBTopbarBackButtonDelegate> delegate;

// 设置图标
- (void)setIcon:(NSImage *)icon;

// 设置是否可用
- (void)setEnabled:(BOOL)enabled;

@end

NS_ASSUME_NONNULL_END

//
//  YYBTopbarItemView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/18.
//

#import "YYBTopbarItemView.h"
#import "Masonry.h"

@interface YYBTopbarItemView ()

@property (nonatomic, strong, readwrite) YYBTopbarItem *item;
@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, strong) NSVisualEffectView *backgroundView;
@property (nonatomic, strong) NSTrackingArea *trackingArea;

@end

@implementation YYBTopbarItemView

- (instancetype)initWithItem:(YYBTopbarItem *)item {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
        _item = item;
        _state = YYBTopbarItemViewStateNormal;
        [self setupView];
        [self setupSubviews];
        [self updateContent];
    }
    return self;
}

- (void)setupView {
    self.wantsLayer = YES;
    self.layer.masksToBounds = YES;
}

- (void)setupSubviews {
    // 创建背景视图
    self.backgroundView = [[NSVisualEffectView alloc] initWithFrame:self.bounds];
    self.backgroundView.material = NSVisualEffectMaterialSelection;
    self.backgroundView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.backgroundView.state = NSVisualEffectStateActive;
    self.backgroundView.wantsLayer = YES;
    self.backgroundView.alphaValue = 0.3;
    [self addSubview:self.backgroundView];
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 创建图标视图
    self.iconView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.height.equalTo(@24);
    }];
}

- (void)updateContent {
    self.iconView.image = self.item.icon;
    self.toolTip = self.item.title;
    
    // 根据item的状态更新视图
    self.hidden = !self.item.visible;
    self.alphaValue = self.item.enabled ? 1.0 : 0.5;
}

- (void)setCornerRadius:(CGFloat)cornerRadius {
    self.layer.cornerRadius = cornerRadius;
    self.backgroundView.layer.cornerRadius = cornerRadius;
}

- (void)updateState:(YYBTopbarItemViewState)state {
    if (_state == state) {
        return;
    }
    
    _state = state;
    
    switch (state) {
        case YYBTopbarItemViewStateNormal:
            self.backgroundView.alphaValue = 0.3;
            break;
            
        case YYBTopbarItemViewStateHover:
            self.backgroundView.alphaValue = 0.5;
            break;
            
        case YYBTopbarItemViewStatePressed:
            self.backgroundView.alphaValue = 0.8;
            break;
    }
}

#pragma mark - 鼠标事件处理

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }
    
    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    if (self.item.enabled) {
        [self updateState:YYBTopbarItemViewStateHover];
    }
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    [self updateState:YYBTopbarItemViewStateNormal];
}

- (void)mouseDown:(NSEvent *)event {
    [super mouseDown:event];
    if (self.item.enabled) {
        [self updateState:YYBTopbarItemViewStatePressed];
    }
}

- (void)mouseUp:(NSEvent *)event {
    [super mouseUp:event];
    
    if (!self.item.enabled) {
        return;
    }
    
    NSPoint point = [self convertPoint:event.locationInWindow fromView:nil];
    if (NSPointInRect(point, self.bounds)) {
        [self updateState:YYBTopbarItemViewStateHover];
        
        // 通知代理点击事件
        if ([self.delegate respondsToSelector:@selector(topbarItemViewDidClick:)]) {
            [self.delegate topbarItemViewDidClick:self];
        }
    } else {
        [self updateState:YYBTopbarItemViewStateNormal];
    }
}

@end

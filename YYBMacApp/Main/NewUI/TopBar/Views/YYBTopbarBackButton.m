//
//  YYBTopbarBackButton.m
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import "YYBTopbarBackButton.h"
#import "Masonry.h"
#import "NSColor+Utils.h"

typedef NS_ENUM(NSUInteger, YYBTopbarBackButtonState) {
    YYBTopbarBackButtonStateNormal,
    YYBTopbarBackButtonStateHover,
    YYBTopbarBackButtonStateDisabled
};

@interface YYBTopbarBackButton ()

@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, assign) YYBTopbarBackButtonState buttonState;
@property (nonatomic, assign) BOOL isEnabled;
@property (nonatomic, strong) NSTrackingArea *trackingArea;

@end

@implementation YYBTopbarBackButton

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 设置视觉效果视图属性
    self.material = NSVisualEffectMaterialHUDWindow;
    self.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.buttonState = YYBTopbarBackButtonStateNormal;
    self.isEnabled = YES;
    
    // 创建图标视图
    _iconView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    _iconView.imageScaling = NSImageScaleProportionallyDown;
    [self addSubview:_iconView];
    
    // 使用Masonry设置约束
    [_iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.height.equalTo(@16);
    }];
    
    // 设置默认图标
    NSImage *backIcon = [NSImage imageNamed:@"back_icon"];
    if (backIcon) {
        [self setIcon:backIcon];
    }
    
    // 更新视图状态
    [self updateViewForState:self.buttonState];
    
    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (_trackingArea) {
        [self removeTrackingArea:_trackingArea];
    }
    
    NSTrackingAreaOptions options = (NSTrackingActiveInKeyWindow | NSTrackingMouseEnteredAndExited | NSTrackingMouseMoved);
    _trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                 options:options
                                                   owner:self
                                                userInfo:nil];
    [self addTrackingArea:_trackingArea];
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    if (self.isEnabled) {
        self.buttonState = YYBTopbarBackButtonStateHover;
        [self updateViewForState:self.buttonState];
    }
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    if (self.isEnabled) {
        self.buttonState = YYBTopbarBackButtonStateNormal;
        [self updateViewForState:self.buttonState];
    }
}

- (void)handleClick:(NSGestureRecognizer *)gestureRecognizer {
    if (self.isEnabled) {
        if ([self.delegate respondsToSelector:@selector(topbarBackButtonDidClick:)]) {
            [self.delegate topbarBackButtonDidClick:self];
        }
    }
}

- (void)updateViewForState:(YYBTopbarBackButtonState)state {
    switch (state) {
        case YYBTopbarBackButtonStateNormal:
            self.alphaValue = 1.0;
            self.layer.backgroundColor = [NSColor clearColor].CGColor;
            break;
            
        case YYBTopbarBackButtonStateHover:
            self.alphaValue = 1.0;
            self.layer.backgroundColor = [NSColor colorWithHexString:@"#2A2A2A"].CGColor;
            break;
            
        case YYBTopbarBackButtonStateDisabled:
            self.alphaValue = 0.5;
            self.layer.backgroundColor = [NSColor clearColor].CGColor;
            break;
    }
}

#pragma mark - Public Methods

- (void)setIcon:(NSImage *)icon {
    self.iconView.image = icon;
}

- (void)setEnabled:(BOOL)enabled {
    _isEnabled = enabled;
    self.buttonState = enabled ? YYBTopbarBackButtonStateNormal : YYBTopbarBackButtonStateDisabled;
    [self updateViewForState:self.buttonState];
}

@end

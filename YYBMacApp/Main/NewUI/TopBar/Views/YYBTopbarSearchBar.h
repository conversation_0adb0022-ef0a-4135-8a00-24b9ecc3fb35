//
//  YYBTopbarSearchBar.h
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/19.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@class YYBTopbarSearchBar;

@protocol YYBTopbarSearchBarDelegate <NSObject>

@optional
- (void)searchBar:(YYBTopbarSearchBar *)searchBar textDidChange:(NSString *)searchText;
- (void)searchBarDidBeginEditing:(YYBTopbarSearchBar *)searchBar;
- (void)searchBarDidEndEditing:(YYBTopbarSearchBar *)searchBar;
- (void)searchBarSearchButtonClicked:(YYBTopbarSearchBar *)searchBar;

@end

@interface YYBTopbarSearchBar : NSVisualEffectView

@property (nonatomic, weak) id<YYBTopbarSearchBarDelegate> delegate;
@property (nonatomic, copy, readonly) NSString *searchText;
@property (nonatomic, strong, readonly) NSTextField *searchField;

// 设置占位文本
- (void)setPlaceholder:(NSString *)placeholder;

// 清空搜索框
- (void)clearSearchText;

// 设置搜索框是否可编辑
- (void)setEditable:(BOOL)editable;

@end

NS_ASSUME_NONNULL_END
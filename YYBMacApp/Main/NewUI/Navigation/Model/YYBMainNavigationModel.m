//
//  YYBMainNavigationModel.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/14.
//

#import "YYBMainNavigationModel.h"

@implementation YYBMainNavigationModel

- (instancetype)init {
    self = [super init];
    if (self) {
        _items = [NSMutableArray array];
    }
    return self;
}

- (instancetype)initWithItems:(NSArray<YYBMainNavigationItem *> *)items {
    self = [self init];
    if (self) {
        [self addItems:items];
    }
    return self;
}

#pragma mark - 添加操作

- (void)addItem:(YYBMainNavigationItem *)item {
    if (item) {
        [self.items addObject:item];
    }
}

- (void)addItems:(NSArray<YYBMainNavigationItem *> *)items {
    if (items.count > 0) {
        [self.items addObjectsFromArray:items];
    }
}

- (void)insertItem:(YYBMainNavigationItem *)item atIndex:(NSUInteger)index {
    if (item && index <= self.items.count) {
        [self.items insertObject:item atIndex:index];
    }
}

#pragma mark - 移除操作

- (void)removeItem:(YYBMainNavigationItem *)item {
    if (item) {
        [self.items removeObject:item];
        
        // 如果移除的是当前选中的项，则清空选中状态
        if (self.selectedItem == item) {
            self.selectedItem = nil;
        }
    }
}

- (void)removeItemWithIdentifier:(NSString *)identifier {
    YYBMainNavigationItem *item = [self itemWithIdentifier:identifier];
    if (item) {
        [self removeItem:item];
    }
}

- (void)removeItemAtIndex:(NSUInteger)index {
    if (index < self.items.count) {
        YYBMainNavigationItem *item = self.items[index];
        [self removeItem:item];
    }
}

- (void)removeAllItems {
    [self.items removeAllObjects];
    self.selectedItem = nil;
}

#pragma mark - 获取操作

- (nullable YYBMainNavigationItem *)itemWithIdentifier:(NSString *)identifier {
    if (!identifier) {
        return nil;
    }
    
    for (YYBMainNavigationItem *item in self.items) {
        if ([item.identifier isEqualToString:identifier]) {
            return item;
        }
    }
    
    return nil;
}

- (nullable YYBMainNavigationItem *)itemAtIndex:(NSUInteger)index {
    if (index < self.items.count) {
        return self.items[index];
    }
    return nil;
}

- (NSArray<YYBMainNavigationItem *> *)visibleItems {
    NSMutableArray *visibleItems = [NSMutableArray array];
    
    for (YYBMainNavigationItem *item in self.items) {
        if (item.isVisible) {
            [visibleItems addObject:item];
        }
    }
    
    return [visibleItems copy];
}

#pragma mark - 选择操作

- (void)selectItem:(YYBMainNavigationItem *)item {
    if (item && [self.items containsObject:item]) {
        // 取消之前选中项的选中状态
        if (self.selectedItem) {
            self.selectedItem.isSelected = NO;
        }
        
        // 设置新的选中项
        item.isSelected = YES;
        self.selectedItem = item;
    }
}

- (void)selectItemWithIdentifier:(NSString *)identifier {
    YYBMainNavigationItem *item = [self itemWithIdentifier:identifier];
    if (item) {
        [self selectItem:item];
    }
}

- (void)selectItemAtIndex:(NSUInteger)index {
    YYBMainNavigationItem *item = [self itemAtIndex:index];
    if (item) {
        [self selectItem:item];
    }
}

@end

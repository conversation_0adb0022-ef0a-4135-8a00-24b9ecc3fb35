//
//  YYBMainNavigationModel.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/14.
//

#import <Foundation/Foundation.h>
#import "YYBMainNavigationItem.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBMainNavigationModel : NSObject

// 导航项数组
@property (nonatomic, strong) NSMutableArray<YYBMainNavigationItem *> *items;

// 当前选中的导航项
@property (nonatomic, strong, nullable) YYBMainNavigationItem *selectedItem;

// 初始化方法
- (instancetype)init;
- (instancetype)initWithItems:(NSArray<YYBMainNavigationItem *> *)items;

// 添加导航项
- (void)addItem:(YYBMainNavigationItem *)item;

// 添加多个导航项
- (void)addItems:(NSArray<YYBMainNavigationItem *> *)items;

// 插入导航项
- (void)insertItem:(YYBMainNavigationItem *)item atIndex:(NSUInteger)index;

// 移除导航项
- (void)removeItem:(YYBMainNavigationItem *)item;
- (void)removeItemWithIdentifier:(NSString *)identifier;
- (void)removeItemAtIndex:(NSUInteger)index;

// 获取导航项
- (nullable YYBMainNavigationItem *)itemWithIdentifier:(NSString *)identifier;
- (nullable YYBMainNavigationItem *)itemAtIndex:(NSUInteger)index;

// 选择导航项
- (void)selectItem:(YYBMainNavigationItem *)item;
- (void)selectItemWithIdentifier:(NSString *)identifier;
- (void)selectItemAtIndex:(NSUInteger)index;

// 清空所有导航项
- (void)removeAllItems;

// 获取可见的导航项
- (NSArray<YYBMainNavigationItem *> *)visibleItems;

@end

NS_ASSUME_NONNULL_END
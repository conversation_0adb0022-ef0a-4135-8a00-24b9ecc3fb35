//
//  YYBMainNavigationItemView.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/14.
//

#import <Cocoa/Cocoa.h>
#import "YYBMainNavigationItem.h"

NS_ASSUME_NONNULL_BEGIN

@class YYBMainNavigationItemView;

/**
 * 导航项点击回调协议
 */
@protocol YYBMainNavigationItemViewDelegate <NSObject>

@optional
/**
 * 导航项被点击时的回调
 */
- (void)navigationItemViewDidClick:(YYBMainNavigationItemView *)itemView;

@end

/**
 * app的主页面左侧导航栏列表项
 */
@interface YYBMainNavigationItemView : NSView

/**
 * 导航项数据模型
 */
@property (nonatomic, strong) YYBMainNavigationItem *item;

/**
 * 代理对象
 */
@property (nonatomic, weak) id<YYBMainNavigationItemViewDelegate> delegate;

/**
 * 是否处于选中状态
 */
@property (nonatomic, assign, getter=isSelected) BOOL selected;

/**
 * 使用导航项数据模型初始化视图
 */
- (instancetype)initWithItem:(YYBMainNavigationItem *)item;

/**
 * 更新视图显示
 */
- (void)updateView;

@end

NS_ASSUME_NONNULL_END

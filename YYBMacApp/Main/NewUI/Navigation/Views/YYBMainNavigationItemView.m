//
//  YYBMainNavigationItemView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/14.
//

#import "YYBMainNavigationItemView.h"
#import "Masonry.h"

// 定义导航项视图状态枚举
typedef NS_ENUM(NSUInteger, YYBNavigationItemViewState) {
    YYBNavigationItemViewStateNormal,   // 正常状态
    YYBNavigationItemViewStateHover,    // 鼠标悬停状态
    YYBNavigationItemViewStateSelected  // 选中状态
};

@interface YYBMainNavigationItemView ()

@property (nonatomic, strong) NSImageView *iconImageView;
@property (nonatomic, strong) NSVisualEffectView *containerView;
@property (nonatomic, strong) NSTrackingArea *trackingArea;
@property (nonatomic, assign) YYBNavigationItemViewState viewState;

@end

@implementation YYBMainNavigationItemView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (instancetype)initWithItem:(YYBMainNavigationItem *)item {
    self = [self initWithFrame:NSZeroRect];
    if (self) {
        _item = item;
        [self updateView];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // 设置初始状态
    self.viewState = YYBNavigationItemViewStateNormal;
    
    // 创建容器视图
    self.containerView = [[NSVisualEffectView alloc] init];
    self.containerView.wantsLayer = YES;
    self.containerView.material = NSVisualEffectMaterialHUDWindow; // 使用HUD风格的材质
    self.containerView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    self.containerView.state = NSVisualEffectStateActive;
    self.containerView.layer.cornerRadius = 10.0;
    self.containerView.layer.masksToBounds = YES;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 创建图标视图
    self.iconImageView = [[NSImageView alloc] init];
    self.iconImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self.containerView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.containerView);
        make.width.height.equalTo(@20.0f);
    }];
    
    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }
    
    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

#pragma mark - 属性设置方法

- (void)setItem:(YYBMainNavigationItem *)item {
    _item = item;
    [self updateView];
}

- (void)setSelected:(BOOL)selected {
    _selected = selected;
    
    // 根据选中状态更新视图状态
    YYBNavigationItemViewState newState = selected ? YYBNavigationItemViewStateSelected : YYBNavigationItemViewStateNormal;
    [self updateViewWithState:newState];
    
    // 更新图标
    [self updateIconForSelectedState];
}

// 更新视图状态
- (void)updateViewWithState:(YYBNavigationItemViewState)state {
    self.viewState = state;
    
    switch (state) {
        case YYBNavigationItemViewStateNormal:
            self.containerView.material = NSVisualEffectMaterialHUDWindow;
            self.containerView.emphasized = NO;
            break;
            
        case YYBNavigationItemViewStateHover:
            self.containerView.material = NSVisualEffectMaterialSelection;
            self.containerView.emphasized = NO;
            break;
            
        case YYBNavigationItemViewStateSelected:
            self.containerView.material = NSVisualEffectMaterialSelection;
            self.containerView.emphasized = YES;
            break;
    }
}

#pragma mark - 视图更新

- (void)updateView {
    if (!self.item) {
        return;
    }
    
    self.selected = self.item.isSelected;
    YYBNavigationItemViewState newState = self.item.isSelected ?
        YYBNavigationItemViewStateSelected : YYBNavigationItemViewStateNormal;
    [self updateViewWithState:newState];
    [self updateIconForSelectedState];
    self.hidden = !self.item.isVisible;
}

- (void)updateIconForSelectedState {
    if (self.selected && self.item.selectedIcon) {
        self.iconImageView.image = self.item.selectedIcon;
    } else {
        self.iconImageView.image = self.item.icon;
    }
    
    // 如果没有图标，创建一个占位图标
    if (!self.iconImageView.image) {
        self.iconImageView.image = [self createPlaceholderIcon];
    }
}

#pragma mark - 事件处理

- (void)handleClick:(NSClickGestureRecognizer *)gesture {
    if ([self.delegate respondsToSelector:@selector(navigationItemViewDidClick:)]) {
        [self.delegate navigationItemViewDidClick:self];
    }
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    
    // 鼠标悬停效果
    if (!self.selected) {
        [self updateViewWithState:YYBNavigationItemViewStateHover];
    }
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    
    // 恢复正常效果
    if (!self.selected) {
        [self updateViewWithState:YYBNavigationItemViewStateNormal];
    }
}

#pragma mark - 辅助方法

- (NSImage *)createPlaceholderIcon {
    NSSize size = NSMakeSize(40, 40);
    NSImage *image = [[NSImage alloc] initWithSize:size];
    
    [image lockFocus];
    
    // Draw a white background
    [[NSColor whiteColor] set];
    NSRectFill(NSMakeRect(0, 0, size.width, size.height));
    
    // Draw colorful shapes
    // Yellow triangle
    NSBezierPath *yellowPath = [NSBezierPath bezierPath];
    [yellowPath moveToPoint:NSMakePoint(5, 5)];
    [yellowPath lineToPoint:NSMakePoint(20, 5)];
    [yellowPath lineToPoint:NSMakePoint(5, 20)];
    [yellowPath closePath];
    [[NSColor yellowColor] set];
    [yellowPath fill];
    
    // Pink/red circle
    NSBezierPath *pinkPath = [NSBezierPath bezierPathWithOvalInRect:NSMakeRect(20, 20, 15, 15)];
    [[NSColor systemPinkColor] set];
    [pinkPath fill];
    
    // Blue rectangle
    NSBezierPath *bluePath = [NSBezierPath bezierPathWithRect:NSMakeRect(10, 25, 15, 10)];
    [[NSColor blueColor] set];
    [bluePath fill];
    
    [image unlockFocus];
    
    return image;
}

@end

//
//  YYBAppLaunchHistoryViewController.h
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import <Cocoa/Cocoa.h>

@class YYBAppLaunchHistoryView;

NS_ASSUME_NONNULL_BEGIN

/**
 * APP打开历史记录控制器
 * 负责管理历史记录视图，监听APP打开和卸载通知
 */
@interface YYBAppLaunchHistoryViewController : NSViewController

/**
 * 历史记录视图
 */
@property (nonatomic, strong, readonly) YYBAppLaunchHistoryView *historyView;

/**
 * 刷新历史记录显示
 */
- (void)refreshHistoryDisplay;

@end

NS_ASSUME_NONNULL_END

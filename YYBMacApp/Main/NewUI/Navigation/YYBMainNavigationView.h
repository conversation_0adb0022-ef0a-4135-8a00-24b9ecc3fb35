//
//  YYBMainNavigationView.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import <Cocoa/Cocoa.h>

@class YYBMainNavigationView;
@class YYBMainNavigationItem;
@class YYBMainNavigationModel;


NS_ASSUME_NONNULL_BEGIN

/**
 * 导航视图数据源协议
 */
@protocol YYBMainNavigationViewDataSource <NSObject>

@required
/**
 * 获取导航视图的数据模型
 * @param navigationView 导航视图
 * @return 导航数据模型
 */
- (nullable YYBMainNavigationModel *)navigationModelForNavigationView:(YYBMainNavigationView *)navigationView;

@end

/**
 * 导航视图代理协议
 */
@protocol YYBMainNavigationViewDelegate <NSObject>

@optional
/**
 * 当导航项被选中时调用
 * @param navigationView 导航视图
 * @param item 被选中的导航项
 */
- (void)navigationView:(YYBMainNavigationView *)navigationView didSelectItem:(YYBMainNavigationItem *)item;

@end

/**
 * app的主页面左侧导航栏
 */
@interface YYBMainNavigationView : NSView

/**
 * 数据源对象
 */
@property (nonatomic, weak) id<YYBMainNavigationViewDataSource> dataSource;

/**
 * 代理对象
 */
@property (nonatomic, weak) id<YYBMainNavigationViewDelegate> delegate;

/**
 * 导航数据模型
 */
@property (nonatomic, strong, readonly) YYBMainNavigationModel *navigationModel;

/**
 * 重新加载导航数据
 */
- (void)reloadData;

/**
 * 选择指定的导航项
 * @param item 要选择的导航项
 */
- (void)selectItem:(YYBMainNavigationItem *)item;

/**
 * 更新选中的导航项
 * @param item 要选中的导航项
 */
- (void)updateSelectedItem:(YYBMainNavigationItem *)item;

@end

NS_ASSUME_NONNULL_END

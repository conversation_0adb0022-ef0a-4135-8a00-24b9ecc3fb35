//
//  MainViewController.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/7.
//

#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import "OldMainViewController.h"
#import "YYBWKWebView.h"
#import "MainWKWebView.h"
#import "YYBDefine.h"

static NSString *const kMainWebViewURL = @"kMainWebViewURL";
//static NSString *const defaultMainWebViewURL = @"http://9.135.95.152:3000";

static NSString *const defaultMainWebViewURL = @"https://testmac.yyb.qq.com/";
//static NSString *const defaultMainWebViewURL = @"https://devmaccclient.yyb.qq.com/";
//static NSString *const defaultMainWebViewURL = @"";

@interface OldMainViewController ()

@property (strong, nonatomic) YYBWKWebView *webView;
@property (strong, nonatomic) NSString *rootUrl;

@end

@implementation OldMainViewController

- (instancetype)init {
    if (self = [super init]) {
        NSString *defaultUrl = defaultMainWebViewURL;
        NSString *url = [[YYBMacMMKV sharedInstance] getStringForKey:kMainWebViewURL];
        if (url.length == 0) {
            url = defaultUrl;
        }
        _rootUrl = url;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    YYBWKWebViewConfig *config = [[YYBWKWebViewConfig alloc] initWithProcess:kProcessAppStore windowId:@"main_window"];
    self.webView = [[YYBWKWebView alloc] initWithFrame:self.view.bounds config:config webViewClass:[MainWKWebView class]];
    self.webView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [self.view addSubview:self.webView];
    [self loadWebViewWithUrl:self.rootUrl];
}

- (void)replaceWebViewWithUrl:(NSString *)url {
    self.rootUrl = url;
    [[YYBMacMMKV sharedInstance] setString:url forKey:kMainWebViewURL];
    [self loadWebViewWithUrl:url];
}

- (void)loadWebViewWithUrl:(NSString *)url {
    if (url.length == 0) {
        return;
    }
    
    NSURL *targetURL = [NSURL URLWithString:url];
    if (!targetURL) {
        return;
    }
    
    NSURLRequest *request = [NSURLRequest requestWithURL:targetURL];
    [self.webView loadRequest:request];
}

@end

//
//  WKWebViewEx.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/9.
//

#import "MainWKWebView.h"
#import "YYBFile.h"
#import "YYBApkPackage.h"

@interface MainWKWebView ()

@end

@implementation MainWKWebView

- (instancetype)initWithFrame:(NSRect)frame configuration:(WKWebViewConfiguration *)configuration {
    self = [super initWithFrame:frame configuration:configuration];
    if (self) {
        [self registerForDraggedTypes:@[NSPasteboardTypeFileURL]];
    }
    return self;
}

- (NSDragOperation)draggingEntered:(id<NSDraggingInfo>)sender {
    NSPasteboard *pboard = [sender draggingPasteboard];
    if ([[pboard types] containsObject:NSPasteboardTypeFileURL]) {
        return NSDragOperationCopy;
    }
    return NSDragOperationNone;
}

- (BOOL)performDragOperation:(id<NSDraggingInfo>)sender {
    NSPasteboard *pboard = [sender draggingPasteboard];
    if (![[pboard types] containsObject:NSPasteboardTypeFileURL]) {
        return NO;
    }
    
    NSArray<NSPasteboardItem *> *items = [pboard pasteboardItems];
    for (NSPasteboardItem *item in items) {
        NSString *urlString = [item stringForType:NSPasteboardTypeFileURL];
        if (![self isValidApkFileURLString:urlString]) {
            continue;
        }
        
        NSURL *url = [NSURL URLWithString:urlString];
        NSString *apkPath = nil;
        BOOL valid = [YYBFile validateAndResolveFilePath:url.path resolvedPath:&apkPath error:nil];
        if (valid && apkPath.length > 0) {
            [self installApkAtPath:apkPath];
        }
    }
    return YES;
}

- (BOOL)isValidApkFileURLString:(NSString *)urlString {
    if (urlString.length == 0) {
        return NO;
    }
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        return NO;
    }
    return [[url.path pathExtension].lowercaseString isEqualToString:@"apk"];
}

- (void)installApkAtPath:(NSString *)apkPath {
    
    InstallApkInfo* apkInfo = [[InstallApkInfo alloc] init];
    apkInfo.filePath = apkPath;
    [[YYBApkPackage shared] installLocalApp:apkInfo
                                 completion:^(InstallApkInfo * _Nullable   info, NSInteger retCode, NSString * _Nullable  msg) {
        if (retCode == 0) {
            NSLog(@"安装成功");
        } else {
            NSLog(@"安装失败: %@", info.name);
        }
    }];
}

@end

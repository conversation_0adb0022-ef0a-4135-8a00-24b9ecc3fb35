//
//  main.m
//  Package
//
//  Created by <PERSON> on 2025/6/26.
//

#import <Cocoa/Cocoa.h>
#include <dlfcn.h>
#import "AppDelegate.h"
#include <objc/runtime.h>
int main(int argc, const char * argv[]) {
    @autoreleasepool {
        NSLog(@"service main");
        NSApplication *app = [NSApplication sharedApplication];
        NSDictionary *infoDict = [[NSBundle mainBundle] infoDictionary];
        NSString *yybModeDir = infoDict[@"YYBModeDir"];

        if ([[NSFileManager defaultManager] fileExistsAtPath:yybModeDir]) {
            NSString *libPath = [NSString stringWithFormat:@"%@/Contents/Frameworks/YYBPackageLib.framework/YYBPackageLib", yybModeDir];
            void *libHandle = dlopen(libPath.UTF8String, RTLD_NOW | RTLD_GLOBAL);
            if (!libHandle) {
                char *error = dlerror();
                NSLog(@"[Error] Failed to load FusionSDK at %@: %s", libPath, error ? error : "Unknown error");
                NSAlert *alert = [[NSAlert alloc] init];
                alert.messageText = [NSString stringWithFormat:@"打开失败：%@-%@", libPath, [NSString stringWithUTF8String:error]];
                alert.alertStyle = NSAlertStyleWarning;
                [alert addButtonWithTitle:@"确定"];
                [alert runModal];
            }
        } else {
            NSLog(@"lib is not exists");
            NSAlert *alert = [[NSAlert alloc] init];
            alert.messageText = @"没有找到应用宝商店";
            alert.alertStyle = NSAlertStyleWarning;
            [alert addButtonWithTitle:@"确定"];
            [alert runModal];
        }
        Class cls = objc_getClass("YYBAppDelegate");
        id obj = [[cls alloc] init];
        [app setDelegate:obj];
        [app run];
        return 0;
    }
}

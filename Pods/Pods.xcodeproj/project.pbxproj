// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		000832FE1EAF036D619E0E61F3B34326 /* SSZipCommon.h in Headers */ = {isa = PBXBuildFile; fileRef = F19FD4C6BF58AA18B92DA7DFEC6D174F /* SSZipCommon.h */; settings = {ATTRIBUTES = (Project, ); }; };
		034A13EC5ACE84A0EDDA2EB5FBAB679A /* mz_strm_split.c in Sources */ = {isa = PBXBuildFile; fileRef = 1F7C75AEC8974EE1843BF2B2F84F3CC1 /* mz_strm_split.c */; };
		0351E581F014FC8B860327E924FADA67 /* JLRoutes.m in Sources */ = {isa = PBXBuildFile; fileRef = 200ED36448C68A7CBEC2079313C7812D /* JLRoutes.m */; };
		09ACECBDE9AE6FDCFFABFA1B0D7B4920 /* mz_strm_buf.c in Sources */ = {isa = PBXBuildFile; fileRef = 88EDEA0220AE8AEB85301FFEFB3F2E2C /* mz_strm_buf.c */; };
		0B16507B3C6C424B39C3553C583DA08C /* JLRRouteDefinition.h in Headers */ = {isa = PBXBuildFile; fileRef = EE4223BBB635473D560A331747F84DDF /* JLRRouteDefinition.h */; settings = {ATTRIBUTES = (Project, ); }; };
		0C933810E06DB2A41562D50BCA090AE2 /* mz_strm_mem.c in Sources */ = {isa = PBXBuildFile; fileRef = 009ADB3C9294797DE2640BE9DEAB185E /* mz_strm_mem.c */; };
		0FF9FBE41BDBDE8257EE8827A40337D2 /* mz_zip.c in Sources */ = {isa = PBXBuildFile; fileRef = 080279E30DC5CC9A8521EE443377DB04 /* mz_zip.c */; };
		1487775A1FDB154A667B6CB002A722F1 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = E4C503FA8653709150CDD1561F62A9D8 /* PrivacyInfo.xcprivacy */; };
		1970768CA4899F2C709CE402E5946806 /* mz_strm.c in Sources */ = {isa = PBXBuildFile; fileRef = 0734C59FCB16441076E85737B6897142 /* mz_strm.c */; };
		1CF2C68F10741292A7BA53CEAD57BA7C /* SSZipArchive.m in Sources */ = {isa = PBXBuildFile; fileRef = 78B6F53ED94AD31CB4468B4EE420EB29 /* SSZipArchive.m */; };
		20EAF10BCBEB9CAA08615C9156E78B5A /* mz_zip_rw.c in Sources */ = {isa = PBXBuildFile; fileRef = A7593EAF07E88F1189DC2A4C322233EB /* mz_zip_rw.c */; };
		279BC4EB48F6DC8369F42851CACBC999 /* JLRoutes.h in Headers */ = {isa = PBXBuildFile; fileRef = 4C0ED29B6216A2884CE877086F2B96F5 /* JLRoutes.h */; settings = {ATTRIBUTES = (Project, ); }; };
		2945097AB81C80CA13CF14AA9F141968 /* JLRoutes-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 829C88B0956E7B5AD5FA2570832BB0BB /* JLRoutes-dummy.m */; };
		2FCD09D677ADAFBAB35A80EDBB8B9017 /* mz_strm_wzaes.c in Sources */ = {isa = PBXBuildFile; fileRef = 5DA07629D44BE74AC430B38D3F271DC4 /* mz_strm_wzaes.c */; };
		34E654E12AC15C818DACE5FF0E2D86E2 /* mz_os.h in Headers */ = {isa = PBXBuildFile; fileRef = 6A735E5E74D556592785ED15AF69A9DD /* mz_os.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3AFC08E18E4387007F66A6217EF49F7A /* mz_crypt.h in Headers */ = {isa = PBXBuildFile; fileRef = CB99B83590962149D8759FFAA0D2ED9D /* mz_crypt.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3C34899BDAF131047D410165B8BD1602 /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 43554F9BA0DE2F7AA2CDD3B392335D0C /* Reachability.m */; };
		4134C08B6C80B1943AC0951F2DD24CD5 /* JLRRouteDefinition.m in Sources */ = {isa = PBXBuildFile; fileRef = EEB05F6DA3C02EE9CAC866FB653B2C44 /* JLRRouteDefinition.m */; };
		43C3F4C9342F01C199EBD10BCF60B521 /* JLRRouteHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = FCED26CA268509CE787550624B1905FA /* JLRRouteHandler.h */; settings = {ATTRIBUTES = (Project, ); }; };
		44578A9EDE012ED011CB525269D7539B /* mz_strm_pkcrypt.h in Headers */ = {isa = PBXBuildFile; fileRef = D2A1B80C050B4622C1E6B9F69B410CBC /* mz_strm_pkcrypt.h */; settings = {ATTRIBUTES = (Project, ); }; };
		4EDB4EE1B5E3A4BB662AFDF3DEC2CBE8 /* mz_strm.h in Headers */ = {isa = PBXBuildFile; fileRef = 6F0752869B6E3C252F98B86062D3518C /* mz_strm.h */; settings = {ATTRIBUTES = (Project, ); }; };
		5F9F7C748666EFC33E3DB274B62733D0 /* mz_compat.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AD0E391C56838CE5D4B26CE30389AA3 /* mz_compat.h */; settings = {ATTRIBUTES = (Project, ); }; };
		612E34C324BD2F630D1FF565F7B8B58A /* mz_strm_os_posix.c in Sources */ = {isa = PBXBuildFile; fileRef = E7B96A27B658A35E8C35CFC4ECAD7109 /* mz_strm_os_posix.c */; };
		691C6B2D531C0FC5C2032EDA4FB2A1EA /* JLRRouteHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 368C6880BDEE771B2EEDC399B0A19BD5 /* JLRRouteHandler.m */; };
		6D53BF080645E381C9AF441A5BB5715D /* Pods-YYBMacApp-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D4DB96579AC7DF6F146B6D9BD6CD042D /* Pods-YYBMacApp-dummy.m */; };
		6FAF1CD26127F07F8056CCFD6AD5A37F /* mz_strm_os.h in Headers */ = {isa = PBXBuildFile; fileRef = 1CAF900E68610A4864DCE2645AFE2AEB /* mz_strm_os.h */; settings = {ATTRIBUTES = (Project, ); }; };
		703DCBECBB3982C5B05BEF414EC16424 /* SSZipArchive.h in Headers */ = {isa = PBXBuildFile; fileRef = B2D46E50EEF9CD0A84BA5D668F8952AC /* SSZipArchive.h */; settings = {ATTRIBUTES = (Project, ); }; };
		727F0328B7FF4DC7636AF0C06DB0A3B6 /* ZipArchive.h in Headers */ = {isa = PBXBuildFile; fileRef = F5DCC6805964B778412D1810D59181D3 /* ZipArchive.h */; settings = {ATTRIBUTES = (Project, ); }; };
		74CE8131927A7FEBEC78C4E12842B3B5 /* mz_zip_rw.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E779DB64A17306F67C00FD8EF58A4C5 /* mz_zip_rw.h */; settings = {ATTRIBUTES = (Project, ); }; };
		7884400D24EB07B6A74E16297CC5458A /* SSZipArchive-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D9E4A52EEBA206F33F01A91B6F0069B4 /* SSZipArchive-dummy.m */; };
		7DA21BA5E5E0E10757253CB48D99B501 /* JLRParsingUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 5B687A22B917880A439DE5EB04E63C1F /* JLRParsingUtilities.h */; settings = {ATTRIBUTES = (Project, ); }; };
		80B4D347E843CB98D4FFBF226E04BA4F /* mz.h in Headers */ = {isa = PBXBuildFile; fileRef = A45D83B1F1B6AC846E5DEA63940A1009 /* mz.h */; settings = {ATTRIBUTES = (Project, ); }; };
		8CF3DA00FD205C2FF408A1987ADC4D1D /* mz_strm_zlib.c in Sources */ = {isa = PBXBuildFile; fileRef = 34DD8BA29DBD8059A735053F3F7D1617 /* mz_strm_zlib.c */; };
		8F38C92E6BAD48EDAE3EADA1D2B15391 /* mz_os.c in Sources */ = {isa = PBXBuildFile; fileRef = 12AD4E2287757843EA2B90BE08C6F094 /* mz_os.c */; };
		B02CE854D19C3B7D945DDA653789DF21 /* JLRRouteResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = 68AAF544C698B91793721FD82C0AF486 /* JLRRouteResponse.m */; };
		B51DFCEDCD33A19CF7D3D62CDF8FFC30 /* JLRRouteRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 5FC7C8EDD3E0871E5F20D2F5D74D43D2 /* JLRRouteRequest.m */; };
		B70533D59E4CCA2AD8E8F89AF3FFF201 /* mz_strm_mem.h in Headers */ = {isa = PBXBuildFile; fileRef = A6B49AEF3FAE96C4DF9150ABBB6FDE60 /* mz_strm_mem.h */; settings = {ATTRIBUTES = (Project, ); }; };
		B7AFFCE5C0A77D5632C91DD63865F7C1 /* mz_os_posix.c in Sources */ = {isa = PBXBuildFile; fileRef = 2C568F7351A60CE8A6568FFDE9BC41DB /* mz_os_posix.c */; };
		BC858A3CCCC42D4F2801FC9E516C5518 /* mz_strm_pkcrypt.c in Sources */ = {isa = PBXBuildFile; fileRef = E762E0922E2E427E6CF64594F16BDD48 /* mz_strm_pkcrypt.c */; };
		BDE94C8D2BF4BEC4CE2B10A4BE4FE805 /* mz_strm_zlib.h in Headers */ = {isa = PBXBuildFile; fileRef = 887446DCF31F2270C3339ABC70FA0D2E /* mz_strm_zlib.h */; settings = {ATTRIBUTES = (Project, ); }; };
		C0DE524F2A493CE480B5C5B1D8A802A6 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 24256A05E000B655AC215DBF7F96E673 /* PrivacyInfo.xcprivacy */; };
		C386B3A83C0D35C497253A96E1C7411B /* mz_crypt_apple.c in Sources */ = {isa = PBXBuildFile; fileRef = 58083E8016066E141BA90F2BA074FB7A /* mz_crypt_apple.c */; };
		CA5092299E1D52A7CE065F46C769804E /* mz_zip.h in Headers */ = {isa = PBXBuildFile; fileRef = F526195E94B61E7015C643CC908EC9A5 /* mz_zip.h */; settings = {ATTRIBUTES = (Project, ); }; };
		D430321AB1027B3FCF2025A9FD9F5C15 /* mz_strm_split.h in Headers */ = {isa = PBXBuildFile; fileRef = E7F92D0523D64ADC8DAFA55684C3D8EC /* mz_strm_split.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DADCFF1B04DC7AB8544A3414B1C70590 /* Reachability.h in Headers */ = {isa = PBXBuildFile; fileRef = C2448F7D6922304F411382999DE4CE8B /* Reachability.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DB79ACC4AAE13CCBFC27F704A47E4B83 /* JLRParsingUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 065D3CF1870E464FC6F446470338EFB5 /* JLRParsingUtilities.m */; };
		DD0FEC1870DC0D556DE32F27A8BEA3AE /* Reachability-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = BA2247AE5388E0242E4DC53EB82B39F4 /* Reachability-dummy.m */; };
		DD7CF499075554A895257B5C90F7AA4B /* mz_strm_buf.h in Headers */ = {isa = PBXBuildFile; fileRef = C95A2D54114D6139AF5845756D8A8028 /* mz_strm_buf.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DE87C2DC796DCD3AE9E79487330EF78A /* SSZipArchive-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 94A12B3BC0E77866E1C59E700B4EEF9E /* SSZipArchive-umbrella.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DF97EED5D0E7C486C8C3EEC14EE66D1F /* mz_crypt.c in Sources */ = {isa = PBXBuildFile; fileRef = 501837943C7794EC0DFA54EBF87D0548 /* mz_crypt.c */; };
		EA9B5AC5BDADAAFB228E43023A144627 /* JLRRouteRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = E39B4447E81DFE7C43BD5676AA34F141 /* JLRRouteRequest.h */; settings = {ATTRIBUTES = (Project, ); }; };
		F67926D7B0AA6FBE4FD14C2D51177BFB /* mz_compat.c in Sources */ = {isa = PBXBuildFile; fileRef = AF1D967FCF65AB43B8E0C95A5AD48ECC /* mz_compat.c */; };
		F8B14CAB486273C9C07C5912B28E22EE /* JLRRouteResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = B7AF7FA370E0ED09D55216557D46A252 /* JLRRouteResponse.h */; settings = {ATTRIBUTES = (Project, ); }; };
		FEC73FD9558BD8A4F53F3D27BA18644E /* mz_strm_wzaes.h in Headers */ = {isa = PBXBuildFile; fileRef = DAA17EBFAD2CE701AF789D2F63CA30DF /* mz_strm_wzaes.h */; settings = {ATTRIBUTES = (Project, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		44980913CE3BB6ED65EF201B7FC39A06 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D8EA004283833F4910C46F5335BE8F07;
			remoteInfo = "SSZipArchive-SSZipArchive";
		};
		934880D97B1B89920A1DBC8C8E7C1A81 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D2787856C227A709315E3C9C4355A440;
			remoteInfo = "Reachability-Reachability_Privacy";
		};
		97C99F1B67C2E49BB28D26588BE90B38 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F60E38364AFF5E1349FF07415B944396;
			remoteInfo = SSZipArchive;
		};
		A5C3FD126C4217CD012776F55386FB33 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 94C1BAA17BCEBC27586488A205D2E0CB;
			remoteInfo = JLRoutes;
		};
		D34BB9B9FEF4F8A5B9A119AB83F5A02A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CAA047C0F5E4106F3904E8497FA17F97;
			remoteInfo = Reachability;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		009ADB3C9294797DE2640BE9DEAB185E /* mz_strm_mem.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_mem.c; path = SSZipArchive/minizip/mz_strm_mem.c; sourceTree = "<group>"; };
		05D780DFA6A89B2EDD18806186A34969 /* Pods-YYBMacApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-YYBMacApp.release.xcconfig"; sourceTree = "<group>"; };
		065D3CF1870E464FC6F446470338EFB5 /* JLRParsingUtilities.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JLRParsingUtilities.m; path = JLRoutes/Classes/JLRParsingUtilities.m; sourceTree = "<group>"; };
		0734C59FCB16441076E85737B6897142 /* mz_strm.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm.c; path = SSZipArchive/minizip/mz_strm.c; sourceTree = "<group>"; };
		080279E30DC5CC9A8521EE443377DB04 /* mz_zip.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_zip.c; path = SSZipArchive/minizip/mz_zip.c; sourceTree = "<group>"; };
		0B91D5E1A6E41A38550F0A20CB732B3F /* Pods-YYBMacApp-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-YYBMacApp-resources.sh"; sourceTree = "<group>"; };
		12AD4E2287757843EA2B90BE08C6F094 /* mz_os.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_os.c; path = SSZipArchive/minizip/mz_os.c; sourceTree = "<group>"; };
		173A28305FA2E2F7DFA229B00E0E4479 /* SSZipArchive.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SSZipArchive.release.xcconfig; sourceTree = "<group>"; };
		1AD0E391C56838CE5D4B26CE30389AA3 /* mz_compat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_compat.h; path = SSZipArchive/minizip/mz_compat.h; sourceTree = "<group>"; };
		1CAF900E68610A4864DCE2645AFE2AEB /* mz_strm_os.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_os.h; path = SSZipArchive/minizip/mz_strm_os.h; sourceTree = "<group>"; };
		1F7C75AEC8974EE1843BF2B2F84F3CC1 /* mz_strm_split.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_split.c; path = SSZipArchive/minizip/mz_strm_split.c; sourceTree = "<group>"; };
		200ED36448C68A7CBEC2079313C7812D /* JLRoutes.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JLRoutes.m; path = JLRoutes/JLRoutes.m; sourceTree = "<group>"; };
		236814CE4A6530F8E7897CD523DE7511 /* JLRoutes.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JLRoutes.debug.xcconfig; sourceTree = "<group>"; };
		24256A05E000B655AC215DBF7F96E673 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "SSZipArchive/Supporting Files/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		2A579D998F34FD06F9442E0E14BF4D1C /* Reachability-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Reachability-prefix.pch"; sourceTree = "<group>"; };
		2C443FF9F65086EC1EB8ED8AA34E718C /* SSZipArchive.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SSZipArchive.debug.xcconfig; sourceTree = "<group>"; };
		2C568F7351A60CE8A6568FFDE9BC41DB /* mz_os_posix.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_os_posix.c; path = SSZipArchive/minizip/mz_os_posix.c; sourceTree = "<group>"; };
		2EF43ABFD9FD152778DA427184572EB9 /* SSZipArchive-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SSZipArchive-prefix.pch"; sourceTree = "<group>"; };
		34DD8BA29DBD8059A735053F3F7D1617 /* mz_strm_zlib.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_zlib.c; path = SSZipArchive/minizip/mz_strm_zlib.c; sourceTree = "<group>"; };
		353BB5A83373E1CD79A095F05C2AA928 /* Pods-YYBMacApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-YYBMacApp.debug.xcconfig"; sourceTree = "<group>"; };
		368C6880BDEE771B2EEDC399B0A19BD5 /* JLRRouteHandler.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JLRRouteHandler.m; path = JLRoutes/Classes/JLRRouteHandler.m; sourceTree = "<group>"; };
		400FF55D0451E7A8F33A3D0D3E11C1B9 /* Reachability */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = Reachability; path = libReachability.a; sourceTree = BUILT_PRODUCTS_DIR; };
		43554F9BA0DE2F7AA2CDD3B392335D0C /* Reachability.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = Reachability.m; sourceTree = "<group>"; };
		47818FA269C04B50A51A8D1D2122C796 /* SSZipArchive-SSZipArchive */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SSZipArchive-SSZipArchive"; path = SSZipArchive.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		4A9A5F00101D6B261D2E9A8BA62F7866 /* Reachability.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Reachability.release.xcconfig; sourceTree = "<group>"; };
		4C0ED29B6216A2884CE877086F2B96F5 /* JLRoutes.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JLRoutes.h; path = JLRoutes/JLRoutes.h; sourceTree = "<group>"; };
		501837943C7794EC0DFA54EBF87D0548 /* mz_crypt.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_crypt.c; path = SSZipArchive/minizip/mz_crypt.c; sourceTree = "<group>"; };
		50FFBAE87DAAA5D19C6D04413ED5E6D3 /* JLRoutes */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = JLRoutes; path = libJLRoutes.a; sourceTree = BUILT_PRODUCTS_DIR; };
		58083E8016066E141BA90F2BA074FB7A /* mz_crypt_apple.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_crypt_apple.c; path = SSZipArchive/minizip/mz_crypt_apple.c; sourceTree = "<group>"; };
		5B687A22B917880A439DE5EB04E63C1F /* JLRParsingUtilities.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JLRParsingUtilities.h; path = JLRoutes/Classes/JLRParsingUtilities.h; sourceTree = "<group>"; };
		5DA07629D44BE74AC430B38D3F271DC4 /* mz_strm_wzaes.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_wzaes.c; path = SSZipArchive/minizip/mz_strm_wzaes.c; sourceTree = "<group>"; };
		5FC7C8EDD3E0871E5F20D2F5D74D43D2 /* JLRRouteRequest.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JLRRouteRequest.m; path = JLRoutes/Classes/JLRRouteRequest.m; sourceTree = "<group>"; };
		68AAF544C698B91793721FD82C0AF486 /* JLRRouteResponse.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JLRRouteResponse.m; path = JLRoutes/Classes/JLRRouteResponse.m; sourceTree = "<group>"; };
		6A735E5E74D556592785ED15AF69A9DD /* mz_os.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_os.h; path = SSZipArchive/minizip/mz_os.h; sourceTree = "<group>"; };
		6F0752869B6E3C252F98B86062D3518C /* mz_strm.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm.h; path = SSZipArchive/minizip/mz_strm.h; sourceTree = "<group>"; };
		78B6F53ED94AD31CB4468B4EE420EB29 /* SSZipArchive.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SSZipArchive.m; path = SSZipArchive/SSZipArchive.m; sourceTree = "<group>"; };
		7E779DB64A17306F67C00FD8EF58A4C5 /* mz_zip_rw.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_zip_rw.h; path = SSZipArchive/minizip/mz_zip_rw.h; sourceTree = "<group>"; };
		829C88B0956E7B5AD5FA2570832BB0BB /* JLRoutes-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "JLRoutes-dummy.m"; sourceTree = "<group>"; };
		887446DCF31F2270C3339ABC70FA0D2E /* mz_strm_zlib.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_zlib.h; path = SSZipArchive/minizip/mz_strm_zlib.h; sourceTree = "<group>"; };
		88EDEA0220AE8AEB85301FFEFB3F2E2C /* mz_strm_buf.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_buf.c; path = SSZipArchive/minizip/mz_strm_buf.c; sourceTree = "<group>"; };
		9103808C771131DD76DEED0E04DBF2B8 /* JLRoutes-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JLRoutes-prefix.pch"; sourceTree = "<group>"; };
		91B23470DEB9A986332BEB5034234BC7 /* SSZipArchive */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = SSZipArchive; path = libSSZipArchive.a; sourceTree = BUILT_PRODUCTS_DIR; };
		941C027251CBD579AD743EFC2411FC82 /* ResourceBundle-Reachability_Privacy-Reachability-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-Reachability_Privacy-Reachability-Info.plist"; sourceTree = "<group>"; };
		94A12B3BC0E77866E1C59E700B4EEF9E /* SSZipArchive-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SSZipArchive-umbrella.h"; sourceTree = "<group>"; };
		9B2D446821DF1AA87E7CCC97EA093DF9 /* Pods-YYBMacApp-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-YYBMacApp-acknowledgements.markdown"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A45D83B1F1B6AC846E5DEA63940A1009 /* mz.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz.h; path = SSZipArchive/minizip/mz.h; sourceTree = "<group>"; };
		A6B49AEF3FAE96C4DF9150ABBB6FDE60 /* mz_strm_mem.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_mem.h; path = SSZipArchive/minizip/mz_strm_mem.h; sourceTree = "<group>"; };
		A7593EAF07E88F1189DC2A4C322233EB /* mz_zip_rw.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_zip_rw.c; path = SSZipArchive/minizip/mz_zip_rw.c; sourceTree = "<group>"; };
		AF1D967FCF65AB43B8E0C95A5AD48ECC /* mz_compat.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_compat.c; path = SSZipArchive/minizip/mz_compat.c; sourceTree = "<group>"; };
		B2D46E50EEF9CD0A84BA5D668F8952AC /* SSZipArchive.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SSZipArchive.h; path = SSZipArchive/SSZipArchive.h; sourceTree = "<group>"; };
		B7AF7FA370E0ED09D55216557D46A252 /* JLRRouteResponse.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JLRRouteResponse.h; path = JLRoutes/Classes/JLRRouteResponse.h; sourceTree = "<group>"; };
		BA2247AE5388E0242E4DC53EB82B39F4 /* Reachability-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Reachability-dummy.m"; sourceTree = "<group>"; };
		C2448F7D6922304F411382999DE4CE8B /* Reachability.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = Reachability.h; sourceTree = "<group>"; };
		C95A2D54114D6139AF5845756D8A8028 /* mz_strm_buf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_buf.h; path = SSZipArchive/minizip/mz_strm_buf.h; sourceTree = "<group>"; };
		CB99B83590962149D8759FFAA0D2ED9D /* mz_crypt.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_crypt.h; path = SSZipArchive/minizip/mz_crypt.h; sourceTree = "<group>"; };
		D0614DA55271E2E3FAFD390BC0B3F232 /* Pods-YYBMacApp-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-YYBMacApp-acknowledgements.plist"; sourceTree = "<group>"; };
		D10C7A9B99F0632F6A9BFEF96358141B /* ResourceBundle-SSZipArchive-SSZipArchive-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SSZipArchive-SSZipArchive-Info.plist"; sourceTree = "<group>"; };
		D2A1B80C050B4622C1E6B9F69B410CBC /* mz_strm_pkcrypt.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_pkcrypt.h; path = SSZipArchive/minizip/mz_strm_pkcrypt.h; sourceTree = "<group>"; };
		D4DB96579AC7DF6F146B6D9BD6CD042D /* Pods-YYBMacApp-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-YYBMacApp-dummy.m"; sourceTree = "<group>"; };
		D6A40E1FCA26A649EAD121FA758AD5E7 /* JLRoutes.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JLRoutes.release.xcconfig; sourceTree = "<group>"; };
		D74C4CBF27ACCADF187DEEA0A31F0BD6 /* Pods-YYBMacApp */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = "Pods-YYBMacApp"; path = "libPods-YYBMacApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		D9E4A52EEBA206F33F01A91B6F0069B4 /* SSZipArchive-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SSZipArchive-dummy.m"; sourceTree = "<group>"; };
		DAA17EBFAD2CE701AF789D2F63CA30DF /* mz_strm_wzaes.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_wzaes.h; path = SSZipArchive/minizip/mz_strm_wzaes.h; sourceTree = "<group>"; };
		DFC89BE171DE7E648C53797695D8A220 /* Reachability-Reachability_Privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "Reachability-Reachability_Privacy"; path = Reachability_Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		E31A6C60FD2BE37AA380C71BBFFD5037 /* Reachability.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Reachability.debug.xcconfig; sourceTree = "<group>"; };
		E39B4447E81DFE7C43BD5676AA34F141 /* JLRRouteRequest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JLRRouteRequest.h; path = JLRoutes/Classes/JLRRouteRequest.h; sourceTree = "<group>"; };
		E4C503FA8653709150CDD1561F62A9D8 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Framework/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		E762E0922E2E427E6CF64594F16BDD48 /* mz_strm_pkcrypt.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_pkcrypt.c; path = SSZipArchive/minizip/mz_strm_pkcrypt.c; sourceTree = "<group>"; };
		E7B96A27B658A35E8C35CFC4ECAD7109 /* mz_strm_os_posix.c */ = {isa = PBXFileReference; includeInIndex = 1; name = mz_strm_os_posix.c; path = SSZipArchive/minizip/mz_strm_os_posix.c; sourceTree = "<group>"; };
		E7F92D0523D64ADC8DAFA55684C3D8EC /* mz_strm_split.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_strm_split.h; path = SSZipArchive/minizip/mz_strm_split.h; sourceTree = "<group>"; };
		EE4223BBB635473D560A331747F84DDF /* JLRRouteDefinition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JLRRouteDefinition.h; path = JLRoutes/Classes/JLRRouteDefinition.h; sourceTree = "<group>"; };
		EEB05F6DA3C02EE9CAC866FB653B2C44 /* JLRRouteDefinition.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JLRRouteDefinition.m; path = JLRoutes/Classes/JLRRouteDefinition.m; sourceTree = "<group>"; };
		EF166360401DD035E11384570BFB7A74 /* SSZipArchive.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SSZipArchive.modulemap; sourceTree = "<group>"; };
		F19FD4C6BF58AA18B92DA7DFEC6D174F /* SSZipCommon.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SSZipCommon.h; path = SSZipArchive/SSZipCommon.h; sourceTree = "<group>"; };
		F526195E94B61E7015C643CC908EC9A5 /* mz_zip.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mz_zip.h; path = SSZipArchive/minizip/mz_zip.h; sourceTree = "<group>"; };
		F5DCC6805964B778412D1810D59181D3 /* ZipArchive.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ZipArchive.h; path = SSZipArchive/include/ZipArchive.h; sourceTree = "<group>"; };
		FCED26CA268509CE787550624B1905FA /* JLRRouteHandler.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JLRRouteHandler.h; path = JLRoutes/Classes/JLRRouteHandler.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		15F957AA5348E43149BA4A183609CDAF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1928263579FC396B1FC3870EB0656F24 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25A669070799ACCF20970301A45C529F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A95C3230CAA8C58EDFB168DB54F3037B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9AA727541DC70C3524FE26155F1E7FB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDF9B86FB79E4B80C0EBB2E5D04E834B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		29564D63B730490C1D49BDFA69D7B0B3 /* JLRoutes */ = {
			isa = PBXGroup;
			children = (
				4C0ED29B6216A2884CE877086F2B96F5 /* JLRoutes.h */,
				200ED36448C68A7CBEC2079313C7812D /* JLRoutes.m */,
				5B687A22B917880A439DE5EB04E63C1F /* JLRParsingUtilities.h */,
				065D3CF1870E464FC6F446470338EFB5 /* JLRParsingUtilities.m */,
				EE4223BBB635473D560A331747F84DDF /* JLRRouteDefinition.h */,
				EEB05F6DA3C02EE9CAC866FB653B2C44 /* JLRRouteDefinition.m */,
				FCED26CA268509CE787550624B1905FA /* JLRRouteHandler.h */,
				368C6880BDEE771B2EEDC399B0A19BD5 /* JLRRouteHandler.m */,
				E39B4447E81DFE7C43BD5676AA34F141 /* JLRRouteRequest.h */,
				5FC7C8EDD3E0871E5F20D2F5D74D43D2 /* JLRRouteRequest.m */,
				B7AF7FA370E0ED09D55216557D46A252 /* JLRRouteResponse.h */,
				68AAF544C698B91793721FD82C0AF486 /* JLRRouteResponse.m */,
				503380369D9F37C7CE68DE312461B964 /* Support Files */,
			);
			name = JLRoutes;
			path = JLRoutes;
			sourceTree = "<group>";
		};
		32FF1E3077979959A453063FDF58B686 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				BA2247AE5388E0242E4DC53EB82B39F4 /* Reachability-dummy.m */,
				2A579D998F34FD06F9442E0E14BF4D1C /* Reachability-prefix.pch */,
				E31A6C60FD2BE37AA380C71BBFFD5037 /* Reachability.debug.xcconfig */,
				4A9A5F00101D6B261D2E9A8BA62F7866 /* Reachability.release.xcconfig */,
				941C027251CBD579AD743EFC2411FC82 /* ResourceBundle-Reachability_Privacy-Reachability-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/Reachability";
			sourceTree = "<group>";
		};
		499ECA25FC76725B4E95AC58B864895F /* Resources */ = {
			isa = PBXGroup;
			children = (
				24256A05E000B655AC215DBF7F96E673 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		503380369D9F37C7CE68DE312461B964 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				829C88B0956E7B5AD5FA2570832BB0BB /* JLRoutes-dummy.m */,
				9103808C771131DD76DEED0E04DBF2B8 /* JLRoutes-prefix.pch */,
				236814CE4A6530F8E7897CD523DE7511 /* JLRoutes.debug.xcconfig */,
				D6A40E1FCA26A649EAD121FA758AD5E7 /* JLRoutes.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/JLRoutes";
			sourceTree = "<group>";
		};
		71B68B9D9D75571866ABBCFF79A6423A /* Products */ = {
			isa = PBXGroup;
			children = (
				50FFBAE87DAAA5D19C6D04413ED5E6D3 /* JLRoutes */,
				D74C4CBF27ACCADF187DEEA0A31F0BD6 /* Pods-YYBMacApp */,
				400FF55D0451E7A8F33A3D0D3E11C1B9 /* Reachability */,
				DFC89BE171DE7E648C53797695D8A220 /* Reachability-Reachability_Privacy */,
				91B23470DEB9A986332BEB5034234BC7 /* SSZipArchive */,
				47818FA269C04B50A51A8D1D2122C796 /* SSZipArchive-SSZipArchive */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		725392A085D4597A4053E945E7D872DE /* Reachability */ = {
			isa = PBXGroup;
			children = (
				C2448F7D6922304F411382999DE4CE8B /* Reachability.h */,
				43554F9BA0DE2F7AA2CDD3B392335D0C /* Reachability.m */,
				F26C86207BED42AEEEB4A70EA92F09DE /* Resources */,
				32FF1E3077979959A453063FDF58B686 /* Support Files */,
			);
			name = Reachability;
			path = Reachability;
			sourceTree = "<group>";
		};
		840373F9A1F4F204C9C24462F5DF42C3 /* Pods-YYBMacApp */ = {
			isa = PBXGroup;
			children = (
				9B2D446821DF1AA87E7CCC97EA093DF9 /* Pods-YYBMacApp-acknowledgements.markdown */,
				D0614DA55271E2E3FAFD390BC0B3F232 /* Pods-YYBMacApp-acknowledgements.plist */,
				D4DB96579AC7DF6F146B6D9BD6CD042D /* Pods-YYBMacApp-dummy.m */,
				0B91D5E1A6E41A38550F0A20CB732B3F /* Pods-YYBMacApp-resources.sh */,
				353BB5A83373E1CD79A095F05C2AA928 /* Pods-YYBMacApp.debug.xcconfig */,
				05D780DFA6A89B2EDD18806186A34969 /* Pods-YYBMacApp.release.xcconfig */,
			);
			name = "Pods-YYBMacApp";
			path = "Target Support Files/Pods-YYBMacApp";
			sourceTree = "<group>";
		};
		8EA1DDFCE0ABBC89699B1DBB2B79145C /* Support Files */ = {
			isa = PBXGroup;
			children = (
				D10C7A9B99F0632F6A9BFEF96358141B /* ResourceBundle-SSZipArchive-SSZipArchive-Info.plist */,
				EF166360401DD035E11384570BFB7A74 /* SSZipArchive.modulemap */,
				D9E4A52EEBA206F33F01A91B6F0069B4 /* SSZipArchive-dummy.m */,
				2EF43ABFD9FD152778DA427184572EB9 /* SSZipArchive-prefix.pch */,
				94A12B3BC0E77866E1C59E700B4EEF9E /* SSZipArchive-umbrella.h */,
				2C443FF9F65086EC1EB8ED8AA34E718C /* SSZipArchive.debug.xcconfig */,
				173A28305FA2E2F7DFA229B00E0E4479 /* SSZipArchive.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SSZipArchive";
			sourceTree = "<group>";
		};
		995AFDB08848287FD251F1FCD5245526 /* Pods */ = {
			isa = PBXGroup;
			children = (
				29564D63B730490C1D49BDFA69D7B0B3 /* JLRoutes */,
				725392A085D4597A4053E945E7D872DE /* Reachability */,
				B9F2FE2EBCB55A5DD25730CB16D4B87C /* SSZipArchive */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		B9F2FE2EBCB55A5DD25730CB16D4B87C /* SSZipArchive */ = {
			isa = PBXGroup;
			children = (
				A45D83B1F1B6AC846E5DEA63940A1009 /* mz.h */,
				AF1D967FCF65AB43B8E0C95A5AD48ECC /* mz_compat.c */,
				1AD0E391C56838CE5D4B26CE30389AA3 /* mz_compat.h */,
				501837943C7794EC0DFA54EBF87D0548 /* mz_crypt.c */,
				CB99B83590962149D8759FFAA0D2ED9D /* mz_crypt.h */,
				58083E8016066E141BA90F2BA074FB7A /* mz_crypt_apple.c */,
				12AD4E2287757843EA2B90BE08C6F094 /* mz_os.c */,
				6A735E5E74D556592785ED15AF69A9DD /* mz_os.h */,
				2C568F7351A60CE8A6568FFDE9BC41DB /* mz_os_posix.c */,
				0734C59FCB16441076E85737B6897142 /* mz_strm.c */,
				6F0752869B6E3C252F98B86062D3518C /* mz_strm.h */,
				88EDEA0220AE8AEB85301FFEFB3F2E2C /* mz_strm_buf.c */,
				C95A2D54114D6139AF5845756D8A8028 /* mz_strm_buf.h */,
				009ADB3C9294797DE2640BE9DEAB185E /* mz_strm_mem.c */,
				A6B49AEF3FAE96C4DF9150ABBB6FDE60 /* mz_strm_mem.h */,
				1CAF900E68610A4864DCE2645AFE2AEB /* mz_strm_os.h */,
				E7B96A27B658A35E8C35CFC4ECAD7109 /* mz_strm_os_posix.c */,
				E762E0922E2E427E6CF64594F16BDD48 /* mz_strm_pkcrypt.c */,
				D2A1B80C050B4622C1E6B9F69B410CBC /* mz_strm_pkcrypt.h */,
				1F7C75AEC8974EE1843BF2B2F84F3CC1 /* mz_strm_split.c */,
				E7F92D0523D64ADC8DAFA55684C3D8EC /* mz_strm_split.h */,
				5DA07629D44BE74AC430B38D3F271DC4 /* mz_strm_wzaes.c */,
				DAA17EBFAD2CE701AF789D2F63CA30DF /* mz_strm_wzaes.h */,
				34DD8BA29DBD8059A735053F3F7D1617 /* mz_strm_zlib.c */,
				887446DCF31F2270C3339ABC70FA0D2E /* mz_strm_zlib.h */,
				080279E30DC5CC9A8521EE443377DB04 /* mz_zip.c */,
				F526195E94B61E7015C643CC908EC9A5 /* mz_zip.h */,
				A7593EAF07E88F1189DC2A4C322233EB /* mz_zip_rw.c */,
				7E779DB64A17306F67C00FD8EF58A4C5 /* mz_zip_rw.h */,
				B2D46E50EEF9CD0A84BA5D668F8952AC /* SSZipArchive.h */,
				78B6F53ED94AD31CB4468B4EE420EB29 /* SSZipArchive.m */,
				F19FD4C6BF58AA18B92DA7DFEC6D174F /* SSZipCommon.h */,
				F5DCC6805964B778412D1810D59181D3 /* ZipArchive.h */,
				499ECA25FC76725B4E95AC58B864895F /* Resources */,
				8EA1DDFCE0ABBC89699B1DBB2B79145C /* Support Files */,
			);
			name = SSZipArchive;
			path = SSZipArchive;
			sourceTree = "<group>";
		};
		CDB914DC2748C42AC6C5A11354E5E095 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				840373F9A1F4F204C9C24462F5DF42C3 /* Pods-YYBMacApp */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D89477F20FB1DE18A04690586D7808C4 /* Frameworks */,
				995AFDB08848287FD251F1FCD5245526 /* Pods */,
				71B68B9D9D75571866ABBCFF79A6423A /* Products */,
				CDB914DC2748C42AC6C5A11354E5E095 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D89477F20FB1DE18A04690586D7808C4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F26C86207BED42AEEEB4A70EA92F09DE /* Resources */ = {
			isa = PBXGroup;
			children = (
				E4C503FA8653709150CDD1561F62A9D8 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		1494AE6B223FE97449EDA16483E89C7A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				80B4D347E843CB98D4FFBF226E04BA4F /* mz.h in Headers */,
				5F9F7C748666EFC33E3DB274B62733D0 /* mz_compat.h in Headers */,
				3AFC08E18E4387007F66A6217EF49F7A /* mz_crypt.h in Headers */,
				34E654E12AC15C818DACE5FF0E2D86E2 /* mz_os.h in Headers */,
				4EDB4EE1B5E3A4BB662AFDF3DEC2CBE8 /* mz_strm.h in Headers */,
				DD7CF499075554A895257B5C90F7AA4B /* mz_strm_buf.h in Headers */,
				B70533D59E4CCA2AD8E8F89AF3FFF201 /* mz_strm_mem.h in Headers */,
				6FAF1CD26127F07F8056CCFD6AD5A37F /* mz_strm_os.h in Headers */,
				44578A9EDE012ED011CB525269D7539B /* mz_strm_pkcrypt.h in Headers */,
				D430321AB1027B3FCF2025A9FD9F5C15 /* mz_strm_split.h in Headers */,
				FEC73FD9558BD8A4F53F3D27BA18644E /* mz_strm_wzaes.h in Headers */,
				BDE94C8D2BF4BEC4CE2B10A4BE4FE805 /* mz_strm_zlib.h in Headers */,
				CA5092299E1D52A7CE065F46C769804E /* mz_zip.h in Headers */,
				74CE8131927A7FEBEC78C4E12842B3B5 /* mz_zip_rw.h in Headers */,
				703DCBECBB3982C5B05BEF414EC16424 /* SSZipArchive.h in Headers */,
				DE87C2DC796DCD3AE9E79487330EF78A /* SSZipArchive-umbrella.h in Headers */,
				000832FE1EAF036D619E0E61F3B34326 /* SSZipCommon.h in Headers */,
				727F0328B7FF4DC7636AF0C06DB0A3B6 /* ZipArchive.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5CF502695ED293C811CCA214008F9F9A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7290FB7F6A5903025F306E8A613FA5D6 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DADCFF1B04DC7AB8544A3414B1C70590 /* Reachability.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FFD6EBEDF14B70330D71841EE1093088 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				279BC4EB48F6DC8369F42851CACBC999 /* JLRoutes.h in Headers */,
				7DA21BA5E5E0E10757253CB48D99B501 /* JLRParsingUtilities.h in Headers */,
				0B16507B3C6C424B39C3553C583DA08C /* JLRRouteDefinition.h in Headers */,
				43C3F4C9342F01C199EBD10BCF60B521 /* JLRRouteHandler.h in Headers */,
				EA9B5AC5BDADAAFB228E43023A144627 /* JLRRouteRequest.h in Headers */,
				F8B14CAB486273C9C07C5912B28E22EE /* JLRRouteResponse.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		94C1BAA17BCEBC27586488A205D2E0CB /* JLRoutes */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C7CC2B94B4834F9A9266E79FC2AE82 /* Build configuration list for PBXNativeTarget "JLRoutes" */;
			buildPhases = (
				FFD6EBEDF14B70330D71841EE1093088 /* Headers */,
				C3AD190DD65A5C93C8C980820226818D /* Sources */,
				25A669070799ACCF20970301A45C529F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = JLRoutes;
			productName = JLRoutes;
			productReference = 50FFBAE87DAAA5D19C6D04413ED5E6D3 /* JLRoutes */;
			productType = "com.apple.product-type.library.static";
		};
		CAA047C0F5E4106F3904E8497FA17F97 /* Reachability */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7A0974B5C2020827E6DD5133A2522A53 /* Build configuration list for PBXNativeTarget "Reachability" */;
			buildPhases = (
				7290FB7F6A5903025F306E8A613FA5D6 /* Headers */,
				73FAD46FB7979BEB503E3DD7061DD642 /* Sources */,
				FDF9B86FB79E4B80C0EBB2E5D04E834B /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				8FE90D3930BA4B45BDB85745C9C74AAB /* PBXTargetDependency */,
			);
			name = Reachability;
			productName = Reachability;
			productReference = 400FF55D0451E7A8F33A3D0D3E11C1B9 /* Reachability */;
			productType = "com.apple.product-type.library.static";
		};
		D2787856C227A709315E3C9C4355A440 /* Reachability-Reachability_Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C41A091BA2FF2907840CF8881E98ED14 /* Build configuration list for PBXNativeTarget "Reachability-Reachability_Privacy" */;
			buildPhases = (
				FCB47ABD702BD9F8CB995B7588A7472E /* Sources */,
				A9AA727541DC70C3524FE26155F1E7FB /* Frameworks */,
				B6513F5357D3995F79247ADC2AF6A8B5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Reachability-Reachability_Privacy";
			productName = Reachability_Privacy;
			productReference = DFC89BE171DE7E648C53797695D8A220 /* Reachability-Reachability_Privacy */;
			productType = "com.apple.product-type.bundle";
		};
		D8EA004283833F4910C46F5335BE8F07 /* SSZipArchive-SSZipArchive */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8CCCCC614A019F54681587F4CDEC5DB6 /* Build configuration list for PBXNativeTarget "SSZipArchive-SSZipArchive" */;
			buildPhases = (
				7F58B7E4F80D7D19ECA3438DFBBDA394 /* Sources */,
				15F957AA5348E43149BA4A183609CDAF /* Frameworks */,
				C445AF978DF68D207210486F06BDB8BD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SSZipArchive-SSZipArchive";
			productName = SSZipArchive;
			productReference = 47818FA269C04B50A51A8D1D2122C796 /* SSZipArchive-SSZipArchive */;
			productType = "com.apple.product-type.bundle";
		};
		E1A4940FAEB2D03BBA628FC64151EBC4 /* Pods-YYBMacApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7137CFD5405E2395198E9F0D52001F85 /* Build configuration list for PBXNativeTarget "Pods-YYBMacApp" */;
			buildPhases = (
				5CF502695ED293C811CCA214008F9F9A /* Headers */,
				2C9E204F1256AC70200F7E0D8D3CEB5A /* Sources */,
				1928263579FC396B1FC3870EB0656F24 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				C3D080DDA21ED53E49D5628F653F6DB3 /* PBXTargetDependency */,
				6C0D86C6DBA259317AD1D055EB17A68A /* PBXTargetDependency */,
				51435E95A4DC6183B59ECC8BE235F4FC /* PBXTargetDependency */,
			);
			name = "Pods-YYBMacApp";
			productName = "Pods-YYBMacApp";
			productReference = D74C4CBF27ACCADF187DEEA0A31F0BD6 /* Pods-YYBMacApp */;
			productType = "com.apple.product-type.library.static";
		};
		F60E38364AFF5E1349FF07415B944396 /* SSZipArchive */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD852CE79703894C56F7EE09A418B8FB /* Build configuration list for PBXNativeTarget "SSZipArchive" */;
			buildPhases = (
				1494AE6B223FE97449EDA16483E89C7A /* Headers */,
				A9E40AFDC4B3631BAAC45A4266098CED /* Sources */,
				A95C3230CAA8C58EDFB168DB54F3037B /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				49E607D3C744ADAF9316BD39BEB15F2D /* PBXTargetDependency */,
			);
			name = SSZipArchive;
			productName = SSZipArchive;
			productReference = 91B23470DEB9A986332BEB5034234BC7 /* SSZipArchive */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 71B68B9D9D75571866ABBCFF79A6423A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				94C1BAA17BCEBC27586488A205D2E0CB /* JLRoutes */,
				E1A4940FAEB2D03BBA628FC64151EBC4 /* Pods-YYBMacApp */,
				CAA047C0F5E4106F3904E8497FA17F97 /* Reachability */,
				D2787856C227A709315E3C9C4355A440 /* Reachability-Reachability_Privacy */,
				F60E38364AFF5E1349FF07415B944396 /* SSZipArchive */,
				D8EA004283833F4910C46F5335BE8F07 /* SSZipArchive-SSZipArchive */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B6513F5357D3995F79247ADC2AF6A8B5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1487775A1FDB154A667B6CB002A722F1 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C445AF978DF68D207210486F06BDB8BD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C0DE524F2A493CE480B5C5B1D8A802A6 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2C9E204F1256AC70200F7E0D8D3CEB5A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D53BF080645E381C9AF441A5BB5715D /* Pods-YYBMacApp-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		73FAD46FB7979BEB503E3DD7061DD642 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3C34899BDAF131047D410165B8BD1602 /* Reachability.m in Sources */,
				DD0FEC1870DC0D556DE32F27A8BEA3AE /* Reachability-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F58B7E4F80D7D19ECA3438DFBBDA394 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9E40AFDC4B3631BAAC45A4266098CED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F67926D7B0AA6FBE4FD14C2D51177BFB /* mz_compat.c in Sources */,
				DF97EED5D0E7C486C8C3EEC14EE66D1F /* mz_crypt.c in Sources */,
				C386B3A83C0D35C497253A96E1C7411B /* mz_crypt_apple.c in Sources */,
				8F38C92E6BAD48EDAE3EADA1D2B15391 /* mz_os.c in Sources */,
				B7AFFCE5C0A77D5632C91DD63865F7C1 /* mz_os_posix.c in Sources */,
				1970768CA4899F2C709CE402E5946806 /* mz_strm.c in Sources */,
				09ACECBDE9AE6FDCFFABFA1B0D7B4920 /* mz_strm_buf.c in Sources */,
				0C933810E06DB2A41562D50BCA090AE2 /* mz_strm_mem.c in Sources */,
				612E34C324BD2F630D1FF565F7B8B58A /* mz_strm_os_posix.c in Sources */,
				BC858A3CCCC42D4F2801FC9E516C5518 /* mz_strm_pkcrypt.c in Sources */,
				034A13EC5ACE84A0EDDA2EB5FBAB679A /* mz_strm_split.c in Sources */,
				2FCD09D677ADAFBAB35A80EDBB8B9017 /* mz_strm_wzaes.c in Sources */,
				8CF3DA00FD205C2FF408A1987ADC4D1D /* mz_strm_zlib.c in Sources */,
				0FF9FBE41BDBDE8257EE8827A40337D2 /* mz_zip.c in Sources */,
				20EAF10BCBEB9CAA08615C9156E78B5A /* mz_zip_rw.c in Sources */,
				1CF2C68F10741292A7BA53CEAD57BA7C /* SSZipArchive.m in Sources */,
				7884400D24EB07B6A74E16297CC5458A /* SSZipArchive-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3AD190DD65A5C93C8C980820226818D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0351E581F014FC8B860327E924FADA67 /* JLRoutes.m in Sources */,
				2945097AB81C80CA13CF14AA9F141968 /* JLRoutes-dummy.m in Sources */,
				DB79ACC4AAE13CCBFC27F704A47E4B83 /* JLRParsingUtilities.m in Sources */,
				4134C08B6C80B1943AC0951F2DD24CD5 /* JLRRouteDefinition.m in Sources */,
				691C6B2D531C0FC5C2032EDA4FB2A1EA /* JLRRouteHandler.m in Sources */,
				B51DFCEDCD33A19CF7D3D62CDF8FFC30 /* JLRRouteRequest.m in Sources */,
				B02CE854D19C3B7D945DDA653789DF21 /* JLRRouteResponse.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FCB47ABD702BD9F8CB995B7588A7472E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		49E607D3C744ADAF9316BD39BEB15F2D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SSZipArchive-SSZipArchive";
			target = D8EA004283833F4910C46F5335BE8F07 /* SSZipArchive-SSZipArchive */;
			targetProxy = 44980913CE3BB6ED65EF201B7FC39A06 /* PBXContainerItemProxy */;
		};
		51435E95A4DC6183B59ECC8BE235F4FC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SSZipArchive;
			target = F60E38364AFF5E1349FF07415B944396 /* SSZipArchive */;
			targetProxy = 97C99F1B67C2E49BB28D26588BE90B38 /* PBXContainerItemProxy */;
		};
		6C0D86C6DBA259317AD1D055EB17A68A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Reachability;
			target = CAA047C0F5E4106F3904E8497FA17F97 /* Reachability */;
			targetProxy = D34BB9B9FEF4F8A5B9A119AB83F5A02A /* PBXContainerItemProxy */;
		};
		8FE90D3930BA4B45BDB85745C9C74AAB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Reachability-Reachability_Privacy";
			target = D2787856C227A709315E3C9C4355A440 /* Reachability-Reachability_Privacy */;
			targetProxy = 934880D97B1B89920A1DBC8C8E7C1A81 /* PBXContainerItemProxy */;
		};
		C3D080DDA21ED53E49D5628F653F6DB3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JLRoutes;
			target = 94C1BAA17BCEBC27586488A205D2E0CB /* JLRoutes */;
			targetProxy = A5C3FD126C4217CD012776F55386FB33 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		223B864BF990CF1759D784AE815E50C0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 236814CE4A6530F8E7897CD523DE7511 /* JLRoutes.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_PREFIX_HEADER = "Target Support Files/JLRoutes/JLRoutes-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = JLRoutes;
				PRODUCT_NAME = JLRoutes;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
			};
			name = Debug;
		};
		50F21A2682A89E919FA13375DE4E5207 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4A9A5F00101D6B261D2E9A8BA62F7866 /* Reachability.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/Reachability";
				IBSC_MODULE = Reachability;
				INFOPLIST_FILE = "Target Support Files/Reachability/ResourceBundle-Reachability_Privacy-Reachability-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = Reachability_Privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		5155EB51363003FD2BD8B798D7D1FBEC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D6A40E1FCA26A649EAD121FA758AD5E7 /* JLRoutes.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_PREFIX_HEADER = "Target Support Files/JLRoutes/JLRoutes-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = JLRoutes;
				PRODUCT_NAME = JLRoutes;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
			};
			name = Release;
		};
		51CC90BDC88FA59077009EB51DB4D1FE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 353BB5A83373E1CD79A095F05C2AA928 /* Pods-YYBMacApp.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		6360D4FBEB1B7B1326CDE0F3D482B166 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 05D780DFA6A89B2EDD18806186A34969 /* Pods-YYBMacApp.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		7357E55C61A0C0D0F3DB7D69901B69E4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 173A28305FA2E2F7DFA229B00E0E4479 /* SSZipArchive.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SSZipArchive";
				IBSC_MODULE = SSZipArchive;
				INFOPLIST_FILE = "Target Support Files/SSZipArchive/ResourceBundle-SSZipArchive-SSZipArchive-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = SSZipArchive;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		82E73717A2562031D5E1F4D85DD1A982 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		91B4F53CC52BE64187F8649AE685C7C9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2C443FF9F65086EC1EB8ED8AA34E718C /* SSZipArchive.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_PREFIX_HEADER = "Target Support Files/SSZipArchive/SSZipArchive-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MODULEMAP_FILE = Headers/Public/SSZipArchive/SSZipArchive.modulemap;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = SSZipArchive;
				PRODUCT_NAME = SSZipArchive;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
			};
			name = Debug;
		};
		B40F1EAAEFC4958FCFED647E49EBE715 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 173A28305FA2E2F7DFA229B00E0E4479 /* SSZipArchive.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_PREFIX_HEADER = "Target Support Files/SSZipArchive/SSZipArchive-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MODULEMAP_FILE = Headers/Public/SSZipArchive/SSZipArchive.modulemap;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = SSZipArchive;
				PRODUCT_NAME = SSZipArchive;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
			};
			name = Release;
		};
		B90E00FCF169AADE4D76EC7AE36E72F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E31A6C60FD2BE37AA380C71BBFFD5037 /* Reachability.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/Reachability";
				IBSC_MODULE = Reachability;
				INFOPLIST_FILE = "Target Support Files/Reachability/ResourceBundle-Reachability_Privacy-Reachability-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = Reachability_Privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		CB1202739EF8D5A81D7F8E9A6E558D82 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2C443FF9F65086EC1EB8ED8AA34E718C /* SSZipArchive.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SSZipArchive";
				IBSC_MODULE = SSZipArchive;
				INFOPLIST_FILE = "Target Support Files/SSZipArchive/ResourceBundle-SSZipArchive-SSZipArchive-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_NAME = SSZipArchive;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		D39E634FC2CFF97419EB3682DD2807FA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		D96E9517C1A85B69720E2A044660FB97 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4A9A5F00101D6B261D2E9A8BA62F7866 /* Reachability.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_PREFIX_HEADER = "Target Support Files/Reachability/Reachability-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = Reachability;
				PRODUCT_NAME = Reachability;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.1;
			};
			name = Release;
		};
		F805EB3EFD1D7A2C6A98A8188BD1C737 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E31A6C60FD2BE37AA380C71BBFFD5037 /* Reachability.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_PREFIX_HEADER = "Target Support Files/Reachability/Reachability-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = Reachability;
				PRODUCT_NAME = Reachability;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.1;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D39E634FC2CFF97419EB3682DD2807FA /* Debug */,
				82E73717A2562031D5E1F4D85DD1A982 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7137CFD5405E2395198E9F0D52001F85 /* Build configuration list for PBXNativeTarget "Pods-YYBMacApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				51CC90BDC88FA59077009EB51DB4D1FE /* Debug */,
				6360D4FBEB1B7B1326CDE0F3D482B166 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7A0974B5C2020827E6DD5133A2522A53 /* Build configuration list for PBXNativeTarget "Reachability" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F805EB3EFD1D7A2C6A98A8188BD1C737 /* Debug */,
				D96E9517C1A85B69720E2A044660FB97 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8CCCCC614A019F54681587F4CDEC5DB6 /* Build configuration list for PBXNativeTarget "SSZipArchive-SSZipArchive" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CB1202739EF8D5A81D7F8E9A6E558D82 /* Debug */,
				7357E55C61A0C0D0F3DB7D69901B69E4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C7CC2B94B4834F9A9266E79FC2AE82 /* Build configuration list for PBXNativeTarget "JLRoutes" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				223B864BF990CF1759D784AE815E50C0 /* Debug */,
				5155EB51363003FD2BD8B798D7D1FBEC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C41A091BA2FF2907840CF8881E98ED14 /* Build configuration list for PBXNativeTarget "Reachability-Reachability_Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B90E00FCF169AADE4D76EC7AE36E72F4 /* Debug */,
				50F21A2682A89E919FA13375DE4E5207 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD852CE79703894C56F7EE09A418B8FB /* Build configuration list for PBXNativeTarget "SSZipArchive" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				91B4F53CC52BE64187F8649AE685C7C9 /* Debug */,
				B40F1EAAEFC4958FCFED647E49EBE715 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}

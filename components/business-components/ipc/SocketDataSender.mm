//
//  SocketDataSender.m
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import "SocketDataSender.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBSocketEngine.h"
#import "YYBDefine.h"

NSString* const kTag = @"SocketDataSender";
NSString* const SocketErrorDomain = @"SocketErrorDomain";
static NSString* const kErrorDescription = @"error"; // 需要对齐ERROR_KEY

// 辅助函数声明
std::map<std::string, std::string> changeDictionaryToStdMap(NSMutableDictionary *dict);
NSDictionary* changeStdMapToDictionary(const std::optional<std::map<std::string, std::string>>& stdMap);

@implementation SocketDataSender

+ (BOOL)sendMessage:(SocketMessageAction)action
               from:(NSString *)from
               with:(nullable NSMutableDictionary*)info
                 to:(nullable NSString*)to {
    YYBMacLogInfo(kTag, @"sendMessage: %@ from: %@ info:%@ to:%@", action, from, info, to);
    if (from.length == 0) {
        return false;
    }
    YYBSocketMsgData msg;
    msg.action = action.UTF8String;
    msg.to = (to.UTF8String == nullptr) ? kProcessBoastcast.UTF8String : to.UTF8String;
    msg.info = changeDictionaryToStdMap(info);
    msg.from = from.UTF8String;
    msg.type = SocketMessageType::event;
    return YYBSocketEngine::instance().sendMessage(msg);
}

+ (BOOL)sendRequest:(SocketMessageAction)action
               from:(NSString *)from
               with:(nullable NSMutableDictionary*)info
                 to:(nullable NSString*)to
           callback:(SocketResponseCallback)callback {
    return [self sendRequest:action
                        from:from
                        with:info
                          to:to
                    callback:callback
                     timeout:DEFAULT_REQUEST_TIMEOUT_MS];
}

+ (BOOL)sendRequest:(SocketMessageAction)action
               from:(NSString *)from
               with:(nullable NSMutableDictionary*)info
                 to:(nullable NSString*)to
           callback:(SocketResponseCallback)callback
            timeout:(NSInteger)timeout {
    if (from.length == 0) {
        return false;
    }
    YYBMacLogInfo(kTag, @"sendRequest: %@ from: %@ info:%@ to:%@ timeout:%@", action, from, info, to, @(timeout));
    YYBSocketMsgData request;
    request.action = action.UTF8String;
    request.to = (to.UTF8String == nullptr) ? kProcessBoastcast.UTF8String : to.UTF8String;
    request.info = changeDictionaryToStdMap(info);
    request.from = from.UTF8String;
    request.type = SocketMessageType::request;
    
    return YYBSocketEngine::instance().sendRequest(
        request,
        [callback](const YYBSocketMsgData& response, int64_t errorCode) {
            NSDictionary *responseDictionary = changeStdMapToDictionary(response.info);
            NSError *error = nil;
            if (errorCode != SocketErrorCodeNone) {
                NSString *errorMsg = responseDictionary[kErrorDescription];
                if (errorMsg.length == 0) {
                    errorMsg = @"ipc请求异常";
                }
                error = [NSError errorWithDomain:SocketErrorDomain code:errorCode userInfo:@{NSLocalizedDescriptionKey: errorMsg}];
            }
            callback(responseDictionary, error);
        },
        (int)timeout
    );
}

// 将OC的NSDictionary转换为C++的std::map
std::map<std::string, std::string> changeDictionaryToStdMap(NSMutableDictionary *dict) {
    std::map<std::string, std::string> result;
    if (!dict) {
        return result;
    }
    for (id key in dict) {
        id value = [dict objectForKey:key];
        if ([key isKindOfClass:[NSString class]] && [value isKindOfClass:[NSString class]]) {
            NSString *keyStr = (NSString *)key;
            NSString *valStr = (NSString *)value;
            result[[keyStr UTF8String]] = [valStr UTF8String];
        }
        if ([key isKindOfClass:[NSString class]] && [value isKindOfClass:[NSNumber class]]) {
            NSString *keyStr = (NSString *)key;
            NSString *valStr = [value stringValue];
            result[[keyStr UTF8String]] = [valStr UTF8String];
        }
    }
    return result;
}

// 将C++的std::map转换为OC的NSDictionary
NSDictionary* changeStdMapToDictionary(const std::optional<std::map<std::string, std::string>>& stdMap) {
    if (!stdMap.has_value()) {
        return nil;
    }
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    for (const auto& pair : stdMap.value()) {
        NSString *key = [NSString stringWithUTF8String:pair.first.c_str()];
        NSString *value = [NSString stringWithUTF8String:pair.second.c_str()];
        [dict setObject:value forKey:key];
    }
    
    return [dict copy];
}
@end

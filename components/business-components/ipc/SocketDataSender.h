//
//  SocketDataSender.h
//  YYBMacApp
//
//  Created by bethahuang on 2025/8/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NSString *SocketMessageAction;

FOUNDATION_EXPORT NSString *const SocketErrorDomain;

// 需要同步SocketError::ErrorCode
typedef NS_ENUM(NSInteger, SocketErrorCode) {
    SocketErrorCodeNone = 0,          // 无错误
    SocketErrorCodeTimeout = 1001,    // 请求超时
    SocketErrorCodeService = 1002,    // SERVICE错误（中间层）
    SocketErrorCodeServer = 1003,     // 服务器异常
    SocketErrorCodeNotFound = 1004,   // 未找到响应
    SocketErrorCodeUnknown = 9999     // 未知错误
};

typedef void(^SocketResponseCallback)(NSDictionary* _Nullable response, NSError* _Nullable error);

/**
 * 进程间通讯
 *
 * 用于实现进程间通讯的OC类
 */
@interface SocketDataSender : NSObject

/**
 * 进程通信接口。
 * 注意：info参数的NSMutableDictionary仅支持NSString和NSNumber对象。
 *
 * @param action 指令（SocketMessageAction类型）。
 * @param from 指令发送对象（NSString类型）。
 * @param info 额外信息（NSMutableDictionary，仅支持NSString和NSNumber）。
 * @param to 接收对象（NSString类型）。
 * @return 是否成功发送请求。
 */
+ (BOOL)sendMessage:(SocketMessageAction)action
               from:(NSString *)from
               with:(nullable NSMutableDictionary*)info
                 to:(nullable NSString*)to;

/**
 * 进程通信请求-响应接口（使用默认超时时间）。
 *
 * @param action 指令（SocketMessageAction类型）。
 * @param from 指令发送对象（NSString类型）。
 * @param info 额外信息（NSMutableDictionary，仅支持NSString和NSNumber）。
 * @param to 接收对象（NSString类型）。
 * @param callback 回调函数，参数为响应数据和错误码，错误码见SocketErrorCode。
 * @return 是否成功发送请求。
 */
+ (BOOL)sendRequest:(SocketMessageAction)action
               from:(NSString *)from
               with:(nullable NSMutableDictionary*)info
                 to:(nullable NSString*)to
           callback:(SocketResponseCallback)callback;

/**
 * 进程通信请求-响应接口。
 * 注意：info参数的NSMutableDictionary仅支持NSString和NSNumber对象。
 *
 * @param action 指令（SocketMessageAction类型）。
 * @param from 指令发送对象（NSString类型）。
 * @param info 额外信息（NSMutableDictionary，仅支持NSString和NSNumber）。
 * @param to 接收对象（NSString类型）。
 * @param callback 回调函数，参数为响应数据和错误码，错误码见SocketErrorCode。
 * @param timeout 超时时间（毫秒），默认为30000毫秒。
 * @return 是否成功发送请求。
 */
+ (BOOL)sendRequest:(SocketMessageAction)action
               from:(NSString *)from
               with:(nullable NSMutableDictionary*)info
                 to:(nullable NSString*)to
           callback:(SocketResponseCallback)callback
            timeout:(NSInteger)timeout;

@end

NS_ASSUME_NONNULL_END

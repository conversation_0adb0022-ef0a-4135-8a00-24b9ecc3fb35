//
//  YYBFile.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/6/20.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

// 错误码定义
typedef NS_ENUM(NSInteger, YYBFileErrorCode) {
    YYBFileErrorCodeInvalidPath = 400,      // 无效路径
    YYBFileErrorCodeNotFound = 404,         // 文件不存在
    YYBFileErrorCodeNoPermission = 403,     // 没有权限
    YYBFileErrorCodeCreateFailed = 500,     // 创建文件失败
    YYBFileErrorCodeOpenFailed = 501,       // 打开文件失败
    YYBFileErrorCodeDeleteFailed = 502,     // 删除文件失败
};

@interface YYBFile : NSObject

extern NSString *const kErrorKey;
extern NSString *const kSHA256Key;
extern NSString *const kYYBFileErrorDomain;

// 检查文件是否存在
+ (BOOL)isFileExistsAtPath:(NSString *)path error:(NSError **)error;

// 创建文件
+ (BOOL)createFileAtPath:(NSString *)path error:(NSError **)error;

// 打开文件
+ (nullable NSFileHandle *)openFileAtPath:(NSString *)path error:(NSError **)error;

// 删除文件
+ (BOOL)deleteFileAtPath:(NSString *)path error:(NSError **)error;

// 获取文件基础信息
+ (NSDictionary *)getFileInfoAtPath:(NSString *)path;

// 获取文件MD5
+ (NSString *)getFileMD5AtPath:(NSString *)path;

// 分片读取文件
+ (void)readFileAtPath:(NSString *)path chunkSize:(NSUInteger)chunkSize completion:(void (^)(NSData *chunkData, BOOL isFinished))completion;

// 验证文件路径是否合法，并返回合法的文件路径
+ (BOOL)validateAndResolveFilePath:(NSString *)path resolvedPath:(NSString *_Nonnull*_Nonnull)resolvedPath error:(NSError **)error;

// 在Finder中打开文件
+ (BOOL)openFileInFilder:(NSString *)path;

@end

NS_ASSUME_NONNULL_END

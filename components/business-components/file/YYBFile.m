//
//  YYBFile.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/6/20.
//

#import "YYBFile.h"
#import <CommonCrypto/CommonDigest.h>
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif

static NSString *const kYYBFileTag = @"YYBFile";
NSString *const kErrorKey = @"error";
NSString *const kSHA256Key = @"NSFileSHA256";
NSString *const kMD5Key = @"NSFileMD5";
NSString *const kYYBFileErrorDomain = @"YYBFileErrorDomain";

@implementation YYBFile

#pragma mark - File Operations

// 检查文件是否存在
+ (BOOL)isFileExistsAtPath:(NSString *)path error:(NSError **)error {
    NSString *resolvedPath;
    if (![self validateAndResolveFilePath:path resolvedPath:&resolvedPath error:error]) {
        return NO;
    }
    
    BOOL exists = [[NSFileManager defaultManager] fileExistsAtPath:resolvedPath];
    YYBMacLogInfo(kYYBFileTag, @"File exists check for path: %@, result: %@", resolvedPath, exists ? @"YES" : @"NO");
    
    if (!exists && error) {
        *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                     code:YYBFileErrorCodeNotFound
                                 userInfo:@{NSLocalizedDescriptionKey: @"File does not exist"}];
    }
    
    return exists;
}

// 创建文件
+ (BOOL)createFileAtPath:(NSString *)path error:(NSError **)error {
    NSString *resolvedPath;
    if (![self validateAndResolveFilePath:path resolvedPath:&resolvedPath error:error]) {
        return NO;
    }
    
    BOOL success = [[NSFileManager defaultManager] createFileAtPath:resolvedPath contents:nil attributes:nil];
    if (!success && error) {
        *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                     code:YYBFileErrorCodeCreateFailed
                                 userInfo:@{NSLocalizedDescriptionKey: @"Failed to create file"}];
    }
    return success;
}

// 打开文件
+ (nullable NSFileHandle *)openFileAtPath:(NSString *)path error:(NSError **)error {
    NSString *resolvedPath;
    if (![self validateAndResolveFilePath:path resolvedPath:&resolvedPath error:error]) {
        return nil;
    }
    
    NSFileHandle *fileHandle = nil;
    
    if (@available(macOS 10.15, *)) {
        NSError *fileHandleError = nil;
        fileHandle = [NSFileHandle fileHandleForReadingFromURL:[NSURL fileURLWithPath:resolvedPath] error:&fileHandleError];
        if (fileHandleError && error) {
            *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                         code:YYBFileErrorCodeOpenFailed
                                     userInfo:fileHandleError.userInfo];
        }
    } else {
        fileHandle = [NSFileHandle fileHandleForReadingAtPath:resolvedPath];
        if (!fileHandle && error) {
            *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                         code:YYBFileErrorCodeOpenFailed
                                     userInfo:@{NSLocalizedDescriptionKey: @"Failed to open file"}];
        }
    }
    
    return fileHandle;
}

// 删除文件
+ (BOOL)deleteFileAtPath:(NSString *)path error:(NSError **)error {
    NSString *resolvedPath;
    if (![self validateAndResolveFilePath:path resolvedPath:&resolvedPath error:error]) {
        return NO;
    }
    
    NSError *deleteError = nil;
    BOOL success = [[NSFileManager defaultManager] removeItemAtPath:resolvedPath error:&deleteError];
    
    if (!success && error) {
        if (deleteError) {
            *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                         code:YYBFileErrorCodeDeleteFailed
                                     userInfo:deleteError.userInfo];
        } else {
            *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                         code:YYBFileErrorCodeDeleteFailed
                                     userInfo:@{NSLocalizedDescriptionKey: @"Failed to delete file"}];
        }
    }
    
    return success;
}

// 获取文件基础信息
+ (NSDictionary *)getFileInfoAtPath:(NSString *)path {
    NSString *resolvedPath;
    NSError *error;
    if (![self validateAndResolveFilePath:path resolvedPath:&resolvedPath error:&error]) {
        return @{
            kErrorKey: error.localizedDescription,
        };
    }
    
    NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:resolvedPath error:&error];
    if (error) {
        YYBMacLogError(kYYBFileTag, @"Failed to get attributes: %@", error);
        return @{
            kErrorKey: error.localizedDescription,
        };
    }
    
    NSData *fileData = [NSData dataWithContentsOfFile:resolvedPath];
    if (!fileData) {
        YYBMacLogError(kYYBFileTag, @"Failed to read file data: %@", resolvedPath);
        return @{
            kErrorKey: @"Failed to read file data",
        };
    }
    
    unsigned char sha256Buffer[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(fileData.bytes, (CC_LONG)fileData.length, sha256Buffer);
    NSMutableString *sha256String = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [sha256String appendFormat:@"%02x", sha256Buffer[i]];
    }
    
    NSMutableDictionary *info = [NSMutableDictionary dictionaryWithDictionary:attributes];
    [info setObject:sha256String forKey:kSHA256Key];
    
    NSString *md5 = [self getFileMD5AtPath:path];
    [info setObject:md5 forKey:kMD5Key];

    return info;
}

+ (NSString *)getFileMD5AtPath:(NSString *)path {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    if (!path) return nil;
    NSFileHandle *handle = [NSFileHandle fileHandleForReadingAtPath:path];
    if (!handle) return nil;
    CC_MD5_CTX md5;
    CC_MD5_Init(&md5);
    while (true) {
        @autoreleasepool {
            NSData *fileData = [handle readDataOfLength:1024 * 1024];
            if (fileData.length == 0) break;
            CC_MD5_Update(&md5, [fileData bytes], (CC_LONG)fileData.length);
        }
    }
    [handle closeFile];
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5_Final(digest, &md5);
    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", digest[i]];
    }
    return [md5String copy];
#pragma clang diagnostic pop
}

// 分片读取文件
+ (void)readFileAtPath:(NSString *)path chunkSize:(NSUInteger)chunkSize completion:(void (^)(NSData *chunkData, BOOL isFinished))completion {
    NSString *resolvedPath;
    if (![self validateAndResolveFilePath:path resolvedPath:&resolvedPath error:nil]) {
        completion(nil, YES);
        return;
    }
    
    NSFileHandle *fileHandle = [NSFileHandle fileHandleForReadingAtPath:resolvedPath];
    if (!fileHandle) {
        completion(nil, YES);
        return;
    }
    
    NSData *chunkData;
    BOOL isFinished = NO;
    while (!isFinished) {
        chunkData = [fileHandle readDataOfLength:chunkSize];
        isFinished = (chunkData.length == 0);
        completion(chunkData, isFinished);
    }
    
    [fileHandle closeFile];
}

#pragma mark - Path Validation

+ (BOOL)validateAndResolveFilePath:(NSString *)path resolvedPath:(NSString **)resolvedPath error:(NSError **)error {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    // Handle file URL format
    NSString *actualPath = path;
    if ([path hasPrefix:@"file://"]) {
        NSURL *url = [NSURL URLWithString:path];
        actualPath = url.path;
    }
    
    // Check file existence
    if (![fileManager fileExistsAtPath:actualPath]) {
        YYBMacLogError(kYYBFileTag, @"File not exist at path: %@", actualPath);
        if (error) {
            *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                        code:YYBFileErrorCodeNotFound
                                    userInfo:@{NSLocalizedDescriptionKey: @"File does not exist"}];
        }
        return NO;
    }
    
    // Check file accessibility
    if (![fileManager isReadableFileAtPath:actualPath]) {
        YYBMacLogError(kYYBFileTag, @"No read permission for file: %@", actualPath);
        if (error) {
            *error = [NSError errorWithDomain:kYYBFileErrorDomain
                                        code:YYBFileErrorCodeNoPermission
                                    userInfo:@{NSLocalizedDescriptionKey: @"No read permission"}];
        }
        return NO;
    }
    
    if (resolvedPath) {
        *resolvedPath = actualPath;
    }
    return YES;
}

+ (BOOL)openFileInFilder:(NSString *)path {
    NSString *filePath = path;
    NSString *directory = [filePath stringByDeletingLastPathComponent];
    return [[NSWorkspace sharedWorkspace] selectFile:filePath inFileViewerRootedAtPath:directory];
}

@end

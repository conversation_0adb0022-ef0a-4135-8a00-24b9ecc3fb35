//
//  YYBDirectory.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/6/20.
//

#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import "YYBMacLog.h"
#endif
#import "YYBDirectory.h"

static NSString *const kYYBDirectoryTag = @"YYBDirectory";
NSString *const kYYBDirectoryErrorDomain = @"YYBDirectoryErrorDomain";

@implementation YYBDirectory

// 创建目录
+ (BOOL)createDirectoryAtPath:(NSString *)path error:(NSError **)error {
    if (path.length == 0) {
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeInvalidPath
                                     userInfo:@{NSLocalizedDescriptionKey: @"目录路径为空"}];
        }
        return NO;
    }
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *createError = nil;
    BOOL success = [fileManager createDirectoryAtPath:path withIntermediateDirectories:YES attributes:nil error:&createError];
    
    if (!success) {
        YYBMacLogError(kYYBDirectoryTag, @"创建目录失败: %@, 错误是%@", path, createError.localizedDescription);
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeCreateFailed
                                     userInfo:createError.userInfo];
        }
    } else {
        YYBMacLogInfo(kYYBDirectoryTag, @"创建目录成功: %@", path);
    }
    return success;
}

// 打开目录
+ (NSArray<NSString *> *)openDirectoryAtPath:(NSString *)path error:(NSError **)error {
    if (path.length == 0) {
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeInvalidPath
                                     userInfo:@{NSLocalizedDescriptionKey: @"目录路径为空"}];
        }
        return nil;
    }
    
    // 检查目录是否存在
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDirectory = NO;
    BOOL exists = [fileManager fileExistsAtPath:path isDirectory:&isDirectory];
    
    if (!exists) {
        YYBMacLogError(kYYBDirectoryTag, @"目录不存在: %@", path);
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeNotFound
                                     userInfo:@{NSLocalizedDescriptionKey: @"目录不存在"}];
        }
        return nil;
    }
    
    if (!isDirectory) {
        YYBMacLogError(kYYBDirectoryTag, @"路径不是目录: %@", path);
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeInvalidPath
                                     userInfo:@{NSLocalizedDescriptionKey: @"路径不是目录"}];
        }
        return nil;
    }
    
    NSError *openError = nil;
    NSArray<NSString *> *contents = [fileManager contentsOfDirectoryAtPath:path error:&openError];
    
    if (!contents) {
        YYBMacLogError(kYYBDirectoryTag, @"打开目录失败: %@, 错误是%@", path, openError.localizedDescription);
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeOpenFailed
                                     userInfo:openError.userInfo];
        }
    } else {
        YYBMacLogInfo(kYYBDirectoryTag, @"打开目录成功: %@", path);
    }
    return contents;
}

// 删除目录
+ (BOOL)deleteDirectoryAtPath:(NSString *)path error:(NSError **)error {
    if (path.length == 0) {
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeInvalidPath
                                     userInfo:@{NSLocalizedDescriptionKey: @"目录路径为空"}];
        }
        return NO;
    }
    
    // 检查目录是否存在
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDirectory = NO;
    BOOL exists = [fileManager fileExistsAtPath:path isDirectory:&isDirectory];
    
    if (!exists) {
        YYBMacLogError(kYYBDirectoryTag, @"目录不存在: %@", path);
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeNotFound
                                     userInfo:@{NSLocalizedDescriptionKey: @"目录不存在"}];
        }
        return NO;
    }
    
    NSError *deleteError = nil;
    BOOL success = [fileManager removeItemAtPath:path error:&deleteError];
    
    if (!success) {
        YYBMacLogError(kYYBDirectoryTag, @"删除目录失败: %@, 错误是%@", path, deleteError.localizedDescription);
        if (error) {
            *error = [NSError errorWithDomain:kYYBDirectoryErrorDomain
                                         code:YYBDirectoryErrorCodeDeleteFailed
                                     userInfo:deleteError.userInfo];
        }
    } else {
        YYBMacLogInfo(kYYBDirectoryTag, @"删除目录成功: %@", path);
    }
    return success;
}

/// 校验目录是否存在，不存在或不为文件夹 则重建恢复
+ (NSString *)ensureDirectoryExists:(NSString *)dirPath {
    if (dirPath.length == 0) {
        YYBMacLogError(kYYBDirectoryTag, @"目录路径为空，无法校验/创建目录");
        return nil;
    }
    BOOL isDir = NO;
    NSFileManager *fm = [NSFileManager defaultManager];
    NSError *error = nil;
    if ([fm fileExistsAtPath:dirPath isDirectory:&isDir]) {
        if (!isDir) {
            // 路径存在但不是目录，先删掉再创建
            YYBMacLogWarn(kYYBDirectoryTag, @"路径%@已存在但不是目录，先删除再重建", dirPath);
            if (![fm removeItemAtPath:dirPath error:&error]) {
                YYBMacLogError(kYYBDirectoryTag, @"删除非目录文件失败: %@, error: %@", dirPath, error.localizedDescription);
                return nil;
            }
            if (![fm createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:&error]) {
                YYBMacLogError(kYYBDirectoryTag, @"重建目录失败: %@, error: %@", dirPath, error.localizedDescription);
                return nil;
            }
            YYBMacLogInfo(kYYBDirectoryTag, @"已重建目录: %@", dirPath);
        }
    } else {
        if (![fm createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:&error]) {
            YYBMacLogError(kYYBDirectoryTag, @"创建目录失败: %@, error: %@", dirPath, error.localizedDescription);
            return nil;
        }
        YYBMacLogInfo(kYYBDirectoryTag, @"已创建目录: %@", dirPath);
    }
    return dirPath;
}

@end

//
//  YYBDirectory.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/6/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 错误码定义
typedef NS_ENUM(NSInteger, YYBDirectoryErrorCode) {
    YYBDirectoryErrorCodeNotFound = 404,        // 目录不存在
    YYBDirectoryErrorCodeNoPermission = 403,    // 没有权限
    YYBDirectoryErrorCodeCreateFailed = 500,    // 创建目录失败
    YYBDirectoryErrorCodeOpenFailed = 501,      // 打开目录失败
    YYBDirectoryErrorCodeDeleteFailed = 502,    // 删除目录失败
    YYBDirectoryErrorCodeInvalidPath = 400      // 无效路径
};

@interface YYBDirectory : NSObject

extern NSString *const kYYBDirectoryErrorDomain;

// 创建目录
+ (BOOL)createDirectoryAtPath:(NSString *)path error:(NSError **)error;

// 打开目录
+ (NSArray<NSString *> *)openDirectoryAtPath:(NSString *)path error:(NSError **)error;

// 删除目录
+ (BOOL)deleteDirectoryAtPath:(NSString *)path error:(NSError **)error;

/// 校验目录是否存在，不存在或不为文件夹 则重建恢复
+ (NSString *)ensureDirectoryExists:(NSString *)dirPath;

@end

NS_ASSUME_NONNULL_END

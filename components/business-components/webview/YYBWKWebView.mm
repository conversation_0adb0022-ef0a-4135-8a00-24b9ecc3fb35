//
//  YYBWKWebView.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/7.
//

#import "YYBWKWebView.h"
#import <WebKit/WebKit.h>
#import "YYBJSAPIManager.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "WKWebView+JSAPI.h"
#import "YYBWebViewManager.h"
#import "YYBJSScript.h"

static NSString *const kTag = @"YYBWKWebView";
static NSString *const kJSAPIName = @"macYYBJsbridge";
static NSString *const kJSLogName = @"macYYBLog";
static NSString *const kJSAPICallbackName = @"macYYBJsbridgeCallback";
static NSString *const kApplicationUAName = @"yyb";
static NSString *const kQQDomain = @"qq.com";

@interface YYBWKWebView () <WKNavigationDelegate, WKScriptMessageHandler, WKUIDelegate>

@property (nonatomic, strong) WKWebView *webView;

@end

@implementation YYBWKWebView

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
static WKProcessPool *sharedProcessPool;

+ (WKProcessPool *)sharedProcessPool {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedProcessPool = [[WKProcessPool alloc] init];
    });
    return sharedProcessPool;
}

+ (WKWebViewConfiguration *)defaultConfiguration {
    WKWebViewConfiguration *config = [[WKWebViewConfiguration alloc] init];
    config.processPool = [self sharedProcessPool];
    return config;
}
#pragma clang diagnostic pop

- (void)configConfiguration:(WKWebViewConfiguration *)configuration {
    configuration.applicationNameForUserAgent = kApplicationUAName;
    configuration.mediaTypesRequiringUserActionForPlayback = WKAudiovisualMediaTypeNone;
    if (@available(macOS 12.3, *)) {
        [configuration.preferences setElementFullscreenEnabled:YES];
    }
    // TODO: halehuang 暂时开启网页调试
//#ifndef RELEASE
    [configuration.preferences setValue:@(YES) forKey:@"developerExtrasEnabled"];
//#endif
    
    WKUserContentController *userContentController = [self configUserContentController];
    configuration.userContentController = userContentController;
}

+ (void)clearAllCookies {
    YYBMacLogInfo(kTag, @"删除所有Cookie");
    WKWebsiteDataStore *dataStore = [WKWebsiteDataStore defaultDataStore];
    NSSet *dataTypes = [NSSet setWithObjects:WKWebsiteDataTypeLocalStorage, WKWebsiteDataTypeCookies, nil];
    [dataStore removeDataOfTypes:dataTypes
                  modifiedSince:[NSDate distantPast]
              completionHandler:^{
        YYBMacLogInfo(kTag, @"所有 Cookie 已删除");
    }];
}

- (instancetype)initWithFrame:(CGRect)frame config:(YYBWKWebViewConfig *)config {
    return [self initWithFrame:frame config:config webViewClass:[WKWebView class]];
}

- (instancetype)initWithFrame:(CGRect)frame config:(YYBWKWebViewConfig *)config webViewClass:(Class)webViewClass {
    if (self = [super initWithFrame:frame]) {
        // 验证webViewClass是否是WKWebView的子类
        if (![webViewClass isSubclassOfClass:[WKWebView class]]) {
            NSAssert(NO, @"Invalid webViewClass: %@, must be a subclass of WKWebView", NSStringFromClass(webViewClass));
            return nil;
        }
        
        WKWebViewConfiguration *configuration = [[self class] defaultConfiguration];
        [self configConfiguration:configuration];
        _webView = [[webViewClass alloc] initWithFrame:frame configuration:configuration];
        _webView.navigationDelegate = self;
        _webView.UIDelegate = self;
        _webView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
        _webView.config = config;
        [self addSubview:_webView];
        
        [YYBJSAPIManager bindToWebView:self.webView];
        [[YYBWebViewManager sharedManager] setWebView:self forWindowId:config.windowId];
    }
    return self;
}

- (void)loadRequest:(NSURLRequest *)request {
    [self.webView loadRequest:request];
}

- (void)loadFileURL:(NSURL *)url {
    NSURL *directoryURL = [url URLByDeletingLastPathComponent];
    [self.webView loadFileURL:url allowingReadAccessToURL:directoryURL];
}

- (void)reload {
    [self.webView reload];
}

- (WKWebView *)wkWebView {
    return self.webView;
}

- (BOOL)isWhiteListURL:(NSURL *)url {
    NSString *host = url.host.lowercaseString;
    if ([host hasSuffix:kQQDomain]) {
        return YES;
    }
    return NO;
}

- (void)dealloc {
    [[YYBWebViewManager sharedManager] removeWebViewForWindowId:self.webView.config.windowId];
}

#pragma mark - WKScriptMessageHandler

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    if (![self isWhiteListURL:message.webView.URL]) {
        // TODO: halehuang
//        return;
    }
    
    BOOL isJSAPI = [message.name isEqualToString:kJSAPIName];
    BOOL isJSLog = [message.name isEqualToString:kJSLogName];
    if (isJSAPI || isJSLog) {
        NSString *bodyString = message.body;
        NSData *data = [bodyString dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *dic = nil;
        NSError *jsonError = nil;
        @try {
            dic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingFragmentsAllowed error:&jsonError];
        } @catch (NSException *exception) {
            YYBMacLogError(kTag, @"Exception during JSON parsing: %@", exception.reason);
            return;
        }
        if (jsonError) {
            YYBMacLogError(kTag, @"JSON parsing error: %@", jsonError.localizedDescription);
            return;
        }
        if (![dic isKindOfClass:[NSDictionary class]]) {
            YYBMacLogError(kTag, @"Invalid JSON format: expected dictionary, got %@", NSStringFromClass([dic class]));
            return;
        }
        
        NSString *name = dic[@"name"];
        NSArray *params = dic[@"params"];
        NSArray *nameComponents = [name componentsSeparatedByString:@"."];
        if (isJSLog) {
            if (nameComponents.count == 2 && params.count > 0) {
                NSString *level = nameComponents[1];
                if ([level isEqualToString:@"log"]) {
                    YYBMacLogInfo(kTag, @"[%@]: %@", name, params.count > 0 ? params[0] : @"");
                } else if ([level isEqualToString:@"info"]) {
                    YYBMacLogInfo(kTag, @"[%@]: %@", name, params.count > 0 ? params[0] : @"");
                } else if ([level isEqualToString:@"warn"]) {
                    YYBMacLogWarn(kTag, @"[%@]: %@", name, params.count > 0 ? params[0] : @"");
                } else if ([level isEqualToString:@"error"]) {
                    YYBMacLogError(kTag, @"[%@]: %@", name, params.count > 0 ? params[0] : @"");
                }
            } else {
                YYBMacLogError(kTag, @"Invalid console log message: %@", dic);
            }
        } else {
            NSString *callbackId = dic[@"callbackId"];
            if (nameComponents.count == 2 && callbackId.length != 0) {
                NSString *moduleName = nameComponents[0];
                NSString *methodName = nameComponents[1];
                [[YYBJSAPIManager sharedManager] handleJSAPICall:moduleName
                                                      methodName:methodName
                                                          params:params ?: @[]
                                                      callbackId:callbackId
                                                         webView:self.webView];
            } else {
                YYBMacLogError(kTag, @"Invalid jsapi message: %@", dic);
            }
        }
    }
}

#pragma mark - WKNavigationDelegate

- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    YYBMacLogInfo(kTag, @"didStartProvisionalNavigation");
}

- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation {
    YYBMacLogInfo(kTag, @"didCommitNavigation");
}

- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation {
    YYBMacLogInfo(kTag, @"didReceiveServerRedirectForProvisionalNavigation");
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    YYBMacLogInfo(kTag, @"didFinishNavigation");
}

- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    YYBMacLogWarn(kTag, @"didFailProvisionalNavigation with error: %@", error.localizedDescription);
}

- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    YYBMacLogWarn(kTag, @"didFailNavigation with error: %@", error.localizedDescription);
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    YYBMacLogInfo(kTag, @"decidePolicyForNavigationAction: %@", navigationAction.request);
    decisionHandler(WKNavigationActionPolicyAllow);
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    YYBMacLogInfo(kTag, @"decidePolicyForNavigationResponse: %@", navigationResponse.response);
    decisionHandler(WKNavigationResponsePolicyAllow);
}

- (void)webViewWebContentProcessDidTerminate:(WKWebView *)webView {
    YYBMacLogError(kTag, @"webViewWebContentProcessDidTerminate");
    [webView reload];
}

#pragma mark - WKUIDelegate
// 创建新窗口
- (nullable WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures {
    YYBMacLogInfo(kTag, @"createWebViewWithConfiguration for URL: %@", navigationAction.request.URL);
    NSURL *url = navigationAction.request.URL;
    if (url && ([url.scheme isEqualToString:@"http"] || [url.scheme isEqualToString:@"https"])) {
        [[NSWorkspace sharedWorkspace] openURL:url];
    }
    return nil;
}

// 窗口关闭通知
- (void)webViewDidClose:(WKWebView *)webView {
    YYBMacLogInfo(kTag, @"webViewDidClose");
    [self.window close];
}

// alert 弹窗
- (void)webView:(WKWebView *)webView runJavaScriptAlertPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(void))completionHandler {
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = [self appName];
    alert.informativeText = message;
    [alert addButtonWithTitle:@"确定"];
    [alert beginSheetModalForWindow:self.window completionHandler:^(NSModalResponse returnCode) {
        completionHandler();
    }];
}

// confirm 弹窗
- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL result))completionHandler {
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = [self appName];
    alert.informativeText = message;
    [alert addButtonWithTitle:@"确定"];
    [alert addButtonWithTitle:@"取消"];
    [alert beginSheetModalForWindow:self.window completionHandler:^(NSModalResponse returnCode) {
        BOOL result = (returnCode == NSAlertFirstButtonReturn);
        completionHandler(result);
    }];
}

// prompt 弹窗
- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * _Nullable result))completionHandler {
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = [self appName];
    alert.informativeText = prompt;
    [alert addButtonWithTitle:@"确定"];
    [alert addButtonWithTitle:@"取消"];
    NSTextField *input = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 200, 24)];
    input.stringValue = defaultText ?: @"";
    alert.accessoryView = input;
    [alert beginSheetModalForWindow:self.window completionHandler:^(NSModalResponse returnCode) {
        if (returnCode == NSAlertFirstButtonReturn) {
            completionHandler(input.stringValue);
        } else {
            completionHandler(nil);
        }
    }];
}

#pragma mark - User Content Controller
- (WKUserContentController *)configUserContentController {
    WKUserContentController *userContentController = [[WKUserContentController alloc] init];
    // jsapi
    [userContentController addScriptMessageHandler:self name:kJSAPIName];
    // hook js log
    WKUserScript *userScript = [[WKUserScript alloc] initWithSource:hookConsoleScript injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:NO];
    [userContentController addUserScript:userScript];
    [userContentController addScriptMessageHandler:self name:kJSLogName];
    return userContentController;
}

#pragma mark - private
- (NSString *)appName {
    NSString *appName = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"];
    return appName;
}

@end

//
//  YYBBasicInfoModule.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/23.
//

#import "YYBBasicInfoModule.h"
#import "YYBJSAPIMacros.h"
#import "system_info.h"
#import "YYBDefine.h"
#import "SocketDataSender.h"
#import "WKWebView+JSAPI.h"

@interface JSGetGraphicsInfoBase : NSObject

@property (nonatomic, copy) NSString *driverVersion;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *openglVersion;
@property (nonatomic, copy) NSString *vendor;
@property (nonatomic, copy) NSString *deviceId;
@property (nonatomic, copy) NSString *driverDate;
@property (nonatomic, copy, nullable) NSString *vendorId;

@end


@implementation JSGetGraphicsInfoBase

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"driverVersion" : @"driver_version",
        @"openglVersion" : @"opengl_version",
        @"deviceId" : @"device_id",
        @"driverDate" : @"driver_date",
        @"vendorId" : @"vendor_id"
    };
}

@end

@interface JSGetGraphicsInfos : NSObject

@property (nonatomic, strong) JSGetGraphicsInfoBase *primaryGraphics;
@property (nonatomic, strong, nullable) JSGetGraphicsInfoBase *viceGraphics;

@end

@implementation JSGetGraphicsInfos

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"primaryGraphics" : @"primary_graphics",
        @"viceGraphics" : @"vice_graphics"
    };
}

@end

static JSGetGraphicsInfoBase *ConvertToOCGraphicsInfo(const SystemInfo::GraphicsInfoBase &info) {
    JSGetGraphicsInfoBase *ocInfo = [[JSGetGraphicsInfoBase alloc] init];
    ocInfo.driverVersion = [NSString stringWithUTF8String:info.driverVersion.c_str()];
    ocInfo.model = [NSString stringWithUTF8String:info.model.c_str()];
    ocInfo.openglVersion = [NSString stringWithUTF8String:info.openglVersion.c_str()];
    ocInfo.vendor = [NSString stringWithUTF8String:info.vendor.c_str()];
    ocInfo.deviceId = [NSString stringWithUTF8String:info.deviceId.c_str()];
    ocInfo.driverDate = [NSString stringWithUTF8String:info.driverDate.c_str()];
    ocInfo.vendorId = info.vendorId.empty() ? nil : [NSString stringWithUTF8String:info.vendorId.c_str()];
    return ocInfo;
}

static JSGetGraphicsInfos *ConvertToOCGraphicsInfos(const SystemInfo::GraphicsInfos &infos) {
    JSGetGraphicsInfos *ocInfos = [[JSGetGraphicsInfos alloc] init];
    ocInfos.primaryGraphics = ConvertToOCGraphicsInfo(infos.primaryGraphics);
    if (infos.viceGraphics) {
        ocInfos.viceGraphics = ConvertToOCGraphicsInfo(*infos.viceGraphics);
    } else {
        ocInfos.viceGraphics = nil;
    }
    return ocInfos;
}

@implementation YYBBasicInfoModule

YYB_EXPORT_MODULE(basicInfo)

/**
 * @description 获取主板型号
 * @param 无
 * @return {String} 主板制造商名称
 */
YYB_EXPORT_METHOD(GetBaseboardVendor) {
    SystemInfo& sysInfo = SystemInfo::getInstance();
    SystemInfo::MotherboardInfo motherboardInfo = sysInfo.getMotherboardInfo();
    YYB_SEND_SUCCESS([NSString stringWithUTF8String:motherboardInfo.manufacturer.c_str()]);
}

/**
 * @description 获取可用硬盘大小
 * @param 无
 * @return {Number} 可用硬盘空间大小（字节）
 */
YYB_EXPORT_METHOD(GetFreeDiskSize) {
    SystemInfo& sysInfo = SystemInfo::getInstance();
    SystemInfo::DiskInfo diskInfo = sysInfo.getDiskInfo();
    YYB_SEND_SUCCESS(@(diskInfo.free));
}

/**
 * @description 获取可用内存大小
 * @param 无
 * @return {Number} 可用内存大小（字节）
 */
YYB_EXPORT_METHOD(GetFreeMemorySize) {
    SystemInfo& sysInfo = SystemInfo::getInstance();
    SystemInfo::MemoryInfo memoryInfo = sysInfo.getMemoryInfo();
    YYB_SEND_SUCCESS(@(memoryInfo.free));
}

/**
 * @description 获取Android设备可用存储空间大小
 * @param 无
 * @return {Number} Android设备可用存储空间大小（字节）
 */
YYB_EXPORT_METHOD(GetAndroidFreeSize) {
    SocketMessageAction action = @"getAndroidDiskInfo";
    [SocketDataSender sendRequest:action from:self.webView.config.process with:nil to:kProcessEngine callback:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        if (error.code == SocketErrorCodeNone) {
            NSNumber *availableSize = response[@"availableSize"];
            if (availableSize != nil) {
                YYB_SEND_SUCCESS(availableSize);
                return;
            }
        }
        
        YYB_SEND_ERROR(error);
    }];
}

/**
 * @description 获取图形信息
 * @param 无
 * @return {Object} 图形信息对象
 *   @property {Object} primary_graphics 主显卡信息
 *     @property {String} driver_version 驱动版本
 *     @property {String} model 显卡型号
 *     @property {String} opengl_version OpenGL版本
 *     @property {String} vendor 厂商
 *     @property {String} device_id 设备ID
 *     @property {String} driver_date 驱动日期
 *     @property {String} vendor_id 厂商ID（可选）
 *   @property {Object} vice_graphics 副显卡信息（可选，结构同primary_graphics）
 */
YYB_EXPORT_METHOD(GetGraphicsInfos) {
    SystemInfo::GraphicsInfos cppInfos = SystemInfo::getInstance().getGraphicsInfos();
    JSGetGraphicsInfos *ocInfos = ConvertToOCGraphicsInfos(cppInfos);
    YYB_SEND_SUCCESS(ocInfos);
}

@end

//
//  YYBDirectoryModule.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/8.
//

#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBDirectoryModule.h"
#import "YYBJSAPIMacros.h"
#import "YYBDirectory.h"
#import "YYBFile.h"

static NSString *const kTag = @"YYBDirectoryModule";

@implementation YYBDirectoryModule

YYB_EXPORT_MODULE(directory)

/**
 * @description 创建目录
 * @param {String} path 要创建的目录路径
 * @return {Number} 创建目录成功返回成功码，不成功返回错误码
 */
YYB_EXPORT_METHOD(Create) {
    YYB_DECLARE_PARAM(0, NSString, path);
    NSError *error;
    BOOL success = [YYBDirectory createDirectoryAtPath:path error:&error];
    if (error) {
        YYBMacLogInfo(kTag, @"Create error: %@", error);
    }
    YYB_SEND_RESULT_BY_CONDITION(success, nil, nil);
}

/**
 * @description 在Finder中打开文件所在的目录并选中文件
 * @param {String} path 文件路径
 * @return {Number} 打开目录成功返回成功码，不成功返回错误码
 */
YYB_EXPORT_METHOD(OpenFilePath) {
    YYB_DECLARE_PARAM(0, NSString, path);
    BOOL success = [YYBFile openFileInFilder:path];
    YYB_SEND_RESULT_BY_CONDITION(success, nil, nil);
}

@end

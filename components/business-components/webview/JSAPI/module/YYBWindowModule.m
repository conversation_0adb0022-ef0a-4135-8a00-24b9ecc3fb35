//
//  YYBWindwoModule.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/14.
//

#import "YYBWindowModule.h"
#import "YYBJSAPIMacros.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "WKWebView+JSAPI.h"
#import "JSWebViewObject.h"
#import "YYBWebWindowController.h"
#import "YYBWebViewManager.h"

static NSString *const kTag = @"YYBWindowModule";
static NSString *const YYBJSBProcessNotification = @"YYBJSBProcessNotification";
static YYBJSAPIEventName const JSAPIBroadcastEvent = @"broadcastEvent";

@implementation YYBWindowModule

YYB_EXPORT_MODULE(window)

/**
 * @description 向其他窗口广播消息
 * @param {Dictionary} message 要广播的消息内容
 * @return 无
 */
YYB_EXPORT_METHOD(GetWebWindowId) {
    YYB_SEND_SUCCESS(self.webView.config.windowId);
}

/**
 * @description 添加广播消息监听器
 * @param 无
 * @return 无
 * @event broadcastEvent 当收到广播消息时触发
 *    @property {Dictionary} message 广播内容
 */
YYB_EXPORT_METHOD(GetApiList) {
    NSMutableArray *apiList = [NSMutableArray array];
    for (YYBJSAPIModule *module in [[[YYBJSAPIManager sharedManager] modules] allValues]) {
        NSMutableDictionary *moduleDict = [NSMutableDictionary dictionary];
        NSString *moduleName = [module class].moduleName;
        [moduleDict setObject:moduleName forKey:@"module_name"];
        NSArray *apiNames = [[YYBJSAPIManager sharedManager].moduleMethods objectForKey:moduleName].allKeys;
        if (apiNames.count != 0) {
            [moduleDict setObject:apiNames forKey:@"apis"];
        }
        [apiList addObject:moduleDict];
    }
    YYB_SEND_SUCCESS(apiList);
}

/**
 * @description 将窗口置顶显示
 * @param 无
 * @return 无
 */
YYB_EXPORT_METHOD(ShowTop) {
    [NSApp activateIgnoringOtherApps:YES];
    [self.webView.window makeKeyAndOrderFront:nil];
}

/**
 * @description 最小化窗口
 * @param 无
 * @return 无
 */
YYB_EXPORT_METHOD(minimize) {
    [self.webView.window miniaturize:nil];
}

/**
 * @description 关闭窗口
 * @param 无
 * @return 无
 */
YYB_EXPORT_METHOD(close) {
    [self.webView.window close];
}

/**
 * @description 窗口是否放大状态
 * @param 无
 * @return {Boolean} 窗口是否放大状态
 */
YYB_EXPORT_METHOD(IsMaximize) {
    YYB_SEND_SUCCESS(@(self.webView.window.isZoomed));
}

/**
 * @description 打开新的WebView窗口
 * @param {JSWebViewObject} object WebView配置对象
 *   @property {String} url 要加载的URL
 *   @property {String} win_id 窗口ID（可选）
 *   @property {String} display_name 窗口显示名称（可选）
 *   @property {Number} x 相对屏幕左下角x坐标（可选）
 *   @property {Number} y 相对屏幕左下角y坐标（可选）
 *   @property {Number} width 窗口宽度（可选）
 *   @property {Number} height 窗口高度（可选）
 *   @property {Number} min_width 窗口最小宽度（可选）
 *   @property {Number} min_height 窗口最小高度（可选）
 *   @property {Number} max_width 窗口最大宽度（可选）
 *   @property {Number} max_height 窗口最大高度（可选）
 *   @property {Boolean} frameless 是否无边框（可选）
 *   @property {Boolean} resizable 是否可调整大小（可选）
 *   @property {Boolean} modal 是否模态窗口（可选）
 *   @property {String} close_schema 关闭时执行的schema（可选）
 *   @property {Boolean} translucent 是否半透明（可选）
 *   @property {Number} show_position 展示位置（可选，1:父容器中间，2:屏幕中间）
 *   @property {Object} extra_info 额外信息（可选）
 *     @property {Boolean} show_top_btn 是否显示置顶按钮
 *     @property {Boolean} tool 是否工具窗口
 * @return 无
 */
YYB_EXPORT_METHOD(OpenWebview) {
    YYB_DECLARE_PARAM(0, JSWebViewObject, object);
    object.process = self.webView.config.process;
    YYBWebWindowController *windowController = [[YYBWebWindowController alloc] initWithWebViewObject:object parentWindow:self.webView.window];
    [windowController showWindow:nil];
}

/**
 * @description 关闭指定的WebView窗口
 * @param {String} windowId 要关闭的窗口ID
 * @return 无
 */
YYB_EXPORT_METHOD(CloseWebview) {
    YYB_DECLARE_PARAM(0, NSString, windowId);
    NSView *view = self.webView;
    YYBWKWebView *webView = [[YYBWebViewManager sharedManager] webViewForWindowId:windowId];
    if (webView) {
        view = (NSView *)webView;
    }
    [view.window close];
}

/**
 * @description 向其他窗口广播消息
 * @param {Dictionary} message 要广播的消息内容
 * @return 无
 */
YYB_EXPORT_METHOD(Broadcast) {
    YYBMacLogInfo(kTag, @"Broadcast: %@", params);
    YYB_DECLARE_PARAM(1, NSDictionary, message);
    if (message != nil) {
        [[NSDistributedNotificationCenter defaultCenter] postNotificationName:YYBJSBProcessNotification
                                                                       object:nil
                                                                     userInfo:message
                                                           deliverImmediately:YES];
    }
}

/**
 * @description 添加广播消息监听器
 * @param 无
 * @return 无
 * @event broadcastEvent 当收到广播消息时触发
 *    @property {Dictionary} message 广播内容
 */
YYB_EXPORT_METHOD(AddBroadcastListener) {
    YYB_ADD_EVENT_LISTENER(JSAPIBroadcastEvent);
    [[NSDistributedNotificationCenter defaultCenter] addObserver:self
                                                        selector:@selector(receiveNotification:)
                                                            name:YYBJSBProcessNotification
                                                        object:nil];
}

- (void)receiveNotification:(NSNotification *)notification {
    YYBMacLogInfo(kTag, @"Received: %@", notification.userInfo);
    YYB_TRIGGER_EVENT_LISTENER(JSAPIBroadcastEvent, notification.userInfo);
}

- (void)dealloc {
    [[NSDistributedNotificationCenter defaultCenter] removeObserver:self];
}

@end

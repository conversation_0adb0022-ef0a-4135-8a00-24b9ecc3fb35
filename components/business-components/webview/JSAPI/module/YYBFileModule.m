//
//  YYBFileModule.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/8.
//

#import "YYBFileModule.h"
#import "YYBJSAPIMacros.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBFile.h"

static NSString *const kTag = @"YYBFileModule";

@implementation YYBFileModule

YYB_EXPORT_MODULE(file)

/**
 * @description 检查文件是否存在
 * @param {String} path 文件路径
 * @return {Number} 文件存在返回成功码，不存在返回错误码
 */
YYB_EXPORT_METHOD(Exists) {
    YYB_DECLARE_PARAM(0, NSString, path);
    NSError *aError;
    BOOL isExists = [YYBFile isFileExistsAtPath:path error:&aError];
    if (aError) {
        YYBMacLogError(kTag, @"Exists error: %@", aError);
    }
    YYB_SEND_RESULT_BY_CONDITION(isExists, nil, aError);
}

/**
 * @description 删除文件
 * @param {String} path 要删除的文件路径
 * @return {Number} 文件存在返回成功码，不存在返回错误码
 */
YYB_EXPORT_METHOD(Delete) {
    YYB_DECLARE_PARAM(0, NSString, path);
    NSError *aError;
    BOOL success = [YYBFile deleteFileAtPath:path error:&aError];
    if (aError) {
        YYBMacLogError(kTag, @"Delete error: %@", aError);
    }
    YYB_SEND_RESULT_BY_CONDITION(success, nil, aError);
}

/**
 * @description 获取文件的MD5值
 * @param {String} path 文件路径
 * @return {Object} 包含文件MD5值的对象
 *   @property {String} md5 文件的MD5值
 */
YYB_EXPORT_METHOD(getMd5) {
    YYB_DECLARE_PARAM(0, NSString, path);
    NSDictionary *info = [YYBFile getFileInfoAtPath:path];
    YYB_SEND_SUCCESS(@{@"md5": info[kSHA256Key]});
}

@end

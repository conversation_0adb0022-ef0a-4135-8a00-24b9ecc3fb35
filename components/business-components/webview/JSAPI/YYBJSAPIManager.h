//
//  YYBJSAPIManager.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class WKWebView;
@class YYBJSAPIModule;

@interface YYBJSAPIManager : NSObject

@property (nonatomic, strong, readonly) NSMutableDictionary<NSString *, YYBJSAPIModule *> *modules;
@property (nonatomic, strong, readonly) NSMutableDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *moduleMethods;

+ (instancetype)sharedManager;
+ (void)registerModuleClass:(Class)moduleClass;
+ (void)bindToWebView:(WKWebView *)webView;
+ (NSArray<Class> *)getRegisteredModuleClasses;
- (NSDictionary<NSString *, YYBJSAPIModule *> *)allModules;
- (nullable YYBJSAPIModule *)moduleWithName:(NSString *)moduleName;
- (void)handleJSAPICall:(NSString *)moduleName
              methodName:(NSString *)methodName
                  params:(NSArray *)params
              callbackId:(NSString *)callbackId
                 webView:(WKWebView *)webView;
- (void)sendCallback:(NSString *)callbackId
              result:(nullable id)result
               error:(nullable NSError *)error
             webView:(WKWebView *)webView;
+ (nullable id)convertParam:(id)param modelClass:(Class)modelClass;

@end

NS_ASSUME_NONNULL_END 

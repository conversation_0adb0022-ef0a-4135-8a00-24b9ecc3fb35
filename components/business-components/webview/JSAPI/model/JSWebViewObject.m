//
//  JSWebViewObject.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/22.
//

#import "JSWebViewObject.h"

@implementation IOpenWebViewExtraInfo

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"showTopBtn": @"show_top_btn",
    };
}

@end

@implementation JSWebViewObject

- (instancetype)init {
    self = [super init];
    if (self) {
        _frameless = NO;
        _resizable = YES;
        _modal = NO;
        _translucent = NO;
    }
    return self;
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"winId": @"win_id",
        @"displayName": @"display_name",
        @"minWidth": @"min_width",
        @"minHeight": @"min_height",
        @"maxWidth": @"max_width",
        @"maxHeight": @"max_height",
        @"closeSchema": @"close_schema",
        @"showPosition": @"show_position",
        @"extraInfo": @"extra_info",
    };
}

@end

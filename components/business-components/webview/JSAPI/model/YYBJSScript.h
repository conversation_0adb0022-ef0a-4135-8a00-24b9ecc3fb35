//
//  YYBJSScript.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/25.
//

#import <Foundation/Foundation.h>

// 用于拦截前端日志打印，桥接到客户端
static NSString *const hookConsoleScript = @"(function() {\n"
"  var originalLog = console.log;\n"
"  var originalError = console.error;\n"
"  var originalWarn = console.warn;\n"
"  var originalInfo = console.info;\n"
"  function sendToClient(type, args) {\n"
"    if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.macYYBLog) {\n"
"      try {\n"
"        window.webkit.messageHandlers.macYYBLog.postMessage(JSON.stringify({ name: 'console.' + type, params: [args.map(String).join(' ')] }));\n"
"      } catch (e) {}\n"
"    }\n"
"  }\n"
"  console.log = function() {\n"
"    sendToClient('log', Array.prototype.slice.call(arguments));\n"
"    originalLog.apply(console, arguments);\n"
"  };\n"
"  console.error = function() {\n"
"    sendToClient('error', Array.prototype.slice.call(arguments));\n"
"    originalError.apply(console, arguments);\n"
"  };\n"
"  console.warn = function() {\n"
"    sendToClient('warn', Array.prototype.slice.call(arguments));\n"
"    originalWarn.apply(console, arguments);\n"
"  };\n"
"  console.info = function() {\n"
"    sendToClient('info', Array.prototype.slice.call(arguments));\n"
"    originalInfo.apply(console, arguments);\n"
"  };\n"
"})();";

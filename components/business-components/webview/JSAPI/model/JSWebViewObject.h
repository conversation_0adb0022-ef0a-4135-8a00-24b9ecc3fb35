//
//  JSWebViewObject.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, IOpenWebviewShowPosition) {
    IOpenWebviewShowPositionParentCenter = 1, // 父容器中间
    IOpenWebviewShowPositionScreenCenter = 2  // 屏幕中间
};

@interface IOpenWebViewExtraInfo : NSObject

@property (nonatomic, assign) BOOL showTopBtn; // 是否置顶按钮
@property (nonatomic, assign) BOOL tool;

@end

// 前端使用的webview对象
@interface JSWebViewObject : NSObject

@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSString *process;
@property (nonatomic, copy, nullable) NSString *winId;
@property (nonatomic, copy, nullable) NSString *displayName;

@property (nonatomic, strong, nullable) NSNumber *x;    // 相对屏幕左下角x坐标
@property (nonatomic, strong, nullable) NSNumber *y;    // 相对屏幕左下角y坐标
@property (nonatomic, strong, nullable) NSNumber *width;
@property (nonatomic, strong, nullable) NSNumber *height;
@property (nonatomic, strong, nullable) NSNumber *minWidth;
@property (nonatomic, strong, nullable) NSNumber *minHeight;
@property (nonatomic, strong, nullable) NSNumber *maxWidth;
@property (nonatomic, strong, nullable) NSNumber *maxHeight;

@property (nonatomic, assign) BOOL frameless;
@property (nonatomic, assign) BOOL resizable;
@property (nonatomic, assign) BOOL modal;       // 是否模态

@property (nonatomic, copy, nullable) NSString *closeSchema;    // 关闭时执行的schema
@property (nonatomic, assign) BOOL translucent;
@property (nonatomic, assign) IOpenWebviewShowPosition showPosition;    // 展示位置，优先级高于x,y
@property (nonatomic, strong, nullable) IOpenWebViewExtraInfo *extraInfo;

@end

NS_ASSUME_NONNULL_END

//
//  YYBJSAPIModule.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/7.
//

#import "YYBJSAPIModule.h"
#import "YYBJSAPIMacros.h"

@interface YYBJSAPIModule ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableArray<NSString *> *> *listeners;

@end

@implementation YYBJSAPIModule

+ (NSString *)moduleName {
    NSAssert(NO, @"请在子类通过YYB_EXPORT_METHOD实现该方法");
    return @"JSAPI";
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _listeners = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)addListener:(YYBJSAPIEventName)eventName callbackId:(NSString *)callbackId {
    NSMutableArray<NSString *> *callbackIds = self.listeners[eventName];
    if (!callbackIds) {
        callbackIds = [NSMutableArray array];
        self.listeners[eventName] = callbackIds;
    }
    [callbackIds addObject:callbackId];
}

- (void)removeListenerWithCallbackId:(NSString *)removedCallbackId {
    NSArray<NSString *> *eventNames = [self.listeners allKeys];
    for (NSString *eventName in eventNames) {
        NSMutableArray<NSString *> *callbackIds = self.listeners[eventName];
        if ([callbackIds containsObject:removedCallbackId]) {
            [callbackIds removeObject:removedCallbackId];
            if (callbackIds.count == 0) {
                [self.listeners removeObjectForKey:eventName];
            }
            break;
        }
    }
}

- (void)triggerListener:(YYBJSAPIEventName)eventName data:(id)data error:(NSError *)error  {
    NSMutableArray<NSString *> *callbackIds = self.listeners[eventName];
    for (NSString *callbackId in callbackIds) {
        if (error) {
            YYB_SEND_ERROR_ASYNC(callbackId, error);
        } else {
            YYB_SEND_SUCCESS_ASYNC(callbackId, data);
        }
    }
}

@end

//
//  YYBJSAPIManager.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/7.
//

#import "YYBJSAPIManager.h"
#import <WebKit/WebKit.h>
#import <objc/runtime.h>
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYModel.h"
#import "YYBJSAPIModule.h"
#import "MacroUtils.h"
#import "YYBJSAPIDefine.h"

static NSString *const kJSAPICallbackName = @"macYYBJsbridgeCallback";
static NSString *const kTag = @"YYBJSAPIManager";

typedef NS_ENUM(NSInteger, YYBJSAPIErrorCode) {
    YYBJSAPIErrorCodeModuleNotFound = -10001,
    YYBJSAPIErrorCodeNoMethodsFound = -10002,
    YYBJSAPIErrorCodeMethodNotFound = -10003,
    YYBJSAPIErrorCodeExceptionOccurred = -10004,
    YYBJSAPIErrorCodeSelectorNotResponded = -10005,
    YYBJSAPIErrorCodeInvalidMethodSignature = -10006
};

@interface YYBJSAPIManager ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, YYBJSAPIModule *> *modules;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *moduleMethods;
@property (nonatomic, strong) NSMutableArray<Class> *registeredModuleClasses;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDate *> *callbackStartTimes;
@property (nonatomic, strong) dispatch_queue_t callbackTimeQueue;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDictionary *> *callbackInfoDict;

@end

@implementation YYBJSAPIManager

+ (instancetype)sharedManager {
    static YYBJSAPIManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[YYBJSAPIManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _modules = [NSMutableDictionary dictionary];
        _moduleMethods = [NSMutableDictionary dictionary];
        _registeredModuleClasses = [NSMutableArray array];
        _callbackStartTimes = [NSMutableDictionary dictionary];
        _callbackTimeQueue = dispatch_queue_create("com.yybjsapi.callbackTimeQueue", DISPATCH_QUEUE_CONCURRENT);
        _callbackInfoDict = [NSMutableDictionary dictionary];
    }
    return self;
}

+ (void)registerModuleClass:(Class)moduleClass {
    if (![moduleClass isSubclassOfClass:[YYBJSAPIModule class]]) {
        NSLog(@"Failed to register module class %@: not a subclass of YYBJSAPIModule", NSStringFromClass(moduleClass));
        return;
    }
    
    YYBJSAPIManager *manager = [self sharedManager];
    if (![manager.registeredModuleClasses containsObject:moduleClass]) {
        [manager.registeredModuleClasses addObject:moduleClass];
        NSLog(@"Registered module class: %@", NSStringFromClass(moduleClass));
    }
}

+ (void)bindToWebView:(WKWebView *)webView {
    for (Class moduleClass in [self getRegisteredModuleClasses]) {
        YYBJSAPIManager *manager = [self sharedManager];
        
        // 创建模块实例
        YYBJSAPIModule *module = [[moduleClass alloc] init];
        module.webView = webView;
        
        // 从实例获取模块名，这样可以确保使用子类的实现
        NSString *moduleName = [moduleClass moduleName];
        
        if (!moduleName || moduleName.length == 0) {
            YYBMacLogError(kTag, @"Failed to register module %@: invalid module name", NSStringFromClass(moduleClass));
            return;
        }
        
        manager.modules[moduleName] = module;
        
        // 扫描模块方法
        [manager scanModuleMethods:moduleClass moduleName:moduleName];
        
        YYBMacLogInfo(kTag, @"Registered module: %@", moduleName);
    }
}

+ (NSArray<Class> *)getRegisteredModuleClasses {
    return [[self sharedManager].registeredModuleClasses copy];
}

- (NSDictionary<NSString *, YYBJSAPIModule *> *)allModules {
    return [self.modules copy];
}

- (nullable YYBJSAPIModule *)moduleWithName:(NSString *)moduleName {
    return self.modules[moduleName];
}

- (void)handleJSAPICall:(NSString *)moduleName
              methodName:(NSString *)methodName
                  params:(NSArray *)params
              callbackId:(NSString *)callbackId
                 webView:(WKWebView *)webView {
    
    YYBMacLogInfo(kTag, @"Handling JSAPI call: %@.%@ with params: %@ and callbackId: %@", moduleName, methodName, params, callbackId);
    // 记录开始时间和来源
    if (callbackId.length > 0) {
        NSDate *now = [NSDate date];
        dispatch_barrier_async(self.callbackTimeQueue, ^{
            self.callbackStartTimes[callbackId] = now;
            self.callbackInfoDict[callbackId] = @{
                @"moduleName": moduleName ?: @"",
                @"methodName": methodName ?: @"",
                @"params": params ?: @[],
            };
        });
    }
    
    // 获取模块
    YYBJSAPIModule *module = [self moduleWithName:moduleName];
    if (!module) {
        NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                           code:YYBJSAPIErrorCodeModuleNotFound
                                       userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Module '%@' not found", moduleName]}];
        [self sendCallback:callbackId result:nil error:error webView:webView];
        return;
    }
    
    // 获取方法映射
    NSDictionary<NSString *, NSString *> *methods = self.moduleMethods[moduleName];
    if (!methods) {
        NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                             code:YYBJSAPIErrorCodeNoMethodsFound
                                         userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"No methods found for module '%@'", moduleName]}];
        [self sendCallback:callbackId result:nil error:error webView:webView];
        return;
    }
    
    // 查找方法
    NSString *selectorName = methods[methodName];
    if (!selectorName) {
        NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                             code:YYBJSAPIErrorCodeMethodNotFound
                                         userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Method '%@' not found in module '%@'", methodName, moduleName]}];
        [self sendCallback:callbackId result:nil error:error webView:webView];
        return;
    }
    
    SEL selector = NSSelectorFromString(selectorName);
    if (!selector) {
        NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                             code:YYBJSAPIErrorCodeMethodNotFound
                                         userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Method '%@' not found in module '%@'", methodName, moduleName]}];
        [self sendCallback:callbackId result:nil error:error webView:webView];
        return;
    }
    
    // 调用方法
    if ([module respondsToSelector:selector]) {
        @try {
            // 使用NSInvocation来安全地调用方法，避免performSelector的内存泄漏警告
            NSMethodSignature *signature = [module methodSignatureForSelector:selector];
            if (signature) {
                NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
                invocation.target = module;
                invocation.selector = selector;
                
                // 设置参数
                if (signature.numberOfArguments > 2) {
                    [invocation setArgument:&params atIndex:2];
                }
                if (signature.numberOfArguments > 3) {
                    [invocation setArgument:&callbackId atIndex:3];
                }
                
                [invocation invoke];
            } else {
                NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                                     code:YYBJSAPIErrorCodeInvalidMethodSignature
                                                 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Invalid method signature for '%@'", methodName]}];
                [self sendCallback:callbackId result:nil error:error webView:webView];
            }
        } @catch (NSException *exception) {
            NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                                 code:YYBJSAPIErrorCodeExceptionOccurred
                                             userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Exception calling method '%@': %@", methodName, exception.reason]}];
            [self sendCallback:callbackId result:nil error:error webView:webView];
        }
    } else {
        NSError *error = [NSError errorWithDomain:kJSAPIErrorDomain
                                             code:YYBJSAPIErrorCodeSelectorNotResponded
                                         userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Module '%@' does not respond to selector '%@'", moduleName, NSStringFromSelector(selector)]}];
        [self sendCallback:callbackId result:nil error:error webView:webView];
    }
}

- (void)sendCallback:(NSString *)callbackId
              result:(nullable id)result
               error:(nullable NSError *)error
             webView:(WKWebView *)webView {
    __block NSTimeInterval costTime = -1;
    __block NSString *moduleName = nil;
    __block NSString *methodName = nil;
    __block NSArray *params = nil;
    if (callbackId.length > 0) {
        dispatch_sync(self.callbackTimeQueue, ^{
            NSDate *start = self.callbackStartTimes[callbackId];
            if (start) {
                costTime = [[NSDate date] timeIntervalSinceDate:start];
            }
            NSDictionary *info = self.callbackInfoDict[callbackId];
            moduleName = info[@"moduleName"];
            methodName = info[@"methodName"];
            params =  info[@"params"];
        });
        // 移除记录，防止内存泄漏
        dispatch_barrier_async(self.callbackTimeQueue, ^{
            [self.callbackStartTimes removeObjectForKey:callbackId];
            [self.callbackInfoDict removeObjectForKey:callbackId];
        });
    }
    
    YYBMacLogInfo(kTag, @"SendCallback _time_jsapi_send JSAPI with callbackId: %@, moduleName: %@, methodName: %@, params: %@, error: %@, costTime: %.3f ms",
                  callbackId,
                  moduleName,
                  methodName,
                  params,
//                  result,
                  error,
                  costTime >= 0 ? costTime * 1000 : -1);
    
    NSInteger code = error ? error.code : ResponseCodeSuccess;
    NSString *body = @"";
    NSString *message = @"";
    
    if (error) {
        message = error.localizedDescription;
    } else if (result) {
        if ([result isKindOfClass:[NSString class]]) {
            body = result;
        } else if ([result isKindOfClass:[NSDictionary class]] || [result isKindOfClass:[NSArray class]]) {
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:result options:0 error:nil];
            if (jsonData) {
                body = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            }
        } else if ([result isKindOfClass:[NSNumber class]]) {
            body = [result description];
        } else if ([result isKindOfClass:[NSObject class]]){
            body = [result yy_modelToJSONString];
        } else {
            body = [result description];
        }
    }
    
    // 转义特殊字符
    NSString *escapedBody = [body stringByReplacingOccurrencesOfString:@"\"" withString:@"\\\""];
    NSString *escapedMessage = [message stringByReplacingOccurrencesOfString:@"\"" withString:@"\\\""];
    NSString *jsString = [NSString stringWithFormat:@"window.%@(\"%@\", \"%@\", \"%@\", \"%@\")",
                          kJSAPICallbackName, callbackId, @(code), escapedBody, escapedMessage];
    
    DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
        [webView evaluateJavaScript:jsString completionHandler:^(id _Nullable res, NSError * _Nullable jsError) {
            if (jsError) {
                YYBMacLogError(kTag, @"Failed to execute: %@ callback JavaScript: %@", jsString, jsError.localizedDescription);
            }
        }];
    });
}

+ (id)convertParam:(id)param modelClass:(Class)modelClass {
    if (!param) {
        return nil;
    }
    if (modelClass == [NSString class]) {
        if ([param isKindOfClass:[NSString class]]) {
            return param;
        } else {
            return [param description];
        }
    } else if (modelClass == [NSDictionary class]) {
        if ([param isKindOfClass:[NSDictionary class]]) {
            return param;
        } else if ([param isKindOfClass:[NSString class]]) {
            NSData *jsonData = [param dataUsingEncoding:NSUTF8StringEncoding];
            if (jsonData) {
                id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:nil];
                if ([jsonObject isKindOfClass:[NSDictionary class]]) {
                    return jsonObject;
                }
            }
        }
    } else if (modelClass == [NSNumber class]) {
        if ([param isKindOfClass:[NSNumber class]]) {
            return param;
        } else if ([param isKindOfClass:[NSString class]]) {
            return @([param doubleValue]);
        }
    } else if (modelClass == [NSArray class]) {
        if ([param isKindOfClass:[NSArray class]]) {
            return param;
        } else if ([param isKindOfClass:[NSString class]]) {
            NSData *jsonData = [param dataUsingEncoding:NSUTF8StringEncoding];
            if (jsonData) {
                id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:nil];
                if ([jsonObject isKindOfClass:[NSArray class]]) {
                    return jsonObject;
                }
            }
        }
    } else {
        if ([param isKindOfClass:[NSString class]]) {
            NSData *jsonData = [param dataUsingEncoding:NSUTF8StringEncoding];
            if (jsonData) {
                id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:nil];
                if ([jsonObject isKindOfClass:[NSDictionary class]]) {
                    return [modelClass yy_modelWithJSON:jsonObject];
                }
            }
        }
    }
    return nil;
}

#pragma mark - Private Methods

- (void)scanModuleMethods:(Class)moduleClass moduleName:(NSString *)moduleName {
    NSMutableDictionary<NSString *, NSString *> *methods = [NSMutableDictionary dictionary];
    
    // 获取所有方法
    unsigned int methodCount;
    Method *methodList = class_copyMethodList(moduleClass, &methodCount);
    
    for (unsigned int i = 0; i < methodCount; i++) {
        Method method = methodList[i];
        SEL selector = method_getName(method);
        NSString *methodName = NSStringFromSelector(selector);
        
        // 检查是否是导出的方法（以jsapi_开头的方法）
        if ([methodName hasPrefix:@"jsapi_"]) {
            NSString *exportedMethodName = [methodName substringFromIndex:6]; // 去掉"jsapi_"前缀
            
            // 提取方法的基础名称（去掉参数部分）
            NSArray *components = [exportedMethodName componentsSeparatedByString:@":"];
            NSString *baseMethodName = components[0];
            
            methods[baseMethodName] = methodName;
            YYBMacLogInfo(kTag, @"Found exported method: %@ -> %@", baseMethodName, methodName);
        }
    }
    
    free(methodList);
    
    if (methods.count > 0) {
        self.moduleMethods[moduleName] = methods;
    }
}

@end

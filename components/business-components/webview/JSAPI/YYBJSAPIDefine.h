//
//  YYBJSAPIDefine.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/16.
//

#ifndef YYBJSAPIDefine_h
#define YYBJSAPIDefine_h

static NSString *const kJSAPIErrorDomain = @"YYBJSAPIErrorDomain";

typedef NS_ENUM(NSInteger, ResponseCode) {
    // 通用
    ResponseCodeSuccess = 0,
    ResponseCodeError = -1,
    ResponseCodeTimeout = -2,
    ResponseCodeCancel = -3,
    
    // JSB错误
    // TODO：halehuang后续需要接入到JSB，提前接入会引起问题
    ResponseCodeMethodObjectError = -10022, // 桥object不存在
    ResponseCodeMethodNameError = -10023,   // 桥method不存在
    ResponseCodeMethodParamError = -10024,  // 桥调用参数异常
};


#endif /* YYBJSAPIDefine_h */

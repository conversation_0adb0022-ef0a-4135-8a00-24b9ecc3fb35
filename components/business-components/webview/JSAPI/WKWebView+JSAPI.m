//
//  WKWebView+JSAPI.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/15.
//

#import "WKWebView+JSAPI.h"
#import <objc/runtime.h>

static char kWebViewConfigKey;

@implementation WKWebView (JSAPI)

- (void)setConfig:(YYBWKWebViewConfig *)config {
    objc_setAssociatedObject(self, &kWebViewConfigKey, config, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (YYBWKWebViewConfig *)config {
    return objc_getAssociatedObject(self, &kWebViewConfigKey);
}

@end

//
//  YYBJSAPIModule.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/7.
//

#import <Foundation/Foundation.h>
#import "YYBJSAPIModuleProtocol.h"

typedef NSString *YYBJSAPIEventName;

NS_ASSUME_NONNULL_BEGIN

@class WKWebView;

/**
 * JSAPI模块基类
 * 所有JSAPI模块都应该继承自这个类
 */
@interface YYBJSAPIModule : NSObject <YYBJSAPIModuleProtocol>

/**
 * 关联的WebView实例
 */
@property (nonatomic, assign) WKWebView *webView;

- (void)addListener:(YYBJSAPIEventName)eventName callbackId:(NSString *)callbackId;
- (void)removeListenerWithCallbackId:(NSString *)removedCallbackId;
- (void)triggerListener:(YYBJSAPIEventName)eventName data:(nullable id)data error:(nullable NSError *)error;

@end

NS_ASSUME_NONNULL_END

#import <Foundation/Foundation.h>
#import "YYBJSAPIManager.h"
#import "YYBJSAPIDefine.h"

/**
 * 导出模块宏
 * 使用方式：YYB_EXPORT_MODULE(ModuleName)
 * 会自动注册模块到YYBJSAPIManager中
 */
#define YYB_EXPORT_MODULE(name) \
+ (NSString *)moduleName { \
    return @#name; \
} \
+ (void)load { \
    static dispatch_once_t onceToken; \
    dispatch_once(&onceToken, ^{ \
        [YYBJSAPIManager registerModuleClass:[self class]]; \
    }); \
}

/**
 * 导出方法宏
 * 使用方式：YYB_EXPORT_METHOD(methodName:params:callbackId:)
 *
 * 注意：
 * 1. 方法名必须以jsapi_开头
 * 2. 方法签名应该是：(id)jsapi_methodName:(NSArray *)params callbackId:(NSString *)callbackId
 * 3. 参数params是JavaScript传递的参数数组
 * 4. 参数callbackId是回调ID，用于向JavaScript返回结果
 */
#define YYB_EXPORT_METHOD(methodName) \
- (void)jsapi_##methodName:(NSArray *)params callbackId:(NSString *)callbackId

/**
 * 声明参数宏
 * 使用方式：YYB_DECLARE_PARAM(0, NSString, path);
 * 支持基本类型的自动转换
 */
#define YYB_DECLARE_PARAM(index, type, name) \
type *name = (params.count > index) ? [YYBJSAPIManager convertParam:params[index] modelClass:[type class]] : nil; \
NSAssert(name, @"Parameter name cannot be nil"); \
if (!name) { \
    NSError *error = [NSError errorWithDomain:@"YYBJSAPIErrorDomain" \
                                         code:ResponseCodeMethodParamError \
                                     userInfo:@{NSLocalizedDescriptionKey: @"param name error"}]; \
    YYB_SEND_ERROR(error); \
}

/**
 * 发送成功回调的宏
 * 使用方式：YYB_SEND_SUCCESS( result)
 */
#define YYB_SEND_SUCCESS(param) \
[[YYBJSAPIManager sharedManager] sendCallback:callbackId result:param error:nil webView:((YYBJSAPIModule *)self).webView]

/**
 * 发送错误回调的宏
 * 使用方式：YYB_SEND_ERROR(error)
 */
#define YYB_SEND_ERROR(error) \
[[YYBJSAPIManager sharedManager] sendCallback:callbackId result:nil error:error webView:((YYBJSAPIModule *)self).webView]

/**
 * 条件响应宏
 * 使用方式：YYB_SEND_RESULT_BY_CONDITION(condition, successData, aError)
 * 当条件为真时发送成功响应，否则发送错误响应
 */
#define YYB_SEND_RESULT_BY_CONDITION(condition, successData, aError) \
if (condition) { \
    YYB_SEND_SUCCESS(successData); \
} else { \
    NSError *error = aError; \
    if (aError == nil) { \
        error = [NSError errorWithDomain:kJSAPIErrorDomain code:ResponseCodeError userInfo:nil]; \
    } \
    YYB_SEND_ERROR(error); \
}

/**
 * 异步发送成功回调的宏
 * 使用方式：YYB_SEND_SUCCESS(callbackId, result)
 */
#define YYB_SEND_SUCCESS_ASYNC(callbackId, param) \
[[YYBJSAPIManager sharedManager] sendCallback:callbackId result:param error:nil webView:((YYBJSAPIModule *)self).webView]

/**
 * 异步发送错误回调的宏
 * 使用方式：YYB_SEND_ERROR(callbackId, error)
 */
#define YYB_SEND_ERROR_ASYNC(callbackId, error) \
[[YYBJSAPIManager sharedManager] sendCallback:callbackId result:nil error:error webView:((YYBJSAPIModule *)self).webView]


/**
 * 添加事件监听的宏
 */
#define YYB_ADD_EVENT_LISTENER(eventName) \
[self addListener:eventName callbackId:callbackId]

/**
 * 移除事件监听的宏
 */
#define YYB_REMOVE_EVENT_LISTENER(remvoedCallbackId) \
[self removeListenerWithCallbackId:remvoedCallbackId]

/**
 * 触发事件监听的宏
 */
#define YYB_TRIGGER_EVENT_LISTENER(eventName, aData) \
[self triggerListener:eventName data:aData error:nil]

/**
 * 触发事件监听的宏（失败情况下）
 */
#define YYB_TRIGGER_EVENT_FAIL_LISTENER(eventName, aError) \
[self triggerListener:eventName data:nil error:aError]


//
//  YYBWKWebViewConfig.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/8/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBWKWebViewConfig : NSObject

@property (nonatomic, strong, nullable) NSString *windowId; // 和前端约定的windowId，假如为nil则由系统生成。
@property (nonatomic, strong) NSString *process; // 进程名，见kProcessAppstore等常量。

- (instancetype)initWithProcess:(NSString *)process
                       windowId:(nullable NSString *)windowId NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END

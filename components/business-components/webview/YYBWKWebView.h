//
//  YYBWKWebView.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/7.
//

#import <Cocoa/Cocoa.h>
#import <WebKit/WebKit.h>
#import "YYBWKWebViewConfig.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 网页视图控件
 */
@interface YYBWKWebView : NSView

/**
 * 网页初始化
 *
 * @param config 网页配置
 * @param webViewClass 自定义WKWebView的子类，默认为WKWebView
 */
- (instancetype)initWithFrame:(CGRect)frame
                       config:(YYBWKWebViewConfig *)config
                 webViewClass:(Class)webViewClass NS_DESIGNATED_INITIALIZER;
- (instancetype)initWithFrame:(CGRect)frame
                       config:(YYBWKWebViewConfig *)config;
- (instancetype)initWithFrame:(CGRect)frame NS_UNAVAILABLE;
- (instancetype)initWithCoder:(NSCoder *)coder NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

/**
 * 加载请求
 *
 * @param request 请求
 */
- (void)loadRequest:(NSURLRequest *)request;

/**
 * 加载本地网页
 *
 * @param url 本地url
 */
- (void)loadFileURL:(NSURL *)url;

/**
 * 刷新网页
 */
- (void)reload;

/**
 * 清除所有cookie
 *
 */
+ (void)clearAllCookies;

/// 获取内部的WKWebView
@property (nonatomic, readonly) WKWebView *wkWebView;

@end

NS_ASSUME_NONNULL_END

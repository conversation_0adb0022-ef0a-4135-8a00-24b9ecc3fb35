//
//  YYBWebViewManager.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/22.
//

#import <Foundation/Foundation.h>
@class YYBWKWebView;

NS_ASSUME_NONNULL_BEGIN

@interface YYBWebViewManager : NSObject

+ (instancetype)sharedManager;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

/// 添加或更新 webView
- (void)setWebView:(YYBWKWebView *)webView forWindowId:(NSString *)windowId;
/// 获取 webView
- (nullable YYBWKWebView *)webViewForWindowId:(NSString *)windowId;
/// 移除 webView
- (void)removeWebViewForWindowId:(NSString *)windowId;

@end

NS_ASSUME_NONNULL_END 

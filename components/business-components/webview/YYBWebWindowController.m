//
//  YYBWebWindowController.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/18.
//

#import "YYBWebWindowController.h"
#import "YYBWKWebView.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif

static NSString *const kTag = @"YYBWebWindowController";
static CGFloat const kDefaultWindowWidth = 800;
static CGFloat const kDefaultWindowHeight = 600;
static CGFloat const kDefaultWindowX = 100;
static CGFloat const kDefaultWindowY = 100;

@interface YYBWebWindowController ()

@property (nonatomic, strong) YYBWKWebView *yybWebView;

@end

@implementation YYBWebWindowController

- (instancetype)initWithWebViewObject:(JSWebViewObject *)object
                         parentWindow:(nullable NSWindow *)parentWindow
{
    // 1. 计算窗口大小和位置
    CGFloat width = object.width ? object.width.floatValue : kDefaultWindowWidth;
    CGFloat height = object.height ? object.height.floatValue : kDefaultWindowHeight;
    CGFloat x = kDefaultWindowX, y = kDefaultWindowY;
    NSScreen *screen = [NSScreen mainScreen];
    NSRect screenFrame = screen ? screen.visibleFrame : NSMakeRect(0, 0, 1440, 900);

    if (object.showPosition == IOpenWebviewShowPositionParentCenter && parentWindow) {
        x = parentWindow.frame.origin.x + (parentWindow.frame.size.width - width) / 2.0;
        y = parentWindow.frame.origin.y + (parentWindow.frame.size.height - height) / 2.0;
    } else if (object.showPosition == IOpenWebviewShowPositionScreenCenter) {
        x = screenFrame.origin.x + (screenFrame.size.width - width) / 2.0;
        y = screenFrame.origin.y + (screenFrame.size.height - height) / 2.0;
    } else {
        x = object.x ? object.x.floatValue : x;
        y = object.y ? object.y.floatValue : y;
    }

    // 2. 计算窗口样式
    NSWindowStyleMask styleMask = NSWindowStyleMaskTitled | NSWindowStyleMaskClosable | NSWindowStyleMaskMiniaturizable;
    if (object.resizable) {
        styleMask |= NSWindowStyleMaskResizable;
    }
    if (object.frameless) {
        styleMask = NSWindowStyleMaskBorderless;
    }

    // 3. 创建窗口
    NSWindow *window = [[NSWindow alloc] initWithContentRect:NSMakeRect(x, y, width, height)
                                                   styleMask:styleMask
                                                     backing:NSBackingStoreBuffered
                                                       defer:NO];
    if (object.displayName.length > 0) {
        window.title = object.displayName;
    }
    
    window.minSize = NSMakeSize(object.minWidth ? object.minWidth.floatValue : window.minSize.width, object.minHeight ? object.minHeight.floatValue : window.minSize.height);
    window.maxSize = NSMakeSize(object.maxWidth ? object.maxWidth.floatValue : window.maxSize.width, object.maxHeight ? object.maxHeight.floatValue : window.maxSize.height);

    // 4. 创建 WebView
    YYBWKWebViewConfig *config = [[YYBWKWebViewConfig alloc] initWithProcess:object.process windowId:object.winId];
    YYBWKWebView *yybWebView = [[YYBWKWebView alloc] initWithFrame:NSMakeRect(0, 0, width, height)
                                                          config:config
                                                     webViewClass:[WKWebView class]];
    yybWebView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    window.contentView = yybWebView;

    // 5. 加载 URL
    if (object.url.length > 0) {
        NSURL *url = [NSURL URLWithString:object.url];
        if (url) {
            NSURLRequest *request = [NSURLRequest requestWithURL:url];
            [yybWebView loadRequest:request];
        }
    }

    self = [super initWithWindow:window];
    if (self) {
        _yybWebView = yybWebView;
    }
    return self;
}

- (WKWebView *)webView {
    return self.yybWebView.wkWebView;
}

#pragma mark - Private Methods

- (NSWindowStyleMask)styleMaskForWindowFeatures:(WKWindowFeatures *)windowFeatures {
    NSWindowStyleMask styleMask = NSWindowStyleMaskTitled | NSWindowStyleMaskClosable | NSWindowStyleMaskMiniaturizable;
    if (windowFeatures.allowsResizing) {
        if (windowFeatures.allowsResizing.boolValue) {
            styleMask |= NSWindowStyleMaskResizable;
        }
    } else {
        styleMask |= NSWindowStyleMaskResizable;
    }
    
    return styleMask;
}

@end

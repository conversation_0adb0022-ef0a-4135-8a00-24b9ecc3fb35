//
//  YYBWebViewManager.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/22.
//

#import "YYBWebViewManager.h"
#import "YYBWKWebView.h"

@interface YYBWebViewManager ()
@property (nonatomic, strong) NSMapTable<NSString *, YYBWKWebView *> *webViewMap;
@end

@implementation YYBWebViewManager

+ (instancetype)sharedManager {
    static YYBWebViewManager *manager;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[self alloc] init];
    });
    return manager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _webViewMap = [NSMapTable strongToWeakObjectsMapTable];
    }
    return self;
}

- (void)setWebView:(YYBWKWebView *)webView forWindowId:(NSString *)windowId {
    if (windowId.length > 0 && webView) {
        [self.webViewMap setObject:webView forKey:windowId];
    }
}

- (nullable YYBWKWebView *)webViewForWindowId:(NSString *)windowId {
    if (windowId.length == 0) {
        return nil;
    }
    return [self.webViewMap objectForKey:windowId];
}

- (void)removeWebViewForWindowId:(NSString *)windowId {
    if (windowId.length > 0) {
        [self.webViewMap removeObjectForKey:windowId];
    }
}

@end 

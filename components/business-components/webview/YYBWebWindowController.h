//
//  YYBWebWindowController.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/7/18.
//

#import <Cocoa/Cocoa.h>
#import <WebKit/WebKit.h>
#import "JSWebViewObject.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBWebWindowController : NSWindowController

@property (nonatomic, readonly) WKWebView *webView;

/// 通过JSWebViewObject初始化窗口
/// @param object JSWebViewObject前端对象
- (instancetype)initWithWebViewObject:(JSWebViewObject *)object
                         parentWindow:(nullable NSWindow *)parentWindow;

@end

NS_ASSUME_NONNULL_END

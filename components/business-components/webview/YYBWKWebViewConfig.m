//
//  YYBWKWebViewConfig.m
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/8/4.
//

#import "YYBWKWebViewConfig.h"

@implementation YYBWKWebViewConfig

- (instancetype)initWithProcess:(NSString *)process
                       windowId:(nullable NSString *)windowId {
    if (self == [super init]) {
        self.process = process;
        self.windowId = windowId.length != 0 ? windowId : [[NSUUID UUID] UUIDString];
    }
    return self;
}

@end

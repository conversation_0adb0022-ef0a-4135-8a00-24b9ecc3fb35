//
//  YYBDefine.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/4.
//

#ifndef YYBDefine_h
#define YYBDefine_h

// 应用名称
static NSString *const YYBBundleName = @"腾讯应用宝";
static NSString *const YYBEngineBundleName = @"yyb_mac";

// Bundle ID
static NSString *const YYBMainBundleID = @"com.tencent.yybmac";
static NSString *const YYBEngineBundleID = @"com.tencent.yybmac.engine";
static NSString *const YYBPackageBundleID = @"com.tencent.yybmac.app";
static NSString *const YYBServiceBundleID = @"com.tencent.yybmac.yybService";

// Process Name
static NSString *const kProcessEngine = @"engine";
static NSString *const kProcessAppStore = @"appstore";
static NSString *const kProcessBoastcast = @"boastcast";
static NSString *const kProcessService = @"service";

// reshub resId
static NSString *const kVmsResId = @"VMS";

#endif /* YYBDefine_h */

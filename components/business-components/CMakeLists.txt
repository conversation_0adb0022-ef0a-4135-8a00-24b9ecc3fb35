cmake_minimum_required(VERSION 3.10)
project(yyb_business_components)

# 添加所有源文件
file(GLOB_RECURSE SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/system/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/system/*.mm"
    "${CMAKE_CURRENT_SOURCE_DIR}/webview/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/webview/*.mm"
    "${CMAKE_CURRENT_SOURCE_DIR}/directory/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/file/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/YYModel/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/ipc/*.mm"
    "${CMAKE_CURRENT_SOURCE_DIR}/DiagnosticLog/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/Masonry/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/SDWebImage/Core/*.m"
    "${CMAKE_CURRENT_SOURCE_DIR}/SDWebImage/Private/*.m"
)
add_library(yyb_business_components SHARED ${SOURCES})

# 设置安装路径
install(TARGETS yyb_business_components
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

set_target_properties(yyb_business_components PROPERTIES
    INSTALL_RPATH "@loader_path"
    BUILD_WITH_INSTALL_RPATH TRUE
    MACOSX_RPATH ON
)

target_link_libraries(yyb_business_components PRIVATE "-framework Foundation")
target_link_libraries(yyb_business_components PRIVATE "-framework Cocoa")
target_link_libraries(yyb_business_components PRIVATE "-framework WebKit")
target_link_libraries(yyb_business_components PRIVATE "-framework AppKit")
target_link_libraries(yyb_business_components PRIVATE "-framework SystemConfiguration")
target_link_libraries(yyb_business_components PRIVATE "-framework OpenGL")
target_link_libraries(yyb_business_components PRIVATE
        "-framework IOKit"
        "-framework QuartzCore"
        "-framework Accelerate"
)
target_link_libraries(yyb_business_components PRIVATE
    "-F${CMAKE_BINARY_DIR}/Frameworks"
    "-framework YYBMacFusionSDK"
    "-lsqlite3"
    YYBIPC
)

# 设置头文件包含路径
target_include_directories(yyb_business_components PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/directory
    ${CMAKE_CURRENT_SOURCE_DIR}/file
    ${CMAKE_CURRENT_SOURCE_DIR}/system
    ${CMAKE_CURRENT_SOURCE_DIR}/webview
    ${CMAKE_CURRENT_SOURCE_DIR}/webview/JSAPI
    ${CMAKE_CURRENT_SOURCE_DIR}/webview/JSAPI/model
    ${CMAKE_CURRENT_SOURCE_DIR}/YYModel
    ${CMAKE_CURRENT_SOURCE_DIR}/ipc
    ${CMAKE_CURRENT_SOURCE_DIR}/DiagnosticLog
    ${CMAKE_CURRENT_SOURCE_DIR}/Masonry
    ${CMAKE_CURRENT_SOURCE_DIR}/SDWebImage
    ${CMAKE_CURRENT_SOURCE_DIR}/SDWebImage/Core
    ${CMAKE_BINARY_DIR}/Frameworks/YYBMacFusionSDK.framework/Headers
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/SDWebImage/Private

)

target_link_directories(yyb_business_components PRIVATE
    ${CMAKE_BINARY_DIR}/Frameworks
)

target_compile_options(yyb_business_components PRIVATE -fobjc-arc)

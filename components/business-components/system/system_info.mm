#define GL_SILENCE_DEPRECATION
#include "system_info.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <sys/sysctl.h>
#include <sys/types.h>
#include <sys/statvfs.h>
#include <mach/mach.h>
#include <mach/vm_statistics.h>
#include <mach/mach_types.h>
#include <mach/mach_init.h>
#include <mach/mach_host.h>
#include <unistd.h>
#include <IOKit/IOKitLib.h>
#include <IOKit/ps/IOPSKeys.h>
#include <IOKit/ps/IOPowerSources.h>
#include <CoreFoundation/CoreFoundation.h>
#include <OpenGL/OpenGL.h>
#include <OpenGL/gl3.h>

#ifdef kIOMainPortDefault
#define YYB_IOKIT_PORT_DEFAULT kIOMainPortDefault
#else
#define YYB_IOKIT_PORT_DEFAULT kIOMasterPortDefault
#endif

SystemInfo& SystemInfo::getInstance() {
    static SystemInfo instance;
    return instance;
}

SystemInfo::CPUInfo SystemInfo::getCPUInfo() const {
    CPUInfo info = {"Unknown", 0, "Unknown", false};
    
    // 获取CPU品牌
    char brand[256];
    size_t size = sizeof(brand);
    if (sysctlbyname("machdep.cpu.brand_string", &brand, &size, NULL, 0) == 0) {
        info.brand = std::string(brand);
    }
    
    // 获取CPU核数
    int coreCount;
    size = sizeof(coreCount);
    if (sysctlbyname("hw.ncpu", &coreCount, &size, NULL, 0) == 0) {
        info.coreCount = coreCount;
    }
    
    // 获取CPU类型
    int cpuType;
    size = sizeof(cpuType);
    if (sysctlbyname("hw.cputype", &cpuType, &size, NULL, 0) == 0) {
        switch (cpuType) {
            case 16777228: // CPU_TYPE_ARM64
                // 对于Apple Silicon，尝试获取更具体的芯片信息
                if (info.brand.find("Apple") != std::string::npos) {
                    info.isAppleSilicon = true;
                    if (info.brand.find("M1") != std::string::npos) {
                        info.cpuType = "Apple M1";
                    } else if (info.brand.find("M2") != std::string::npos) {
                        info.cpuType = "Apple M2";
                    } else if (info.brand.find("M3") != std::string::npos) {
                        info.cpuType = "Apple M3";
                    } else if (info.brand.find("M4") != std::string::npos) {
                        info.cpuType = "Apple M4";
                    } else {
                        info.cpuType = "Apple Silicon";
                    }
                } else {
                    info.cpuType = "ARM64";
                    info.isAppleSilicon = false;
                }
                break;
            case 7: // CPU_TYPE_X86_64
                info.cpuType = "x86_64";
                info.isAppleSilicon = false;
                break;
            case 18: // CPU_TYPE_POWERPC64
                info.cpuType = "PowerPC64";
                info.isAppleSilicon = false;
                break;
            default:
                info.cpuType = "Unknown";
                info.isAppleSilicon = false;
                break;
        }
    }
    
    return info;
}

SystemInfo::GPUInfo SystemInfo::getGPUInfo() const {
    GPUInfo info = {"Unknown", "Unknown", false};
    io_iterator_t iter = 0;
    kern_return_t kr = IOServiceGetMatchingServices(
        YYB_IOKIT_PORT_DEFAULT,
        IOServiceMatching("IOPCIDevice"),
        &iter
    );
    bool found = false;
    if (kr == KERN_SUCCESS && iter != 0) {
        io_service_t device = IOIteratorNext(iter);
        if (device) {
            // 获取设备型号
            CFTypeRef modelData = IORegistryEntryCreateCFProperty(
                device,
                CFSTR("model"),
                kCFAllocatorDefault,
                0
            );
            if (modelData) {
                if (CFGetTypeID(modelData) == CFDataGetTypeID()) {
                    char model[128] = {0};
                    CFDataGetBytes((CFDataRef)modelData, CFRangeMake(0, CFDataGetLength((CFDataRef)modelData)), (UInt8*)model);
                    info.model = model;
                } else if (CFGetTypeID(modelData) == CFStringGetTypeID()) {
                    char model[128];
                    CFStringGetCString((CFStringRef)modelData, model, sizeof(model), kCFStringEncodingUTF8);
                    info.model = model;
                }
                CFRelease(modelData);
            }
            // 获取驱动版本
            CFTypeRef driverVer = IORegistryEntryCreateCFProperty(
                device,
                CFSTR("IOAccelRevision"),
                kCFAllocatorDefault,
                0
            );
            if (driverVer) {
                if (CFGetTypeID(driverVer) == CFStringGetTypeID()) {
                    char ver[64];
                    CFStringGetCString((CFStringRef)driverVer, ver, sizeof(ver), kCFStringEncodingUTF8);
                    info.driver = ver;
                } else if (CFGetTypeID(driverVer) == CFDataGetTypeID()) {
                    char ver[64] = {0};
                    CFDataGetBytes((CFDataRef)driverVer, CFRangeMake(0, CFDataGetLength((CFDataRef)driverVer)), (UInt8*)ver);
                    info.driver = ver;
                }
                CFRelease(driverVer);
            }
            info.discrete = isDiscreteGPU(device);
            IOObjectRelease(device);
            found = (info.model != "Unknown");
        }
        IOObjectRelease(iter);
    }
    // 如果没找到，尝试IOAccelerator/AGXAccelerator
    if (!found) {
        const char* acceleratorClasses[] = {"IOAccelerator", "AGXAccelerator"};
        for (int i = 0; i < 2 && !found; ++i) {
            iter = 0;
            kr = IOServiceGetMatchingServices(
                YYB_IOKIT_PORT_DEFAULT,
                IOServiceMatching(acceleratorClasses[i]),
                &iter
            );
            if (kr == KERN_SUCCESS && iter != 0) {
                io_service_t device = IOIteratorNext(iter);
                if (device) {
                    // 型号
                    CFTypeRef modelData = IORegistryEntryCreateCFProperty(
                        device,
                        CFSTR("model"),
                        kCFAllocatorDefault,
                        0
                    );
                    if (modelData) {
                        if (CFGetTypeID(modelData) == CFDataGetTypeID()) {
                            char model[128] = {0};
                            CFDataGetBytes((CFDataRef)modelData, CFRangeMake(0, CFDataGetLength((CFDataRef)modelData)), (UInt8*)model);
                            info.model = model;
                        } else if (CFGetTypeID(modelData) == CFStringGetTypeID()) {
                            char model[128];
                            CFStringGetCString((CFStringRef)modelData, model, sizeof(model), kCFStringEncodingUTF8);
                            info.model = model;
                        }
                        CFRelease(modelData);
                    } else {
                        // Apple Silicon 通常没有model字段，可以用IOClass
                        CFTypeRef ioClass = IORegistryEntryCreateCFProperty(
                            device,
                            CFSTR("IOClass"),
                            kCFAllocatorDefault,
                            0
                        );
                        if (ioClass && CFGetTypeID(ioClass) == CFStringGetTypeID()) {
                            char className[128];
                            CFStringGetCString((CFStringRef)ioClass, className, sizeof(className), kCFStringEncodingUTF8);
                            info.model = className;
                        }
                        if (ioClass) CFRelease(ioClass);
                    }
                    // 驱动版本
                    CFTypeRef bundleId = IORegistryEntryCreateCFProperty(
                        device,
                        CFSTR("CFBundleIdentifier"),
                        kCFAllocatorDefault,
                        0
                    );
                    if (bundleId && CFGetTypeID(bundleId) == CFStringGetTypeID()) {
                        char ver[128];
                        CFStringGetCString((CFStringRef)bundleId, ver, sizeof(ver), kCFStringEncodingUTF8);
                        info.driver = ver;
                        CFRelease(bundleId);
                    }
                    // Apple Silicon GPU 一般为集成显卡
                    info.discrete = false;
                    IOObjectRelease(device);
                    found = (info.model != "Unknown");
                }
                IOObjectRelease(iter);
            }
        }
    }
    return info;
}

bool SystemInfo::isDiscreteGPU(io_service_t device) const {
    // 方法1：检查IOName是否包含"AMD"或"NVIDIA"
    CFStringRef nameRef = (CFStringRef)IORegistryEntryCreateCFProperty(
        device,
        CFSTR("IOName"),
        kCFAllocatorDefault,
        0
    );
    if (nameRef) {
        char name[64];
        CFStringGetCString(nameRef, name, sizeof(name), kCFStringEncodingUTF8);
        CFRelease(nameRef);
        if (strstr(name, "AMD") || strstr(name, "NVIDIA")) {
            return true;
        }
    }

    // 方法2：检查连接类型
    CFStringRef connectType = (CFStringRef)IORegistryEntryCreateCFProperty(
        device,
        CFSTR("display-connect"),
        kCFAllocatorDefault,
        0
    );
    if (connectType) {
        return true;
    }
    return false;
}

SystemInfo::MemoryInfo SystemInfo::getMemoryInfo() const {
    MemoryInfo info = {0, 0, 0};
    vm_size_t page_size;
    vm_statistics64_data_t vm_stats;
    mach_msg_type_number_t info_count = sizeof(vm_stats) / sizeof(integer_t);
    mach_port_t mach_port = mach_host_self();
    
    host_page_size(mach_port, &page_size);
    
    if (host_statistics64(mach_port, HOST_VM_INFO64, (host_info64_t)&vm_stats, &info_count) == KERN_SUCCESS) {
        info.total = (uint64_t)sysconf(_SC_PHYS_PAGES) * (uint64_t)sysconf(_SC_PAGE_SIZE);
        info.free = vm_stats.free_count * page_size;
        info.used = info.total - info.free;
    }
    
    return info;
}

SystemInfo::DiskInfo SystemInfo::getDiskInfo() const {
    DiskInfo info = {0, 0, 0};
    struct statvfs stat;
    if (statvfs("/", &stat) == 0) {
        info.total = (uint64_t)stat.f_blocks * (uint64_t)stat.f_frsize;
        info.free = (uint64_t)stat.f_bfree * (uint64_t)stat.f_frsize;
        info.used = info.total - info.free;
    }
    return info;
}

int SystemInfo::getProcessorCount() const {
    int count;
    size_t size = sizeof(count);
    if (sysctlbyname("hw.ncpu", &count, &size, NULL, 0) == 0) {
        return count;
    }
    return 0;
}

std::string SystemInfo::getHostname() const {
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        return std::string(hostname);
    }
    return "Unknown";
}

std::string SystemInfo::formatBytes(uint64_t bytes) const {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024 && unit_index < 4) {
        size /= 1024;
        unit_index++;
    }
    
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return ss.str();
}


std::string SystemInfo::getOSVersion() const {
    NSString* version = [[NSProcessInfo processInfo] operatingSystemVersionString];
    return [version UTF8String];
}

time_t SystemInfo::getBootTimeStamp() const {
    struct timeval bootTime;
    size_t len = sizeof(bootTime);
    int ret = sysctlbyname("kern.boottime", &bootTime, &len, nullptr, 0);

    if (ret == 0) {
        return bootTime.tv_sec;
    }
    return time(nullptr);
}

std::string SystemInfo::getBootTime() const {
    time_t seconds = getBootTimeStamp();
    if (seconds) {
        struct tm *timeInfo = localtime(&seconds);
        char buffer[80];
        strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeInfo);
        return std::string(buffer);
    }
    return "Unknown";
}



std::string SystemInfo::getBootUptime() const {
    time_t bootTime = getBootTimeStamp();
    time_t now = time(nullptr);
    
    long long seconds = static_cast<long long>(difftime(now, bootTime));
    if (seconds <= 0) return "Unknown";

    long long days = seconds / 86400;
    seconds %= 86400;
    long hours = seconds / 3600;
    seconds %= 3600;
    long minutes = seconds / 60;
    seconds %= 60;

    char buffer[128];
    snprintf(buffer, sizeof(buffer), "%lld天%ld小时%ld分钟%lld秒",
             days, hours, minutes, seconds);
    return std::string(buffer);
}
 
std::vector<SystemInfo::BatteryInfo> SystemInfo::getBatteryInfo() const {
    std::vector<BatteryInfo> batteryInfos;
    CFTypeRef blob = IOPSCopyPowerSourcesInfo();
    if (!blob) {
        return batteryInfos;
    }
    CFArrayRef powerSourcesList = IOPSCopyPowerSourcesList(blob);
    if (!powerSourcesList) {
        CFRelease(blob);
        return batteryInfos;
    }
    CFIndex numOfPowerSources = CFArrayGetCount(powerSourcesList);

    for (CFIndex i = 0; i < numOfPowerSources; i++) {
        BatteryInfo info = {"", 0, false, false, false};
        CFTypeRef powerSource = CFArrayGetValueAtIndex(powerSourcesList, i);
        CFDictionaryRef powerSourceDict = IOPSGetPowerSourceDescription(blob, powerSource);

        // 获取电池名称
        CFStringRef name = (CFStringRef)CFDictionaryGetValue(powerSourceDict, CFSTR(kIOPSNameKey));
        info.name = name ? CFStringGetCStringPtr(name, kCFStringEncodingUTF8) : "Unknown";

        // 获取电量
        int currentCapacity = 0;
        CFNumberRef currentCapacityNum = (CFNumberRef)CFDictionaryGetValue(powerSourceDict, CFSTR(kIOPSCurrentCapacityKey));
        if (currentCapacityNum) {
            CFNumberGetValue(currentCapacityNum, kCFNumberIntType, &currentCapacity);
        }
        info.chargeLevel = currentCapacity;

        // 获取电池状态
        CFBooleanRef isCharging = (CFBooleanRef)CFDictionaryGetValue(powerSourceDict, CFSTR(kIOPSIsChargingKey));
        
        info.isCharging = isCharging ? CFBooleanGetValue(isCharging) : false;
        if (@available(macOS 12.0, *)) {
            info.isLowPower = [NSProcessInfo processInfo].lowPowerModeEnabled;
        } else {
            info.isLowPower = false;
        }

        // 检查交流电
        CFStringRef powerSourceType = (CFStringRef)CFDictionaryGetValue(powerSourceDict, CFSTR(kIOPSTypeKey));
        info.isPoweredByAC = powerSourceType && CFEqual(powerSourceType, CFSTR(kIOPSACPowerValue));
        batteryInfos.push_back(info);
    }
    
    CFRelease(powerSourcesList);
    CFRelease(blob);
    return batteryInfos;
}

SystemInfo::MotherboardInfo SystemInfo::getMotherboardInfo() const {
    MotherboardInfo info = {"Unknown", "Unknown", "Unknown", "Unknown"};
    
    // 获取主板制造商、型号、序列号、版本
    io_registry_entry_t platformExpert = IOServiceGetMatchingService(YYB_IOKIT_PORT_DEFAULT, IOServiceMatching("IOPlatformExpertDevice"));
    if (platformExpert) {
        // 制造商
        CFTypeRef manufacturer = IORegistryEntryCreateCFProperty(platformExpert, CFSTR("manufacturer"), kCFAllocatorDefault, 0);
        if (manufacturer) {
            if (CFGetTypeID(manufacturer) == CFStringGetTypeID()) {
                char buffer[128];
                CFStringGetCString((CFStringRef)manufacturer, buffer, sizeof(buffer), kCFStringEncodingUTF8);
                info.manufacturer = buffer;
            }
            CFRelease(manufacturer);
        }
        // 型号
        CFTypeRef model = IORegistryEntryCreateCFProperty(platformExpert, CFSTR("model"), kCFAllocatorDefault, 0);
        if (model) {
            if (CFGetTypeID(model) == CFDataGetTypeID()) {
                char buffer[128] = {0};
                CFDataGetBytes((CFDataRef)model, CFRangeMake(0, CFDataGetLength((CFDataRef)model)), (UInt8*)buffer);
                info.model = buffer;
            } else if (CFGetTypeID(model) == CFStringGetTypeID()) {
                char buffer[128];
                CFStringGetCString((CFStringRef)model, buffer, sizeof(buffer), kCFStringEncodingUTF8);
                info.model = buffer;
            }
            CFRelease(model);
        }
        // 序列号
        CFTypeRef serial = IORegistryEntryCreateCFProperty(platformExpert, CFSTR("IOPlatformSerialNumber"), kCFAllocatorDefault, 0);
        if (serial) {
            if (CFGetTypeID(serial) == CFStringGetTypeID()) {
                char buffer[128];
                CFStringGetCString((CFStringRef)serial, buffer, sizeof(buffer), kCFStringEncodingUTF8);
                info.serialNumber = buffer;
            }
            CFRelease(serial);
        }
        // 版本
        CFTypeRef version = IORegistryEntryCreateCFProperty(platformExpert, CFSTR("version"), kCFAllocatorDefault, 0);
        if (version) {
            if (CFGetTypeID(version) == CFStringGetTypeID()) {
                char buffer[128];
                CFStringGetCString((CFStringRef)version, buffer, sizeof(buffer), kCFStringEncodingUTF8);
                info.version = buffer;
            }
            CFRelease(version);
        }
        IOObjectRelease(platformExpert);
    }
    // 如果manufacturer依然为Unknown，默认设为Apple Inc.
    if (info.manufacturer == "Unknown") {
        info.manufacturer = "Apple Inc.";
    }
    return info;
}

void SystemInfo::printSystemInfo() const {
    std::cout << "=== System Information ===" << std::endl;
    
    // OS信息
    std::cout << "OS: " << getOSVersion() << std::endl;
    
    // CPU信息
    CPUInfo cpu = getCPUInfo();
    std::cout << "CPU: " << cpu.brand << " (" << cpu.cpuType << "), " << cpu.coreCount << " cores";
    std::cout << (cpu.isAppleSilicon ? " [Apple Silicon]" : "") << std::endl;
    
    // 处理器数量
    std::cout << "Processor Count: " << getProcessorCount() << std::endl;
    
    // GPU信息
    GPUInfo gpu = getGPUInfo();
    std::cout << "GPU Model: " << gpu.model << std::endl;
    std::cout << "GPU Driver Version: " << gpu.driver << std::endl;
    std::cout << "GPU Discrete: " << (gpu.discrete ? "ture" : "false") << std::endl;
    
    // 内存信息
    MemoryInfo memory = getMemoryInfo();
    std::cout << "Memory Total: " << formatBytes(memory.total) << std::endl;
    std::cout << "Memory Used: " << formatBytes(memory.used) << std::endl;
    std::cout << "Memory Free: " << formatBytes(memory.free) << std::endl;
    
    // 磁盘信息
    DiskInfo disk = getDiskInfo();
    std::cout << "Disk Total: " << formatBytes(disk.total) << std::endl;
    std::cout << "Disk Used: " << formatBytes(disk.used) << std::endl;
    std::cout << "Disk Free: " << formatBytes(disk.free) << std::endl;
    
    // 启动时间
    std::cout << "Boot Time: " << getBootTime() << std::endl;
    std::cout << "Boot Uptime: " << getBootUptime() << std::endl;
    std::cout << "Boot Time UTC(ms): " << getBootTimeUtcMsec() << std::endl;
    
    // 电池信息
    std::vector<BatteryInfo> batteries = getBatteryInfo();
    for (const auto& battery : batteries) {
        std::cout << "Power Source " << battery.name << ":" << std::endl;
        std::cout << "  Charge Level: " << battery.chargeLevel << "%" << std::endl;
        std::cout << "  Is Charging: " << (battery.isCharging ? "Yes" : "No") << std::endl;
        std::cout << "  Is Powered by AC: " << (battery.isPoweredByAC ? "Yes" : "No") << std::endl;
        std::cout << "  Is Low Power Mode: " << (battery.isLowPower ? "Yes" : "No") << std::endl;
    }
    
    // 主板信息
    MotherboardInfo mb = getMotherboardInfo();
    std::cout << "Motherboard Manufacturer: " << mb.manufacturer << std::endl;
    std::cout << "Motherboard Model: " << mb.model << std::endl;
    std::cout << "Motherboard Serial Number: " << mb.serialNumber << std::endl;
    std::cout << "Motherboard Version: " << mb.version << std::endl;
    
    // 主机名
    std::cout << "Hostname: " << getHostname() << std::endl;

    // 显卡详细信息
    auto graphicsInfos = getGraphicsInfos();
    std::cout << "Primary Graphics:" << std::endl;
    std::cout << "  Model: " << graphicsInfos.primaryGraphics.model << std::endl;
    std::cout << "  Driver Version: " << graphicsInfos.primaryGraphics.driverVersion << std::endl;
    std::cout << "  OpenGL Version: " << graphicsInfos.primaryGraphics.openglVersion << std::endl;
    std::cout << "  Vendor: " << graphicsInfos.primaryGraphics.vendor << std::endl;
    std::cout << "  Device ID: " << graphicsInfos.primaryGraphics.deviceId << std::endl;
    std::cout << "  Driver Date: " << graphicsInfos.primaryGraphics.driverDate << std::endl;
    std::cout << "  Vendor ID: " << graphicsInfos.primaryGraphics.vendorId << std::endl;
    if (graphicsInfos.viceGraphics) {
        std::cout << "Vice Graphics:" << std::endl;
        std::cout << "  Model: " << graphicsInfos.viceGraphics->model << std::endl;
        std::cout << "  Driver Version: " << graphicsInfos.viceGraphics->driverVersion << std::endl;
        std::cout << "  OpenGL Version: " << graphicsInfos.viceGraphics->openglVersion << std::endl;
        std::cout << "  Vendor: " << graphicsInfos.viceGraphics->vendor << std::endl;
        std::cout << "  Device ID: " << graphicsInfos.viceGraphics->deviceId << std::endl;
        std::cout << "  Driver Date: " << graphicsInfos.viceGraphics->driverDate << std::endl;
        std::cout << "  Vendor ID: " << graphicsInfos.viceGraphics->vendorId << std::endl;
    }
}

namespace {
SystemInfo::GraphicsInfoBase getGraphicsInfoFromDevice(io_service_t device) {
    SystemInfo::GraphicsInfoBase info;
    info.driverVersion = "Unknown";
    info.model = "Unknown";
    info.openglVersion = "Unknown";
    info.vendor = "Unknown";
    info.deviceId = "Unknown";
    info.driverDate = "Unknown";
    info.vendorId = "Unknown";
    // 型号
    CFTypeRef modelData = IORegistryEntryCreateCFProperty(device, CFSTR("model"), kCFAllocatorDefault, 0);
    if (modelData) {
        if (CFGetTypeID(modelData) == CFDataGetTypeID()) {
            char model[128] = {0};
            CFDataGetBytes((CFDataRef)modelData, CFRangeMake(0, CFDataGetLength((CFDataRef)modelData)), (UInt8*)model);
            info.model = model;
        } else if (CFGetTypeID(modelData) == CFStringGetTypeID()) {
            char model[128];
            CFStringGetCString((CFStringRef)modelData, model, sizeof(model), kCFStringEncodingUTF8);
            info.model = model;
        }
        CFRelease(modelData);
    }
    // 驱动版本
    CFTypeRef driverVer = IORegistryEntryCreateCFProperty(device, CFSTR("IOAccelRevision"), kCFAllocatorDefault, 0);
    if (driverVer) {
        if (CFGetTypeID(driverVer) == CFStringGetTypeID()) {
            char ver[64];
            CFStringGetCString((CFStringRef)driverVer, ver, sizeof(ver), kCFStringEncodingUTF8);
            info.driverVersion = ver;
        } else if (CFGetTypeID(driverVer) == CFDataGetTypeID()) {
            char ver[64] = {0};
            CFDataGetBytes((CFDataRef)driverVer, CFRangeMake(0, CFDataGetLength((CFDataRef)driverVer)), (UInt8*)ver);
            info.driverVersion = ver;
        }
        CFRelease(driverVer);
    }
    // vendor-id
    CFTypeRef vendorId = IORegistryEntryCreateCFProperty(device, CFSTR("vendor-id"), kCFAllocatorDefault, 0);
    if (vendorId && CFGetTypeID(vendorId) == CFDataGetTypeID()) {
        uint32_t vid = 0;
        CFDataGetBytes((CFDataRef)vendorId, CFRangeMake(0, sizeof(uint32_t)), (UInt8*)&vid);
        char buf[16];
        snprintf(buf, sizeof(buf), "0x%04x", vid);
        info.vendorId = buf;
        // 通过常见厂商ID推断vendor
        if (vid == 0x1002) info.vendor = "AMD";
        else if (vid == 0x10de) info.vendor = "NVIDIA";
        else if (vid == 0x8086) info.vendor = "Intel";
        else if (vid == 0x106b) info.vendor = "Apple";
    }
    if (vendorId) CFRelease(vendorId);
    // device-id
    CFTypeRef deviceId = IORegistryEntryCreateCFProperty(device, CFSTR("device-id"), kCFAllocatorDefault, 0);
    if (deviceId && CFGetTypeID(deviceId) == CFDataGetTypeID()) {
        uint32_t did = 0;
        CFDataGetBytes((CFDataRef)deviceId, CFRangeMake(0, sizeof(uint32_t)), (UInt8*)&did);
        char buf[16];
        snprintf(buf, sizeof(buf), "0x%04x", did);
        info.deviceId = buf;
    }
    if (deviceId) CFRelease(deviceId);
    // 驱动日期（macOS一般无，留空）
    // OpenGL信息（需上下文）
    return info;
}
} // 匿名命名空间
SystemInfo::GraphicsInfos SystemInfo::getGraphicsInfos() const {
    GraphicsInfos infos;
    infos.viceGraphics = nullptr;
    std::vector<GraphicsInfoBase> gpus;
    io_iterator_t iter = 0;
    kern_return_t kr = IOServiceGetMatchingServices(
        YYB_IOKIT_PORT_DEFAULT,
        IOServiceMatching("IOPCIDevice"),
        &iter
    );
    if (kr == KERN_SUCCESS && iter != 0) {
        io_service_t device;
        while ((device = IOIteratorNext(iter))) {
            gpus.push_back(getGraphicsInfoFromDevice(device));
            IOObjectRelease(device);
        }
        IOObjectRelease(iter);
    }
    // Apple Silicon补充
    if (gpus.empty()) {
        const char* acceleratorClasses[] = {"IOAccelerator", "AGXAccelerator"};
        for (int i = 0; i < 2; ++i) {
            iter = 0;
            kr = IOServiceGetMatchingServices(
                YYB_IOKIT_PORT_DEFAULT,
                IOServiceMatching(acceleratorClasses[i]),
                &iter
            );
            if (kr == KERN_SUCCESS && iter != 0) {
                io_service_t device;
                while ((device = IOIteratorNext(iter))) {
                    gpus.push_back(getGraphicsInfoFromDevice(device));
                    IOObjectRelease(device);
                }
                IOObjectRelease(iter);
            }
            if (!gpus.empty()) break;
        }
    }
    // OpenGL信息（仅主显卡）
    CGLPixelFormatAttribute attribs[] = {kCGLPFAAccelerated, (CGLPixelFormatAttribute)0};
    CGLPixelFormatObj pix;
    GLint npix;
    CGLChoosePixelFormat(attribs, &pix, &npix);
    if (pix) {
        CGLContextObj ctx;
        if (CGLCreateContext(pix, NULL, &ctx) == kCGLNoError) {
            CGLSetCurrentContext(ctx);
            const GLubyte* glver = glGetString(GL_VERSION);
            const GLubyte* glvendor = glGetString(GL_VENDOR);
            if (!gpus.empty()) {
                if (glver) gpus[0].openglVersion = (const char*)glver;
                if (glvendor) gpus[0].vendor = (const char*)glvendor;
            }
            CGLSetCurrentContext(NULL);
            CGLDestroyContext(ctx);
        }
        CGLReleasePixelFormat(pix);
    }
    if (!gpus.empty()) {
        infos.primaryGraphics = gpus[0];
        if (gpus.size() > 1) {
            const auto& a = gpus[0];
            const auto& b = gpus[1];
            if (a.driverVersion != b.driverVersion ||
                a.model != b.model ||
                a.vendor != b.vendor ||
                a.deviceId != b.deviceId ||
                a.driverDate != b.driverDate ||
                a.vendorId != b.vendorId) {
                infos.viceGraphics = new SystemInfo::GraphicsInfoBase(gpus[1]);
            }
            // 否则保持nullptr
        }
    }
    return infos;
}

int64_t SystemInfo::getBootTimeUtcMsec() const {
    time_t bootSec = getBootTimeStamp();
    struct tm gmt_tm;
    gmtime_r(&bootSec, &gmt_tm);
    int64_t bootMsec = static_cast<int64_t>(bootSec) * 1000;
    return bootMsec;
}

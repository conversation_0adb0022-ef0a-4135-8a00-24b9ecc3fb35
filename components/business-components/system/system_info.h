#ifndef SYSTEM_INFO_HPP
#define SYSTEM_INFO_HPP

#include <string>
#include <cstdint>
#include <memory>
#include <vector>
#import <Foundation/Foundation.h>
#include <optional>

class SystemInfo {
public:
    // 单例模式获取实例
    static SystemInfo& getInstance();

    // 获取CPU信息
    struct CPUInfo {
        std::string brand;
        int coreCount;
        std::string cpuType;
        bool isAppleSilicon;
    };
    CPUInfo getCPUInfo() const;
    
    //获取GPU信息
    struct GPUInfo {
        std::string model;
        std::string driver;
        bool discrete;
    };
    GPUInfo getGPUInfo() const;
    
    //判断是否为独显
    bool isDiscreteGPU(io_service_t device) const;

    // 获取内存信息
    struct MemoryInfo {
        uint64_t total;
        uint64_t used;
        uint64_t free;
    };
    MemoryInfo getMemoryInfo() const;

    // 获取磁盘信息
    struct DiskInfo {
        uint64_t total;
        uint64_t free;
        uint64_t used;
    };
    DiskInfo getDiskInfo() const;

    // 获取处理器数量
    int getProcessorCount() const;

    // 获取主机名
    std::string getHostname() const;

    // 格式化字节数
    std::string formatBytes(uint64_t bytes) const;
    
    // 获取操作系统版本号
    std::string getOSVersion() const;
    
    // 获取系统启动时间戳
    time_t getBootTimeStamp() const;

    // 获取系统启动时间
    std::string getBootTime() const;
    
    // 获取系统运行时间
    std::string getBootUptime() const;
    
    // 获取系统启动UTC毫秒时间戳
    int64_t getBootTimeUtcMsec() const;
    
    // 获取电池信息
    struct BatteryInfo {
        std::string name;
        int chargeLevel;
        bool isCharging;
        bool isPoweredByAC;
        bool isLowPower;
    };
    std::vector<BatteryInfo> getBatteryInfo() const;

    // 获取主板信息
    struct MotherboardInfo {
        std::string manufacturer;
        std::string model;
        std::string serialNumber;
        std::string version;
    };
    MotherboardInfo getMotherboardInfo() const;

    // 显卡详细信息结构体
    struct GraphicsInfoBase {
        std::string driverVersion;
        std::string model;
        std::string openglVersion;
        std::string vendor;
        std::string deviceId;
        std::string driverDate;
        std::string vendorId;
    };
    struct GraphicsInfos {
        GraphicsInfoBase primaryGraphics;
        GraphicsInfoBase* viceGraphics; // 若无副显卡则为nullptr
    };
    // 获取主副显卡信息
    GraphicsInfos getGraphicsInfos() const;

    // 打印系统信息
    void printSystemInfo() const;

private:
    // 私有构造函数（单例模式）
    SystemInfo() = default;
    // 禁用拷贝构造和赋值操作
    SystemInfo(const SystemInfo&) = delete;
    SystemInfo& operator=(const SystemInfo&) = delete;
};

#endif // SYSTEM_INFO_HPP 

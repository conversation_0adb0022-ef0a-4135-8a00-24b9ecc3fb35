//
//  utils_macros.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/28.
//

#ifndef macro_utils_h
#define macro_utils_h

// dispatch async on main queue
#define DISPATCH_ASYNC_ON_MAIN_QUEUE(block) \
    if ([NSThread isMainThread]) { \
        block(); \
    } else { \
        dispatch_async(dispatch_get_main_queue(), block); \
    }


// weak self
#define WEAKIFY(VAR) __weak __typeof__(VAR) VAR##_weak_ = VAR
// strong self
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wshadow"
#if __has_feature(objc_arc)
#define STRONGIFY(VAR) __strong __typeof__(VAR) VAR = (VAR##_weak_)
#else
#define STRONGIFY(VAR) __strong __typeof__(VAR) VAR = [[(VAR##_weak_) retain] autorelease];
#endif
#pragma clang diagnostic pop

#endif /* macro_utils_h */

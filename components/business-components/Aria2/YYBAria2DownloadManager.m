//
//  YYBAria2DownloadManager.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/10.
//

#import "YYBAria2DownloadManager.h"
#import "YYBAria2TaskManager.h"
#import "YYBAria2TaskCallbackCenter.h"
#import "YYBAria2TaskFileValidator.h"
#import "YYBAria2NetworkMonitor.h"
#import "YYBAria2ServiceMonitor.h"
#import "YYBLibAria2JSONRPCAdapter.h"
#import "YYBLibAria2ServiceFacade.h"
#import "YYBAria2TaskScheduler.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBAria2Task.h"
#import "YYBAria2Error.h"

static NSString *const kLogTag = @"YYBAria2DownloadManager";

@interface YYBAria2DownloadManager () <YYBAria2ServiceMonitorDelegate, YYBAria2NetworkMonitorDelegate>

// 子模块
@property (nonatomic, strong) YYBAria2TaskManager *taskManager;         // 任务管理器
@property (nonatomic, strong) YYBAria2TaskCallbackCenter *callbackCenter;   // 回调
@property (nonatomic, strong) YYBAria2TaskFileValidator *fileValidator;     // 任务和文件校验
@property (nonatomic, strong) YYBAria2NetworkMonitor *networkMonitor;       // 网络监听
@property (nonatomic, strong) YYBAria2ServiceMonitor *serviceMonitor;       // 服务状态监听
@property (nonatomic, strong) YYBLibAria2JSONRPCAdapter *jsonrpcAdapter;    // rpc转发
@property (nonatomic, strong) YYBLibAria2ServiceFacade *serviceFacade;      // 下载服务
@property (nonatomic, strong) dispatch_queue_t rpcSyncQueue;                // rpc接口队列
@property (nonatomic, strong) YYBAria2TaskScheduler *taskScheduler;         // 优先级&静默调度器

// 下载状态变化block
@property (nonatomic, copy) YYBLibAria2DownloadEventBlock downloadEventBlock;

@end

@implementation YYBAria2DownloadManager

+ (instancetype)sharedManager {
    static YYBAria2DownloadManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[YYBAria2DownloadManager alloc] init];
        instance.rpcSyncQueue = dispatch_queue_create("com.yyb.YYBLibAria2.dosnlad.rpc.sync", DISPATCH_QUEUE_SERIAL);
        [instance setupModules];
    });
    return instance;
}

- (BOOL)hasUserActive {
    return self.taskScheduler.hasUserActive;
}

- (void)setupModules {
    self.serviceFacade = [YYBLibAria2ServiceFacade sharedService];
    self.callbackCenter = [[YYBAria2TaskCallbackCenter alloc] init];
    self.fileValidator = [[YYBAria2TaskFileValidator alloc] init];
    self.taskManager = [[YYBAria2TaskManager alloc] initWithSessionManager:self.serviceFacade.sessionManager
                                                           callbackCenter:self.callbackCenter
                                                            fileValidator:self.fileValidator];
    self.jsonrpcAdapter = [[YYBLibAria2JSONRPCAdapter alloc] initWithSessionManager:self.serviceFacade.sessionManager
                                                                        taskManager:self.taskManager];
    self.networkMonitor = [[YYBAria2NetworkMonitor alloc] initWithDelegate:self];
    self.serviceMonitor = [[YYBAria2ServiceMonitor alloc] initWithDelegate:self];
    self.taskScheduler = [[YYBAria2TaskScheduler alloc] init];
    
    __weak typeof(self) weakSelf = self;
    self.downloadEventBlock = ^(YYBLibAria2DownloadEvent event, NSString * _Nonnull taskId, NSDictionary * _Nonnull info) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf handleAria2Event:event taskId:taskId info:info];
    };
    
    // 监听任务状态变化
    [self.serviceFacade addDownloadEventListener:self.downloadEventBlock];
    
    // 批量进度桥接
    [self.serviceFacade.sessionManager setDownloadBatchProgressCallback:^(NSDictionary<NSString *, NSDictionary *> *progressDict) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        // 抹平调度任务后，再分流到EventCenter
        NSDictionary<NSString *, NSDictionary *> * allProgressDict = [strongSelf allTasksProgressDict:progressDict];
        [strongSelf.serviceFacade.eventCenter dispatchBatchProgressEvent:allProgressDict];
    }];
}

#pragma mark - 任务状态变化
- (void)handleAria2Event:(YYBLibAria2DownloadEvent)event taskId:(NSString *)taskId info:(NSDictionary *)info {
    __weak typeof(self) weakSelf = self;
    [self.taskManager handleAria2Event:event taskId:taskId info:info completion:^{
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf scheduleAllTasksIfNeeded];
    }];
}

#pragma mark - 调度核心

- (void)scheduleAllTasksIfNeeded {
    NSArray<YYBAria2Task *> *allTasks = [self.taskManager allTasks];
    NSDictionary<NSString *, YYBAria2TaskScheduleResult *> *scheduleMap = [self.taskScheduler scheduleChangedTasksFromAllTasks:allTasks];
    for (YYBAria2Task *task in allTasks) {
        YYBAria2TaskScheduleResult *r = scheduleMap[task.taskId];
        switch (r.scheduleStatus) {
            case YYBAria2TaskScheduleStatusActive:
                YYBMacLogInfo(kLogTag, @"调度: 激活任务 %@", task);
                if (task.isStarted && task.status == YYBAria2TaskStatusPaused) {
                    [self.taskManager startTask:task];
                }
                break;
            case YYBAria2TaskScheduleStatusWaiting:
                if (task.status != YYBAria2TaskStatusPaused) {
                    YYBMacLogInfo(kLogTag, @"调度: 排队等待任务(也手动暂停住) %@", task);
                    [self.taskManager pauseTask:task byUser:NO completion:nil];
                }
                break;
            case YYBAria2TaskScheduleStatusPaused:
                if (task.status != YYBAria2TaskStatusPaused) {
                    YYBMacLogInfo(kLogTag, @"调度: 暂停静默任务 %@", task);
                    [self.taskManager pauseTask:task byUser:NO completion:nil];
                }
                break;
            case YYBAria2TaskScheduleStatusUserPaused:
                YYBMacLogInfo(kLogTag, @"无需调度: 用户已手动暂停 %@", task);
                break;
            case YYBAria2TaskScheduleStatusNone:
            case YYBAria2TaskScheduleStatusFailed:
            case YYBAria2TaskScheduleStatusCompleted:
            case YYBAria2TaskScheduleStatusNetworkError:
            case YYBAria2TaskScheduleStatusRemoved:
                // 不做操作
                break;
        }
    }
}

#pragma mark - 对外API（全部委托给taskManager/callbackCenter/persistence）

- (YYBAria2Task *)createDownloadTaskWithURL:(NSString *)url
                                    destDir:(nullable NSString *)destDir
                                   fileName:(nullable NSString *)fileName
                                        md5:(nullable NSString *)md5
{
    return [self createDownloadTaskWithURL:url
                                   destDir:destDir
                                  fileName:fileName
                                       md5:md5
                                visibility:YYBAria2TaskVisibilityUser
                                  priority:YYBAria2TaskPriorityNormal];
}

- (YYBAria2Task *)createDownloadTaskWithURL:(NSString *)url
                                    destDir:(nullable NSString *)destDir
                                   fileName:(nullable NSString *)fileName
                                        md5:(nullable NSString *)md5
                                 visibility:(YYBAria2TaskVisibility)visibility
                                   priority:(YYBAria2TaskPriority)priority {
    YYBAria2Task *task = [self.taskManager createDownloadTaskWithURL:url destDir:destDir fileName:fileName md5:md5];
    task.visibility = visibility;
    task.priority = priority;
    [self scheduleAllTasksIfNeeded];
    return task;
}


/// 启动任务
- (void)startTask:(YYBAria2Task *)task {
    task.userPaused = NO;
    task.isStarted = YES;
    NSArray *allTasks = [self.taskManager allTasks];
    YYBAria2TaskScheduleResult *r = [self.taskScheduler scheduleForTask:task inAllTasks:allTasks];
    if (![r needWait]) {
        [self.taskManager startTask:task];
    } else {
        YYBMacLogInfo(kLogTag, @"调度: startTask:delegate被拦截，排队等待 %@", task);
        [self scheduleAllTasksIfNeeded];
    }
}

- (void)cancelTask:(YYBAria2Task *)task deleteFile:(BOOL)deleteFile {
    [self.taskManager cancelTask:task deleteFile:deleteFile];
    [self scheduleAllTasksIfNeeded];
}

- (void)pauseTask:(YYBAria2Task *)task completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion {
    // pause一般不需要调度拦截，直接下发
    task.userPaused = YES;
    [self.taskManager pauseTask:task byUser:YES completion:completion];
    [self scheduleAllTasksIfNeeded];
}

- (void)resumeTask:(YYBAria2Task *)task completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion {
    task.userPaused = NO;
    NSArray *allTasks = [self.taskManager allTasks];
     YYBAria2TaskScheduleResult *r = [self.taskScheduler scheduleForTask:task inAllTasks:allTasks];
     if (![r needWait]) {
         YYBMacLogInfo(kLogTag, @"调度: resumeTask放行 %@", task);
         [self.taskManager resumeTask:task completion:completion];
     } else {
         // 拦截，修改状态后直接回调 成功（排队到号后会自动启动）
         if (task.status == YYBAria2TaskStatusPaused) {
             // 已暂停的状态，手动还原成待下发状态
             [task updateStatus:YYBAria2TaskStatusPending reason:@"手动-恢复需将暂停状态改为pending"];
         }
         YYBMacLogInfo(kLogTag, @"调度: resumeTask被拦截， 原因：%@，排队等待 %@", r.reason, task);
         if (completion) completion(YES, nil);
         [self scheduleAllTasksIfNeeded];
     }
}

- (NSArray<YYBAria2Task *> *)allTasks {
    return [self.taskManager allTasks];
}

- (void)clearAllTasksDeleteFiles:(BOOL)deleteFiles {
    [self.taskManager clearAllTasksDeleteFiles:deleteFiles];
}

- (YYBAria2Task *)taskForTaskId:(NSString *)taskId {
    return [self.taskManager taskForTaskId:taskId];
}

- (nullable YYBAria2Task *)findLocalCompleteFileWithMD5:(NSString *)md5 {
    return [self.taskManager findLocalCompleteFileWithMD5:md5];
}

#pragma mark - YYBAria2ServiceMonitorDelegate
- (void)yybAria2OnServiceAvailable {
    [self.taskManager onServiceAvailable];
    [self scheduleAllTasksIfNeeded];
}

- (void)yybAria2OnServiceUnavailable {
    [self.taskManager onServiceUnavailable];
    [self scheduleAllTasksIfNeeded];
}

- (void)yybAria2OnAppDidBecomeActive {
    [self.taskManager onAppDidBecomeActive];
    [self scheduleAllTasksIfNeeded];
}

- (void)yybAria2OnAppWillTerminate {
    [self.taskManager onAppWillTerminate];
}

- (void)yybAria2OnAppWillResignActive {
    [self.taskManager onAppWillResignActive];
}

#pragma mark - YYBAria2NetworkMonitorDelegate
- (void)yybAria2OnNetworkChange:(BOOL)reachable {
    [self.taskManager onNetworkChange:reachable];
    [self scheduleAllTasksIfNeeded];
}

#pragma mark - RPC接口
- (void)sendJSONRPCRequestWithMethod:(NSString *)method
                              params:(nullable id)params
                              source:(YYBAria2TaskSource)source
                          completion:(void(^)(NSDictionary * _Nullable result, NSData * _Nullable rawData, NSError * _Nullable error))completion {
    if (source != YYBAria2TaskSourceDebug) {
        YYBMacLogInfo(kLogTag, @"sendJSONRPCRequestWithMethod source = %@, method:%@ , params = %@", @(source), method, params);
    }
    
    // 1. 先处理调度相关的RPC方法
    if ([self shouldInterceptRPCMethod:method]) {
        [self handleInterceptedRPCMethod:method params:params source:source completion:completion];
        return;
    }
    
    // 2. 其他方法直接转发
    dispatch_async(_rpcSyncQueue, ^{
        [self.jsonrpcAdapter handleRequestWithMethod:method params:params source:source completion:completion];
    });
}

/// 判断是否需要调度拦截
- (BOOL)shouldInterceptRPCMethod:(NSString *)method {
    static NSSet *interceptMethods;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        interceptMethods = [NSSet setWithObjects:
            @"aria2.addUri", @"aria2.unpause", nil];       // pause/remove: 直接放行（不需要调度拦截）
    });
    return [interceptMethods containsObject:method];
}

/// 处理被调度拦截的RPC方法
- (void)handleInterceptedRPCMethod:(NSString *)method
                            params:(nullable id)params
                            source:(YYBAria2TaskSource)source
                        completion:(void(^)(NSDictionary * _Nullable result, NSData * _Nullable rawData, NSError * _Nullable error))completion
{
    // 1. addUri: 判断调度是否允许新建任务
    if ([method isEqualToString:@"aria2.addUri"]) {
        [self handleAddUri:params source:source completion:completion];
        return;
    }
    
    // 2. unpause: 判断调度是否允许
    if ([method isEqualToString:@"aria2.unpause"]) {
        [self handleUnpause:params source:source completion:completion];
        return;
    }

    // 3. 兜底：其他方法直接转发
    dispatch_async(_rpcSyncQueue, ^{
        [self.jsonrpcAdapter handleRequestWithMethod:method params:params source:source completion:completion];
    });
}

- (void)handleAddUri:(NSArray *)params
              source:(YYBAria2TaskSource)source
          completion:(void(^)(NSDictionary *, NSData *, NSError *))completion {
    // 解析url和参数
    NSString *method = @"aria2.addUri";
    NSString *url = nil;
    NSDictionary *options = nil;
    if ([params isKindOfClass:[NSArray class]]) {
        if ([params count] > 0) {
            id urlParam = params[0];
            if ([urlParam isKindOfClass:[NSString class]]) {
                url = urlParam;
            } else if ([urlParam isKindOfClass:[NSArray class]] && [urlParam count] > 0) {
                url = urlParam[0];
            }
        }
        if ([params count] > 1 && [params[1] isKindOfClass:[NSDictionary class]]) {
            options = params[1];
        }
    }
    if (!url) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"参数无效无uri-addUri" userInfo:nil];
        if (completion) completion(nil, nil, err);
        YYBMacLogError(kLogTag, @"调度: addUri参数无效，拦截");
        return;
    }
    NSString *fileName = options[@"out"];
    NSString *destDir = options[@"dir"];
    NSInteger visibility = YYBAria2TaskVisibilityUser;
    NSInteger priority = YYBAria2TaskPriorityNormal;
    id visObj = options[@"visibility"];
    if (visObj && ![visObj isKindOfClass:[NSNull class]]) {
        if ([visObj isKindOfClass:[NSNumber class]] || [visObj isKindOfClass:[NSString class]]) {
            NSInteger v = [visObj intValue];
            // 假设只允许0/1/2
            if (v >= YYBAria2TaskVisibilityUser && v <= YYBAria2TaskVisibilitySilent) {
                visibility = v;
            } else {
                YYBMacLogWarn(kLogTag, @"addUri visibility参数非法: %@，已用默认值", visObj);
            }
        } else {
            YYBMacLogWarn(kLogTag, @"addUri visibility参数类型非法: %@，已用默认值", visObj);
        }
    }

    id priObj = options[@"priority"];
    if (priObj && ![priObj isKindOfClass:[NSNull class]]) {
        if ([priObj isKindOfClass:[NSNumber class]] || [priObj isKindOfClass:[NSString class]]) {
            NSInteger p = [priObj intValue];
            // 假设只允许0/1/2
            if (p >= YYBAria2TaskPriorityLow && p <= YYBAria2TaskPriorityHigh) {
                priority = p;
            } else {
                YYBMacLogWarn(kLogTag, @"addUri priority参数非法: %@，已用默认值", priObj);
            }
        } else {
            YYBMacLogWarn(kLogTag, @"addUri priority参数类型非法: %@，已用默认值", priObj);
        }
    }
    // 先创建任务（但不启动）
    YYBAria2Task *task = [self createDownloadTaskWithURL:url
                                                 destDir:destDir
                                                fileName:fileName
                                                     md5:nil];
    task.visibility = (YYBAria2TaskVisibility)visibility;
    task.priority = (YYBAria2TaskPriority)priority;
    task.userPaused = NO;
    task.isStarted = YES;
    // 这里不同source如果复用同一个任务，source会更新为最后的一个任务来源(aria2不允许同个url重复添加任务)
    task.source = source;
    if (task.status == YYBAria2TaskStatusPaused) {
        // 已暂停的状态，手动还原成待下发状态（web侧的Unpaus手动调用的AddUri接口）
        [task updateStatus:YYBAria2TaskStatusPending reason:@"web恢复需将暂停状态改为pending"];
    }
    // 调度判断
    NSArray *allTasks = [self.taskManager allTasks];
    YYBAria2TaskScheduleResult *r = [self.taskScheduler scheduleForTask:task inAllTasks:allTasks];
    if (![r needWait]) {
        // 允许，走正常流程
        YYBMacLogInfo(kLogTag, @"调度: addUri放行，task=%@", task);
        dispatch_async(_rpcSyncQueue, ^{
            [self.jsonrpcAdapter handleRequestWithMethod:method params:params source:source completion:completion];
        });
    } else {
        // 不允许，直接回包对外gid
        YYBMacLogInfo(kLogTag, @"调度: addUri被拦截，原因：%@， 排队等待 url=%@", r.reason, url);
        NSDictionary *result = @{
            @"result":
                //                task.gidForUser ?: @"",
            @{@"gid": task.gidForUser ?: @"",
              @"scheduleStatus": @"waiting",
              @"reason": r.reason ?: @"被调度拦截",
              @"progress": [task progressDictionaryForWaitting]}
        };
        if (completion) completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], nil);
        [self scheduleAllTasksIfNeeded];
    }
}


- (void)handleUnpause:(NSArray *)params
               source:(YYBAria2TaskSource)source
           completion:(void(^)(NSDictionary *, NSData *, NSError *))completion {
    NSString *method = @"aria2.unpause";
    NSString *taskId = nil;
    if ([params isKindOfClass:[NSArray class]] && [params count] > 0) {
        taskId = params[0];
    }
    YYBAria2Task *task = [self.taskManager taskForTaskId:taskId];
    task.userPaused = NO;
    if (task) {
        NSArray *allTasks = [self.taskManager allTasks];
        YYBAria2TaskScheduleResult *r = [self.taskScheduler scheduleForTask:task inAllTasks:allTasks];
        if (![r needWait]) {
            // 允许，正常转发
            dispatch_async(_rpcSyncQueue, ^{
                [self.jsonrpcAdapter handleRequestWithMethod:method params:params source:source completion:completion];
            });
            YYBMacLogInfo(kLogTag, @"调度: unpause放行 taskId=%@", taskId);
        } else {
            // 不允许，直接回包对外gid
            YYBMacLogInfo(kLogTag, @"调度: unpause被拦截，原因：%@， 排队等待 taskId=%@", r.reason, taskId);
            NSDictionary *result = @{
                @"result":
                    //                    task.gidForUser ?: @"",
                @{@"gid": task.gidForUser ?: @"",
                  @"scheduleStatus": @"waiting",
                  @"reason": r.reason ?: @"被调度拦截",
                  @"progress": [task progressDictionaryForWaitting]}
            };
            if (completion) completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], nil);
            [self scheduleAllTasksIfNeeded];
        }
    } else {
        // 未找到任务，直接转发
        dispatch_async(_rpcSyncQueue, ^{
            [self.jsonrpcAdapter handleRequestWithMethod:method params:params source:source completion:completion];
        });
    }
}

#pragma mark - 任务进度抹平
/// 获取全量任务的进度字典（包含被调度拦截/未下发的任务，业务层抹平）
- (NSDictionary<NSString *, NSDictionary *> *)allTasksProgressDict:(NSDictionary<NSString *, NSDictionary *> *)aria2ProgressDict {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];

    // 1. 合并所有任务（未删除+已删除）
    NSMutableArray<YYBAria2Task *> *allTasks = [[self.taskManager allTasks] mutableCopy];
    NSArray<YYBAria2Task *> *removedTasks = [self.taskManager allRemoveTasks];
    if (removedTasks.count > 0) {
        [allTasks addObjectsFromArray:removedTasks];
    }

    // 2. 遍历所有任务
    for (YYBAria2Task *task in allTasks) {
        NSDictionary *progress = aria2ProgressDict[task.gidForUser];

        if (!progress) {
            // 未下发/被调度拦截/已删除，构造虚拟进度
            progress = [self fakeProgressDictForTask:task];
        } else {
            // 如有调度拦截，也需抹平
            progress = [self patchProgress:progress forTask:task];
        }

        if (progress) {
            result[task.taskId] = progress;
        }
    }

    return result;
}

/// 构造虚拟进度（被调度拦截/未下发的任务）
- (nullable NSDictionary *)fakeProgressDictForTask:(YYBAria2Task *)task {
    BOOL needFake = (task.scheduleStatus == YYBAria2TaskScheduleStatusPaused ||
                      task.scheduleStatus == YYBAria2TaskScheduleStatusWaiting);
    if (!needFake) {
        // 只有调度状态为pause和waiting的任务，才需构造/抹平进度
        return nil;
    }
    
    return [task progressDictionaryForWaitting];
}

/// 对已下发任务，根据调度状态抹平进度
- (NSDictionary *)patchProgress:(NSDictionary *)progress forTask:(YYBAria2Task *)task {
    BOOL needPatch = (task.scheduleStatus == YYBAria2TaskScheduleStatusPaused ||
                      task.scheduleStatus == YYBAria2TaskScheduleStatusWaiting);
    if (!needPatch) {
        // 只有调度状态为pause和waiting的任务，才需构造/抹平进度
        return progress;
    }
    
    NSString *oldStatus = progress[@"status"];
    NSMutableDictionary *patched = [progress mutableCopy];
    NSString *status = @"waiting";
    int statusCode = 1; // 对应DownloadStatus的DOWNLOAD_WAITING枚举值
    YYBMacLogInfo(kLogTag, @"patchProgress task :%@ , oldStatus = %@", task.taskId, oldStatus);
    patched[@"status"] = status;
    patched[@"statusCode"] = @(statusCode);
    patched[@"scheduleStatus"] = @(task.scheduleStatus);
    patched[@"scheduleReason"] = task.scheduleStatusString ?: @"调度暂停/排队";
    return patched;
}

#pragma mark - listener
/// 注册YYBDownloadListener（支持多对一监听）
- (void)addDownloadListener:(id<YYBDownloadListener>)listener {
    [self.callbackCenter addDownloadListener:listener];
}

- (void)removeDownloadListener:(id<YYBDownloadListener>)listener {
    [self.callbackCenter removeDownloadListener:listener];
}

/// 同步查询获取当前的下载任务信息
- (nullable YYBAria2Task *)getAppDownloadTaskInfo:(id<YYBDownloadListener>)listener
{
    if (!listener) {
        YYBMacLogError(kLogTag, @"[getAppDownloadTaskInfo] 空listener参数，无法查询");
        return nil;
    }
    NSString *classKey = NSStringFromClass([listener class]);
    NSString *listenerKey = [listener listenerDownloadKey];
    if (!classKey.length || !listenerKey.length) {
        YYBMacLogError(kLogTag, @"[getAppDownloadTaskInfo] 无效listener: class=%@, key=%@", classKey, listenerKey);
        return nil;
    }
    Class listenerClass = [listener class];
    NSArray<YYBAria2Task *> *allTasks = [self.taskManager allTasks];
    for (YYBAria2Task *task in allTasks) {
        // 使用listener类的downloadKeyForTask:生成key，和注册用key比对
        if (![listenerClass respondsToSelector:@selector(downloadKeyForTask:)]) {
            YYBMacLogWarn(kLogTag, @"[getAppDownloadTaskInfo] ListenerClass %@ 未实现+downloadKeyForTask:", classKey);
            continue;
        }
        NSString *taskKey = [listenerClass downloadKeyForTask:task];
        // 确保匹配，否则不返回
        if (taskKey.length && [listenerKey isEqualToString:taskKey]) {
            YYBMacLogInfo(kLogTag, @"[getAppDownloadTaskInfo] 命中任务: listener=%@, class=%@, key=%@, taskId=%@", listener, classKey, listenerKey, task.taskId);
            return task;
        }
    }
    // 未查到
    YYBMacLogInfo(kLogTag, @"[getAppDownloadTaskInfo] 未找到符合条件的任务: listener=%@, class=%@, key=%@", listener, classKey, listenerKey);
    return nil;
}

@end

//
//  YYBLibAria2ServiceManager.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/18.
//  JSON-RPC分发，参数校验，线程安全

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

@class YYBLibAria2SessionManager, YYBAria2TaskManager;

typedef void(^YYBLibAria2JSONRPCCompletion)(NSDictionary * _Nullable result, NSData * _Nullable rawData, NSError * _Nullable error);

/// JSON-RPC适配器，线程安全：所有方法需在串行队列调用
@interface YYBLibAria2JSONRPCAdapter : NSObject

- (instancetype)initWithSessionManager:(YYBLibAria2SessionManager *)sessionManager
                           taskManager:(YYBAria2TaskManager *)taskManager;

/// 分发JSON-RPC请求
- (void)handleRequestWithMethod:(NSString *)method
                         params:(id)params
                         source:(NSInteger)source
                     completion:(YYBLibAria2JSONRPCCompletion)completion;

@end

NS_ASSUME_NONNULL_END

//
//  YYBLibAria2JSONRPCAdapter.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/18.
//

#import "YYBLibAria2JSONRPCAdapter.h"
#import "YYBLibAria2EventCenter.h"
#import "YYBLibAria2ConfigManager.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBLibAria2SessionManager.h"
#import "YYBAria2TaskManager.h"
#import "YYBAria2TaskManager+RPC.h"
#import "YYBAria2TaskIdGidMap.h"
#import "YYBAria2Error.h"

#define kLogTag @"YYBLibAria2JSONRPCAdapter"

@interface YYBLibAria2JSONRPCAdapter ()
@property (nonatomic, strong) YYBLibAria2SessionManager *sessionManager;
@property (nonatomic, strong) YYBAria2TaskManager *taskManager;
@end

@implementation YYBLibAria2JSONRPCAdapter

- (instancetype)initWithSessionManager:(YYBLibAria2SessionManager *)sessionManager
                           taskManager:(YYBAria2TaskManager *)taskManager {
    if (self = [super init]) {
        _sessionManager = sessionManager;
        _taskManager = taskManager;
    }
    return self;
}

- (void)handleRequestWithMethod:(NSString *)method
                         params:(id)params
                         source:(NSInteger)source
                     completion:(YYBLibAria2JSONRPCCompletion)completion
{
    static NSSet *businessMethods;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        businessMethods = [NSSet setWithObjects:
                           @"aria2.addUri", @"aria2.pause", @"aria2.unpause", @"aria2.remove", nil];
    });
    
    if ([businessMethods containsObject:method]) {
        [self.taskManager handleJSONRPCMethod:method params:params source:(YYBAria2TaskSource)source completion:completion];
    } else {
        // 直接分发到底层SessionManager
        [self routeToSessionManagerWithMethod:method params:params completion:completion];
    }
}

- (NSString *)gidFromTaskIdOrGid:(NSString *)input {
    // 如果input是taskId，查表转成gid；如果本身就是gid，直接返回
    NSString *gid = [[YYBAria2TaskIdGidMap sharedMap] gidForTaskId:input];
    if (!gid || !gid.length) {
        YYBMacLogError(kLogTag, @"[YYBAria2TaskIdGidMap gidFromTaskIdOrGid] 失效, input=%@", input);
    }
    return gid ?: input;
}

- (void)routeToSessionManagerWithMethod:(NSString *)method
                                 params:(id)params
                             completion:(YYBLibAria2JSONRPCCompletion)completion {
    // 参数深拷贝
    NSArray *paramsArray = [self.class parseParamsArray:params];
    // 分发
    if ([method isEqualToString:@"aria2.pause"]) {
        [self handlePause:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.unpause"]) {
        [self handleUnpause:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.tellStatus"]) {
        [self handleTellStatus:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.remove"]) {
        [self handleRemove:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.tellActive"]) {
        [self handleTellActive:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.getGlobalStat"]) {
        [self handleGetGlobalStat:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.removeDownloadResult"]) {
        [self handleRemoveDownloadResult:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.tellWaiting"]) {
        [self handleTellWaiting:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.tellStopped"]) {
        [self handleTellStopped:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.purgeDownloadResult"]) {
        [self handlePurgeDownloadResult:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.getFiles"]) {
        [self handleGetFiles:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.changeOption"]) {
        [self handleChangeOption:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.changeGlobalOption"]) {
        [self handleChangeGlobalOption:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.getVersion"]) {
        [self handleGetVersion:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.forcePause"]) {
        [self handleForcePause:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.pauseAll"]) {
        [self handlePauseAll:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.unpauseAll"]) {
        [self handleUnpauseAll:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.getUris"]) {
        [self handleGetUris:paramsArray completion:completion];
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"RPC-不支持的方法 method = %@", method];
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorRPCMethodNotSupported message:errorMessage userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        }
        YYBMacLogError(kLogTag, @"[handleRequestWithMethod] 不支持的方法: %@", method);
    }
}

#pragma mark - 参数处理

+ (NSArray *)parseParamsArray:(id)params {
    if ([params isKindOfClass:[NSArray class]]) return params;
    else if (params) return @[params];
    else return @[];
}

+ (NSString *)extractTokenIfNeeded:(NSArray * __strong *)paramsArrayPtr {
    NSArray *paramsArray = *paramsArrayPtr;
    NSString *token = nil;
    if (paramsArray.count > 0 && [paramsArray[0] isKindOfClass:[NSString class]] && [paramsArray[0] hasPrefix:@"token:"]) {
        token = [paramsArray[0] substringFromIndex:6];
        *paramsArrayPtr = [paramsArray subarrayWithRange:NSMakeRange(1, paramsArray.count-1)];
    }
    return token;
}

#pragma mark - JSONRPC方法分发实现

- (void)handlePause:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    if (!gid) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"参数缺失gid-pause" userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        };
        YYBMacLogError(kLogTag, @"[handlePause] 参数缺失gid, params=%@", params);
        return;
    }
    [self.sessionManager pauseDownload:gid completion:^(BOOL success, NSError *error) {
        NSDictionary *result = success ? @{@"result": taskId} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
        }
        YYBMacLogInfo(kLogTag, @"[handlePause] gid=%@, ok=%d, err=%@", gid, success, error);
    }];
}

- (void)handleUnpause:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    if (!gid) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"参数缺失gid-unpause" userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        };
        YYBMacLogError(kLogTag, @"[handleUnpause] 参数缺失gid, params=%@", params);
        return;
    }
    [self.sessionManager resumeDownload:gid completion:^(BOOL success, NSError *error) {
        NSDictionary *result = success ? @{@"result": taskId} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
        }
        YYBMacLogInfo(kLogTag, @"[handleUnpause] gid=%@, ok=%d, err=%@", gid, success, error);
    }];
}

- (void)handleTellStatus:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    if (!gid) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"参数缺失gid-tellStatus" userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        };
        YYBMacLogError(kLogTag, @"[handleTellStatus] 参数缺失gid, params=%@", params);
        return;
    }
    [self.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
        NSDictionary *result = progress ? @{@"result": progress} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, nil);
        }
        //        YYBMacLogInfo(kLogTag, @"[handleTellStatus] gid=%@, progress=%@", gid, progress);
    }];
}

- (void)handleRemove:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    if (!gid) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"参数缺失gid-remove" userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        };
        YYBMacLogError(kLogTag, @"[handleRemove] 参数缺失gid, params=%@", params);
        return;
    }
    [self.sessionManager removeDownload:gid completion:^(BOOL success, NSError *error) {
        NSDictionary *result = success ? @{@"result": taskId} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
        }
        YYBMacLogInfo(kLogTag, @"[handleRemove] gid=%@, ok=%d, err=%@", gid, success, error);
    }];
}

- (void)handleTellActive:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    __weak typeof(self) weakSelf = self;
    [self.sessionManager activeDownloadGIDsWithCompletion:^(NSArray<NSString *> *gids) {
        if (!gids) gids = @[];
        NSMutableArray *arr = [NSMutableArray array];
        dispatch_queue_t arrQueue = dispatch_queue_create("com.yyb.tellActiveArrQueue", DISPATCH_QUEUE_SERIAL);
        dispatch_group_t group = dispatch_group_create();
        for (NSString *gid in gids) {
            dispatch_group_enter(group);
            [weakSelf.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
                // 线程安全写入
                if (progress) {
                    dispatch_async(arrQueue, ^{
                        [arr addObject:progress];
                        dispatch_group_leave(group);
                    });
                } else {
                    dispatch_group_leave(group);
                }
            }];
        }
        dispatch_group_notify(group, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            // 读取 arr 也要在 arrQueue 上，防止写未完成
            dispatch_sync(arrQueue, ^{
                NSDictionary *result = @{@"result": arr};
                if (completion) {
                    completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], nil);
                }
                //                YYBMacLogInfo(kLogTag, @"[handleTellActive] activeGIDs=%@", arr);
            });
        });
    }];
}

- (void)handleGetGlobalStat:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    [self.sessionManager globalStatWithCompletion:^(NSDictionary *stat) {
        NSDictionary *result = stat ? @{@"result": stat} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, nil);
        }
        //        YYBMacLogInfo(kLogTag, @"[handleGetGlobalStat] stat=%@", stat);
    }];
}

- (void)handleTellWaiting:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSArray<NSString *> *allGIDs = [self.sessionManager allKnownGIDs];
    NSMutableArray *waitingArr = [NSMutableArray array];
    dispatch_queue_t arrQueue = dispatch_queue_create("com.yyb.tellWaitingArrQueue", DISPATCH_QUEUE_SERIAL);
    dispatch_group_t group = dispatch_group_create();
    for (NSString *gid in allGIDs) {
        dispatch_group_enter(group);
        [self.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
            NSString *status = progress[@"status"];
            // aria2c官方waiting/paused
            if ([status isEqualToString:@"waiting"] || [status isEqualToString:@"paused"]) {
                dispatch_async(arrQueue, ^{
                    [waitingArr addObject:progress ?: @{}];
                    dispatch_group_leave(group);
                });
            } else {
                dispatch_group_leave(group);
            }
        }];
    }
    dispatch_group_notify(group, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        dispatch_sync(arrQueue, ^{
            NSDictionary *result = @{@"result": waitingArr};
            if (completion) {
                completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], nil);
            }
            //            YYBMacLogInfo(kLogTag, @"[handleTellWaiting] waitingGIDs=%@", waitingArr);
        });
    });
}

- (void)handleTellStopped:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSArray<NSString *> *allGIDs = [self.sessionManager allKnownGIDs];
    NSMutableArray *stoppedArr = [NSMutableArray array];
    dispatch_queue_t stoppedArrQueue = dispatch_queue_create("com.yyb.stoppedArrQueue", DISPATCH_QUEUE_SERIAL);
    dispatch_group_t group = dispatch_group_create();
    for (NSString *gid in allGIDs) {
        dispatch_group_enter(group);
        [self.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
            NSString *status = progress[@"status"];
            // aria2c官方complete/error/removed
            if ([status isEqualToString:@"complete"] ||
                [status isEqualToString:@"error"] ||
                [status isEqualToString:@"removed"]) {
                dispatch_async(stoppedArrQueue, ^{
                    [stoppedArr addObject:progress ?: @{}];
                    dispatch_group_leave(group);
                });
            } else {
                dispatch_group_leave(group);
            }
        }];
    }
    dispatch_group_notify(group, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 读取 stoppedArr 也要在 stoppedArrQueue 上，防止写未完成
        dispatch_sync(stoppedArrQueue, ^{
            NSDictionary *result = @{@"result": stoppedArr};
            if (completion) {
                completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], nil);
            }
            //            YYBMacLogInfo(kLogTag, @"handleTellStopped stoppedGIDs=%@", stoppedArr);
        });
    });
}

- (void)handleRemoveDownloadResult:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    // 这里和remove任务一样
    [self handleRemove:params completion:completion];
}

- (void)handlePurgeDownloadResult:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSArray<NSString *> *allGIDs = [self.sessionManager allKnownGIDs];
    NSMutableArray *removedGIDs = [NSMutableArray array];
    dispatch_queue_t arrQueue = dispatch_queue_create("com.yyb.purgeRemovedArrQueue", DISPATCH_QUEUE_SERIAL);
    __block NSUInteger total = 0;
    dispatch_group_t group = dispatch_group_create();
    __weak typeof(self) weakSelf = self;
    for (NSString *gid in allGIDs) {
        dispatch_group_enter(group);
        [self.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
            NSNumber *statusNum = progress[@"statusCode"];
            if (!statusNum) {
                dispatch_group_leave(group);
                return;
            }
            NSInteger status = [statusNum integerValue];
            // DownloadStatus 3/4/5 = COMPLETE/ERROR/REMOVED
            if (status == 3 || status == 4 || status == 5) {
                [weakSelf.sessionManager removeDownload:gid completion:^(BOOL ok, NSError *err) {
                    dispatch_async(arrQueue, ^{
                        if (ok) {
                            [removedGIDs addObject:gid];
                        } else {
                            YYBMacLogError(kLogTag, @"[handlePurgeDownloadResult] 移除任务失败, gid=%@, err=%@", gid, err);
                        }
                        total++;
                        dispatch_group_leave(group);
                    });
                }];
            } else {
                dispatch_group_leave(group);
            }
        }];
    }
    dispatch_group_notify(group, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        dispatch_sync(arrQueue, ^{
            YYBMacLogInfo(kLogTag, @"[handlePurgeDownloadResult] 批量清理已完成/错误/移除任务, count=%lu, total遍历=%lu", (unsigned long)removedGIDs.count, (unsigned long)total);
            NSDictionary *result = @{@"result": removedGIDs};
            if (completion) {
                completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], nil);
            }
        });
    });
}

- (void)handleGetFiles:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    if (!gid) {
        YYBMacLogError(kLogTag, @"[handleGetFiles] 参数缺失gid");
        if (completion) {
            completion(nil, nil, [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"参数缺失gid-getFiles" userInfo:nil]);
        }
        return;
    }
    [self.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
        NSArray *files = nil;
        if ([progress isKindOfClass:[NSDictionary class]]) {
            files = progress[@"files"];
        }
        NSDictionary *result = files ? @{@"result": files} : nil;
        if (!files) {
            YYBMacLogError(kLogTag, @"[handleGetFiles] 未找到任务或无文件信息, gid=%@", gid);
        } else {
            YYBMacLogInfo(kLogTag, @"[handleGetFiles] 返回文件列表, gid=%@, count=%lu", gid, (unsigned long)files.count);
        }
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, files ? nil :
                       [YYBAria2Error errorWithCode:YYBAria2ErrorRPCNotGetFiles taskId:taskId message:@"未找到任务或无文件信息-getFiles" userInfo:nil]);
        }
    }];
}

- (void)handleChangeOption:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    NSDictionary *options = (params.count > 1 && [params[1] isKindOfClass:[NSDictionary class]]) ? params[1] : nil;
    if (!gid || !options) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"参数无效-changeOption" userInfo:nil];
        YYBMacLogError(kLogTag, @"[handleChangeOption] 参数无效, gid=%@, options=%@", gid, options);
        if (completion) {
            completion(nil, nil, err);
        }
        return;
    }
    [self.sessionManager changeOption:options forGid:gid completion:^(BOOL ok, NSError *err) {
        NSDictionary *result = ok ? @{@"result": taskId} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, err);
        }
        YYBMacLogInfo(kLogTag, @"[handleChangeOption] 修改任务参数, gid=%@, ok=%d, options=%@", gid, ok, options);
    }];
}

- (void)handleChangeGlobalOption:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSDictionary *options = (params.count > 0 && [params[0] isKindOfClass:[NSDictionary class]]) ? params[0] : nil;
    if (!options) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"参数无效-changeGlobalOption" userInfo:nil];
        YYBMacLogError(kLogTag, @"[handleChangeGlobalOption] 参数无效, options=%@", options);
        if (completion) {
            completion(nil, nil, err);
        }
        return;
    }
    [self.sessionManager changeGlobalOption:options completion:^(BOOL ok, NSError *err) {
        NSDictionary *result = ok ? @{@"result": @(ok)} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, err);
        }
        YYBMacLogInfo(kLogTag, @"[handleChangeGlobalOption] 修改全局参数, ok=%d, options=%@", ok, options);
    }];
}

- (void)handleGetVersion:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSDictionary *result = @{@"version": @"1.37.0"};
    if (completion) {
        completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, nil);
    }
    YYBMacLogInfo(kLogTag, @"[handleGetVersion] 返回版本信息: %@", result);
}

/// aria2.forcePause 强制暂停单个任务
- (void)handleForcePause:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    YYBAria2Task *task = [self.taskManager taskForTaskId:taskId];
    task.userPaused = YES;
    if (!gid) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"缺失gid-forcePause" userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        }
        YYBMacLogError(kLogTag, @"[handleForcePause] 参数缺失gid, params=%@", params);
        return;
    }
    [self.sessionManager forcePauseDownload:gid completion:^(BOOL success, NSError *error) {
        NSDictionary *result = success ? @{@"result": taskId} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
        }
        YYBMacLogInfo(kLogTag, @"[handleForcePause] gid=%@, ok=%d, err=%@", gid, success, error);
    }];
}

/// aria2.pauseAll 暂停所有任务
- (void)handlePauseAll:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSArray<YYBAria2Task *> *allTasks = [self.taskManager allTasks];
    for (YYBAria2Task *task in allTasks) {
        task.userPaused = YES;
    }
    [self.sessionManager pauseAllWithCompletion:^(NSArray<NSString *> *pausedGIDs, NSError *error) {
        NSDictionary *result = @{@"result": pausedGIDs ?: @[]};
        if (completion) {
            completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], error);
        }
        YYBMacLogInfo(kLogTag, @"[handlePauseAll] 暂停所有任务, count=%lu, gids=%@", (unsigned long)pausedGIDs.count, pausedGIDs);
    }];
}

/// aria2.unpauseAll 恢复所有任务
- (void)handleUnpauseAll:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSArray<YYBAria2Task *> *allTasks = [self.taskManager allTasks];
    for (YYBAria2Task *task in allTasks) {
        task.userPaused = NO;
    }
    [self.sessionManager unpauseAllWithCompletion:^(NSArray<NSString *> *unpausedGIDs, NSError *error) {
        NSDictionary *result = @{@"result": unpausedGIDs ?: @[]};
        if (completion) {
            completion(result, [NSJSONSerialization dataWithJSONObject:result options:0 error:nil], error);
        }
        YYBMacLogInfo(kLogTag, @"[handleUnpauseAll] 恢复所有任务, count=%lu, gids=%@", (unsigned long)unpausedGIDs.count, unpausedGIDs);
    }];
}

/// aria2.getUris 查询任务所有URI及其状态
- (void)handleGetUris:(NSArray *)params completion:(YYBLibAria2JSONRPCCompletion)completion {
    NSString *taskId = params.count > 0 ? params[0] : nil;
    NSString *gid = [self gidFromTaskIdOrGid:taskId];
    if (!gid) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam taskId:taskId message:@"缺失gid-getUris" userInfo:nil];
        if (completion) {
            completion(nil, nil, err);
        }
        YYBMacLogError(kLogTag, @"[handleGetUris] 参数缺失gid, params=%@", params);
        return;
    }
    [self.sessionManager urisForGID:gid completion:^(NSArray<NSDictionary *> *uris) {
        // 返回格式严格对齐aria2c官方: result为数组，每个元素为@{@"uri":..., @"status":...}
        NSDictionary *result = uris ? @{@"result": uris} : nil;
        if (!uris) {
            YYBMacLogError(kLogTag, @"[handleGetUris] 未找到任务或无URI, gid=%@", gid);
        } else {
            YYBMacLogInfo(kLogTag, @"[handleGetUris] 返回URI列表, gid=%@, count=%lu", gid, (unsigned long)uris.count);
        }
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, uris ? nil : [YYBAria2Error errorWithCode:YYBAria2ErrorRPCNotGetUris
                                                                                                                                                      taskId:taskId
                                                                                                                                                     message:@"未找到任务或无URI-getUris"
                                                                                                                                                    userInfo:nil]);
        }
    }];
}

@end

//
//  YYBAria2DownloadManager.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/10.
//

#import <Foundation/Foundation.h>
#import "YYBAria2Task.h"
#import "YYBDownloadListener.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBAria2DownloadManager : NSObject

+ (instancetype)sharedManager;

/// 当前是否有用户感知任务（注意：瞬间态，会发生变化）
@property (nonatomic, assign, readonly) BOOL hasUserActive;

/// 添加下载任务（支持block和delegate两种回调方式）
/// @param url 下载URL
/// @param destDir 目标根目录（为空是会放到默认路径下）
/// @param fileName 文件名（可选，默认取url最后一段）
/// @param md5 文件md5（可选，核心文件强烈建议带上，便于完整性校验和复用）
- (YYBAria2Task *)createDownloadTaskWithURL:(NSString *)url
                              destDir:(nullable NSString *)destDir
                             fileName:(nullable NSString *)fileName
                                  md5:(nullable NSString *)md5;

/// 添加下载任务（支持block和delegate两种回调方式）
/// @param url 下载URL
/// @param destDir 目标根目录（为空是会放到默认路径下）
/// @param fileName 文件名（可选，默认取url最后一段）
/// @param md5 文件md5（可选，核心文件强烈建议带上，便于完整性校验和复用）
/// @param visibility 任务感知属性
/// @param priority 任务优先级
- (YYBAria2Task *)createDownloadTaskWithURL:(NSString *)url
                                    destDir:(nullable NSString *)destDir
                                   fileName:(nullable NSString *)fileName
                                        md5:(nullable NSString *)md5
                                 visibility:(YYBAria2TaskVisibility)visibility
                                   priority:(YYBAria2TaskPriority)priority;

/// 启动任务
/// @param task 任务task（进度和结果在task的属性中设置block或代理）
- (void)startTask:(YYBAria2Task *)task;

/// 取消任务
/// @param task 任务对象
/// @param deleteFile 是否同时删除已下载的文件（包括未完成的临时文件）
- (void)cancelTask:(YYBAria2Task *)task deleteFile:(BOOL)deleteFile;

/// 暂停任务
- (void)pauseTask:(YYBAria2Task *)task completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;

/// 恢复任务
- (void)resumeTask:(YYBAria2Task *)task completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;

/// 查询所有“有效”任务（不含已删除/移除任务）
- (NSArray<YYBAria2Task *> *)allTasks;

/// 清理所有任务（可选是否删除文件）
/// @param deleteFiles 是否同时删除所有相关文件
- (void)clearAllTasksDeleteFiles:(BOOL)deleteFiles;

/// 查询单个任务
- (YYBAria2Task *)taskForTaskId:(NSString *)taskId;

/// 根据md5查找本地已下载完成的文件
/// @param md5 文件md5
- (nullable YYBAria2Task *)findLocalCompleteFileWithMD5:(NSString *)md5;

/// RPC接口（供JSB桥接/web业务侧直接调用）
/// method: aria2.addUri/aria2.pause/aria2.unpause/aria2.remove/aria2.tellStatus等
/// params: 参数数组
/// source: 来源
/// completion: 回调
- (void)sendJSONRPCRequestWithMethod:(NSString *)method
                              params:(nullable id)params
                              source:(YYBAria2TaskSource)source
                          completion:(void(^)(NSDictionary * _Nullable result, NSData * _Nullable rawData, NSError * _Nullable error))completion;


#pragma mark - listener
/// 注册YYBDownloadListener（支持多对一监听）
- (void)addDownloadListener:(id<YYBDownloadListener>)listener;
- (void)removeDownloadListener:(id<YYBDownloadListener>)listener;
/// 同步查询获取当前的下载任务信息
- (nullable YYBAria2Task *)getAppDownloadTaskInfo:(id<YYBDownloadListener>)listener;

@end

NS_ASSUME_NONNULL_END

//
//  YYBLibAria2ServiceFacade.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/18.
//

#import "YYBLibAria2ServiceFacade.h"
#import "YYBLibAria2SessionManager.h"
#import "YYBLibAria2EventCenter.h"
#import "YYBLibAria2ConfigManager.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBFile.h"
#import "MacroUtils.h"

#define kLogTag @"YYBLibAria2ServiceFacade"

@interface YYBLibAria2ServiceFacade ()
@property (nonatomic, strong) YYBLibAria2SessionManager *sessionManager;

@property (nonatomic, strong) dispatch_queue_t syncQueue;
@property (nonatomic, assign) void *syncQueueKey;
@end

@implementation YYBLibAria2ServiceFacade

+ (instancetype)sharedService {
    static YYBLibAria2ServiceFacade *svc;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        svc = [[YYBLibAria2ServiceFacade alloc] init];
    });
    return svc;
}

- (instancetype)init {
    if (self = [super init]) {
        _eventCenter = [[YYBLibAria2EventCenter alloc] init];
        _sessionManager = [[YYBLibAria2SessionManager alloc] init];

        _syncQueue = dispatch_queue_create("com.yyb.YYBLibAria2.service.sync", DISPATCH_QUEUE_SERIAL);

        // 队列自锁检测key
        static const void *kSyncQueueKey = &kSyncQueueKey;
        _syncQueueKey = (void *)kSyncQueueKey;
        dispatch_queue_set_specific(_syncQueue, kSyncQueueKey, (void *)kSyncQueueKey, NULL);

        // 事件桥接，务必异步分发，防止回调链路死锁
        __weak typeof(self) weakSelf = self;
        _sessionManager.runningStatusCallback = ^(BOOL isRunning) {
            [weakSelf updateRunningStatus:isRunning];
        };
        
        [_sessionManager setDownloadEventCallback:^(int event, NSString *taskId, NSDictionary *info) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
                [weakSelf.eventCenter dispatchEvent:(YYBLibAria2DownloadEvent)event taskId:taskId info:info];
            });
        }];
        
        // 放到上层去做调度来补全进度回调
//        // 批量进度桥接
//        [_sessionManager setDownloadBatchProgressCallback:^(NSDictionary<NSString *, NSDictionary *> *progressDict) {
//            // 分流到EventCenter
//            [weakSelf.eventCenter dispatchBatchProgressEvent:progressDict];
//        }];
    }
    return self;
}

- (NSString *)currentDownloadDir {
    return [YYBLibAria2ConfigManager sharedManager].downloadDir;
}

- (NSString *)sharedDownloadDir {
    return [[YYBLibAria2ConfigManager sharedManager] sharedDownloadDir];
}

- (void)startWithCompletion:(void(^)(BOOL, NSError * _Nullable))completion {
    dispatch_async(_syncQueue, ^{
        if (self.isRunning) {
            if (completion) {
                DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(YES, nil); });
            }
            return;
        }
        [self.sessionManager startWithCompletion:^(BOOL ok, NSError *err) {
            YYBMacLogInfo(kLogTag, @"[startWithCompletion] success = %@, err = %@", @(ok), err);
            if (completion) {
                DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(ok, err); });
            }
        }];
    });
}

- (void)stopWithCompletion:(void(^_Nullable)(void))completion {
    dispatch_async(_syncQueue, ^{
        if (!self.isRunning) {
            if (completion) {
                DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(); });
            }
            return;
        }
        [self.sessionManager stopWithCompletion:^{
            YYBMacLogInfo(kLogTag, @"[stopWithCompletion]");
            if (completion) {
                DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(); });
            }
        }];
    });
}

- (void)updateRunningStatus:(BOOL)running {
    YYBMacLogInfo(kLogTag, @"[updateRunningStatus] running = %@", @(running));
    [[NSNotificationCenter defaultCenter] postNotificationName:kYYBLibAria2ServiceStatusChangedNotification
                                                        object:self
                                                      userInfo:@{@"running": @(running)}];
    [self.eventCenter dispatchRunningStatusChange:running];
}

- (void)addRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener {
    [self.eventCenter addRunningStatusListener:listener];
}

- (void)removeRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener {
    [self.eventCenter removeRunningStatusListener:listener];
}

- (void)addDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener {
    [self.eventCenter addDownloadEventListener:listener];
}
- (void)removeDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener {
    [self.eventCenter removeDownloadEventListener:listener];
}

- (void)addBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener {
    [self.eventCenter addBatchProgressListener:listener];
}

- (void)removeBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener {
    [self.eventCenter removeBatchProgressListener:listener];
}

- (void)addSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener {
    [self.eventCenter addSingleProgressListenerForTaskId:taskId listener:listener];
}

- (void)removeSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener {
    [self.eventCenter removeSingleProgressListenerForTaskId:taskId listener:listener];
}

- (BOOL)isRunning {
    return self.sessionManager.isRunning;
}

/// 清除所有Aria2下载任务和文件
- (void)cleanAllAria2Task:(void(^_Nullable)(void))completion {
    __weak typeof(self) weakSelf = self;
    [self stopWithCompletion:^{
        NSString *downloadDic = [weakSelf currentDownloadDir];
        if (downloadDic.length) {
            [YYBFile deleteFileAtPath:downloadDic error:nil];
        }
        if (completion) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(); });
        }
    }];
}

@end

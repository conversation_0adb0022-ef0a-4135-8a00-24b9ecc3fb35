//
//  YYBLibAria2ServiceFacade.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/18.
//  libAria2服务 对外主入口，组合各模块，线程安全、生命周期

#import <Foundation/Foundation.h>
#import "YYBLibAria2EventCenter.h"
#import "YYBLibAria2ConfigManager.h"
#import "YYBLibAria2SessionManager.h"

NS_ASSUME_NONNULL_BEGIN

#define kYYBLibAria2ServiceStatusChangedNotification @"kYYBLibAria2ServiceStatusChangedNotification"

@interface YYBLibAria2ServiceFacade : NSObject

@property (nonatomic, assign, readonly) BOOL isRunning;
@property (nonatomic, strong, readonly) YYBLibAria2SessionManager *sessionManager;
@property (nonatomic, strong) YYBLibAria2EventCenter *eventCenter;

/// 单例
+ (instancetype)sharedService;

/// 获取当前实际的通用下载目录
- (NSString *)currentDownloadDir;

/// 可共享的下载路径配置
- (NSString *)sharedDownloadDir;

/// 启动/停止
- (void)startWithCompletion:(void(^)(BOOL success, NSError * _Nullable error))completion;
- (void)stopWithCompletion:(void(^_Nullable)(void))completion;

// 下载服务变化-监听(注意listener的block需要自己持有和释放)
- (void)addRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener;
- (void)removeRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener;

/// 下载状态变化-监听(注意listener的block需要自己持有和释放)
- (void)addDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener;
- (void)removeDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener;

// 批量和单GID进度-监听(注意listener的block需要自己持有和释放)
- (void)addBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener;
- (void)removeBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener;
- (void)addSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener;
- (void)removeSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener;


/// 清除所有Aria2下载任务和文件
- (void)cleanAllAria2Task:(void(^_Nullable)(void))completion;

@end

NS_ASSUME_NONNULL_END

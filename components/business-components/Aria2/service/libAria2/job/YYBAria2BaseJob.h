//
//  YYBAria2BaseJob.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/25.
//

#import <Foundation/Foundation.h>
#import "YYBAria2JobProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBAria2BaseJob : NSObject <YYBAria2Job>

- (void)executeWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager;

/// 子类实现
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager;

/// 统计及打印耗时
- (void)analysisAndLogJobCostIfNeeded:(nullable id)extraInfo;

/// 统计耗时（可控制是否打印，超时必定打印）
- (void)analysisJobCostIfNeeded:(nullable id)extraInfo needLog:(BOOL)needLog;

@end

NS_ASSUME_NONNULL_END

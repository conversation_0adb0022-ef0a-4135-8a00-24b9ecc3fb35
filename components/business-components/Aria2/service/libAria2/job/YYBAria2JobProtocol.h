//
//  YYBAria2JobProtocol.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/22.
//

// YYBAria2JobProtocol.h
#import <Foundation/Foundation.h>
@class YYBLibAria2SessionManager;
namespace aria2 { class Session; }

/// Job协议：所有API操作都实现为Job，下载线程串行执行
@protocol YYBAria2Job <NSObject>

- (void)executeWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager;

@end

/// Notify协议：所有事件/进度回调都实现为Notify，主线程回调
@protocol YYBAria2Notify <NSObject>

- (void)notifyWithManager:(YYBLibAria2SessionManager*)manager;

@end

//
//  YYBAria2JobImpl.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/22.
//

#import <Foundation/Foundation.h>
#import "YYBAria2BaseJob.h"

/// 添加下载任务Job
@interface YYBAria2AddUriJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSDictionary *options;
@property (nonatomic, copy) void(^completion)(NSString *gid, NSError *error);
@end

/// 暂停任务Job
@interface YYBAria2PauseJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^completion)(BOOL, NSError *);
@end

/// 恢复任务Job
@interface YYBAria2ResumeJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^completion)(BOOL, NSError *);
@end

/// 删除任务Job
@interface YYBAria2RemoveJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^completion)(BOOL, NSError *);
@end

/// 修改任务参数Job
@interface YYBAria2ChangeOptionJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) NSDictionary *options;
@property (nonatomic, copy) void(^completion)(BOOL, NSError *);
@end

/// 强制暂停任务Job
@interface YYBAria2ForcePauseJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^completion)(BOOL, NSError *);
@end

/// 批量暂停Job
@interface YYBAria2PauseAllJob : YYBAria2BaseJob
@property (nonatomic, copy) NSArray<NSString *> *gids;
@property (nonatomic, copy) void(^completion)(NSArray<NSString *> *pausedGIDs, NSError *);
@end

/// 批量恢复Job
@interface YYBAria2UnpauseAllJob : YYBAria2BaseJob
@property (nonatomic, copy) NSArray<NSString *> *gids;
@property (nonatomic, copy) void(^completion)(NSArray<NSString *> *unpausedGIDs, NSError *);
@end

/// 修改全局参数Job
@interface YYBAria2ChangeGlobalOptionJob : YYBAria2BaseJob
@property (nonatomic, copy) NSDictionary *options;
@property (nonatomic, copy) void(^completion)(BOOL, NSError *);
@end

/// 查询任务进度Job
@interface YYBAria2ProgressQueryJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^completion)(NSDictionary *progress);

@end

/// 查询全局状态Job
@interface YYBAria2GlobalStatQueryJob : YYBAria2BaseJob
@property (nonatomic, copy) void(^completion)(NSDictionary *stat);
@end

/// 查询活跃GID Job
@interface YYBAria2ActiveGIDsQueryJob : YYBAria2BaseJob
@property (nonatomic, copy) void(^completion)(NSArray<NSString *> *gids);
@end

/// 查询所有URI Job
@interface YYBAria2UrisQueryJob : YYBAria2BaseJob
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^completion)(NSArray<NSDictionary *> *uris);
@end

/// 打印当前完整配置
@interface YYBAria2LogGlobalOptionsJob: YYBAria2BaseJob
@end

/// 唤醒队列Job
@interface YYBAria2WakeupJob: YYBAria2BaseJob
@end

/// 进度通知
@interface YYBAria2ProgressNotify : NSObject <YYBAria2Notify>
@property (nonatomic, strong) NSArray *progressArray;
@property (nonatomic, copy) void(^callback)(NSArray *progressArray);
@end

/// 任务完成通知
@interface YYBAria2CompleteNotify : NSObject <YYBAria2Notify>
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) void(^callback)(NSString *gid);
@end

/// 任务错误通知
@interface YYBAria2ErrorNotify : NSObject <YYBAria2Notify>
@property (nonatomic, copy) NSString *gid;
@property (nonatomic, copy) NSError *error;
@property (nonatomic, copy) void(^callback)(NSString *gid, NSError *error);
@end


//
//  YYBAria2JobImpl.mm
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/22.
//

#import "YYBAria2JobImpl.h"
#import "YYBLibAria2SessionManager.h"
#import "aria2.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBAria2Error.h"

#define kLogTag @"YYBLibAria2SessionManager"

@interface YYBLibAria2SessionManager ()
- (void)registerKnownGID:(NSString *)gidHex;
- (void)clearProgressCacheForGID:(NSString *)gid;
- (NSDictionary *)progressDictFromDownloadHandle:(aria2::DownloadHandle *)dh gid:(NSString *)gid;
@end

#pragma mark - Job实现

@implementation YYBAria2AddUriJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    std::vector<std::string> uris = { [self.url UTF8String] };
    aria2::KeyVals kvs;
    for (NSString *key in self.options) {
        id value = self.options[key];
        NSString *valueString = [self.options[key] description];
        if ([value isKindOfClass:[NSNumber class]]) {
            const char *type = [value objCType];
            if (strcmp(type, @encode(BOOL)) == 0 || strcmp(type, @encode(char)) == 0) {
                BOOL boolValue = [value boolValue];
                valueString = boolValue ? @"true" : @"false";
            }
        }
        if (!key || !valueString) continue;
        kvs.push_back({[key UTF8String], [valueString UTF8String]});
    }
    aria2::A2Gid gid = 0;
    int rv = aria2::addUri(session, &gid, uris, kvs);
    NSString *gidHex = rv == 0 ? [NSString stringWithUTF8String:aria2::gidToHex(gid).c_str()] : nil;
    if (rv == 0) {
        [manager registerKnownGID:gidHex];
        YYBMacLogInfo(kLogTag, @"[AddUriJob] 添加下载成功: %@, gid=%@", self.url, gidHex);
    } else {
        YYBMacLogError(kLogTag, @"[AddUriJob] 添加下载失败, code=%d, url=%@", rv, self.url);
    }
    
    [self analysisAndLogJobCostIfNeeded:gidHex];
    if (self.completion) {
        self.completion(gidHex, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"添加下载失败-addUri" userInfo:@{@"url": self.url ?: @""}]);
    }
}
@end

@implementation YYBAria2PauseJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    int rv = aria2::pauseDownload(session, agid);
    if (rv == 0) {
        [manager registerKnownGID:self.gid];
        [manager clearProgressCacheForGID:self.gid];
        YYBMacLogInfo(kLogTag, @"[PauseJob] 暂停成功, gid=%@", self.gid);
    } else {
        YYBMacLogError(kLogTag, @"[PauseJob] 暂停失败, code=%d, gid=%@", rv, self.gid);
    }
    
    [self analysisAndLogJobCostIfNeeded:nil];
    if (self.completion) {
        self.completion(rv == 0, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"暂停失败-pause" userInfo:@{@"gid": self.gid ?: @""}]);
    }
}
@end

@implementation YYBAria2ResumeJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    int rv = aria2::unpauseDownload(session, agid);
    if (rv == 0) {
        [manager registerKnownGID:self.gid];
        [manager clearProgressCacheForGID:self.gid];
        YYBMacLogInfo(kLogTag, @"[ResumeJob] 恢复成功, gid=%@", self.gid);
    } else {
        YYBMacLogError(kLogTag, @"[ResumeJob] 恢复失败, code=%d, gid=%@", rv, self.gid);
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gid];
    if (self.completion) {
        self.completion(rv == 0, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"恢复失败-resume" userInfo:@{@"gid": self.gid ?: @""}]);
    }
}
@end

@implementation YYBAria2RemoveJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    int rv = aria2::removeDownload(session, agid);
    if (rv == 0) {
        [manager registerKnownGID:self.gid];
        [manager clearProgressCacheForGID:self.gid];
        YYBMacLogInfo(kLogTag, @"[RemoveJob] 删除成功, gid=%@", self.gid);
    } else {
        YYBMacLogError(kLogTag, @"[RemoveJob] 删除失败, code=%d, gid=%@", rv, self.gid);
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gid];
    if (self.completion) {
        self.completion(rv == 0, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"删除失败-remove" userInfo:@{@"gid": self.gid ?: @""}]);
    }
}
@end

@implementation YYBAria2ChangeOptionJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    aria2::KeyVals kvs;
    for (NSString *key in self.options) {
        NSString *value = [self.options[key] description];
        kvs.push_back({[key UTF8String], [value UTF8String]});
    }
    int rv = aria2::changeOption(session, agid, kvs);
    if (rv == 0) {
        YYBMacLogInfo(kLogTag, @"[ChangeOptionJob] 成功, gid=%@, kvs=%@", self.gid, self.options);
    } else {
        YYBMacLogError(kLogTag, @"[ChangeOptionJob] 失败, code=%d, gid=%@", rv, self.gid);
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gid];
    if (self.completion) {
        self.completion(rv == 0, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"任务参数修改失败-changeOption" userInfo:@{@"gid": self.gid, @"options": self.options ?: @""}]);
    }
}
@end

@implementation YYBAria2ForcePauseJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    int rv = aria2::pauseDownload(session, agid, true); // force = true
    if (rv == 0) {
        [manager registerKnownGID:self.gid];
        [manager clearProgressCacheForGID:self.gid];
        YYBMacLogInfo(kLogTag, @"[ForcePauseJob] 强制暂停成功, gid=%@", self.gid);
    } else {
        YYBMacLogError(kLogTag, @"[ForcePauseJob] 强制暂停失败, code=%d, gid=%@", rv, self.gid);
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gid];
    if (self.completion) {
        self.completion(rv == 0, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"强制暂停失败-forcePause" userInfo:@{@"gid": self.gid ?: @""}]);
    }
}
@end

@implementation YYBAria2PauseAllJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    NSMutableArray *pausedGIDs = [NSMutableArray array];
    for (NSString *gid in self.gids) {
        aria2::A2Gid agid = aria2::hexToGid([gid UTF8String]);
        int rv = aria2::pauseDownload(session, agid);
        if (rv == 0) {
            [pausedGIDs addObject:gid];
            [manager registerKnownGID:gid];
            [manager clearProgressCacheForGID:gid]; //只清理实际变更的GID
            YYBMacLogInfo(kLogTag, @"[PauseAllJob] 暂停成功, gid=%@", gid);
        } else {
            YYBMacLogError(kLogTag, @"[PauseAllJob] 暂停失败, code=%d, gid=%@", rv, gid);
        }
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gids];
    if (self.completion) {
        self.completion(pausedGIDs, nil);
    }
}
@end

@implementation YYBAria2UnpauseAllJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    NSMutableArray *unpausedGIDs = [NSMutableArray array];
    for (NSString *gid in self.gids) {
        aria2::A2Gid agid = aria2::hexToGid([gid UTF8String]);
        int rv = aria2::unpauseDownload(session, agid);
        if (rv == 0) {
            [unpausedGIDs addObject:gid];
            [manager registerKnownGID:gid];
            [manager clearProgressCacheForGID:gid]; // 只清理实际变更的GID
            YYBMacLogInfo(kLogTag, @"[UnpauseAllJob] 恢复成功, gid=%@", gid);
        } else {
            YYBMacLogError(kLogTag, @"[UnpauseAllJob] 恢复失败, code=%d, gid=%@", rv, gid);
        }
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gids];
    if (self.completion) {
        self.completion(unpausedGIDs, nil);
    }
}
@end

@implementation YYBAria2ChangeGlobalOptionJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::KeyVals kvs;
    for (NSString *key in self.options) {
        id value = self.options[key];
        NSString *valueString = [self.options[key] description];
        if ([value isKindOfClass:[NSNumber class]]) {
            const char *type = [value objCType];
            if (strcmp(type, @encode(BOOL)) == 0 || strcmp(type, @encode(char)) == 0) {
                BOOL boolValue = [value boolValue];
                valueString = boolValue ? @"true" : @"false";
            }
        }
        if (!key || !valueString) continue;
        kvs.push_back({[key UTF8String], [valueString UTF8String]});
    }
    int rv = aria2::changeGlobalOption(session, kvs);
    if (rv == 0) {
        YYBMacLogInfo(kLogTag, @"[ChangeGlobalOptionJob] 成功, kvs=%@", self.options);
    } else {
        YYBMacLogError(kLogTag, @"[ChangeGlobalOptionJob] 失败, code=%d, kvs=%@", rv, self.options);
    }
    
    [self analysisAndLogJobCostIfNeeded:nil];
    if (self.completion) {
        self.completion(rv == 0, rv == 0 ? nil : [YYBAria2Error errorWithCode:rv message:@"全局参数修改失败-changeGlobalOption" userInfo:@{@"options": self.options ?: @""}]);
    }
}
@end

@implementation YYBAria2ProgressQueryJob

- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    aria2::DownloadHandle *dh = aria2::getDownloadHandle(session, agid);
    NSDictionary *result = [manager progressDictFromDownloadHandle:dh gid:self.gid];
    if (dh) aria2::deleteDownloadHandle(dh);
    
    [self analysisJobCostIfNeeded:self.gid needLog:NO];  // 查询类过于频繁除非调试不打印日志
    if (self.completion) {
        self.completion(result);
    }
}

@end

@implementation YYBAria2GlobalStatQueryJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    aria2::GlobalStat s = aria2::getGlobalStat(session);
    NSDictionary *result = @{
        @"downloadSpeed": @(s.downloadSpeed),
        @"uploadSpeed": @(s.uploadSpeed),
        @"numActive": @(s.numActive),
        @"numWaiting": @(s.numWaiting),
        @"numStopped": @(s.numStopped),
        @"numStoppedTotal":@(s.numStopped),
    };
    
    [self analysisAndLogJobCostIfNeeded:nil];
    if (self.completion) {
        self.completion(result);
    }
}
@end

@implementation YYBAria2ActiveGIDsQueryJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    NSMutableArray *arr = [NSMutableArray array];
    std::vector<aria2::A2Gid> gids = aria2::getActiveDownload(session);
    for (auto &gid : gids) {
        [arr addObject:[NSString stringWithUTF8String:aria2::gidToHex(gid).c_str()]];
    }
    
    [self analysisJobCostIfNeeded:nil needLog:NO];  // 查询类过于频繁除非调试不打印日志
    if (self.completion) {
        self.completion(arr);
    }
}
@end

@implementation YYBAria2UrisQueryJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    NSMutableArray *result = [NSMutableArray array];
    aria2::A2Gid agid = aria2::hexToGid([self.gid UTF8String]);
    aria2::DownloadHandle *dh = aria2::getDownloadHandle(session, agid);
    if (dh) {
        std::vector<aria2::FileData> files = dh->getFiles();
        for (const auto& file : files) {
            for (const auto& uriData : file.uris) {
                NSString *uri = [NSString stringWithUTF8String:uriData.uri.c_str()];
                NSString *status = @"unknown";
                switch (uriData.status) {
                    case aria2::URI_USED: status = @"used"; break;
                    case aria2::URI_WAITING: status = @"waiting"; break;
                    default: status = @"unknown"; break;
                }
                [result addObject:@{@"uri": uri ?: @"", @"status": status}];
            }
        }
        aria2::deleteDownloadHandle(dh);
    } else {
        YYBMacLogInfo(kLogTag, @"[UrisQueryJob] 获取DownloadHandle失败, gid=%@", self.gid);
    }
    
    [self analysisAndLogJobCostIfNeeded:self.gid];
    if (self.completion) {
        self.completion(result);
    }
}
@end


/// 打印当前完整配置
@implementation YYBAria2LogGlobalOptionsJob
- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)mgr {
    // 打印全局参数
    aria2::KeyVals opts = aria2::getGlobalOptions(session);
    NSMutableString *globalOptStr = [NSMutableString string];
    for (const auto& kv : opts) {
        [globalOptStr appendFormat:@"%@=%@; ", @(kv.first.c_str()), @(kv.second.c_str())];
    }
    YYBMacLogInfo(kLogTag, @"[logAllTaskStatus] GlobalOptions: %@", globalOptStr);
    
    [self analysisAndLogJobCostIfNeeded:nil];
}
@end

/// 唤醒队列Job
@implementation YYBAria2WakeupJob

- (void)doExecuteWithSession:(aria2::Session *)session manager:(id)manager {
    [self analysisAndLogJobCostIfNeeded:nil];
}

@end

#pragma mark - Notify实现

@implementation YYBAria2ProgressNotify

- (void)notifyWithManager:(YYBLibAria2SessionManager*)manager {
    if (self.callback) self.callback(self.progressArray);
}

@end

@implementation YYBAria2CompleteNotify

- (void)notifyWithManager:(YYBLibAria2SessionManager*)manager {
    if (self.callback) self.callback(self.gid);
}

@end

@implementation YYBAria2ErrorNotify

- (void)notifyWithManager:(YYBLibAria2SessionManager*)manager {
    if (self.callback) self.callback(self.gid, self.error);
}

@end

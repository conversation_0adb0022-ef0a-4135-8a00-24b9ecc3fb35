#import "YYBAria2SyncQueue.h"
#import <mutex>
#import <queue>
#import <condition_variable>

@interface YYBAria2SyncQueue ()
{
    std::queue<id> _queue;                // 队列容器
    std::mutex _mutex;                    // 互斥锁，保护队列
    std::condition_variable _cv;          // 条件变量，用于线程等待/唤醒
    bool _hasWakeupEvent;                 // 有唤醒标记
    bool _destroyed;                      // 析构标记
}
@end

@implementation YYBAria2SyncQueue

- (instancetype)init
{
    self = [super init];
    if (self) {
        _hasWakeupEvent = false;
        _destroyed = false;
    }
    return self;
}

- (void)dealloc {
    {
        std::lock_guard<std::mutex> l(_mutex);
        _destroyed = true;
        _hasWakeupEvent = true;
    }
    _cv.notify_all();
}

/// 入队，默认不唤醒
- (void)push:(id)obj {
    [self push:obj wakeup:NO];
}

/// 入队，wakeup=YES时唤醒等待线程，NO时不唤醒
- (void)push:(id)obj wakeup:(BOOL)wakeup {
    {
        std::lock_guard<std::mutex> l(_mutex);
        _queue.push(obj);
        if (wakeup) {
            _hasWakeupEvent = true;     // 放在锁里面
        }
    }
    if (wakeup) {
        _cv.notify_one();
    }
}

/// 阻塞出队，直到有元素或100ms超时
- (id)pop {
    return [self popWithTimeout:100];
}

/// pop，带超时（单位：毫秒），超时返回 nil
- (id)popWithTimeout:(double)timeoutMs {
    std::unique_lock<std::mutex> l(_mutex);
    if (_destroyed) return nil;
    if (_hasWakeupEvent && !_queue.empty()) {
        // 有待唤醒的事件，直接处理
        id obj = _queue.front();
        _queue.pop();
        // 处理到最末尾时将标记还原
        if (_queue.empty()) {
            _hasWakeupEvent = false;
        }
        return obj;
    }
    bool hasData = _cv.wait_for(l, std::chrono::milliseconds((int)timeoutMs), [self]{ return _destroyed || !_queue.empty(); });
    if (_destroyed) return nil;
    if (hasData && !_queue.empty()) {
        id obj = _queue.front();
        _queue.pop();
        // 处理到最末尾时将标记还原
        if (_queue.empty()) {
            _hasWakeupEvent = false;
        }
        return obj;
    } else {
        // 超时
        return nil;
    }
}

/// 等待（超时或有人调用了wakeup）
/// 只等待，不消费队列元素
- (void)waitForJobOrTimeout:(double)timeoutMs {
    std::unique_lock<std::mutex> l(_mutex);
    if (_destroyed) return;
    if (_hasWakeupEvent && !_queue.empty()) {
        // 有待唤醒的事件，直接跳过排队
        return;
    }
    // 等待条件变量被唤醒，或超时
    _cv.wait_for(l, std::chrono::milliseconds((int)timeoutMs));
    // 不 pop，不消费队列
}

/// 队列是否为空
- (BOOL)isEmpty {
    std::lock_guard<std::mutex> l(_mutex);
    return _queue.empty();
}

/// 清空队列
- (void)clear {
    std::lock_guard<std::mutex> l(_mutex);
    std::queue<id> empty;
    std::swap(_queue, empty);
}

/// 队列元素个数
- (NSUInteger)count {
    std::lock_guard<std::mutex> l(_mutex);
    return _queue.size();
}

@end

//
//  YYBAria2BaseJob.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/25.
//

#import "YYBAria2BaseJob.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

#define kLogTag @"YYBAria2BaseJob"

@interface YYBAria2BaseJob ()
@property (nonatomic, strong) NSDate *startTime;
@property (nonatomic, strong) NSDate *createTime;
@property (nonatomic, assign) NSTimeInterval jobTimeout; // job超时监控时间
@end

@implementation YYBAria2BaseJob

- (instancetype)init {
    if (self = [super init]) {
        self.createTime = [NSDate date];
        self.jobTimeout = 1.0;
    }
    return self;
}

- (void)executeWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    self.startTime = [NSDate date];
    [self doExecuteWithSession:session manager:manager];
}

- (void)doExecuteWithSession:(aria2::Session*)session manager:(YYBLibAria2SessionManager*)manager {
    // 子类实现
}

- (void)analysisAndLogJobCostIfNeeded:(nullable id)extraInfo {
    [self analysisJobCostIfNeeded:extraInfo needLog:YES];
}

- (void)analysisJobCostIfNeeded:(nullable id)extraInfo needLog:(BOOL)needLog {
    if (!self.createTime) return;
    if (!self.startTime) return;
    NSTimeInterval waitCost = [self.startTime timeIntervalSinceDate:self.createTime];
    NSTimeInterval doJobCost = [[NSDate date] timeIntervalSinceDate:self.startTime];
    NSTimeInterval allCost = [[NSDate date] timeIntervalSinceDate:self.createTime];
    NSString *className = NSStringFromClass([self class]);
    BOOL isTimeOut = allCost > self.jobTimeout;
    
    if (needLog || isTimeOut) {
        YYBMacLogInfo(kLogTag, @"[Job-%@] _time_aria2_job 等待耗时: %.3f ms, 执行任务耗时: %.3f ms, 总耗时: %.3f ms (是否超时：%@), info = %@",
                      className,
                      waitCost*1000,
                      doJobCost*1000,
                      allCost*1000,
                      @(isTimeOut),
                      extraInfo ?: @"");
    }
}

@end

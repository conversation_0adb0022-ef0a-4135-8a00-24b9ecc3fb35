//
//  YYBAria2SyncQueue.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/22.
//
//  线程安全队列，底层C++ std::queue+mutex+condition_variable
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 线程安全泛型队列，支持选择性唤醒和超时pop
@interface YYBAria2SyncQueue<ObjectType> : NSObject

/// 入队，默认不唤醒等待线程
- (void)push:(ObjectType)obj;

/// 入队，wakeup=YES时唤醒等待线程，NO时不唤醒（如进度查询等不需唤醒主循环的场景）
- (void)push:(ObjectType)obj wakeup:(BOOL)wakeup;

/// 等待（超时或有人调用了wakeup）
/// 只等待，不消费队列元素
- (void)waitForJobOrTimeout:(double)timeoutMs;

/// 阻塞出队，直到有元素或100ms超时
- (ObjectType)pop;

/// 队列是否为空
- (BOOL)isEmpty;

/// 清空队列
- (void)clear;

/// 队列元素个数
- (NSUInteger)count;

@end

NS_ASSUME_NONNULL_END

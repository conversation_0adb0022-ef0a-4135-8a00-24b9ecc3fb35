//
//  YYBLibAriaHealingManager.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/22.
//

// SelfHealingManager.h
#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

/// 自愈管理器，负责session重试、runloop自愈等
@interface YYBLibAriaHealingManager : NSObject

@property (nonatomic, assign) NSInteger sessionRetryCount;
@property (nonatomic, assign) NSInteger sessionFileRebuildCount;
@property (nonatomic, assign) NSInteger runLoopRecoverRetryCount;
@property (nonatomic, assign) NSInteger maxSessionRetryCount;
@property (nonatomic, assign) NSInteger maxSessionFileRebuildCount;
@property (nonatomic, assign) NSInteger maxRunLoopRecoverCount;
@property (nonatomic, assign) NSTimeInterval maxRetryInterval;
@property (nonatomic, assign) NSTimeInterval baseRetryInterval;
- (void)reset;
- (NSTimeInterval)exponentialBackoff:(NSInteger)retryCount;

@end

NS_ASSUME_NONNULL_END

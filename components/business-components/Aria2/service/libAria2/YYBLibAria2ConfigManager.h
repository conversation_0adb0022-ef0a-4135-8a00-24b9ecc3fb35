//
//  YYBLibAria2ConfigManager.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 配置管理，线程安全，支持动态参数
@interface YYBLibAria2ConfigManager : NSObject

@property (nonatomic, copy) NSString *downloadDir;         // 下载目录
@property (nonatomic, copy) NSString *logPath;             // 日志文件路径
@property (nonatomic, copy) NSString *sessionFilePath;     // aria2下载session路径(用来断点续传恢复)
@property (nonatomic, assign) NSUInteger maxConcurrent;    // 最大并发下载数
@property (nonatomic, assign) NSUInteger maxConnPerServer; // 每服务器最大连接数
@property (nonatomic, assign) NSUInteger split;            // 分片数
@property (nonatomic, assign) int downloadRunLoopMaxSleepTime;      // 下载runlooop的最大休眠时间（单位毫秒）

/// 单例
+ (instancetype)sharedManager;

/// 默认参数字典（key为aria2参数名，value为字符串）
- (NSDictionary<NSString *, NSString *> *)defaultAria2Options;

/// 校验目录是否存在，不存在或不为文件夹 则重建恢复
- (void)ensureDirectoryExists;

/// 可共享的下载路径配置
- (NSString *)sharedDownloadDir;

@end

NS_ASSUME_NONNULL_END

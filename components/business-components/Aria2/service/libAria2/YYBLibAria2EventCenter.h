//
//  YYBLibAria2EventCenter.h
//  YYBMacApp
//
//  Created by jamieling on 2025/7/4.
//  统一下载事件分发，线程安全
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, YYBLibAria2DownloadEvent) {
    YYBLibAria2DownloadEventUnknown = 0,
    YYBLibAria2DownloadEventStart,
    YYBLibAria2DownloadEventPause,
    YYBLibAria2DownloadEventStop,
    YYBLibAria2DownloadEventComplete,
    YYBLibAria2DownloadEventError,
    YYBLibAria2DownloadEventBTComplete
};

/// 下载服务状态变化
typedef void(^YYBLibAria2RunningStatusBlock)(BOOL isRunning);
/// 下载任务状态变化
typedef void(^YYBLibAria2DownloadEventBlock)(YYBLibAria2DownloadEvent event, NSString *taskId, NSDictionary *info);
/// 全量下载任务进度监听
typedef void(^YYBLibAria2BatchProgressBlock)(NSDictionary<NSString *, NSDictionary *> *progressDict);
/// 单个进度监听
typedef void(^YYBLibAria2SingleProgressBlock)(NSString *taskId, NSDictionary *progress);

/// 统一事件中心，支持Block/Delegate/WebSocket
@interface YYBLibAria2EventCenter : NSObject

// 服务运行状态监听
- (void)addRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener;
- (void)removeRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener;

/// 添加/移除Block监听(下载状态变化)
- (void)addDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener;
- (void)removeDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener;

// 批量进度监听
- (void)addBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener;
- (void)removeBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener;

// 单taskId进度监听
- (void)addSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener;
- (void)removeSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener;

/// 伪WebSocket事件
- (void)setWebSocketEventHandler:(void(^)(NSString *event, NSDictionary *payload, NSData *rawData))handler;

/// 分发事件（仅供内部调用）
- (void)dispatchRunningStatusChange:(BOOL)isRunning;
- (void)dispatchEvent:(YYBLibAria2DownloadEvent)event taskId:(NSString *)taskId info:(NSDictionary *)info;
- (void)dispatchBatchProgressEvent:(NSDictionary<NSString *, NSDictionary *> *)progressDict;

@end

NS_ASSUME_NONNULL_END

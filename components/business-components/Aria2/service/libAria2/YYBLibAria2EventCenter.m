//
//  YYBLibAria2EventCenter.m
//  YYBMacApp
//
//  Created by jamieling on 2025/7/4.
//  统一下载事件分发，线程安全，弱引用block监听，防止循环引用
//

#import "YYBLibAria2EventCenter.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "MacroUtils.h"

#define kLogTag @"YYBLibAria2EventCenter"

@interface YYBLibAria2EventCenter ()

@property (nonatomic, strong) NSHashTable<YYBLibAria2RunningStatusBlock> *runningStatusListeners;
@property (nonatomic, strong) NSLock *runningStatusLock;

@property (nonatomic, strong) NSHashTable<YYBLibAria2DownloadEventBlock> *eventListeners; // 弱引用block包装器集合
@property (nonatomic, strong) NSLock *eventLock;                              // 线程安全
@property (nonatomic, copy) void(^wsHandler)(NSString *, NSDictionary *, NSData *);

@property (nonatomic, strong) NSHashTable<YYBLibAria2BatchProgressBlock> *batchProgressListeners;
@property (nonatomic, strong) NSLock *batchProgressLock;

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSHashTable<YYBLibAria2SingleProgressBlock> *> *singleProgressListeners;
@property (nonatomic, strong) NSLock *singleProgressLock;

@end

@implementation YYBLibAria2EventCenter

- (instancetype)init {
    if (self = [super init]) {
        // NSHashTable weakObjects: 自动移除已释放的包装器
        _runningStatusListeners = [NSHashTable weakObjectsHashTable];
        _runningStatusLock = [NSLock new];
        _eventListeners = [NSHashTable weakObjectsHashTable];
        _eventLock = [NSLock new];
        _batchProgressListeners = [NSHashTable weakObjectsHashTable];
        _batchProgressLock = [NSLock new];
        _singleProgressListeners = [NSMutableDictionary dictionary];
        _singleProgressLock = [NSLock new];
        YYBMacLogInfo(kLogTag, @"[init] 事件中心初始化完成，采用弱引用block监听");
    }
    return self;
}

- (void)addRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener {
    if (!listener) return;
    [self.runningStatusLock lock];
    [self.runningStatusListeners addObject:listener];
    [self.runningStatusLock unlock];
    YYBMacLogInfo(kLogTag, @"[addRunningStatusListener] 添加running监听，当前监听数: %lu", (unsigned long)self.runningStatusListeners.allObjects.count);
}

- (void)removeRunningStatusListener:(YYBLibAria2RunningStatusBlock)listener {
    if (!listener) return;
    [self.runningStatusLock lock];
    [self.runningStatusListeners removeObject:listener];
    [self.runningStatusLock unlock];
    YYBMacLogInfo(kLogTag, @"[removeRunningStatusListener] 移除running监听，当前监听数: %lu", (unsigned long)self.runningStatusListeners.allObjects.count);
}

/// 添加Block监听（弱引用block，防止循环引用）
/// @param listener 事件回调block
- (void)addDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener {
    if (!listener) return;
    [self.eventLock lock];
    [self.eventListeners addObject:listener];
    [self.eventLock unlock];
    YYBMacLogInfo(kLogTag, @"[addDownloadEventListener] 添加block监听，当前监听数: %lu", (unsigned long)self.eventListeners.allObjects.count);
}

/// 移除Block监听（需传入原始block对象）
/// @param listener 事件回调block
- (void)removeDownloadEventListener:(YYBLibAria2DownloadEventBlock)listener {
    if (!listener) return;
    [self.eventLock lock];
    YYBLibAria2DownloadEventBlock toRemove = nil;
    for (YYBLibAria2DownloadEventBlock block in self.eventListeners) {
        // block本身无法直接比较，只能用isEqual:，但block的isEqual:只有同一实例才相等
        if (block == listener) {
            toRemove = listener;
        }
    }
    [self.eventListeners removeObject:toRemove];
    [self.eventLock unlock];
    YYBMacLogInfo(kLogTag, @"[removeDownloadEventListener] 移除block监听，当前监听数: %lu", (unsigned long)self.eventListeners.allObjects.count);
}

/// 设置WebSocket事件回调（强引用，需外部自行管理循环引用问题）
- (void)setWebSocketEventHandler:(void(^)(NSString *, NSDictionary *, NSData *))handler {
    self.wsHandler = [handler copy];
    YYBMacLogInfo(kLogTag, @"[setWebSocketEventHandler] 设置WebSocket事件回调");
}

// 批量进度监听
- (void)addBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener {
    if (!listener) return;
    [self.batchProgressLock lock];
    [self.batchProgressListeners addObject:listener];
    [self.batchProgressLock unlock];
    YYBMacLogInfo(kLogTag, @"[addBatchProgressListener] 添加批量进度监听，当前监听数: %lu", (unsigned long)self.batchProgressListeners.allObjects.count);
}
- (void)removeBatchProgressListener:(YYBLibAria2BatchProgressBlock)listener {
    if (!listener) return;
    [self.batchProgressLock lock];
    [self.batchProgressListeners removeObject:listener];
    [self.batchProgressLock unlock];
    YYBMacLogInfo(kLogTag, @"[removeBatchProgressListener] 移除批量进度监听，当前监听数: %lu", (unsigned long)self.batchProgressListeners.allObjects.count);
}

// 单taskId进度监听
- (void)addSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener {
    if (!taskId || !listener) return;
    [self.singleProgressLock lock];
    NSHashTable *table = self.singleProgressListeners[taskId];
    if (!table) {
        table = [NSHashTable weakObjectsHashTable];
        self.singleProgressListeners[taskId] = table;
    }
    [table addObject:listener];
    [self.singleProgressLock unlock];
    YYBMacLogInfo(kLogTag, @"[addSingleProgressListenerForTaskId] 添加taskId=%@进度监听，当前监听数: %lu", taskId, (unsigned long)table.allObjects.count);
}
- (void)removeSingleProgressListenerForTaskId:(NSString *)taskId listener:(YYBLibAria2SingleProgressBlock)listener {
    if (!taskId || !listener) return;
    [self.singleProgressLock lock];
    NSHashTable *table = self.singleProgressListeners[taskId];
    if (table) {
        [table removeObject:listener];
        if (table.count == 0) {
            [self.singleProgressListeners removeObjectForKey:taskId];
        }
    }
    [self.singleProgressLock unlock];
    YYBMacLogInfo(kLogTag, @"[removeSingleProgressListenerForTaskId] 移除taskId=%@进度监听", taskId);
}

- (void)dispatchRunningStatusChange:(BOOL)isRunning {
    [self.runningStatusLock lock];
    NSArray *listeners = self.runningStatusListeners.allObjects;
    [self.runningStatusLock unlock];
    YYBMacLogInfo(kLogTag, @"[dispatchRunningStatusChange] 分发running状态: %@, 监听数: %lu", @(isRunning), (unsigned long)listeners.count);
    for (YYBLibAria2RunningStatusBlock block in listeners) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            block(isRunning);
        });
    }
}

/// 分发事件到所有监听者（主线程回调，线程安全）
/// @param event 事件类型
/// @param taskId   任务taskId
/// @param info  附加信息
- (void)dispatchEvent:(YYBLibAria2DownloadEvent)event taskId:(NSString *)taskId info:(NSDictionary *)info {
    [self.eventLock lock];
    NSArray *arr = self.eventListeners.allObjects;
    [self.eventLock unlock];
    YYBMacLogInfo(kLogTag, @"[dispatchEvent] 分发事件: %ld, taskId=%@, info=%@, 当前监听数: %lu", (long)event, taskId, info, (unsigned long)arr.count);
    for (YYBLibAria2DownloadEventBlock block in arr) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            block(event, taskId, info);
        });
    }
    if (self.wsHandler) {
        NSString *eventName = [self eventNameForType:event];
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            self.wsHandler(eventName, info, nil);
        });
    }
}

// 批量进度分发
- (void)dispatchBatchProgressEvent:(NSDictionary<NSString *, NSDictionary *> *)progressDict {
    if (!progressDict || progressDict.count == 0) return;
    // 1. 批量监听
    [self.batchProgressLock lock];
    NSArray *batchListeners = self.batchProgressListeners.allObjects;
    [self.batchProgressLock unlock];
    YYBMacLogDebug(kLogTag, @"[dispatchBatchProgressEvent] 批量分发进度事件，任务数: %lu, 监听数: %lu", (unsigned long)progressDict.count, (unsigned long)batchListeners.count);
    for (YYBLibAria2BatchProgressBlock block in batchListeners) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            block(progressDict);
        });
    }
    
    // 2. 单taskId监听
    [self.singleProgressLock lock];
    NSDictionary *singleListeners = [self.singleProgressListeners copy];
    [self.singleProgressLock unlock];
    [progressDict enumerateKeysAndObjectsUsingBlock:^(NSString *taskId, NSDictionary *progress, BOOL *stop) {
        NSHashTable *table = singleListeners[taskId];
        if (!table) return;
        for (YYBLibAria2SingleProgressBlock block in table.allObjects) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
                block(taskId, progress);
            });
        }
    }];
}

/// 事件类型转字符串
- (NSString *)eventNameForType:(YYBLibAria2DownloadEvent)event {
    switch (event) {
        case YYBLibAria2DownloadEventStart:      return @"aria2.onDownloadStart";
        case YYBLibAria2DownloadEventPause:      return @"aria2.onDownloadPause";
        case YYBLibAria2DownloadEventStop:       return @"aria2.onDownloadStop";
        case YYBLibAria2DownloadEventComplete:   return @"aria2.onDownloadComplete";
        case YYBLibAria2DownloadEventError:      return @"aria2.onDownloadError";
        case YYBLibAria2DownloadEventBTComplete: return @"aria2.onBtDownloadComplete";
        default:                                 return @"aria2.unknown";
    }
}

@end

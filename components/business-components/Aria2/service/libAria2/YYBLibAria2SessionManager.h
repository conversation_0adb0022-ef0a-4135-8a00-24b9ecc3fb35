//
//  YYBLibAria2SessionManager.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/6/25.
//  C++桥接，线程安全
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// C++桥接，负责libaria2 session生命周期、任务管理、事件桥接
/// 线程安全：所有公有方法均为线程安全，内部自动串行化
/// 生命周期：start/stop成对调用，stop后所有资源释放
/// RAII：内部C++资源全部用unique_ptr管理
/// 事件回调：所有事件回调均在
@interface YYBLibAria2SessionManager : NSObject

/// 下载事件回调
/// event: 事件类型（int，见aria2文档）
/// gid:   任务GID（16进制字符串）
/// info:  事件附加信息（如gid、错误码等）
@property (nonatomic, copy, nullable) void (^eventCallback)(int event, NSString *gid, NSDictionary *info);

/// 运行状态回调
@property (nonatomic, copy, nullable) void (^runningStatusCallback)(BOOL isRunning);

// 批量进度回调注册接口
typedef void(^YYBLibAria2SessionBatchProgressCallback)(NSDictionary<NSString *, NSDictionary *> *progressDict);

/// 启动session
- (void)startWithCompletion:(void(^)(BOOL success, NSError *error))completion;

/// 停止session，释放所有资源
- (void)stopWithCompletion:(void(^)(void))completion;

/// 添加下载任务
/// @param url 下载链接
/// @param options 任务参数（如dir、out、header等）
/// @param completion 完成回调（返回gid或错误）
- (void)addDownloadWithURL:(NSString *)url
                   options:(NSDictionary *)options
                completion:(void(^)(NSString * _Nullable gid, NSError * _Nullable error))completion;

/// 暂停任务
/// @param gid 任务GID
/// @param completion 完成回调（返回是否成功/错误）
- (void)pauseDownload:(NSString *)gid
           completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

/// 恢复任务
/// @param gid 任务GID
/// @param completion 完成回调（返回是否成功/错误）
- (void)resumeDownload:(NSString *)gid
            completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

/// 删除任务
/// @param gid 任务GID
/// @param completion 完成回调（返回是否成功/错误）
- (void)removeDownload:(NSString *)gid
            completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

/// 查询任务进度
/// @param gid 任务GID
/// @param completion 完成回调（返回进度字典，字段见实现）
- (void)progressForGID:(NSString *)gid
            completion:(void(^)(NSDictionary *progress))completion;

/// 查询全局任务状态
/// @param completion 完成回调（返回全局状态字典，字段见实现）
- (void)globalStatWithCompletion:(void(^)(NSDictionary *stat))completion;

/// 获取所有活跃GID
/// @param completion 完成回调（返回GID数组）
- (void)activeDownloadGIDsWithCompletion:(void(^)(NSArray<NSString *> *gids))completion;

/// 获取所有已知GID（包括活跃、等待、暂停、已完成、已失败、已移除）（线程安全）
/// @return GID数组
- (NSArray<NSString *> *)allKnownGIDs;

/// 动态修改全局参数
/// @param keyVals 参数字典
/// @param completion 完成回调（返回是否成功/错误）
- (void)changeGlobalOption:(NSDictionary *)keyVals
                completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

/// 动态修改任务的下载参数
/// @param keyVals 参数字典
/// @param gid 任务GID
/// @param completion 完成回调（返回是否成功/错误）
- (void)changeOption:(NSDictionary *)keyVals
              forGid:(NSString *)gid
          completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

/// 注册/注销C++事件回调（只允许注册一个，stop时自动注销）
/// @param callback 事件回调block
- (void)setDownloadEventCallback:(void(^)(int event, NSString *gid, NSDictionary *info))callback;

/// 批量监听进度变化回调
- (void)setDownloadBatchProgressCallback:(YYBLibAria2SessionBatchProgressCallback)callback;


/// 当前session是否已启动
@property (nonatomic, assign, readonly) BOOL isRunning;

/// 强制暂停单个任务
- (void)forcePauseDownload:(NSString *)gid completion:(void(^)(BOOL success, NSError *error))completion;

/// 暂停所有任务
- (void)pauseAllWithCompletion:(void(^)(NSArray<NSString *> *pausedGIDs, NSError *error))completion;

/// 恢复所有任务
- (void)unpauseAllWithCompletion:(void(^)(NSArray<NSString *> *unpausedGIDs, NSError *error))completion;

/// 查询任务的所有URI及其状态
- (void)urisForGID:(NSString *)gid completion:(void(^)(NSArray<NSDictionary *> *uris))completion;

@end

NS_ASSUME_NONNULL_END

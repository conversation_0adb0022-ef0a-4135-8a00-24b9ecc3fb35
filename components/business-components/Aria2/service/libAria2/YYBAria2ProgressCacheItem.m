//
//  YYBAria2ProgressCacheItem.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/8.
//

#import "YYBAria2ProgressCacheItem.h"

@implementation YYBAria2ProgressCacheItem

- (BOOL)updateWithProgressDict:(NSDictionary *)progress {
    if (!progress || !progress.count) return NO;
    NSString *status = progress[@"status"];
    int64_t totalLength = [progress[@"totalLength"] longLongValue];
    int64_t completedLength = [progress[@"completedLength"] longLongValue];
    int64_t downloadSpeed = [progress[@"downloadSpeed"] longLongValue];
    
    BOOL changed = [self haveChanged:status
                      newTotalLength:totalLength
                  newCompletedLength:completedLength
                    newDownloadSpeed:downloadSpeed];
    
    self.progressDict = progress;
    self.lastStatus = status;
    self.totalFileSize = totalLength;
    self.lastCompletedFileSize = completedLength;
    self.downloadSpeed = downloadSpeed;
    self.lastPushTime = [NSDate date];
    return changed;
}

- (BOOL)haveChanged:(NSString *)newStatus
     newTotalLength:(int64_t)newTotalLength
 newCompletedLength:(int64_t)newCompletedLength
   newDownloadSpeed:(int64_t)newDownloadSpeed {
    if (![self.lastStatus isEqualToString:newStatus]) {
        // 状态变更
        return YES;
    }
    
    if (newTotalLength <= 0) {
        // 请求头还没下来，放行
        return YES;
    }
    
    if (self.totalFileSize != newTotalLength ||
        self.lastCompletedFileSize != newCompletedLength ||
        self.downloadSpeed != newDownloadSpeed) {
        return YES;
    }
    
    return NO;
}

- (BOOL)shouldPush {
    // removed状态只回调一次
    if ([self.lastStatus isEqualToString:@"removed"]) {
        return !self.removedCallbacked;
    }
    
    // 变化检测
    if (!self.lastPushDict) return YES;
    
    NSString *status = self.lastPushDict[@"status"];
    int64_t totalLength = [self.lastPushDict[@"totalLength"] longLongValue];
    int64_t completedLength = [self.lastPushDict[@"completedLength"] longLongValue];
    int64_t downloadSpeed = [self.lastPushDict[@"downloadSpeed"] longLongValue];
    
    return [self haveChanged:status
              newTotalLength:totalLength
          newCompletedLength:completedLength
            newDownloadSpeed:downloadSpeed];
}


- (void)markPushed {
    self.lastPushDict = [self.progressDict copy];
    self.lastPushTime = [NSDate date];
    if ([self.lastStatus isEqualToString:@"removed"]) {
        self.removedCallbacked = YES;
    }
}

@end

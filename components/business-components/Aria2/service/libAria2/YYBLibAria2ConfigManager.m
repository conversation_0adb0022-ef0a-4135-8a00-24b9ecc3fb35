//
//  YYBLibAria2ConfigManager.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/18.
//

#import "YYBLibAria2ConfigManager.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacShiplyConfig.h>
#import <YYBMacFusionSDK/YYBMacLogUpload.h>
#import "YYBDirectory.h"

#define kLogTag @"YYBLibAria2ConfigManager"

/// 默认下载目录名
#define YYB_ARIA2_DEFAULT_DOWNLOAD_DIR   @"Aria2Download"
/// 默认Session目录名
#define YYB_ARIA2_DEFAULT_SESSION_DIR   @"Aria2Session"
/// 默认最大并发下载数(设置为较大值，通过业务 层再去隔离或管控优先级和个数)
#define YYB_ARIA2_DEFAULT_MAX_CONCURRENT 20
/// 默认每服务器最大连接数
#define YYB_ARIA2_DEFAULT_MAX_CONN_PER_SERVER 16
/// 默认分片数
#define YYB_ARIA2_DEFAULT_SPLIT 16
// 参数远端key
static NSString *const kYYBLibAria2ArgumentsKey = @"yyb_aria2_arguments";

/// 无任务时，下载runloop的最大休眠时间 （单位：毫秒）
#define YYB_LIBARIA2_DOWNLOAD_LOOP_MAX_SLEEP_TIME 500

// 参数远端key
static NSString *const kYYBLibAria2DownloadLoopMaxSleepTimeKey = @"yyb_lib_aria2_download_loop_max_sleep_time";

static NSString *const kDefaultSharedDir = @"yybShared";

@interface YYBLibAria2ConfigManager ()

@property (nonatomic, strong) NSArray<NSString *> *severArguments; // 远端参数

@end

@implementation YYBLibAria2ConfigManager

+ (instancetype)sharedManager {
    static YYBLibAria2ConfigManager *mgr;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        mgr = [[YYBLibAria2ConfigManager alloc] init];
        [mgr loadConfig];
    });
    return mgr;
}

- (NSString *)sharedDownloadDir {
    // 默认用沙盒缓存目录
    NSString *bundleId = [[NSBundle mainBundle] bundleIdentifier] ?: @"com.tencent.yybmac";
    NSString *superDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) firstObject];
    NSString *sharedDir = [[superDir stringByAppendingPathComponent:bundleId] stringByAppendingPathComponent:kDefaultSharedDir];
    // 每次都确保目录存在
    return [YYBDirectory ensureDirectoryExists:sharedDir];
}

- (NSString *)currentDownloadDir {
    // 默认用沙盒缓存目录
    NSString *bundleId = [[NSBundle mainBundle] bundleIdentifier] ?: @"com.tencent.yybmac";
    NSString *cacheDir = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) firstObject];
    NSString *downloadDir = [[cacheDir stringByAppendingPathComponent:bundleId] stringByAppendingPathComponent:YYB_ARIA2_DEFAULT_DOWNLOAD_DIR];
    
    // 每次都确保目录存在
    return [YYBDirectory ensureDirectoryExists:downloadDir];
}

- (NSString *)currentLogPath {
    NSString *logDir = [[self currentDownloadDir] stringByAppendingPathComponent:@"Aria2Logs"];
    // 每次都确保目录存在
    [YYBDirectory ensureDirectoryExists:logDir];
    
    // 清理7天前的日志文件
    [self cleanOldLogFilesInDirectory:logDir maxAge:7*24*60*60];
    
    // 加到诊断日志打包中
    [[YYBMacLogUpload sharedInstance] addExtraFilePath:logDir];
    
    // 日期字符串
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyyMMdd"];
    NSString *dateString = [formatter stringFromDate:[NSDate date]];
    
    // 文件名加上日期
    NSString *logPath = [logDir stringByAppendingPathComponent:
                         [NSString stringWithFormat:@"libAria2-%@.log", dateString]];
    
    YYBMacLogInfo(kLogTag, @"aria2c log path = %@", logPath);
    return logPath;
}

- (NSString *)createSessionFilePath {
    NSString *sessionDir = [[self currentDownloadDir] stringByAppendingPathComponent:YYB_ARIA2_DEFAULT_SESSION_DIR];
    // 每次都确保目录存在
    [YYBDirectory ensureDirectoryExists:sessionDir];
    // 加到诊断日志打包中
    [[YYBMacLogUpload sharedInstance] addExtraFilePath:sessionDir];
    
    NSString *sessionPath = [sessionDir stringByAppendingPathComponent:@"aria2.session"];
    YYBMacLogInfo(kLogTag, @"[sessionFilePath] session文件路径: %@", sessionPath);
    return sessionPath;
}

// 清理指定目录下超过maxAge秒的文件
- (void)cleanOldLogFilesInDirectory:(NSString *)directory maxAge:(NSTimeInterval)maxAge {
    NSFileManager *fm = [NSFileManager defaultManager];
    NSArray *files = [fm contentsOfDirectoryAtPath:directory error:nil];
    NSDate *now = [NSDate date];
    for (NSString *file in files) {
        NSString *filePath = [directory stringByAppendingPathComponent:file];
        NSDictionary *attr = [fm attributesOfItemAtPath:filePath error:nil];
        NSDate *modDate = attr[NSFileModificationDate];
        if (modDate && [now timeIntervalSinceDate:modDate] > maxAge) {
            [fm removeItemAtPath:filePath error:nil];
            YYBMacLogInfo(kLogTag, @"已删除过期日志文件: %@", filePath);
        }
    }
}

- (void)loadConfig {
    self.maxConcurrent = YYB_ARIA2_DEFAULT_MAX_CONCURRENT;
    self.maxConnPerServer = YYB_ARIA2_DEFAULT_MAX_CONN_PER_SERVER;
    self.split = YYB_ARIA2_DEFAULT_SPLIT;
    self.severArguments = [[YYBMacShiplyConfig sharedInstance] arrayValueWithKey:kYYBLibAria2ArgumentsKey defaultValue:@[]];
    self.downloadRunLoopMaxSleepTime = (int)[[YYBMacShiplyConfig sharedInstance] intValueWithKey:kYYBLibAria2DownloadLoopMaxSleepTimeKey
                                                                               defaultValue:YYB_LIBARIA2_DOWNLOAD_LOOP_MAX_SLEEP_TIME];
    YYBMacLogInfo(kLogTag, @"配置已加载");
}

- (NSString *)downloadDir {
    return [self currentDownloadDir];
}

- (NSString *)logPath {
    return [self currentLogPath];
}

- (NSString *)sessionFilePath {
    return [self createSessionFilePath];
}

/// 校验目录是否存在，不存在或不为文件夹 则重建恢复
- (void)ensureDirectoryExists {
    [self currentDownloadDir];
    [self currentLogPath];
//    [self createSessionFilePath];
}

- (NSDictionary<NSString *, NSString *> *)defaultAria2Options {
    NSMutableDictionary *opts = [NSMutableDictionary dictionary];

    // 1. 确保下载目录存在
    NSString *downloadDir = self.downloadDir; // 内部已确保目录存在
    opts[@"dir"] = downloadDir ?: @"";

    // 2. 确保日志目录存在
    NSString *logPath = self.logPath; // 内部已确保目录存在
    opts[@"log"] = logPath ?: @"";
    opts[@"log-level"] = @"info";

    // 3. 其他参数(更多参数及默认值可通过命令行：aria2c --help=#all 查看)
    opts[@"max-concurrent-downloads"] = [NSString stringWithFormat:@"%lu", (unsigned long)self.maxConcurrent];
    opts[@"max-connection-per-server"] = [NSString stringWithFormat:@"%lu", (unsigned long)self.maxConnPerServer];
    opts[@"split"] = [NSString stringWithFormat:@"%lu", (unsigned long)self.split];
    opts[@"console-log-level"] = @"info";
    opts[@"continue"] = @"true";
    opts[@"file-allocation"] = @"trunc";
    opts[@"disk-cache"] = @"32M";
    opts[@"auto-file-renaming"] = @"false";
    opts[@"allow-overwrite"] = @"true";
    opts[@"disable-ipv6"] = @"false";
    opts[@"check-certificate"] = @"false";
    opts[@"min-split-size"] = @"20M";
    opts[@"max-tries"] = @"5";
    opts[@"max-file-not-found"] = @"10";
    opts[@"enable-rpc"] = @"false";
    opts[@"rpc-listen-all"] = @"false";
    opts[@"rpc-allow-origin-all"] = @"false";

    // for 测试断网后快速失败
//    opts[@"max-tries"] = @"1";
//    opts[@"retry-wait"] = @"1";
//    opts[@"timeout"] = @"5";
//    opts[@"split"] = @"1";
    
    //  session 相关不设置，业务自己管理任务，避免addurl重复报错（只要continue为true且文件目录不变，会自动复用断点内容）
//    // 目前看不设置此参数，即便手动调用save-session，也无法生成持久化的ria2.session文件（通常10-30秒）
//    NSString *sessionFilePath = self.sessionFilePath; // 内部已确保目录存在
//    opts[@"save-session"] = sessionFilePath ?: @"";
//    opts[@"save-session-interval"] = @"10";
//    // 4. input-file 只在 session 文件存在时加, 否则会初始化失败sesstion
//    if (sessionFilePath.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:sessionFilePath]) {
//        opts[@"input-file"] = sessionFilePath;
//    }
    
    // app杀掉前（如果任务下载中），最多丢失对应时间内下载的进度（越小丢失的进度越小）
    opts[@"auto-save-interval"] = @"5";

    // 6. 远端参数覆盖
    for (NSString *severArg in self.severArguments) {
        if ([severArg isKindOfClass:[NSString class]] && severArg.length > 0) {
            NSArray *kv = [severArg componentsSeparatedByString:@"="];
            if (kv.count == 2) {
                opts[kv[0]] = kv[1];
            }
        }
    }

    return opts;
}

@end

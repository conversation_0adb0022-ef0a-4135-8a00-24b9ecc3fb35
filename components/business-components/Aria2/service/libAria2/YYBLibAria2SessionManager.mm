//
//  YYBLibAria2SessionManager.m
//  YYBMacApp
//
//  Created by jamieling on 2025/6/25.
//
//  C++桥接，线程安全，企业级实现
//

#import "YYBLibAria2SessionManager.h"
#import "aria2.h"
#import <mutex>
#import <memory>
#import <set>
#import <vector>
#import <atomic>
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBLibAria2ConfigManager.h"
#import "YYBLibAriaHealingManager.h"
#import <AppKit/AppKit.h>
#import "YYBAria2JobProtocol.h"
#import "YYBAria2SyncQueue.h"
#import "YYBAria2JobImpl.h"
#include <thread>
#include <chrono>
#import "MacroUtils.h"
#import "YYBAria2TaskIdGidMap.h"
#import "YYBAria2ProgressCacheItem.h"
#import "YYBAria2Error.h"

#define kLogTag @"YYBLibAria2SessionManager"

/// sesstion状态机
typedef NS_ENUM(NSInteger, YYBAria2SessionState) {
    YYBAria2SessionStateIdle,
    YYBAria2SessionStateStarting,
    YYBAria2SessionStateRunning,
    YYBAria2SessionStateStopping,
    YYBAria2SessionStateStopped,
};

@interface YYBLibAria2SessionManager () {
    std::mutex _mutex;
    std::set<std::string> _allKnownGIDs;
    std::atomic<bool> _running;
    aria2::Session *_session;           // session资源
    NSDictionary *_config;              // 启动配置
    NSMutableDictionary<NSString *, YYBAria2ProgressCacheItem *> *_progressCache; // 进度缓存
    std::mutex _progressCacheMutex;
}
@property (nonatomic, strong) YYBAria2SyncQueue<id<YYBAria2Job>> *jobQueue;         // 所有任务队列
@property (nonatomic, strong) YYBAria2SyncQueue<id<YYBAria2Notify>> *notifyQueue;   // 所有通知队列
@property (nonatomic, strong) NSThread *downloadThread;
@property (nonatomic, strong) YYBLibAriaHealingManager *healingManager;             // 自愈管理器
@property (nonatomic, copy) void(^startCompletion)(BOOL, NSError *);
@property (nonatomic, copy) void (^stopCompletion)(void);
@property (nonatomic, assign) BOOL logGlobalOptions;
@property (atomic, assign) BOOL hasActiveDownloadsFlag;                             // 是否有活跃中的任务
@property (nonatomic, assign) int downloadRunLoopMaxSleepTime;                // 下载runlooop的最大休眠时间（单位毫秒）

@property (atomic, strong) NSDate *lastHeartbeat; // 下载线程心跳时间
@property (nonatomic, strong) dispatch_source_t healthMonitorTimer; // 健康监控与定时持久化定时器
@property (atomic, assign) YYBAria2SessionState sessionState;
@property (nonatomic, strong) id<NSObject> backgroundActivity;

@property (nonatomic, copy) YYBLibAria2SessionBatchProgressCallback batchProgressCallback;
@property (nonatomic, strong) dispatch_queue_t progressPushQueue;

- (void)registerKnownGID:(NSString *)gidHex;
- (void)clearProgressCacheForGID:(NSString *)gid;
+ (NSString *)stringFromDownloadEvent:(int)event;
- (NSDictionary *)progressDictFromDownloadHandle:(aria2::DownloadHandle *)dh gid:(NSString *)gid;
- (NSDictionary *)progressDictFromDownloadHandle:(aria2::DownloadHandle *)dh gid:(NSString *)gid event:(int)event;

@end

/// C++事件桥接，回调到OC层
static int Aria2SessionEventBridge(aria2::Session* session, aria2::DownloadEvent event, aria2::A2Gid gid, void* userData) {
    YYBLibAria2SessionManager *mgr = (__bridge YYBLibAria2SessionManager*)userData;
    if (!mgr) return 0;
    
    // 直接查询当前的状态信息
    NSString *gidHex = [NSString stringWithUTF8String:aria2::gidToHex(gid).c_str()];
    
    // 注册gid
    [mgr registerKnownGID:gidHex];
    
    NSString *taskId = [[YYBAria2TaskIdGidMap sharedMap] taskIdForGid:gidHex];
    if (!taskId) {
        YYBMacLogError(kLogTag, @"[YYBAria2TaskIdGidMap Aria2SessionEventBridge] taskId 失效, 已移除, gid=%@", gidHex);
        return 0;
    }
    
    aria2::DownloadHandle *dh = aria2::getDownloadHandle(session, gid);
    NSDictionary *result = [mgr progressDictFromDownloadHandle:dh gid:gidHex event:event];
    if (dh) aria2::deleteDownloadHandle(dh);
    
    NSDictionary *info = result ?: @{};
    
    YYBMacLogInfo(kLogTag, @"[Aria2SessionEventBridge] , _time_aria2_event event = %@, gid = %@ info = %@",
                  [YYBLibAria2SessionManager stringFromDownloadEvent:(int)event], gidHex, info);
        
    // 事件回调始终在主线程，防止UI线程安全问题(频率不高)
    if (mgr.eventCallback) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            @try {
                // 事件回调始终用taskId
                mgr.eventCallback((int)event, taskId, info);
            } @catch (NSException *exception) {
                YYBMacLogError(kLogTag, @"[eventCallback] 业务方回调异常: %@\n%@", exception, [exception callStackSymbols]);
            }
        });
    }
    
    // 只要是状态变更事件，都清理进度缓存（避免使用到了脏缓存数据）
    switch (event) {
        case aria2::EVENT_ON_DOWNLOAD_START:
        case aria2::EVENT_ON_DOWNLOAD_PAUSE:
        case aria2::EVENT_ON_DOWNLOAD_COMPLETE:
        case aria2::EVENT_ON_DOWNLOAD_ERROR:
        case aria2::EVENT_ON_BT_DOWNLOAD_COMPLETE:
            [mgr clearProgressCacheForGID:gidHex];
            YYBMacLogInfo(kLogTag, @"[EventBridge] 推送清理进度缓存Job, event=%d, gid=%@", (int)event, gidHex);
            break;
        default:
            break;
    }
    
    return 0;
}

@implementation YYBLibAria2SessionManager

#pragma mark - 进程级libaria2初始化/反初始化

+ (void)initialize {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        int rv = aria2::libraryInit();
        if (rv != 0) {
            YYBMacLogError(kLogTag, @"[libraryInit] libaria2初始化失败, code=%d", rv);
        } else {
            YYBMacLogInfo(kLogTag, @"[libraryInit] libaria2初始化成功");
        }
    });
}

+ (void)globalCleanup {
    aria2::libraryDeinit();
    YYBMacLogInfo(kLogTag, @"[libraryDeinit] libaria2已反初始化");
}

#pragma mark - 初始化与销毁

- (instancetype)init {
    if (self = [super init]) {
        _running = false;
        _allKnownGIDs.clear();
        _jobQueue = [YYBAria2SyncQueue new];
        _notifyQueue = [YYBAria2SyncQueue new];
        _progressCache = [NSMutableDictionary dictionary];
        _healingManager = [[YYBLibAriaHealingManager alloc] init];
        _downloadRunLoopMaxSleepTime = [YYBLibAria2ConfigManager sharedManager].downloadRunLoopMaxSleepTime;
        // 监听应用前后台/退出，保证session持久化
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppDidBecomeActive) name:NSApplicationDidBecomeActiveNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppWillResignActive) name:NSApplicationWillResignActiveNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppWillTerminate) name:NSApplicationWillTerminateNotification object:nil];
    }
    return self;
}

- (void)dealloc {
    [self stopHealthMonitorTimer];
    [self stopWithCompletion:^{}];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    YYBMacLogInfo(kLogTag, @"[dealloc] YYBLibAria2SessionManager被释放");
}

#pragma mark - 进程前后台/退出

- (void)onAppWillTerminate {
    YYBMacLogInfo(kLogTag, @"[onAppWillTerminate] 停止下载服务");
    [self stopWithCompletion:^{}];
}

- (void)onAppWillResignActive {
    // 启动后台活动，防止App Nap
    if (!self.backgroundActivity) {
        self.backgroundActivity = [[NSProcessInfo processInfo] beginActivityWithOptions:NSActivityBackground | NSActivityLatencyCritical reason:@"Download"];
        YYBMacLogInfo(kLogTag, @"[AppNap] 已声明后台下载活动，防止App Nap");
    }
    // 不关闭健康监控定时器，后台也要持续自检
    // [self stopHealthMonitorTimer];
}

- (void)onAppDidBecomeActive {
    // 结束后台活动
    if (self.backgroundActivity) {
        [[NSProcessInfo processInfo] endActivity:self.backgroundActivity];
        self.backgroundActivity = nil;
        YYBMacLogInfo(kLogTag, @"[AppNap] 已结束后台下载活动");
    }
    
    [self startHealthMonitorTimer];
}

#pragma mark - 启动与停止

- (void)startWithCompletion:(void(^)(BOOL success, NSError *error))completion {
    @synchronized(self) {
        if (self.sessionState == YYBAria2SessionStateRunning || self.sessionState == YYBAria2SessionStateStarting) {
            // 已经在启动或运行中
            if (completion) completion(YES, nil);
            return;
        }
        self.sessionState = YYBAria2SessionStateStarting;
        [self realyStartWithCompletion:completion];
    }
}

- (void)realyStartWithCompletion:(void(^)(BOOL success, NSError *error))completion {
    if (_running) {
        YYBMacLogInfo(kLogTag, @"[startWithCompletion] 已经启动，无需重复启动");
        if (completion) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(YES, nil); });
        }
        return;
    }
    if (self.downloadThread && self.downloadThread.isExecuting) {
        YYBMacLogError(kLogTag, @"[startWithCompletion] downloadThread已在执行但_running=NO，状态异常，拒绝启动");
        if (completion) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
                completion(NO, [YYBAria2Error errorWithCode:YYBAria2ErrorSessionInitFailed message:@"线程状态异常，无法启动" userInfo:nil]);
            });
        }
        return;
    }
    self.startCompletion = [completion copy];
    __weak typeof(self) weakSelf = self;
    self.downloadThread = [[NSThread alloc] initWithBlock:^{
        [weakSelf downloadThreadMain];
    }];
    [self.downloadThread start];
    
    // 启动健康监控与定时持久化定时器
    [self startHealthMonitorTimer];
}

- (void)stopWithCompletion:(void(^)(void))completion {
    @synchronized(self) {
        if (self.sessionState == YYBAria2SessionStateStopping || self.sessionState == YYBAria2SessionStateStopped) {
            if (completion) completion();
            return;
        }
        self.sessionState = YYBAria2SessionStateStopping;
        [self realyStopWithCompletion:completion];
    }
}

- (void)realyStopWithCompletion:(void(^)(void))completion {
    if (!_running) {
        YYBMacLogInfo(kLogTag, @"[stop] 已经停止，无需重复停止");
        if (completion) completion();
        return;
    }

    // 1. 先标记不再运行
    [self updateRunningStatus:false];
    _logGlobalOptions = NO;
    
    // 2. 先取消健康监控定时器
    [self stopHealthMonitorTimer];
    
//    // 3. 清空Job队列，防止stop后有遗留job // 不清空，有可能还能拉起消费
//    [self.jobQueue clear];
    
    // 4. 唤醒下载线程
    [self.jobQueue push:[YYBAria2WakeupJob new] wakeup:YES];
    
    // 4.不直接cancel，等下载线程自己退出后cleanup
    self.stopCompletion = completion;
    
    // 5. 只保存最新的 completion，前一个如果没回调要先回调闭环
    if (self.stopCompletion && self.stopCompletion != completion) {
        // 闭环上一次的 completion，防止丢失
        void (^prev)(void) = self.stopCompletion;
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            prev();
        });
    }
    self.stopCompletion = completion;
}

#pragma mark - 主循环-主入口

- (void)downloadThreadMain {
    @autoreleasepool {
        @try {
            try {
                [self setupAria2Session];
                [self runDownloadLoop];
            } catch (const std::exception& e) {
                YYBMacLogError(kLogTag, @"[downloadThreadMain] C++异常: %s", e.what());
                [self tryRecoverRunLoop];
            } catch (...) {
                YYBMacLogError(kLogTag, @"[downloadThreadMain] 未知C++异常");
                [self tryRecoverRunLoop];
            }
        } @catch (NSException *exception) {
            YYBMacLogError(kLogTag, @"[downloadThreadMain] OC异常: %@\n%@", exception, [exception callStackSymbols]);
            [self tryRecoverRunLoop];
        }
        [self cleanupAndSaveSession];
        // 回调 stopCompletion
        if (self.stopCompletion) {
            void (^completion)(void) = self.stopCompletion;
            self.stopCompletion = nil;
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
                completion();
            });
        }
    }
}

#pragma mark - 主循环-Session初始化

- (void)setupAria2Session {
    // 1. 初始化session
    NSDictionary *optsDict = [[YYBLibAria2ConfigManager sharedManager] defaultAria2Options];
    aria2::KeyVals opts;
    for (NSString *key in optsDict) {
        NSString *value = [[optsDict objectForKey:key] description];
        opts.push_back({[key UTF8String], [value UTF8String]});
    }
    aria2::SessionConfig cfg;
    cfg.downloadEventCallback = &Aria2SessionEventBridge;
    cfg.userData = (__bridge void*)self;
    cfg.keepRunning = true;
    _session = aria2::sessionNew(opts, cfg);
    if (!_session) {
        YYBMacLogError(kLogTag, @"[downloadThreadMain] sessionNew失败");
        [self retryForSessionNew:0 completion:self.startCompletion];
        @throw [NSException exceptionWithName:@"Aria2SessionInitException"
                                       reason:@"sessionNew失败"
                                     userInfo:nil];
    }
    [self updateRunningStatus:true];
    _allKnownGIDs.clear();
    if (self.startCompletion) {
        self.startCompletion(YES, nil);
    }
    YYBMacLogInfo(kLogTag, @"[setupAria2Session] session初始化成功");
}

#pragma mark - 主循环-内核

- (void)runDownloadLoop {
    int sleepMs = 0;  // 默认有活跃任务时不sleep
    int progressIntervalMs = 500; // 进度推送周期
    int logEveryNProgress = 10;             // 每推送多少次进度，输出一次日志 (正常情况下，无睡眠模式时)
    int logEveryNProgressInSleep = 30;      // 每推送多少次进度，输出一次日志 (正常情况下，有睡眠模式时)
    int progressPushCountSinceLastLog = 0; // 日志推送后，进度推送次数
    NSTimeInterval jobTimeout = 1.0; // job超时监控时间
    NSDate *lastProgressTime = [NSDate dateWithTimeIntervalSince1970:0];
    aria2::GlobalStat lastStat = {};

    while (_running && _session) {
        @try {
            try {
                self.lastHeartbeat = [NSDate date]; // 上报心跳
                
                // 1. 处理所有Job
                [self processAllJobsWithTimeout:jobTimeout];
                
                // 2. 执行aria2::run
                int rv = aria2::run(_session, aria2::RUN_ONCE);
                if (rv < 0) {
                    YYBMacLogError(kLogTag, @"[downloadThreadMain] aria2::run error: %d", rv);
                    [self tryRecoverRunLoop];
                    break;
                }
                
                // 3. 是否到达进度推送周期
                NSDate *now = [NSDate date];
                BOOL needPushProgress = ([now timeIntervalSinceDate:lastProgressTime] * 1000 >= progressIntervalMs);
                BOOL needLog = (progressPushCountSinceLastLog >=
                                (sleepMs >= 10 ? logEveryNProgressInSleep : logEveryNProgress));
                if (needPushProgress || needLog) {
                    lastStat = aria2::getGlobalStat(_session);
                }
                if (needPushProgress) {
                    // 不使用缓存，缓存会导致启动流程变慢
                    [self pushProgressNotifyIfNeeded];
                    lastProgressTime = now;
                    progressPushCountSinceLastLog++;
                }
                if (needLog) {
                    progressPushCountSinceLastLog = 0;
                    // 只在有任务时输出日志，尽量减少无意义日志
                    int numActive = lastStat.numActive;
                    int numWaiting = lastStat.numWaiting;
                    int numStopped = lastStat.numStopped;
                    if (numActive > 0 || numWaiting > 0 || numStopped > 0) {
                        YYBMacLogInfo(kLogTag, @"[runLoop] 状态：活跃:%@ 停止:%@ 等待:%@ 下载速度:%.2f KB/s 上传速度:%.2f KB/s",
                                      @(numActive), @(numStopped), @(numWaiting),
                                      lastStat.downloadSpeed / 1024.0, lastStat.uploadSpeed / 1024.0);
                    }
                }
                
                self.hasActiveDownloadsFlag = (lastStat.numActive > 0);
                
                // 4. 阶梯sleep（降低CPU占用，防止busy loop (设置过高后会影响下载速率)）
                sleepMs = (lastStat.numActive > 0) ? 0 : self.downloadRunLoopMaxSleepTime;
                if (sleepMs > 0) {
                    // 当前本地验证，不sleep相对sleep 1ms速度会快10%左右，且cpu占用相同: 30%左右,(RUN_ONCE内部有事件等待/阻塞，不会导致while(1) 空转消化大量cpu)
                    // 可唤醒的job等待：代替sleep（有新下载任务启动时立即唤醒或跳过等待，提升runloop唤醒效率）
//                    std::this_thread::sleep_for(std::chrono::milliseconds(sleepMs));
                    [self.jobQueue waitForJobOrTimeout:sleepMs];
                }
            } catch (const std::exception& e) {
                YYBMacLogError(kLogTag, @"[runDownloadLoop] C++异常: %s", e.what());
                [self tryRecoverRunLoop];
                break;
            } catch (...) {
                YYBMacLogError(kLogTag, @"[runDownloadLoop] 未知C++异常");
                [self tryRecoverRunLoop];
                break;
            }
        } @catch (NSException *exception) {
            YYBMacLogError(kLogTag, @"[runDownloadLoop] OC异常: %@\n%@", exception, [exception callStackSymbols]);
            [self tryRecoverRunLoop];
            break;
        }
    }
}

#pragma mark - 主循环-Job处理

- (BOOL)processAllJobsWithTimeout:(NSTimeInterval)jobTimeout {
    if ([self.jobQueue count] > 20) {
        YYBMacLogError(kLogTag, @"[JobQueue] 队列长度异常!!!: %ld", (long)[self.jobQueue count]);
    }
    
    while (![self.jobQueue isEmpty]) {
        if (!_running || !_session) return NO;; // 防止野指针
        id<YYBAria2Job> job = [self.jobQueue pop];
        if (!job) continue;
        NSDate *startTime = [NSDate date];
        @try {
            try {
                [job executeWithSession:_session manager:self];
            } catch (const std::exception& e) {
                YYBMacLogError(kLogTag, @"[Job] C++异常: %s, job=%@", e.what(), [job class]);
            } catch (...) {
                YYBMacLogError(kLogTag, @"[Job] 未知C++异常, job=%@", [job class]);
            }
        } @catch (NSException *exception) {
            YYBMacLogError(kLogTag, @"[Job] OC异常: %@, job=%@", exception, [job class]);
        }
        NSTimeInterval cost = [[NSDate date] timeIntervalSinceDate:startTime];
        if (cost > jobTimeout) {
            // TODO: 上报异常监控
            YYBMacLogError(kLogTag, @"[Job] 执行超时(%.1fs): %@, 实际耗时: %.2fs", jobTimeout, [job class], cost);
        }
    }
    
    return YES;
}

#pragma mark - 主循环-退出清理

- (void)cleanupAndSaveSession {
    @synchronized(self) {
        if (_session) {
            aria2::sessionFinal(_session);
            _session = NULL;
        }
        [self updateRunningStatus:false];
        self.sessionState = YYBAria2SessionStateStopped;
        YYBMacLogInfo(kLogTag, @"[downloadThreadMain] 下载线程已退出");
    }
}

#pragma mark - Job API（所有操作均Job化，线程安全）

- (void)addDownloadWithURL:(NSString *)url options:(NSDictionary *)options completion:(void(^)(NSString *gid, NSError *error))completion {
    // 创建任务前先恢复被可能被用户在使用过程中删除的文件夹
    [[YYBLibAria2ConfigManager sharedManager] ensureDirectoryExists];
    [self addDownloadWithURL:url options:options retryCount:0 completion:completion];
}

- (void)addDownloadWithURL:(NSString *)url options:(NSDictionary *)options retryCount:(NSInteger)retryCount completion:(void(^)(NSString *gid, NSError *error))completion {
    YYBAria2AddUriJob *job = [YYBAria2AddUriJob new];
    job.url = url;
    job.options = options;
    __weak typeof(self) weakSelf = self;
    job.completion = ^(NSString *gid, NSError *error) {
        __strong typeof(weakSelf) self = weakSelf;
        if (gid) {
            if (completion) completion(gid, nil);
        } else if (retryCount < 3) {
            YYBMacLogWarn(kLogTag, @"[addDownloadWithURL] 添加下载失败, code=%ld, url=%@, 第%ld次重试", (long)error.code, url, (long)retryCount+1);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self addDownloadWithURL:url options:options retryCount:retryCount+1 completion:completion];
            });
        } else {
            YYBMacLogError(kLogTag, @"[addDownloadWithURL] 添加下载失败, code=%ld, url=%@, 重试超限", (long)error.code, url);
            if (completion) completion(nil, error);
        }
        
//        if (!self.logGlobalOptions) {
//            // 只打印一次 （比较耗时，建议调试时才使用~）
//            self.logGlobalOptions = YES;
//            YYBAria2LogGlobalOptionsJob *logJob = [YYBAria2LogGlobalOptionsJob new];
//            [self.jobQueue push:logJob];
//        }

    };
    // 唤醒可能在sleep的runloop
    [self.jobQueue push:job wakeup:YES];
}

- (void)pauseDownload:(NSString *)gid completion:(void(^)(BOOL, NSError *))completion {
    YYBMacLogInfo(kLogTag, @"[pauseDownload] gid=%@", gid);
    YYBAria2PauseJob *job = [YYBAria2PauseJob new];
    job.gid = gid;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)resumeDownload:(NSString *)gid completion:(void(^)(BOOL, NSError *))completion {
    YYBMacLogInfo(kLogTag, @"[resumeDownload] gid=%@", gid);
    YYBAria2ResumeJob *job = [YYBAria2ResumeJob new];
    job.gid = gid;
    job.completion = completion;
    // 唤醒可能在sleep的runloop
    [self.jobQueue push:job wakeup:YES];
}

- (void)removeDownload:(NSString *)gid completion:(void(^)(BOOL, NSError *))completion {
    YYBMacLogInfo(kLogTag, @"[removeDownload] gid=%@", gid);
    YYBAria2RemoveJob *job = [YYBAria2RemoveJob new];
    job.gid = gid;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)changeOption:(NSDictionary *)keyVals forGid:(NSString *)gid completion:(void(^)(BOOL, NSError *))completion {
    YYBAria2ChangeOptionJob *job = [YYBAria2ChangeOptionJob new];
    job.gid = gid;
    job.options = keyVals;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)changeGlobalOption:(NSDictionary *)keyVals completion:(void(^)(BOOL, NSError *))completion {
    YYBAria2ChangeGlobalOptionJob *job = [YYBAria2ChangeGlobalOptionJob new];
    job.options = keyVals;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)forcePauseDownload:(NSString *)gid completion:(void(^)(BOOL, NSError *))completion {
    YYBAria2ForcePauseJob *job = [YYBAria2ForcePauseJob new];
    job.gid = gid;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)pauseAllWithCompletion:(void(^)(NSArray<NSString *> *pausedGIDs, NSError *error))completion {
    YYBAria2PauseAllJob *job = [YYBAria2PauseAllJob new];
    job.gids = self.allKnownGIDs;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)unpauseAllWithCompletion:(void(^)(NSArray<NSString *> *unpausedGIDs, NSError *error))completion {
    YYBAria2UnpauseAllJob *job = [YYBAria2UnpauseAllJob new];
    job.gids = self.allKnownGIDs;
    job.completion = completion;
    // 唤醒可能在sleep的runloop
    [self.jobQueue push:job wakeup:YES];
}

- (void)progressForGID:(NSString *)gid completion:(void(^)(NSDictionary *progress))completion {
    if (!gid) {
        if (completion) completion(nil);
        return;
    }

    // 先查缓存
    YYBAria2ProgressCacheItem *item = nil;
    BOOL cacheValid = NO;
    int cacheExpireTime = 1;
    {
        std::lock_guard<std::mutex> lock(_progressCacheMutex);
        item = _progressCache[gid];
        NSDate *cacheTime = item ? item.lastPushTime : nil;
        id expireObj = item ? item.progressDict[@"cacheExpireTime"] : nil;
        if ([expireObj respondsToSelector:@selector(intValue)]) {
            cacheExpireTime = [expireObj intValue];
        }
        // 容错，确保在1~90之间
        cacheExpireTime = MAX(MIN(cacheExpireTime, 90),1);
        if (item && cacheTime) {
            if ([[NSDate date] timeIntervalSinceDate:cacheTime] < cacheExpireTime) {
                cacheValid = YES;
            }
        }
    }
    
    if (cacheValid && item && item.progressDict) {
        if (completion) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(item.progressDict); });
        }
        return;
    }
    
    // 缓存无效，走C++查询
    YYBMacLogInfo(kLogTag, @"[progressForGID] 缓存失效, gid=%@, expire=%ds, 下发Job", gid, cacheExpireTime);
    __weak typeof(self) weakSelf = self;
    YYBAria2ProgressQueryJob *job = [YYBAria2ProgressQueryJob new];
    job.gid = gid;
    job.completion = ^(NSDictionary *result) {
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        std::lock_guard<std::mutex> lock(self->_progressCacheMutex);
        YYBAria2ProgressCacheItem *item = self->_progressCache[gid];
        if (!item) {
            item = [YYBAria2ProgressCacheItem new];
            self->_progressCache[gid] = item;
        }
        [item updateWithProgressDict:result];
        if (completion) completion(result);
    };
    [self.jobQueue push:job];
}

- (NSDictionary *)progressDictFromDownloadHandle:(aria2::DownloadHandle *)dh gid:(NSString *)gid event:(int)event {
    if (!dh) return @{};

    NSNumber *statusNum = @(dh->getStatus());
    // 当有明确event时，修正dh中可能滞后的数值
    if (event >= 0) {
        switch (event) {
            case aria2::EVENT_ON_DOWNLOAD_COMPLETE:
            case aria2::EVENT_ON_BT_DOWNLOAD_COMPLETE:
                statusNum = @(aria2::DOWNLOAD_COMPLETE);
                break;
            case aria2::EVENT_ON_DOWNLOAD_START:
                statusNum = @(aria2::DOWNLOAD_ACTIVE);
                break;
            case aria2::EVENT_ON_DOWNLOAD_PAUSE:
                statusNum = @(aria2::DOWNLOAD_PAUSED);
                break;
            case aria2::EVENT_ON_DOWNLOAD_ERROR:
                statusNum = @(aria2::DOWNLOAD_ERROR);
                break;
            default:
                break;
        }
    }

    NSString *statusStr = [YYBLibAria2SessionManager stringFromDownloadStatus:statusNum];
    NSString *dirStr = [NSString stringWithUTF8String:dh->getDir().c_str()];
    NSMutableArray *filesArr = [NSMutableArray array];
    std::vector<aria2::FileData> fileVec = dh->getFiles();
    int idx = 1;
    for (const auto& fd : fileVec) {
        NSString *path = [NSString stringWithUTF8String:fd.path.c_str()];
        [filesArr addObject:@{
            @"index": [NSString stringWithFormat:@"%d", idx++],
            @"path": path ?: @"",
            @"length": [NSString stringWithFormat:@"%lld", (long long)fd.length],
            @"completedLength": [NSString stringWithFormat:@"%lld", (long long)fd.completedLength],
            @"selected": @(fd.selected),
        }];
    }

    // 非激活任务的缓存时间动态配置为60-90秒，激活任务为1秒
    int randomExpire = 1;
    if ([statusNum intValue] != aria2::DOWNLOAD_ACTIVE) {
        randomExpire = 60 + arc4random_uniform(31); // 60~90秒
    }
    
    // 先查taskId
    NSString *taskId = [[YYBAria2TaskIdGidMap sharedMap] taskIdForGid:gid];
    if (!taskId) {
        [self removeGID:gid];
        YYBMacLogError(kLogTag, @"[YYBAria2TaskIdGidMap progressForGID] taskId 失效, 已移除, gid=%@, event = %@", gid, @(event));
        return @{};
    }

    return @{
        @"gid": taskId ?: @"",  // 替换为taskId
        @"aria2Gid": gid ?: @"", // 保留原始gid
        @"status": statusStr,
        @"statusCode": statusNum,
        @"totalLength": @(dh->getTotalLength()),
        @"completedLength": @(dh->getCompletedLength()),
        @"downloadSpeed": @(dh->getDownloadSpeed()),
        @"uploadLength": @(dh->getUploadLength()),
        @"uploadSpeed": @(dh->getUploadSpeed()),
        @"connections": @(dh->getConnections()),
        @"errorCode": @(dh->getErrorCode()),
        @"numPieces": @(dh->getNumPieces()),
        @"dir": dirStr ?: @"",
        @"files": filesArr ?: @[],
        @"cacheExpireTime": @(randomExpire),
    };
}

// 无event时的便捷调用
- (NSDictionary *)progressDictFromDownloadHandle:(aria2::DownloadHandle *)dh gid:(NSString *)gid {
    return [self progressDictFromDownloadHandle:dh gid:gid event:-1];
}

+ (NSString *)stringFromDownloadStatus:(NSNumber *)statusNum {
    if (![statusNum isKindOfClass:[NSNumber class]]) return @"unknown";
    switch ([statusNum integerValue]) {
        case aria2::DOWNLOAD_ACTIVE: return @"active";
        case aria2::DOWNLOAD_WAITING: return @"waiting";
        case aria2::DOWNLOAD_PAUSED: return @"paused";
        case aria2::DOWNLOAD_COMPLETE: return @"complete";
        case aria2::DOWNLOAD_ERROR: return @"error";
        case aria2::DOWNLOAD_REMOVED: return @"removed";
        default: return @"unknown";
    }
}

+ (NSString *)stringFromDownloadEvent:(int)event {
    // 基于DownloadEvent翻译
    switch (event) {
        case aria2::EVENT_ON_DOWNLOAD_START: return @"start";
        case aria2::EVENT_ON_DOWNLOAD_PAUSE: return @"pause";
        case aria2::EVENT_ON_DOWNLOAD_STOP: return @"stop";
        case aria2::EVENT_ON_DOWNLOAD_COMPLETE: return @"complete";
        case aria2::EVENT_ON_DOWNLOAD_ERROR: return @"error";
        case aria2::EVENT_ON_BT_DOWNLOAD_COMPLETE: return @"bt_complete";
        default: return @"unknown";
    }
}

- (void)globalStatWithCompletion:(void(^)(NSDictionary *stat))completion {
    YYBAria2GlobalStatQueryJob *job = [YYBAria2GlobalStatQueryJob new];
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)activeDownloadGIDsWithCompletion:(void(^)(NSArray<NSString *> *gids))completion {
    YYBAria2ActiveGIDsQueryJob *job = [YYBAria2ActiveGIDsQueryJob new];
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)urisForGID:(NSString *)gid completion:(void(^)(NSArray<NSDictionary *> *uris))completion {
    YYBAria2UrisQueryJob *job = [YYBAria2UrisQueryJob new];
    job.gid = gid;
    job.completion = completion;
    [self.jobQueue push:job];
}

- (void)clearProgressCacheForGID:(NSString *)gid {
    std::lock_guard<std::mutex> lock(_progressCacheMutex);
    [_progressCache removeObjectForKey:gid];
}

#pragma mark - 进度/事件回调

- (dispatch_queue_t)progressPushQueue {
    if (!_progressPushQueue) {
        _progressPushQueue = dispatch_queue_create("com.yyb.aria2.progressPush", DISPATCH_QUEUE_SERIAL);
    }
    return _progressPushQueue;
}

- (void)setDownloadBatchProgressCallback:(YYBLibAria2SessionBatchProgressCallback)callback {
    self.batchProgressCallback = [callback copy];
    YYBMacLogInfo(kLogTag, @"[setBatchProgressCallback] 批量进度回调已设置");
}

- (void)pushProgressNotifyIfNeeded {
    // 收集所有活跃任务进度
    std::vector<aria2::A2Gid> activeGids = aria2::getActiveDownload(_session);
    NSMutableSet *allGidSet = [NSMutableSet setWithArray:self.allKnownGIDs];

    {
        std::lock_guard<std::mutex> lock(_progressCacheMutex);
        // 1. 活跃任务
        for (auto gid : activeGids) {
            NSString *gidStr = [NSString stringWithUTF8String:aria2::gidToHex(gid).c_str()];
            aria2::DownloadHandle *dh = aria2::getDownloadHandle(_session, gid);
            if (dh) {
                NSDictionary *progress = [self progressDictFromDownloadHandle:dh gid:gidStr];
                YYBAria2ProgressCacheItem *item = _progressCache[gidStr];
                if (!item) {
                    item = [YYBAria2ProgressCacheItem new];
                    _progressCache[gidStr] = item;
                }
                BOOL changed = [item updateWithProgressDict:progress];
                if (changed) {
                    YYBMacLogDebug(kLogTag, @"[pushProgressNotifyIfNeeded] 状态或active文件大小变更, gid=%@, status=%@", gidStr, progress[@"status"]);
                }
                aria2::deleteDownloadHandle(dh);
            }
            // 移除活跃任务
            [allGidSet removeObject:gidStr];
        }
        
        // 2. 非活跃任务（每5次主循环刷新一次）
        static int nonActiveRefreshCounter = 0;
        nonActiveRefreshCounter++;
        if (nonActiveRefreshCounter >= 5) { // 每5次主循环刷新一次（约2.5秒）
            for (NSString *gidStr in allGidSet) {
                aria2::A2Gid gid = aria2::hexToGid([gidStr UTF8String]);
                aria2::DownloadHandle *dh = aria2::getDownloadHandle(_session, gid);
                if (dh) {
                    NSDictionary *progress = [self progressDictFromDownloadHandle:dh gid:gidStr];
                    YYBAria2ProgressCacheItem *item = _progressCache[gidStr];
                    if (!item) {
                        item = [YYBAria2ProgressCacheItem new];
                        _progressCache[gidStr] = item;
                    }
                    BOOL changed = [item updateWithProgressDict:progress];
                    if (changed) {
                        YYBMacLogDebug(kLogTag, @"[pushProgressNotifyIfNeeded] 非active状态变更, gid=%@, status=%@", gidStr, progress[@"status"]);
                    }
                    aria2::deleteDownloadHandle(dh);
                }
            }
            nonActiveRefreshCounter = 0;
        }
    }
    
    [self realyPushBatchProgress];
}

- (void)realyPushBatchProgress {
    if (!self.batchProgressCallback) {
        return;
    }
    
    // 采集所有任务进度
    NSMutableDictionary<NSString *, NSDictionary *> *progressSnapshot = [NSMutableDictionary dictionary];
    
    {
        // 加锁保护，深拷贝_progressCache内容，防止并发修改
        std::lock_guard<std::mutex> lock(_progressCacheMutex);
        for (NSString *gid in _progressCache) {
            YYBAria2ProgressCacheItem *item = _progressCache[gid];
            if (!item || !item.progressDict) continue;
            
            if (![item shouldPush]) {
                continue;
            }

            NSString *taskId = [[YYBAria2TaskIdGidMap sharedMap] taskIdForGid:gid];
            if (!taskId) {
                [self removeGID:gid];
                YYBMacLogError(kLogTag, @"[YYBAria2TaskIdGidMap realyPushBatchProgress] taskId失效，已移除, gid=%@", gid);
                continue;
            }
            if ([item.lastStatus isEqualToString:@"removed"]) {
                YYBMacLogInfo(kLogTag, @"[realyPushBatchProgress] 首次回调removed状态, gid=%@", gid);
            }
            
            progressSnapshot[taskId] = [item.progressDict copy];
            [item markPushed];
            YYBMacLogDebug(kLogTag, @"[realyPushBatchProgress] 推送进度, gid=%@, status=%@", gid, item.lastStatus);
        }
    }
    
    YYBMacLogDebug(kLogTag, @"[pushProgressNotifyIfNeeded] 推送批量进度，任务数: %lu", (unsigned long)progressSnapshot.count);
    
    // 推送给回调（异步，防止阻塞下载线程）
    if (self.batchProgressCallback && progressSnapshot.count > 0) {
        NSDictionary *immutableSnapshot = [progressSnapshot copy]; // 再copy一层，确保不可变
        __weak typeof(self) weakSelf = self;
        dispatch_async(self.progressPushQueue, ^{
            __strong typeof(weakSelf) self = weakSelf;
            if (!self) return;
            @try {
                // 回调只读，不可变对象
                self.batchProgressCallback(immutableSnapshot);
            } @catch (NSException *exception) {
                YYBMacLogError(kLogTag, @"[batchProgressCallback] 业务方回调异常: %@\n%@", exception, [exception callStackSymbols]);
            }
        });
    }
}


#pragma mark - session持久化/自愈/日志/枚举转换

/// 心跳健康检测
- (void)startHealthMonitorTimer {
    if (self.healthMonitorTimer) return;
    dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, dispatch_get_main_queue());
    // 每10秒检测一次心跳是否正常
    dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 10 * NSEC_PER_SEC), 10 * NSEC_PER_SEC, 1 * NSEC_PER_SEC);
    __weak typeof(self) weakSelf = self;
    dispatch_source_set_event_handler(timer, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !self->_running || !self->_session) return;
        NSDate *now = [NSDate date];
        NSTimeInterval heartbeatInterval = [now timeIntervalSinceDate:self.lastHeartbeat ?: now];
        if (heartbeatInterval > 20) {
            YYBMacLogError(kLogTag, @"[HealthCheck] 下载线程心跳超时(%.1fs)，自动重启", heartbeatInterval);
            [self tryRecoverRunLoop];
        }
        YYBMacLogDebug(kLogTag, @"[HealthCheck] 定时持久化session已触发");
    });
    dispatch_resume(timer);
    self.healthMonitorTimer = timer;
    YYBMacLogInfo(kLogTag, @"[HealthCheck] 健康监控与定时持久化定时器已启动");
}

- (void)stopHealthMonitorTimer {
    if (self.healthMonitorTimer) {
        dispatch_source_cancel(self.healthMonitorTimer);
        self.healthMonitorTimer = nil;
        YYBMacLogInfo(kLogTag, @"[HealthCheck] 健康监控定时器已销毁");
    }
}

/// 自愈机制：runloop异常自动重启
- (void)tryRecoverRunLoop {
    @synchronized(self) {
        if (self.sessionState == YYBAria2SessionStateStopping ||
            self.sessionState == YYBAria2SessionStateStopped ||
            self.sessionState == YYBAria2SessionStateStarting) {
            YYBMacLogWarn(kLogTag, @"[tryRecoverRunLoop] 已在stop/start流程中，跳过本次自愈");
            return;
        }
        [self realyTryRecoverRunLoop];
    }
}

- (void)realyTryRecoverRunLoop {
    if (self.healingManager.runLoopRecoverRetryCount >= self.healingManager.maxRunLoopRecoverCount) {
        YYBMacLogError(kLogTag, @"[tryRecoverRunLoop] runloop自愈重试超限，放弃自动重启。");
        return;
    }
    NSTimeInterval retryInterval = [self.healingManager exponentialBackoff:self.healingManager.runLoopRecoverRetryCount];
    self.healingManager.runLoopRecoverRetryCount++;
    [self stopWithCompletion:^{
        YYBMacLogWarn(kLogTag, @"[tryRecoverRunLoop] runloop异常，%0.2fs后自动重启session", retryInterval);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retryInterval * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self startWithCompletion:^(BOOL success, NSError *error) {
                if (success) {
                    YYBMacLogInfo(kLogTag, @"[tryRecoverRunLoop] session重启成功，runloop已恢复");
                    self.healingManager.runLoopRecoverRetryCount = 0;
                } else {
                    YYBMacLogError(kLogTag, @"[tryRecoverRunLoop] session重启失败: %@", error);
                }
            }];
        });
    }];
}

/// 自愈机制：runloop异常自动重启
- (void)retryForSessionNew:(NSInteger)retryCount completion:(void(^)(BOOL, NSError *))completion {
    if (self.healingManager.sessionRetryCount < self.healingManager.maxSessionRetryCount) {
        NSTimeInterval retryInterval = [self.healingManager exponentialBackoff:self.healingManager.sessionRetryCount];
        YYBMacLogWarn(kLogTag, @"[sessionNew] 失败，第%ld次重试，%0.2fs后重试", (long)self.healingManager.sessionRetryCount+1, retryInterval);
        self.healingManager.sessionRetryCount++;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(retryInterval * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self startWithCompletion:completion];
        });
    } else {
        if (completion) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{ completion(NO, [YYBAria2Error errorWithCode:YYBAria2ErrorSessionRunLoopCrashed message:@"sessionNew失败，无法重启" userInfo:nil]); });
        }
        YYBMacLogError(kLogTag, @"[sessionNew] 失败，重试超限，sessionFileRebuildCount=%ld", (long)self.healingManager.sessionFileRebuildCount);
    }
}

#pragma mark - GID管理

- (NSArray<NSString *> *)allKnownGIDs {
    std::lock_guard<std::mutex> lock(_mutex);
    NSMutableArray *arr = [NSMutableArray arrayWithCapacity:_allKnownGIDs.size()];
    for (const auto &gidHex : _allKnownGIDs) {
        [arr addObject:[NSString stringWithUTF8String:gidHex.c_str()]];
    }
    return arr;
}

- (void)registerKnownGID:(NSString *)gidHex {
    if (!gidHex) return;
    std::lock_guard<std::mutex> lock(_mutex);
    _allKnownGIDs.insert([gidHex UTF8String]);
}

- (void)removeGID:(NSString *)gidHex {
    if (!gidHex) return;
    std::lock_guard<std::mutex> lock(_mutex);
    _allKnownGIDs.erase([gidHex UTF8String]);
}

#pragma mark - 事件回调

- (void)setDownloadEventCallback:(void(^)(int, NSString*, NSDictionary*))callback {
    self.eventCallback = [callback copy];
}

#pragma mark - 属性

- (void)updateRunningStatus:(BOOL)running {
    if (running == _running) {
        return;
    }
    _running = running;
    if (self.runningStatusCallback) {
        self.runningStatusCallback(running);
    }
}

- (BOOL)isRunning {
    return _running;
}

@end

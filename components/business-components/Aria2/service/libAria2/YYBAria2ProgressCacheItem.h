//
//  YYBAria2ProgressCacheItem.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBAria2ProgressCacheItem : NSObject

@property (nonatomic, strong) NSDictionary *progressDict;      // 当前进度字典
@property (nonatomic, copy) NSString *lastStatus;              // 最后一次状态
@property (nonatomic, assign) int64_t totalFileSize;           // 总文件大小
@property (nonatomic, assign) int64_t lastCompletedFileSize;   // 最后已下载文件大小
@property (nonatomic, assign) int64_t downloadSpeed;           // 下载速度
@property (nonatomic, assign) BOOL removedCallbacked;          // 是否已回调removed
@property (nonatomic, strong) NSDate *lastPushTime;            // 最后一次推送时间
@property (nonatomic, strong) NSDictionary *lastPushDict;      // 最后一次推送的快照

/// 更新进度，返回是否有变化（状态变更或active文件大小变更）
- (BOOL)updateWithProgressDict:(NSDictionary *)progress;

/// 判断本次是否需要推送（removed只推送一次，其他按变化推送）
- (BOOL)shouldPush;

/// 标记本次已推送
- (void)markPushed;

@end

NS_ASSUME_NONNULL_END

//
//  YYBLibAriaHealingManager.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/22.
//

#import "YYBLibAriaHealingManager.h"

@implementation YYBLibAriaHealingManager

- (instancetype)init {
    if (self = [super init]) {
        _sessionRetryCount = 0;
        _sessionFileRebuildCount = 0;
        _runLoopRecoverRetryCount = 0;
        _maxSessionRetryCount = 3;
        _maxSessionFileRebuildCount = 1;
        _maxRunLoopRecoverCount = 3;
        _maxRetryInterval = 60.0;
        _baseRetryInterval = 2.0;
    }
    return self;
}

- (void)reset {
    self.sessionRetryCount = 0;
    self.sessionFileRebuildCount = 0;
    self.runLoopRecoverRetryCount = 0;
}

- (NSTimeInterval)exponentialBackoff:(NSInteger)retryCount {
    NSTimeInterval interval = self.baseRetryInterval * pow(2, retryCount);
    return MIN(interval, self.maxRetryInterval);
}

@end

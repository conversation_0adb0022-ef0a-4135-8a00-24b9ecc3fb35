//
//  YYBAria2TaskDelegate.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/10.
//

#import <Foundation/Foundation.h>
@class YYBAria2Task;

NS_ASSUME_NONNULL_BEGIN

@protocol YYBAria2TaskDelegate <NSObject>

@optional

/// 下载进度更新
- (void)yybAria2Task:(YYBAria2Task *)task didUpdateProgress:(double)progress;

/// 下载状态发生变化(status详见YYBAria2TaskStatus)
- (void)yybAria2Task:(YYBAria2Task *)task didChangeStatus:(NSInteger)status error:(NSError * _Nullable)error;

@end

NS_ASSUME_NONNULL_END

//
//  YYBAria2ServiceMonitor.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//

#import "YYBAria2ServiceMonitor.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBLibAria2ServiceFacade.h"
#import <AppKit/AppKit.h>

static NSString *const kLogTag = @"YYBAria2ServiceMonitor";

@interface YYBAria2ServiceMonitor ()
@property (nonatomic, weak) id<YYBAria2ServiceMonitorDelegate> delegate;
@end

@implementation YYBAria2ServiceMonitor

- (instancetype)initWithDelegate:(id<YYBAria2ServiceMonitorDelegate>)delegate {
    if (self = [super init]) {
        _delegate = delegate;
        [self observeServiceStatus];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    YYBMacLogInfo(kLogTag, @"[dealloc] YYBAria2ServiceMonitor被释放");
}

- (void)observeServiceStatus {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleAria2ServiceStatusChanged:)
                                                 name:kYYBLibAria2ServiceStatusChangedNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppDidBecomeActive) name:NSApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppWillResignActive) name:NSApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppWillTerminate) name:NSApplicationWillTerminateNotification object:nil];
}

- (void)handleAria2ServiceStatusChanged:(NSNotification *)notification {
    BOOL running = [notification.userInfo[@"running"] boolValue];
    YYBMacLogInfo(kLogTag, @"[ServiceStatus] Aria2服务状态变化: %@", running ? @"运行中" : @"已停止");
    if (running) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(yybAria2OnServiceAvailable)]) {
            [self.delegate yybAria2OnServiceAvailable];
        }
    } else {
        if (self.delegate && [self.delegate respondsToSelector:@selector(yybAria2OnServiceUnavailable)]) {
            [self.delegate yybAria2OnServiceUnavailable];
        }
    }
}

- (void)onAppDidBecomeActive {
    YYBMacLogInfo(kLogTag, @"[App] App回前台，刷新下载任务状态");
    if (self.delegate && [self.delegate respondsToSelector:@selector(yybAria2OnAppDidBecomeActive)]) {
        [self.delegate yybAria2OnAppDidBecomeActive];
    }
}

- (void)onAppWillResignActive {
    YYBMacLogInfo(kLogTag, @"[App] App回后台，强制持久化任务数据");
    if (self.delegate && [self.delegate respondsToSelector:@selector(yybAria2OnAppWillResignActive)]) {
        [self.delegate yybAria2OnAppWillResignActive];
    }
}

- (void)onAppWillTerminate {
    YYBMacLogInfo(kLogTag, @"[App] App即将退出，强制持久化任务数据");
    if (self.delegate && [self.delegate respondsToSelector:@selector(yybAria2OnAppWillTerminate)]) {
        [self.delegate yybAria2OnAppWillTerminate];
    }
}

@end

//
//  YYBAria2TaskSQLStore.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/13.
//
//

#import "YYBAria2TaskSQLStore.h"
#import "YYBAria2Task.h"
#import <sqlite3.h>
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#if __has_include(<YYBMacFusionSDK/YYBMacLogUpload.h>)
#import <YYBMacFusionSDK/YYBMacLogUpload.h>
#else
#import <YYBMacLogUpload.h>
#endif
#import "YYBLibAria2ServiceFacade.h"

static NSString *const kLogTag = @"YYBAria2TaskSQLStore";
static NSString *const kTableName = @"t_yyb_aria2_task";
static NSString *const kDefaultLocalDownloadDBDir = @"localDownloadDB";

static const int kCurrentDBVersion = 20250819; // 后续升级时递增

static void *kDBQueueSpecificKey = &kDBQueueSpecificKey;

@interface YYBAria2Task ()
- (void)updateStatus:(YYBAria2TaskStatus)status reason:(NSString *)reason fromSql:(BOOL)fromSql;
@end

@interface YYBAria2TaskSQLStore () {
    sqlite3 *_db;
}
@property (nonatomic, strong) dispatch_queue_t dbQueue;
@end

@implementation YYBAria2TaskSQLStore

#pragma mark - 单例

+ (instancetype)sharedStore {
    static YYBAria2TaskSQLStore *store;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        store = [[YYBAria2TaskSQLStore alloc] init];
    });
    return store;
}

- (instancetype)init {
    if (self = [super init]) {
        _dbQueue = dispatch_queue_create("com.yyb.aria2task.sqlstore", DISPATCH_QUEUE_SERIAL);
        dispatch_queue_set_specific(_dbQueue, kDBQueueSpecificKey, kDBQueueSpecificKey, NULL);
        // 先检查版本变化，必要时删除旧库
        [self checkAndRebuildDatabaseIfVersionChanged];
        [self openDatabase];
        [self checkAndUpgradeDatabaseIfNeeded];
    }
    return self;
}

#pragma mark - 版本变化检测与重建

/// 检查数据库版本号是否变化，如变化则删除旧数据库文件（开发环境专用）
- (void)checkAndRebuildDatabaseIfVersionChanged {
    NSString *dbPath = [self dbPath];
    BOOL needDelete = NO;
    int oldVersion = 0;
    if ([[NSFileManager defaultManager] fileExistsAtPath:dbPath]) {
        // 只读方式打开数据库，读取user_version
        sqlite3 *tmpdb = NULL;
        int openRc = sqlite3_open_v2([dbPath UTF8String], &tmpdb, SQLITE_OPEN_READONLY, NULL);
        if (openRc == SQLITE_OK && tmpdb) {
            const char *sql = "PRAGMA user_version";
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(tmpdb, sql, -1, &stmt, NULL) == SQLITE_OK) {
                if (sqlite3_step(stmt) == SQLITE_ROW) {
                    oldVersion = sqlite3_column_int(stmt, 0);
                }
                sqlite3_finalize(stmt);
            }
            sqlite3_close(tmpdb);
        }
        if (oldVersion != kCurrentDBVersion) {
            needDelete = YES;
        }
    }
    if (needDelete) {
        NSError *err = nil;
        [[NSFileManager defaultManager] removeItemAtPath:dbPath error:&err];
        if (err) {
            YYBMacLogError(kLogTag, @"[checkAndRebuildDatabaseIfVersionChanged] 版本变化，删除旧数据库失败: %@", err);
        } else {
            YYBMacLogWarn(kLogTag, @"[checkAndRebuildDatabaseIfVersionChanged] 检测到数据库版本变化(%d -> %d)，已删除旧数据库文件", oldVersion, kCurrentDBVersion);
        }
    } else {
        YYBMacLogInfo(kLogTag, @"[checkAndRebuildDatabaseIfVersionChanged] 数据库版本未变更(%d)，无需重建", oldVersion);
    }
}

#pragma mark - 数据库文件路径与有效性检测

/// 获取数据库文件路径
- (NSString *)dbPath {
    NSString *aria2DownloadDir = [[YYBLibAria2ServiceFacade sharedService] currentDownloadDir];
    NSString *localDownloadDir = [aria2DownloadDir stringByAppendingPathComponent:kDefaultLocalDownloadDBDir];
    [[NSFileManager defaultManager] createDirectoryAtPath:localDownloadDir withIntermediateDirectories:YES attributes:nil error:nil];
    // 加到诊断日志打包中
    [[YYBMacLogUpload sharedInstance] addExtraFilePath:localDownloadDir];
    YYBMacLogInfo(kLogTag, @"localDownload dbPath = %@", localDownloadDir);
    return [localDownloadDir stringByAppendingPathComponent:@"yyb_aria2_task.sqlite"];
}

/// 检查数据库文件是否存在
- (BOOL)isDatabaseFileValid {
    NSString *dbPath = [self dbPath];
    return [[NSFileManager defaultManager] fileExistsAtPath:dbPath];
}

#pragma mark - 数据库打开/关闭/重建

/// 打开数据库并初始化表结构（自动WAL模式）
- (void)openDatabase {
    if (dispatch_get_specific(kDBQueueSpecificKey)) {
        [self _openDatabaseImpl];
    } else {
        __weak typeof(self) weakSelf = self;
        dispatch_sync(_dbQueue, ^{
            __strong typeof(weakSelf) self = weakSelf;
            [self _openDatabaseImpl];
        });
    }
}

- (void)_openDatabaseImpl {
    if (_db) {
        YYBMacLogInfo(kLogTag, @"[openDatabase] 已有数据库连接，无需重复打开");
        return;
    }
    int result = sqlite3_open([self.dbPath UTF8String], &self->_db);
    if (result != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[openDatabase] 打开数据库失败: %d", result);
        self->_db = NULL;
        return;
    }
    // 设置WAL模式，提升并发与容错
    char *errMsg = NULL;
    int walRc = sqlite3_exec(self->_db, "PRAGMA journal_mode=WAL;", NULL, NULL, &errMsg);
    if (walRc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[openDatabase] 设置WAL模式失败: %s", errMsg);
        sqlite3_free(errMsg);
    } else {
        YYBMacLogInfo(kLogTag, @"[openDatabase] WAL模式设置成功");
    }
    // 初始化表结构
    NSString *sql = [NSString stringWithFormat:
                     @"CREATE TABLE IF NOT EXISTS %@ ("
                     "taskId TEXT PRIMARY KEY NOT NULL,"
                     "gid TEXT,"
                     "url TEXT NOT NULL,"
                     "destPath TEXT NOT NULL,"
                     "status INTEGER,"
                     "totalLength INTEGER,"
                     "completedLength INTEGER,"
                     "progress REAL,"
                     "fileName TEXT,"
                     "finalFilePath TEXT,"
                     "md5 TEXT,"
                     "retryCount INTEGER,"
                     "maxRetryCount INTEGER,"
                     "source INTEGER,"
                     "visibility INTEGER,"
                     "priority INTEGER,"
                     "createTime REAL,"
                     "scheduleStatus INTEGER,"
                     "pkgName TEXT"
                     ")", kTableName];
    int rc = sqlite3_exec(self->_db, [sql UTF8String], NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[openDatabase] 创建表失败: %s", errMsg);
        sqlite3_free(errMsg);
    } else {
        YYBMacLogInfo(kLogTag, @"[openDatabase] 数据库和表初始化成功");
    }
    // 创建索引
    [self createIndexIfNeeded:@"idx_yyb_aria2_task_gid" onColumn:@"gid"];
    [self createIndexIfNeeded:@"idx_yyb_aria2_task_md5" onColumn:@"md5"];
    // 记录数据库版本
    [self setDBVersion:kCurrentDBVersion];
}

/// 关闭数据库（用于清理/重建/退出）
- (void)closeDatabase {
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (self->_db) {
            int rc = sqlite3_close(self->_db);
            if (rc != SQLITE_OK) {
                YYBMacLogError(kLogTag, @"[closeDatabase] 关闭数据库失败: %d", rc);
            } else {
                YYBMacLogInfo(kLogTag, @"[closeDatabase] 数据库关闭成功");
            }
            self->_db = NULL;
        }
    });
}

/// 检查数据库可用性，自动重建（每次操作前调用）
- (void)ensureDatabaseAvailable {
    // 必须在dbQueue内调用
    if (!_db || ![self isDatabaseFileValid]) {
        YYBMacLogWarn(kLogTag, @"[ensureDatabaseAvailable] 数据库文件不存在或句柄失效，自动重建");
        if (_db) {
            int rc = sqlite3_close(_db);
            if (rc != SQLITE_OK) {
                YYBMacLogError(kLogTag, @"[ensureDatabaseAvailable] 关闭旧数据库失败: %d", rc);
            }
            _db = NULL;
        }
        [self openDatabase];
    }
}

#pragma mark - 数据库升级与索引

/// 创建索引（如不存在）
- (void)createIndexIfNeeded:(NSString *)indexName onColumn:(NSString *)col {
    if (!_db) return;
    NSString *sql = [NSString stringWithFormat:@"CREATE INDEX IF NOT EXISTS %@ ON %@ (%@)", indexName, kTableName, col];
    char *errMsg = NULL;
    int rc = sqlite3_exec(_db, [sql UTF8String], NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[createIndexIfNeeded] 创建索引%@失败: %s", indexName, errMsg);
        sqlite3_free(errMsg);
    }
}

/// 获取数据库当前版本
- (int)dbVersion {
    int version = 0;
    if (!_db) return version;
    NSString *sql = @"PRAGMA user_version";
    sqlite3_stmt *stmt = NULL;
    if (sqlite3_prepare_v2(_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            version = sqlite3_column_int(stmt, 0);
        }
        sqlite3_finalize(stmt);
    }
    return version;
}

/// 设置数据库版本
- (void)setDBVersion:(int)version {
    if (!_db) return;
    NSString *sql = [NSString stringWithFormat:@"PRAGMA user_version = %d", version];
    char *errMsg = NULL;
    int rc = sqlite3_exec(_db, [sql UTF8String], NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        YYBMacLogError(kLogTag, @"[setDBVersion] 设置数据库版本失败: %s", errMsg);
        sqlite3_free(errMsg);
    }
}

/// 检查并自动升级数据库（如有字段变更）
- (void)checkAndUpgradeDatabaseIfNeeded {
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        [self ensureDatabaseAvailable];
        int version = [self dbVersion];
        if (version < kCurrentDBVersion) {
            // 这里可根据历史版本做ALTER TABLE等升级操作
            // 例：if (version < 2) { ... }
            [self setDBVersion:kCurrentDBVersion];
            YYBMacLogInfo(kLogTag, @"[checkAndUpgradeDatabaseIfNeeded] 数据库升级到版本%d", kCurrentDBVersion);
        }
    });
}

#pragma mark - 任务增删查改

/// 插入或更新任务（主键为taskId）
- (void)insertOrUpdateTask:(YYBAria2Task *)task {
    if (task == nil) {
#ifdef DEBUG
        NSAssert(NO, @"请不要插入空task，可以调用deleteTask方法");
#endif
        return;
    }
    YYBMacLogInfo(kLogTag, @"[insertOrUpdateTask] ----------- : taskInfo = %@,", task);
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !task) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[insertOrUpdateTask] 数据库不可用，写入失败");
                return;
            }
            [self _insertOrUpdateTask:task];
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[insertOrUpdateTask] 异常: %@", e);
        }
    });
}

/// 内部同步插入/更新（事务内调用）
- (void)_insertOrUpdateTask:(YYBAria2Task *)task {
    if (!_db || !task) return;
    NSString *sql = [NSString stringWithFormat:
        @"REPLACE INTO %@ (taskId, gid, url, destPath, status, totalLength, completedLength, progress, fileName, finalFilePath, md5, retryCount, maxRetryCount, source, visibility, priority, createTime, scheduleStatus, pkgName) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", kTableName];;
    sqlite3_stmt *stmt = NULL;
    if (sqlite3_prepare_v2(_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, [task.taskId UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 2, [task.gid UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 3, [task.url UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 4, [task.destPath UTF8String], -1, NULL);
        sqlite3_bind_int(stmt, 5, (int)task.status);
        sqlite3_bind_int64(stmt, 6, task.totalLength);
        sqlite3_bind_int64(stmt, 7, task.completedLength);
        sqlite3_bind_double(stmt, 8, task.progress);
        sqlite3_bind_text(stmt, 9, [task.fileName UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 10, [task.finalFilePath UTF8String], -1, NULL);
        sqlite3_bind_text(stmt, 11, [task.md5 UTF8String], -1, NULL);
        sqlite3_bind_int(stmt, 12, (int)task.retryCount);
        sqlite3_bind_int(stmt, 13, (int)task.maxRetryCount);
        sqlite3_bind_int(stmt, 14, (int)task.source);
        sqlite3_bind_int(stmt, 15, (int)task.visibility);
        sqlite3_bind_int(stmt, 16, (int)task.priority);
        sqlite3_bind_double(stmt, 17, task.createTime);
        sqlite3_bind_int(stmt, 18, (int)task.scheduleStatus);
        sqlite3_bind_text(stmt, 19, [task.pkgName UTF8String], -1, NULL);
        if (sqlite3_step(stmt) != SQLITE_DONE) {
            YYBMacLogError(kLogTag, @"[insertOrUpdateTask] 插入/更新失败: %s", sqlite3_errmsg(_db));
        } else {
            YYBMacLogInfo(kLogTag, @"[insertOrUpdateTask] 成功: taskId=%@", task.taskId);
        }
        sqlite3_finalize(stmt);
    } else {
        YYBMacLogError(kLogTag, @"[insertOrUpdateTask] 预编译失败: %s", sqlite3_errmsg(_db));
    }
}

/// 批量插入或更新任务（事务内同步插入，避免事务失效）
- (void)batchInsertOrUpdateTasks:(NSArray<YYBAria2Task *> *)tasks {
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || tasks.count == 0) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[batchInsertOrUpdateTasks] 数据库不可用，批量写入失败");
                return;
            }
            sqlite3_exec(self->_db, "BEGIN TRANSACTION", NULL, NULL, NULL);
            for (YYBAria2Task *task in tasks) {
                [self _insertOrUpdateTask:task];
            }
            sqlite3_exec(self->_db, "COMMIT", NULL, NULL, NULL);
            YYBMacLogInfo(kLogTag, @"[batchInsertOrUpdateTasks] 批量更新%lu个任务", (unsigned long)tasks.count);
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[batchInsertOrUpdateTasks] 异常: %@", e);
            if (self->_db) sqlite3_exec(self->_db, "ROLLBACK", NULL, NULL, NULL);
        }
    });
}

/// 根据taskId删除任务（仅数据，不删除文件）
- (void)deleteTaskByTaskId:(NSString *)taskId {
    YYBMacLogInfo(kLogTag, @"[deleteTaskByTaskId] : taskId = %@,", taskId);
    
    if (!taskId) return;
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        YYBMacLogInfo(kLogTag, @"[deleteTaskByTaskId] in dbQueue, taskId = %@", taskId);
        if (!self || !taskId) {
            return;
        }
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[deleteTaskByTaskId] 数据库不可用，删除失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@ WHERE taskId=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, [taskId UTF8String], -1, NULL);
                if (sqlite3_step(stmt) != SQLITE_DONE) {
                    YYBMacLogError(kLogTag, @"[deleteTaskByTaskId] 删除失败: %s", sqlite3_errmsg(self->_db));
                } else {
                    YYBMacLogInfo(kLogTag, @"[deleteTaskByTaskId] 成功: taskId=%@", taskId);
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[deleteTaskByTaskId] 异常: %@", e);
        }
    });
}

/// 根据gid删除任务（仅数据，不删除文件）
- (void)deleteTaskByGid:(NSString *)gid {
    YYBMacLogInfo(kLogTag, @"[deleteTaskByGid] : gid = %@,", gid);
    
    if (!gid) return;
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        YYBMacLogInfo(kLogTag, @"[deleteTaskByTaskId] in dbQueue, gid = %@", gid);
        if (!self || !gid) {
            return;
        }
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[deleteTaskByGid] 数据库不可用，删除失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@ WHERE gid=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, [gid UTF8String], -1, NULL);
                if (sqlite3_step(stmt) != SQLITE_DONE) {
                    YYBMacLogError(kLogTag, @"[deleteTaskByGid] 删除失败: %s", sqlite3_errmsg(self->_db));
                } else {
                    YYBMacLogInfo(kLogTag, @"[deleteTaskByGid] 成功: gid=%@", gid);
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[deleteTaskByGid] 异常: %@", e);
        }
    });
}

#pragma mark - 查询

/// 查询单个任务（taskId）
- (nullable YYBAria2Task *)taskForTaskId:(NSString *)taskId {
    if (!taskId) return nil;
    __block YYBAria2Task *task = nil;
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !taskId) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[taskForTaskId] 数据库不可用，查询失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE taskId=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, [taskId UTF8String], -1, NULL);
                if (sqlite3_step(stmt) == SQLITE_ROW) {
                    task = [self taskFromStmt:stmt];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[taskForTaskId] 异常: %@", e);
        }
    });
    return task;
}

/// 查询单个任务（gid）
- (nullable YYBAria2Task *)taskForGid:(NSString *)gid {
    if (!gid) return nil;
    __block YYBAria2Task *task = nil;
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self || !gid) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[taskForGid] 数据库不可用，查询失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE gid=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, [gid UTF8String], -1, NULL);
                if (sqlite3_step(stmt) == SQLITE_ROW) {
                    task = [self taskFromStmt:stmt];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[taskForGid] 异常: %@", e);
        }
    });
    return task;
}

/// 查询所有“有效”任务（不含已删除/移除任务）
- (NSArray<YYBAria2Task *> *)allTasks {
    __block NSMutableArray *arr = [NSMutableArray array];
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[allTasks] 数据库不可用，查询失败");
                return;
            }
            // 只查未被移除的任务
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE status != %d", kTableName, (int)YYBAria2TaskStatusRemoved];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                while (sqlite3_step(stmt) == SQLITE_ROW) {
                    YYBAria2Task *task = [self taskFromStmt:stmt];
                    if (task) [arr addObject:task];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[allTasks] 异常: %@", e);
        }
    });
    return arr;
}

/// 查询指定状态的任务
- (NSArray<YYBAria2Task *> *)tasksWithStatus:(NSInteger)status {
    __block NSMutableArray *arr = [NSMutableArray array];
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[tasksWithStatus] 数据库不可用，查询失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE status=?", kTableName];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                sqlite3_bind_int(stmt, 1, (int)status);
                while (sqlite3_step(stmt) == SQLITE_ROW) {
                    YYBAria2Task *task = [self taskFromStmt:stmt];
                    if (task) [arr addObject:task];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[tasksWithStatus] 异常: %@", e);
        }
    });
    return arr;
}

/// 查询指定多个状态的任务
- (NSArray<YYBAria2Task *> *)tasksWithStatusList:(NSArray<NSNumber *> *)statusList {
    __block NSMutableArray *arr = [NSMutableArray array];
    if (statusList.count == 0) return arr;
    __weak typeof(self) weakSelf = self;
    dispatch_sync(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[tasksWithStatusList] 数据库不可用，查询失败");
                return;
            }
            NSMutableString *placeholders = [NSMutableString string];
            for (NSUInteger i = 0; i < statusList.count; i++) {
                [placeholders appendString:@"?"];
                if (i < statusList.count - 1) [placeholders appendString:@","];
            }
            NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE status IN (%@)", kTableName, placeholders];
            sqlite3_stmt *stmt = NULL;
            if (sqlite3_prepare_v2(self->_db, [sql UTF8String], -1, &stmt, NULL) == SQLITE_OK) {
                for (NSUInteger i = 0; i < statusList.count; i++) {
                    sqlite3_bind_int(stmt, (int)i+1, [statusList[i] intValue]);
                }
                while (sqlite3_step(stmt) == SQLITE_ROW) {
                    YYBAria2Task *task = [self taskFromStmt:stmt];
                    if (task) [arr addObject:task];
                }
                sqlite3_finalize(stmt);
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[tasksWithStatusList] 异常: %@", e);
        }
    });
    return arr;
}

/// 清理所有任务（仅数据，不删除文件）
- (void)clearAllTasks {
    __weak typeof(self) weakSelf = self;
    dispatch_async(_dbQueue, ^{
        __strong typeof(weakSelf) self = weakSelf;
        if (!self) return;
        @try {
            [self ensureDatabaseAvailable];
            if (!self->_db) {
                YYBMacLogError(kLogTag, @"[clearAllTasks] 数据库不可用，清空失败");
                return;
            }
            NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@", kTableName];
            char *errMsg = NULL;
            int rc = sqlite3_exec(self->_db, [sql UTF8String], NULL, NULL, &errMsg);
            if (rc != SQLITE_OK) {
                YYBMacLogError(kLogTag, @"[clearAllTasks] 清空失败: %s", errMsg);
                sqlite3_free(errMsg);
            } else {
                YYBMacLogInfo(kLogTag, @"[clearAllTasks] 全部清空成功");
            }
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[clearAllTasks] 异常: %@", e);
        }
    });
}

#pragma mark - 辅助

/// 从sqlite3_stmt反序列化为YYBAria2Task对象
- (YYBAria2Task *)taskFromStmt:(sqlite3_stmt *)stmt {
    NSString *taskId = [self stringFromStmt:stmt col:0];
    NSString *gid = [self stringFromStmt:stmt col:1];
    NSString *url = [self stringFromStmt:stmt col:2];
    NSString *destPath = [self stringFromStmt:stmt col:3];
    NSString *md5 = [self stringFromStmt:stmt col:10];
    NSString *pkgName = [self stringFromStmt:stmt col:18];
    YYBAria2Task *task = [[YYBAria2Task alloc] initWithTaskId:taskId md5:md5 url:url destPath:destPath];
    YYBAria2TaskStatus status = (YYBAria2TaskStatus)sqlite3_column_int(stmt, 4);
    [task updateStatus:status reason:@"sql还原" fromSql:YES];
    task.totalLength = sqlite3_column_int64(stmt, 5);
    task.completedLength = sqlite3_column_int64(stmt, 6);
    task.progress = sqlite3_column_double(stmt, 7);
    task.fileName = [self stringFromStmt:stmt col:8];
    task.finalFilePath = [self stringFromStmt:stmt col:9];
    task.gid = gid;
    task.retryCount = sqlite3_column_int(stmt, 11);
    task.maxRetryCount = sqlite3_column_int(stmt, 12);
    task.source = (YYBAria2TaskSource)sqlite3_column_int(stmt, 13);
    task.visibility = (YYBAria2TaskVisibility)sqlite3_column_int(stmt, 14);
    task.priority = (YYBAria2TaskPriority)sqlite3_column_int(stmt, 15);
    task.createTime = sqlite3_column_double(stmt, 16);
    task.scheduleStatus = (YYBAria2TaskScheduleStatus)sqlite3_column_int(stmt, 17);
    task.pkgName = pkgName;
    
    return task;
}

/// 取sqlite3_stmt的字符串字段
- (NSString *)stringFromStmt:(sqlite3_stmt *)stmt col:(int)col {
    const unsigned char *cstr = sqlite3_column_text(stmt, col);
    return cstr ? [NSString stringWithUTF8String:(const char *)cstr] : @"";
}

@end

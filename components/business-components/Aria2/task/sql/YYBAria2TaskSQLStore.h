//
//  YYBAria2TaskSQLStore.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/7/13.
//

#import <Foundation/Foundation.h>

@class YYBAria2Task;

NS_ASSUME_NONNULL_BEGIN

/// 任务SQL存储层，负责所有任务的增删查改和表结构管理
@interface YYBAria2TaskSQLStore : NSObject

/// 单例
+ (instancetype)sharedStore;

/// 插入或更新任务（主键为taskId）
- (void)insertOrUpdateTask:(YYBAria2Task *)task;

/// 根据taskId删除任务（仅数据，不删除文件）
- (void)deleteTaskByTaskId:(NSString *)taskId;

/// 根据gid删除任务（仅数据，不删除文件）
- (void)deleteTaskByGid:(NSString *)gid;

/// 查询单个任务（taskId）
- (nullable YYBAria2Task *)taskForTaskId:(NSString *)taskId;

/// 查询单个任务（gid）
- (nullable YYBAria2Task *)taskForGid:(NSString *)gid;

/// 查询所有“有效”任务（不含已删除/移除任务）
- (NSArray<YYBAria2Task *> *)allTasks;

/// 查询指定状态的任务
- (NSArray<YYBAria2Task *> *)tasksWithStatus:(NSInteger)status;

/// 查询指定多个状态的任务
- (NSArray<YYBAria2Task *> *)tasksWithStatusList:(NSArray<NSNumber *> *)statusList;

/// 批量插入或更新任务
- (void)batchInsertOrUpdateTasks:(NSArray<YYBAria2Task *> *)tasks;

/// 清理所有任务（仅数据，不删除文件）
- (void)clearAllTasks;

/// 数据库升级（如有字段变更自动升级）
- (void)checkAndUpgradeDatabaseIfNeeded;

@end

NS_ASSUME_NONNULL_END

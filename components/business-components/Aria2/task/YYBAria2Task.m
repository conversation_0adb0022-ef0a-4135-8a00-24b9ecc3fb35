//
//  YYBAria2Task.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/10.
//

#import "YYBAria2Task.h"
#import "YYBAria2DownloadManager.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <CommonCrypto/CommonDigest.h>
#import "YYBAria2TaskSQLStore.h"
#import "YYBAria2TaskManager.h"
#import "YYBFile.h"
#import "YYBAria2TaskIdGidMap.h"

// 最大重试次数
static const NSUInteger kMaxRetryCount = 3;

static NSString *const kLogTag = @"YYBAria2Task";

@interface YYBAria2Task ()
@property (nonatomic, copy, readwrite, nullable) NSString *md5;    ///< 文件的md5 (业务侧需要强校验时设置)
@property (nonatomic, assign, readwrite) YYBAria2TaskStatus status;
@end

@implementation YYBAria2Task

- (instancetype)initWithTaskId:(NSString *)taskId md5:(NSString *)md5 url:(NSString *)url destPath:(NSString *)destPath {
    if (self = [super init]) {
        _taskId = [taskId copy];
        _md5 = md5;
        _url = [url copy];
        _destPath = [destPath copy];
        _status = YYBAria2TaskStatusPending;
        _totalLength = 0;
        _completedLength = 0;
        _progress = 0;
        _fileName = @"";
        _finalFilePath = @"";
        _retryCount = 0;
        _maxRetryCount = kMaxRetryCount;
        _isStarted = NO; // 新建任务默认未启动
        _createTime = [[NSDate date] timeIntervalSince1970];
        _source = YYBAria2TaskSourceStore;
        _priority = YYBAria2TaskPriorityNormal;
        _visibility = YYBAria2TaskVisibilityUser;
        // 初始化时激活时间不早于创建时间
        _scheduleActiveTime = _createTime;
    }
    return self;
}

- (void)dealloc
{
    self.delegate = nil;
    self.progressBlock = nil;
    self.statusBlock = nil;
}

- (NSString *)fileExtension {
    return [self.fileName.pathExtension lowercaseString];
}

/// 开始下载
- (void)start {
    if (!self.manager) {
        return;
    }
    [self.manager startTask:self];
}

/// 取消下载
- (void)cancel {
    if (!self.manager) {
        return;
    }
    [self.manager cancelTask:self deleteFile:YES];
}

/// 暂停下载
- (void)pause {
    if (!self.manager) {
        return;
    }
    [self.manager pauseTask:self byUser:YES completion:nil];
}

/// 恢复下载
- (void)resume {
    if (!self.manager) {
        return;
    }
    [self.manager resumeTask:self completion:nil];
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<YYBAria2Task: %p, taskId=%@, gid=%@, status=%@, scheduleStatusst=%@, source=%@, createTime=%f, progress=%@, isStarted=%@, md5=%@, url=%@, destPath=%@, visibility=%ld, priority=%ld, pkgName=%@>",
            self, self.taskId, self.gid, self.statusString, self.scheduleStatusString, self.sourceString, self.createTime, @(self.progress), self.isStarted ? @"YES" : @"NO", self.md5, self.url, self.destPath, (long)self.visibility, (long)self.priority, self.pkgName];
}

- (BOOL)fileExists {
    return self.finalFilePath.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:self.finalFilePath];
}

- (NSDictionary *)getTaskInfoDic{
    NSDictionary * taskInfo = @{
        @"taskId": self.taskId ?: @"",
        @"gid": self.gid ?: @"",
        @"url": self.url ?: @"",
        @"destPath": self.destPath ?: @"",
        @"md5": self.md5 ?: @"",
        @"pkgName": self.pkgName ?: @"",
        @"status": @(self.status),
        @"totalLength": [NSNumber numberWithLongLong:self.totalLength],
        @"completedLength": [NSNumber numberWithLongLong:self.completedLength],
        @"progress": [NSNumber numberWithDouble:self.progress],
        @"fileName": self.fileName ?: @"",
        @"finalFilePath": self.finalFilePath ?: @"",
        @"fileExtension": self.fileExtension ?: @""
    };
    return [taskInfo copy];
}

- (unsigned long long)fileSize {
    if (![self fileExists]) return 0;
    NSDictionary *attr = [[NSFileManager defaultManager] attributesOfItemAtPath:self.finalFilePath error:nil];
    return [attr[NSFileSize] unsignedLongLongValue];
}

- (BOOL)validateMD5IfNeededWithLogTag:(NSString *)logTag {
    if (self.md5.length == 0 || self.totalLength == 0) return YES;
    if ([self fileSize] != self.totalLength) return YES; // 只在完整时校验
    NSString *localMd5 = [YYBFile getFileMD5AtPath:self.finalFilePath];
    if (![localMd5 isEqualToString:self.md5]) {
        YYBMacLogError(logTag, @"[validateTaskFile] 任务%@ 状态%@ 文件md5异常: 本地md5=%@, 期望md5=%@", self.taskId, @(self.status), localMd5, self.md5);
        return NO;
    }
    return YES;
}

- (BOOL)noAria2TempFileWithLogTag:(NSString *)logTag {
    NSString *aria2File = [self.finalFilePath stringByAppendingString:@".aria2"];
    if ([[NSFileManager defaultManager] fileExistsAtPath:aria2File]) {
        YYBMacLogError(logTag, @"[validateTaskFile] 任务%@ 完成态但存在.aria2临时文件: %@", self.taskId, aria2File);
        return NO;
    }
    return YES;
}

#pragma mark - 枚举转换
- (NSString *)statusString {
    switch (self.status) {
        case YYBAria2TaskStatusPending: return @"待下发";
        case YYBAria2TaskStatusActive: return @"下载中";
        case YYBAria2TaskStatusPaused: return @"已暂停";
        case YYBAria2TaskStatusComplete: return @"已完成";
        case YYBAria2TaskStatusErrorNetwork: return @"网络错误";
        case YYBAria2TaskStatusErrorFatal: return @"不可恢复错误";
        case YYBAria2TaskStatusRemoved: return @"已移除";
        case YYBAria2TaskStatusWaitingForNetwork: return @"等待网络";
        case YYBAria2TaskStatusWaitingForService: return @"等待服务";
        case YYBAria2TaskStatusRetrying: return @"重试中";
        default: return @"未知";
    }
}

- (NSString *)sourceString {
    switch (self.source) {
        case YYBAria2TaskSourceStore: return @"商店内部任务";
        case YYBAria2TaskSourceWeb: return @"Web任务";
        case YYBAria2TaskSourceDebug: return @"Debug任务";
        default: return @"未知";
    }
}

- (NSString *)scheduleStatusString {
    switch (self.scheduleStatus) {
        case YYBAria2TaskScheduleStatusNone: return @"未调度";
        case YYBAria2TaskScheduleStatusActive: return @"调度-激活";
        case YYBAria2TaskScheduleStatusWaiting: return @"调度-等待";
        case YYBAria2TaskScheduleStatusPaused: return @"调度-暂停";
        case YYBAria2TaskScheduleStatusUserPaused: return @"用户暂停";
        case YYBAria2TaskScheduleStatusCompleted: return @"已完成";
        case YYBAria2TaskScheduleStatusFailed: return @"异常";
        case YYBAria2TaskScheduleStatusNetworkError: return @"网络异常";
        case YYBAria2TaskScheduleStatusRemoved: return @"被移除";
    }
}

#pragma mark - 对外gid(调度管控后，对外不再暴露真实的gid)
- (NSString *)gidForUser {
    return self.taskId;
}

#pragma mark - 任务状态机

- (void)updateStatus:(YYBAria2TaskStatus)status reason:(NSString *)reason fromSql:(BOOL)fromSql {
    YYBAria2TaskStatus oldStatus = self.status;
    if (oldStatus == status) {
        return;
    }
    
    NSString *oldStatusString = [self statusString];
    self.status = status;
    if (!fromSql) {
        // 不是从sql还原的，则要更新sql
        [[YYBAria2TaskSQLStore sharedStore] insertOrUpdateTask:self];
        YYBMacLogInfo(kLogTag, @"[TaskStatus] 任务 taskid = %@, gid = %@,  状态变更: %@ -> %@, reason: %@",
                      self.taskId,
                      self.gid,
                      oldStatusString,
                      [self statusString],
                      reason);
        if (status == YYBAria2TaskStatusErrorFatal || status == YYBAria2TaskStatusErrorNetwork) {
            YYBMacLogError(kLogTag, @"任务异常了！！, taskInfo: %@", [self description]);
        } else if (status == YYBAria2TaskStatusRemoved) {
            YYBMacLogInfo(kLogTag, @"任务被删除, taskInfo: %@", [self description]);
        }
    }
}

- (void)updateStatus:(YYBAria2TaskStatus)status reason:(NSString *)reason {
    [self updateStatus:status reason:reason fromSql:NO];
}


#pragma mark - 状态聚合判断
/// 任务状态能否复用
- (BOOL)canReusable {
    switch (self.status) {
        case YYBAria2TaskStatusPending:
        case YYBAria2TaskStatusWaiting:
        case YYBAria2TaskStatusActive:
        case YYBAria2TaskStatusPaused:
        case YYBAria2TaskStatusErrorNetwork:
        case YYBAria2TaskStatusWaitingForNetwork:
        case YYBAria2TaskStatusWaitingForService:
        case YYBAria2TaskStatusRetrying:
            return YES;
        case YYBAria2TaskStatusComplete:
            return [self fileExists];
        case YYBAria2TaskStatusErrorFatal:
        case YYBAria2TaskStatusRemoved:
        default:
            return NO;
    }
}

/// 是否为活跃/等待/待下发状态（用于轮询/活跃判断）
- (BOOL)isActiveOrWaiting {
    if (self.gid.length) {
        // 有gid时需要满足的状态
        BOOL needActive = (self.status == YYBAria2TaskStatusActive ||
                           self.status == YYBAria2TaskStatusPending ||
                           self.status == YYBAria2TaskStatusWaiting);
        if (needActive) {
            return needActive;
        }
    }
    
    return (self.status == YYBAria2TaskStatusPending ||
            self.status == YYBAria2TaskStatusWaiting);
}

/// 是否为可恢复状态（断点续传/自动恢复）
- (BOOL)isRecoverable {
    if (!self.gid.length) {
        // 无gid时需要满足的状态
        BOOL isRecoverable = (self.status == YYBAria2TaskStatusPending ||
                              self.status == YYBAria2TaskStatusWaiting ||
                              self.status == YYBAria2TaskStatusRetrying ||
                              self.status == YYBAria2TaskStatusWaitingForService ||
                              self.status == YYBAria2TaskStatusWaitingForNetwork);
        if (isRecoverable) {
            return isRecoverable;
        }
    }
    
    return (self.status == YYBAria2TaskStatusErrorNetwork ||
            self.status == YYBAria2TaskStatusErrorFatal ||
            self.status == YYBAria2TaskStatusWaitingForNetwork ||
            self.status == YYBAria2TaskStatusWaitingForService);
}

/// 是否为可以添加uri(aria2在任务没取消前或到，不允许重复添加
- (BOOL)canAddUri {
    if (!self.gid.length) {
        return YES;
    }
    return ((self.status < YYBAria2TaskStatusWaiting) || [self inFinalState]);
}

/// 是否需要同步状态（pollAllTasks时用）
- (BOOL)needsStatusSync {
    if (!self.gid.length) {
        return NO;
    }
    
    switch (self.status) {
        case YYBAria2TaskStatusActive:
        case YYBAria2TaskStatusWaiting:
        case YYBAria2TaskStatusPending:
        case YYBAria2TaskStatusPaused:
        case YYBAria2TaskStatusRetrying:
        case YYBAria2TaskStatusWaitingForNetwork:
        case YYBAria2TaskStatusWaitingForService:
            return YES;
        default:
            return NO;
    }
}

/// 是否需要校验gid有效性（validateAllGidsAfterRestore时用）
- (BOOL)needsGidValidation {
    if (!self.gid.length) {
        return NO;
    }
    
    switch (self.status) {
        case YYBAria2TaskStatusActive:
        case YYBAria2TaskStatusPending:
        case YYBAria2TaskStatusWaiting:
        case YYBAria2TaskStatusPaused:
        case YYBAria2TaskStatusRetrying:
        case YYBAria2TaskStatusWaitingForNetwork:
        case YYBAria2TaskStatusWaitingForService:
        case YYBAria2TaskStatusErrorNetwork:        // 网络异常导致的失败也要校验
            return YES;
        default:
            return NO;
    }
}

/// 是否可unpause（可直接恢复）
- (BOOL)canUnpause {
    return self.status == YYBAria2TaskStatusPaused;
}

/// 是否是终态（完成/致命错误/网络错误）
- (BOOL)inFinalState {
    return (self.status == YYBAria2TaskStatusComplete ||
            self.status == YYBAria2TaskStatusErrorFatal ||
            self.status == YYBAria2TaskStatusErrorNetwork);
}

#pragma mark - 进度信息构造
-  (NSDictionary *)progressDictionaryForWaitting {
    // web侧展示等待中的前提条件是active，且completedLength一直不发生变化
    NSString *status = @"active";
    int statusCode = 0; // 对应DownloadStatus的DOWNLOAD_ACTIVE枚举值
    return [YYBAria2Task progressDictionaryForTask:self status:status statusCode:statusCode];
}

+  (NSDictionary *)progressDictionaryForTask:(YYBAria2Task *)task
                                      status:(NSString *)status
                                  statusCode:(int)statusCode {
    return @{
        @"gid": task.taskId ?: @"",
        @"aria2Gid": task.gid ?: @"",
        @"status": status,
        @"statusCode": @(statusCode),
        @"totalLength": @(task.totalLength),
        @"completedLength": @(task.completedLength),
        @"scheduleStatus": @(task.scheduleStatus),
        @"scheduleReason": task.scheduleStatusString ?: @"调度未下发",
        @"downloadSpeed": @(0),
        @"uploadLength": @(0),
        @"uploadSpeed": @(0),
        @"connections": @(0),
        @"errorCode": @(0),
        @"numPieces": @(0),
        @"dir": @"",
        @"files": @[],
        @"cacheExpireTime": @(0),
    };
}

@end

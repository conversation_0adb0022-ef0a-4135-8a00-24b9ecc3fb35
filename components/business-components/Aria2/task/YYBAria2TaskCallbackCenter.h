//  YYBAria2TaskCallbackCenter.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//  负责所有任务的进度/完成/失败回调注册、分发、清理，支持 block、delegate、listener（多对1监听）

#import <Foundation/Foundation.h>
#import "YYBAria2Task.h"
#import "YYBAria2TaskDelegate.h"
#import "YYBDownloadListener.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBAria2TaskCallbackCenter : NSObject

// 分发进度
- (void)dispatchProgress:(double)progress forTask:(YYBAria2Task *)task;

// 分发状态变化
- (void)dispatchStatus:(YYBAria2TaskStatus)status forTask:(YYBAria2Task *)task error:(NSError * _Nullable)error;

// 清理回调
- (void)clearCallbacksForTask:(YYBAria2Task *)task;

// 增加/移除 delegate
- (void)addDelegate:(id<YYBAria2TaskDelegate>)delegate forTask:(YYBAria2Task *)task;
- (void)removeDelegate:(id<YYBAria2TaskDelegate>)delegate forTask:(YYBAria2Task *)task;

// ========== 支持多对1-分组注册监听 ========== //
// 注册下载监听（listenerKey 为主键）
- (void)addDownloadListener:(id<YYBDownloadListener>)listener;
- (void)removeDownloadListener:(id<YYBDownloadListener>)listener;

@end

NS_ASSUME_NONNULL_END

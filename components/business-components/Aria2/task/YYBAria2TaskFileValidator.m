//
//  YYBAria2TaskFileValidator.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//

#import "YYBAria2TaskFileValidator.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBAria2TaskSQLStore.h"

static NSString *const kLogTag = @"YYBAria2TaskFileValidator";

@implementation YYBAria2TaskFileValidator

- (BOOL)validateTaskFile:(YYBAria2Task *)task {
    // 只对“应该有文件”的状态做finalFilePath校验
    BOOL needFilePath = (task.status == YYBAria2TaskStatusComplete ||
                         ((task.status == YYBAria2TaskStatusActive ||
                         task.status == YYBAria2TaskStatusPaused) &&
                          task.completedLength > 0));
    if (needFilePath && !task.finalFilePath.length) {
        YYBMacLogError(kLogTag, @"[validateTaskFile] 任务%@ 文件路径为空，状态=%@", task.taskId, task.statusString);
        return NO;
    }
    BOOL fileExists = [task fileExists];
    unsigned long long fileSize = [task fileSize];
    
    // 这些状态无需校验
    if (task.status == YYBAria2TaskStatusRemoved ||
        task.status == YYBAria2TaskStatusWaitingForNetwork ||
        task.status == YYBAria2TaskStatusWaitingForService) {
        YYBMacLogInfo(kLogTag, @"[validateTaskFile] 任务%@ 状态%@，无需校验", task.taskId, task.statusString);
        return YES;
    }
    
    // 只有需要文件的状态才强制校验文件存在
    if (needFilePath && !fileExists) {
        YYBMacLogError(kLogTag, @"[validateTaskFile] 任务%@ 状态%@ 本地文件不存在: %@", task.taskId, task.statusString, task.finalFilePath);
        return NO;
    }
    
    if (task.totalLength > 0 && fileSize > task.totalLength) {
        YYBMacLogError(kLogTag, @"[validateTaskFile] 任务%@ 状态%@ 本地文件大小异常: %llu > %lld", task.taskId, task.statusString, fileSize, task.totalLength);
        return NO;
    }
    // 只有有文件时才校验MD5
    switch (task.status) {
        case YYBAria2TaskStatusActive:
        case YYBAria2TaskStatusPaused:
        case YYBAria2TaskStatusRetrying:
        case YYBAria2TaskStatusErrorNetwork:
        case YYBAria2TaskStatusErrorFatal:
            if (fileExists && ![task validateMD5IfNeededWithLogTag:kLogTag]) return NO;
            break;
        case YYBAria2TaskStatusComplete:
            if (task.totalLength > 0 && fileSize != task.totalLength) {
                YYBMacLogError(kLogTag, @"[validateTaskFile] 任务%@ 完成态本地文件大小异常: %llu != %lld", task.taskId, fileSize, task.totalLength);
                return NO;
            }
            if (![task validateMD5IfNeededWithLogTag:kLogTag]) return NO;
            if (![task noAria2TempFileWithLogTag:kLogTag]) return NO;
            break;
        default:
            YYBMacLogWarn(kLogTag, @"[validateTaskFile] 任务%@ 未知状态%@，默认通过", task.taskId, task.statusString);
            break;
    }
    return YES;
}

- (void)cleanupDirtyTask:(YYBAria2Task *)task deleteFile:(BOOL)deleteFile {
    if (!task) return;
    // 删除SQL
    [[YYBAria2TaskSQLStore sharedStore] deleteTaskByTaskId:task.taskId];
    [[YYBAria2TaskSQLStore sharedStore] deleteTaskByGid:task.gid];
    // 删除本地文件
    if (deleteFile) {
        [self deleteFilesForTask:task];
    }
    task.isStarted = NO;
    YYBMacLogWarn(kLogTag, @"[cleanupDirtyTask] 已清理脏任务: %@, taskId=%@, gid=%@, deleteFile=%@", task.url, task.taskId, task.gid, deleteFile ? @"YES" : @"NO");
}

- (void)deleteFilesForTask:(YYBAria2Task *)task {
    if (!task) return;
    if (!task.finalFilePath || !task.finalFilePath.length) {
        YYBMacLogInfo(kLogTag, @"[deleteFilesForTask] 删除文件失败， finalFilePath为空: taskId = %@", task.taskId);
        return;
    }
    NSString *finalFilePath = [task.finalFilePath copy];
    NSString *aria2File = [finalFilePath stringByAppendingString:@".aria2"];
    NSString *taskDir = [task.destPath copy];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSError *err = nil;
        if (finalFilePath.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:finalFilePath]) {
            [[NSFileManager defaultManager] removeItemAtPath:finalFilePath error:&err];
            if (err) {
                YYBMacLogError(kLogTag, @"[deleteFilesForTask] 删除文件失败: %@, error=%@", finalFilePath, err);
            } else {
                YYBMacLogInfo(kLogTag, @"[deleteFilesForTask] 删除文件: %@", finalFilePath);
            }
        }
        if (aria2File.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:aria2File]) {
            err = nil;
            [[NSFileManager defaultManager] removeItemAtPath:aria2File error:&err];
            if (err) {
                YYBMacLogError(kLogTag, @"[deleteFilesForTask] 删除aria2临时文件失败: %@, error=%@", aria2File, err);
            } else {
                YYBMacLogInfo(kLogTag, @"[deleteFilesForTask] 删除aria2临时文件: %@", aria2File);
            }
        }
        if (taskDir.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:taskDir]) {
            err = nil;
            NSArray *contents = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:taskDir error:nil];
            if (contents.count == 0) {
                [[NSFileManager defaultManager] removeItemAtPath:taskDir error:&err];
                if (err) {
                    YYBMacLogError(kLogTag, @"[deleteFilesForTask] 删除任务目录失败: %@, error=%@", taskDir, err);
                } else {
                    YYBMacLogInfo(kLogTag, @"[deleteFilesForTask] 删除任务目录: %@", taskDir);
                }
            }
        }
    });
}

@end

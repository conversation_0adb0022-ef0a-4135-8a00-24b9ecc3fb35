//
//  YYBAria2TaskManager.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//

#import "YYBAria2TaskManager.h"
#import "YYBLibAria2ServiceFacade.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBAria2TaskSQLStore.h"
#import "YYBDirectory.h"
#import "YYBLibAria2SessionManager.h"
#import "YYBAria2TaskIdGidMap.h"
#import "MacroUtils.h"
#import "YYBAria2Error.h"

// ===================== 轮询间隔宏定义 =====================
#define YYB_ARIA2_POLL_INTERVAL_ACTIVE  1.0   // 有活跃任务时的轮询间隔（秒）
#define YYB_ARIA2_POLL_INTERVAL_IDLE   30.0   // 全部任务完成/暂停时的轮询间隔（秒）

static NSString *const kLogTag = @"YYBAria2TaskManager";
static NSString *const kDefaultLocalDownloadDir = @"localDownload";

@interface YYBAria2TaskManager ()

// 业务id->任务
@property (nonatomic, strong) NSMutableDictionary<NSString *, YYBAria2Task *> *taskIdMap;
// gid->任务
@property (nonatomic, strong) NSMutableDictionary<NSString *, YYBAria2Task *> *gidMap;
// 已删除任务(web业务还要用到进行最终的信息回调)
@property (nonatomic, strong) NSMutableDictionary<NSString *, YYBAria2Task *> *removeTaskIdMap;

// 线程安全
@property (nonatomic, strong) dispatch_queue_t syncQueue;

// 持久化/回调/校验
@property (nonatomic, strong) YYBAria2TaskSQLStore *taskSqlStore;
@property (nonatomic, strong) YYBAria2TaskCallbackCenter *callbackCenter;
@property (nonatomic, strong) YYBAria2TaskFileValidator *fileValidator;

// 下载服务
@property (nonatomic, strong) YYBLibAria2SessionManager *sessionManager;


// 网络/服务状态
@property (nonatomic, assign) BOOL networkReachable;
@property (nonatomic, assign) BOOL serviceRunning;

// 定时同步
@property (nonatomic) dispatch_source_t syncTimer;
@property (nonatomic, assign) NSTimeInterval currentPollInterval;

// 持久化优化
@property (nonatomic, strong) NSMutableSet<NSString *> *dirtyTaskIds;
@property (nonatomic, assign) BOOL isPersisting;

// 下载目录
@property (nonatomic, copy) NSString *localDownloadDir;

// gid是否进行过服务器状态校验
@property (nonatomic, assign) BOOL isValidateByServer;

@end

@implementation YYBAria2TaskManager

- (instancetype)initWithSessionManager:(YYBLibAria2SessionManager *)sessionManager
                        callbackCenter:(YYBAria2TaskCallbackCenter *)callbackCenter
                         fileValidator:(YYBAria2TaskFileValidator *)fileValidator {
    if (self = [super init]) {
        _sessionManager = sessionManager;
        _taskSqlStore = [YYBAria2TaskSQLStore sharedStore];
        _callbackCenter = callbackCenter;
        _fileValidator = fileValidator;
        _taskIdMap = [NSMutableDictionary dictionary];
        _gidMap = [NSMutableDictionary dictionary];
        _removeTaskIdMap = [NSMutableDictionary dictionary];
        _syncQueue = dispatch_queue_create("com.yyb.aria2taskmanager.sync", DISPATCH_QUEUE_SERIAL);
        _dirtyTaskIds = [NSMutableSet set];
        _isPersisting = NO;
        _networkReachable = YES;
        _serviceRunning = YES;
        [self restoreTasksFromDisk];
        [self startSyncTimer];
    }
    return self;
}

- (void)dealloc
{
    if (self.syncTimer) {
        dispatch_source_cancel(self.syncTimer);
    }
}

#pragma mark - 下载目录逻辑
- (NSString *)localDownloadDir {
    NSString *dir = _localDownloadDir;
    if (!dir) {
        NSString *aria2DownloadDir = [[YYBLibAria2ServiceFacade sharedService] currentDownloadDir];
        dir = [aria2DownloadDir stringByAppendingPathComponent:kDefaultLocalDownloadDir];
        _localDownloadDir = dir;
    }
    // 每次都check并自动创建
    return [YYBDirectory ensureDirectoryExists:dir];
}

#pragma mark - 任务创建和查重
- (YYBAria2Task *)createDownloadTaskWithURL:(NSString *)url
                                    destDir:(nullable NSString *)destDir
                                   fileName:(nullable NSString *)fileName
                                        md5:(nullable NSString *)md5
{
    if (!url || url.length == 0) return nil;
    NSString *realFileName = fileName.length > 0 ? fileName : [[NSURL URLWithString:url].path lastPathComponent];
    NSString *realDestDir = (destDir.length > 0) ? destDir : self.localDownloadDir;

    // 1. 优先本地MD5查重
    if (md5.length > 0) {
        YYBAria2Task *foundTask = [self findLocalFileWithMD5:md5 needComplete:NO];
        if (foundTask) {
            YYBMacLogInfo(kLogTag, @"[createDownloadTask] 复用本地任务(md5匹配): %@",foundTask);
            return foundTask;
        }
    }
    
    // 2. 本地任务查重
    __block YYBAria2Task *existingTask = nil;
    dispatch_sync(self.syncQueue, ^{
        existingTask = [self findTaskByURL:url destDir:realDestDir fileName:realFileName];
    });
    if (existingTask) {
        YYBMacLogInfo(kLogTag, @"[createDownloadTask] 复用本地已有任务(url&destDir&fileName匹配): %@", existingTask);
        return existingTask;
    }
    
    // 3. 新建任务
    NSString *taskId = [[NSUUID UUID] UUIDString];
    NSString *taskDir = realDestDir;
    [[NSFileManager defaultManager] createDirectoryAtPath:taskDir withIntermediateDirectories:YES attributes:nil error:nil];
    YYBAria2Task *task = [[YYBAria2Task alloc] initWithTaskId:taskId md5:md5 url:url destPath:taskDir];
    task.fileName = realFileName;
    task.retryCount = 0;
    task.isStarted = NO;
    task.manager = self;
    
    // 4. 状态机初始化
    dispatch_async(self.syncQueue, ^{
        YYBAria2TaskStatus status = YYBAria2TaskStatusPending;
        if (!self.serviceRunning) {
            status = YYBAria2TaskStatusWaitingForService;
        } else if (!self.networkReachable) {
            status = YYBAria2TaskStatusWaitingForNetwork;
        } else {
            status = YYBAria2TaskStatusPending;
        }
        [task updateStatus:status reason:@"创建任务"];
        self.taskIdMap[taskId] = task;
        [self.dirtyTaskIds addObject:taskId];
        [self adjustPollIntervalIfNeeded];
    });
    // 5. 数据库持久化
    [self.taskSqlStore insertOrUpdateTask:task];
    YYBMacLogInfo(kLogTag, @"[createDownloadTask] 新建任务: %@", task);
    return task;
}

- (YYBAria2Task *)findTaskByURL:(NSString *)url destDir:(NSString *)destDir fileName:(NSString *)fileName {
    for (YYBAria2Task *task in self.taskIdMap.allValues) {
        if ([task.url isEqualToString:url] &&
            [task.fileName isEqualToString:fileName] &&
            [task.destPath isEqualToString:destDir]) {
            // 1. 文件损坏，清理任务和文件
            if (![self.fileValidator validateTaskFile:task]) {
                YYBMacLogWarn(kLogTag, @"[findTaskByURL] 查重命中文件损坏任务，已清理: %@", task);
                [self removeTask:task deleteFile:YES byUser:NO];
                return nil;
            }

            // 2. 文件没坏，但不可复用
            if (![task canReusable]) {
                YYBMacLogInfo(kLogTag, @"[findTaskByURL] 查重命中不可复用任务: %@", task);
                return nil;
            }

            // 3. 可复用且文件正常
            return task;
        }
    }
    return nil;
}

- (nullable YYBAria2Task *)findLocalCompleteFileWithMD5:(NSString *)md5 {
    return [self findLocalFileWithMD5:md5 needComplete:YES];
}

- (nullable YYBAria2Task *)findLocalFileWithMD5:(NSString *)md5 needComplete:(BOOL)needComplete {
    if (!md5 || md5.length == 0) return nil;
    __block YYBAria2Task *foundTask = nil;
    dispatch_sync(self.syncQueue, ^{
        for (YYBAria2Task *task in self.taskIdMap.allValues) {
            if ([task.md5 isEqualToString:md5]) {
                if ((!needComplete) || (task.status == YYBAria2TaskStatusComplete))  {
                    BOOL validateTaskFile = [self.fileValidator validateTaskFile:task];
                    if ([task canReusable] && validateTaskFile) {
                        foundTask = task;
                        break;
                    }
                    if (!validateTaskFile) {
                        YYBMacLogWarn(kLogTag, @"[findLocalFileWithMD5] 查重命中不可用任务，已清理: %@", task);
                        [self removeTask:task deleteFile:YES byUser:NO];
                    }
                }
            }
        }
    });
    if (foundTask) {
        YYBMacLogInfo(kLogTag, @"[findLocalFileWithMD5] 找到可复用文件 结果：%@", foundTask);
        return foundTask;
    }
    YYBMacLogInfo(kLogTag, @"[findLocalFileWithMD5] 查找md5=%@，结果：未找到", md5);
    return nil;
}

/// 查询所有“有效”任务（不含已删除/移除任务）
- (NSArray<YYBAria2Task *> *)allTasks {
    __block NSArray *tasks = nil;
    dispatch_sync(self.syncQueue, ^{
        NSMutableSet *allTasks = [NSMutableSet set];
        [allTasks addObjectsFromArray:self.taskIdMap.allValues];
        tasks = [allTasks allObjects];
    });
    return tasks;
}

/// 查询单个任务
- (YYBAria2Task *)taskForTaskId:(NSString *)taskId {
    __block YYBAria2Task *task = nil;
    dispatch_sync(self.syncQueue, ^{
        task = self.taskIdMap[taskId];
    });
    return task;
}

/// 查询所有“已删除”任务
- (NSArray<YYBAria2Task *> *)allRemoveTasks {
    __block NSArray *tasks = nil;
    dispatch_sync(self.syncQueue, ^{
        NSMutableSet *allTasks = [NSMutableSet set];
        [allTasks addObjectsFromArray:self.removeTaskIdMap.allValues];
        tasks = [allTasks allObjects];
    });
    return tasks;
}

#pragma mark - 任务启动/回调注册
- (void)startTask:(YYBAria2Task *)task {
    if (!task) {
        return;
    }
    dispatch_async(self.syncQueue, ^{
        task.isStarted = YES;
        task.retryCount = 0;
        task.userPaused = NO;
        
        BOOL fileValid = [self.fileValidator validateTaskFile:task];
        if (!fileValid) {
            // 文件校验失败，立即置为ErrorFatal
            [task updateStatus:YYBAria2TaskStatusErrorFatal reason:@"start时，发现文件校验失败，强制置为错误"];
        }
        if (task.status == YYBAria2TaskStatusPaused) {
            [self resumeTask:task completion:^(BOOL success, NSError * _Nullable error) {
                YYBMacLogInfo(kLogTag, @"[startTask] 自动恢复暂停任务: %@, result=%@, error=%@", task.taskId, @(success), error);
                if (!success) {
                    [task updateStatus:[self checkErrorStatus] reason:@"用户启动任务（恢复）失败"];
                    NSError *currentError = error ?: [YYBAria2Error errorWithCode:error.code
                                                                           taskId:task.taskId
                                                                          message:@"恢复任务失败-start" userInfo:@{@"error": error ?: @""}];
                    [self.callbackCenter dispatchStatus:task.status forTask:task error:currentError];
                }
            }];
        } else if (task.status == YYBAria2TaskStatusComplete) {
            // 任务已完成且前面已校验完整性，直接回调
            [self.callbackCenter dispatchStatus:task.status forTask:task error:nil];
        } else {
            [self tryResumeAllRecoverableTasks];
        }
        [self adjustPollIntervalIfNeeded];
    });
}

- (void)startTaskByRpc:(YYBAria2Task *)task
        resultCallBack:(void(^)(NSString *gid, NSError *error))resultCallBack {
    if (!task) {
        return;
    }
    dispatch_async(self.syncQueue, ^{
        task.isStarted = YES;
        task.userPaused = NO;
        // rpc模式直接启动，不依赖心跳，快速返回
        [self realAddDownloadWithTask:task needTry:NO completion:^(NSString *gid, NSError *error) {
            if (resultCallBack) {
                resultCallBack(gid, error);
            }
        }];
    });
}

#pragma mark - 任务操作
- (void)cancelTask:(YYBAria2Task *)task deleteFile:(BOOL)deleteFile {
    [self cancelTask:task deleteFile:deleteFile completion:nil];
}

- (void)cancelTask:(YYBAria2Task *)task
        deleteFile:(BOOL)deleteFile
        completion:(void(^)(BOOL success, NSError * _Nullable error))completion {
    if (!task) {
        if (completion) completion(NO, [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"任务为空-cancel" userInfo:nil]);
        return;
    }
    dispatch_async(self.syncQueue, ^{
        NSString *gid = task.gid;
        // 1. 先移除任务（服务端和本地内存）
        if (gid && self.gidMap[gid]) {
            [self.sessionManager removeDownload:gid completion:^(BOOL success, NSError *error) {
                YYBMacLogInfo(kLogTag, @"[cancelTask] removeDownload: gid=%@, result=%@, error=%@", gid, @(success), error);
                if (completion) {
                    completion(success, error);
                }
            }];
            [self.gidMap removeObjectForKey:task.gid];
        }
        
        YYBMacLogInfo(kLogTag, @"[cancelTask] 取消已下发任务: %@", task);
        // 发送并清理回调
        [task updateStatus:YYBAria2TaskStatusRemoved reason:@"任务被用户取消"];
        NSError *error = [YYBAria2Error errorWithCode:YYBAria2ErrorUserCancelled taskId:task.taskId message:@"任务被用户取消-cancel" userInfo:nil];
        [self.callbackCenter dispatchStatus:task.status forTask:task error:error];
        [self.callbackCenter clearCallbacksForTask:task];
        
        task.gid = nil;
        // 清理持久化和文件
        [self removeTask:task deleteFile:deleteFile byUser:YES];
        
        // 更新心跳
        [self adjustPollIntervalIfNeeded];
    });
}

- (void)pauseTask:(YYBAria2Task *)task
           byUser:(BOOL)byUser
       completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion {
    if (!task) {
        if (completion) completion(NO, [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"任务为空-pause" userInfo:nil]);
        return;
    }
    if (byUser) {
        task.userPaused = YES;
    }
    if (task.gid && self.gidMap[task.gid]) {
        [self.sessionManager pauseDownload:task.gid completion:^(BOOL success, NSError *error) {
            // 不管成功与否，都更新为用户期望状态
            NSString *reason = [NSString stringWithFormat:@"任务被用户暂停（已有gid）, 暂停结果=%@",@(success)];
            [task updateStatus:YYBAria2TaskStatusPaused reason:reason];
            if (completion) completion(success, error);
            [self adjustPollIntervalIfNeeded];
        }];
    } else {
        [task updateStatus:YYBAria2TaskStatusPaused reason:@"任务被用户暂停(暂无gid)"];
        if (completion) completion(YES, nil);
        [self adjustPollIntervalIfNeeded];
    }
}

- (void)resumeTask:(YYBAria2Task *)task completion:(void(^)(BOOL success, NSError * _Nullable error))completion {
    if (!task) {
        if (completion) completion(NO, [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"任务为空-resumeTask" userInfo:nil]);;
        return;
    }
    task.userPaused = NO;
    if (task.gid && self.gidMap[task.gid]) {
        [self.sessionManager resumeDownload:task.gid completion:^(BOOL success, NSError *error) {
            NSString *reason = [NSString stringWithFormat:@"任务被用户恢复（已有gid）, 恢复结果=%@",@(success)];
            BOOL realySuccess = !success && error.code != -1;
            if (realySuccess) {
                // 为-1表示重入（已恢复了，但又重复调用了恢复,或者对已完成任务调用了恢复），这里兜底对-1的报错不判定为失败
                [task updateStatus:[self checkErrorStatus] reason:reason];
            } else {
                // 更新为用户期望状态
                [task updateStatus:YYBAria2TaskStatusWaiting reason:reason];
            }
            if (completion) completion(realySuccess, error);
            [self adjustPollIntervalIfNeeded];
        }];
    } else {
        YYBMacLogInfo(kLogTag, @"[resumeTask] 用户尝试恢复时发现无gid，直接adduri, taskInfo=%@", task);
        [self realAddDownloadWithTask:task needTry:task.source != YYBAria2TaskSourceWeb completion:^(NSString *gid, NSError *error) {
            if (completion) {
                // 有gid，代表成功
                completion(!error, error);
            }
            [self adjustPollIntervalIfNeeded];
        }];
    }
}

- (void)clearAllTasksDeleteFiles:(BOOL)deleteFiles {
    dispatch_async(self.syncQueue, ^{
        // 1. 清理服务端所有任务
        NSArray *allGids = [self.gidMap.allKeys copy];
        for (NSString *gid in allGids) {
            YYBAria2Task *task = self.gidMap[gid];
            [self.sessionManager removeDownload:gid completion:^(BOOL success, NSError *error) {
                YYBMacLogInfo(kLogTag, @"[clearAllTasksDeleteFiles] removeDownload: gid=%@, result=%@, error=%@, taskInfo=%@", gid, @(success), error, task);
                // 2. 删除文件（如需）
                if (task && deleteFiles) {
                    [self.fileValidator deleteFilesForTask:task];
                }
            }];
            [[YYBAria2TaskIdGidMap sharedMap] unbindTaskId:task.taskId];
            [[YYBAria2TaskIdGidMap sharedMap] unbindGid:task.gid];
        }
        // 3. 清理SQL记录
        [self.taskSqlStore clearAllTasks];
        // 4. 清理内存
        [self.taskIdMap removeAllObjects];
        [self.gidMap removeAllObjects];
        [self.dirtyTaskIds removeAllObjects];
        YYBMacLogInfo(kLogTag, @"[clearAllTasksDeleteFiles] 已清理所有任务，deleteFiles=%@", deleteFiles ? @"YES" : @"NO");
    });
}

/// 彻底清理任务（内存、回调、SQL、文件）
/// @param task 任务对象
/// @param deleteFile 是否删除本地文件
/// @param byUser 是否为用户主动删除
- (void)removeTask:(YYBAria2Task *)task deleteFile:(BOOL)deleteFile byUser:(BOOL)byUser {
    if (!task) return;
    dispatch_async(self.syncQueue, ^{
        task.isStarted = NO;
        // 0. 清理aria2里面的gid，避免重复触发时报错13
        NSString *gid = task.gid;
        if (gid) {
            [self.sessionManager removeDownload:gid completion:^(BOOL success, NSError *error) {
                YYBMacLogInfo(kLogTag, @"[removeTask] removeDownload: gid=%@, result=%@, error=%@", gid, @(success), error);
            }];
        }
        // 1. 清理回调(如果不是用户清除，则肯定是有异常, 清理前先发出通知)
        if (!byUser) {
            [task updateStatus:YYBAria2TaskStatusErrorFatal reason:@"任务异常，被彻底移除（通常为本地文件不合法或缺失）"];
            NSError *error = [YYBAria2Error errorWithCode:YYBAria2ErrorFileCorrupted taskId:task.taskId message:@"任务异常，被彻底移除（通常为本地文件不合法或缺失）" userInfo:nil];;
            [self.callbackCenter dispatchStatus:task.status forTask:task error:error];
        }

        [self.callbackCenter clearCallbacksForTask:task];
        // 2. 清理内存
        if (task.taskId) {
            self.removeTaskIdMap[task.taskId] = task;
            [self.taskIdMap removeObjectForKey:task.taskId];
        }
        if (task.gid) {
            [self.gidMap removeObjectForKey:task.gid];
        }
        // 3. 清理SQL和本地文件
        [self.fileValidator cleanupDirtyTask:task deleteFile:deleteFile];
        
        YYBMacLogWarn(kLogTag, @"[removeTask] 已彻底清理任务:deleteFile=%@, taskInfo=%@", deleteFile ? @"YES" : @"NO", task);
    });
}

#pragma mark - 网络/服务/APP事件
- (void)onNetworkChange:(BOOL)reachable {
    dispatch_async(self.syncQueue, ^{
        self.networkReachable = reachable;
        YYBMacLogInfo(kLogTag, @"[Network] 网络状态变化: %@", reachable ? @"可用" : @"不可用");
        if (reachable) {
            [self checkAndValidateAllGidsAfterRestore];
            [self tryResumeAllRecoverableTasks];
            [self pollAllTasks]; // 网络恢复时主动刷新一次
            [self adjustPollIntervalIfNeeded];
        } else {
            for (YYBAria2Task *task in self.taskIdMap.allValues) {
                if (!task.gid) {
                    // 没有gid的任务，全切换为等待网络
                    [task updateStatus:YYBAria2TaskStatusWaitingForNetwork reason:@"网络不可用"];
                }
            }
        }
    });
}

- (void)onServiceAvailable {
    self.serviceRunning = YES;
    
    // 服务刚起来，批量校验gid
    [self checkAndValidateAllGidsAfterRestore];
    [self tryResumeAllRecoverableTasks];
    [self pollAllTasks];
    [self adjustPollIntervalIfNeeded];
}

- (void)checkAndValidateAllGidsAfterRestore {
    if (self.isValidateByServer) {
        // 已校验过不再重复校验
        return;
    }
    if (!self.serviceRunning || !self.networkReachable) {
        // 服务启来且有网，才进行一次校验
        return;
    }
    self.isValidateByServer = YES;
    [self validateAllGidsAfterRestore];
}

- (void)onServiceUnavailable {
    self.serviceRunning = NO;
    self.isValidateByServer = NO;
    
    dispatch_async(self.syncQueue, ^{
        for (YYBAria2Task *task in self.taskIdMap.allValues) {
            if (!task.gid) {
                // 没有gid的任务，全切换为等待服务
                [task updateStatus:YYBAria2TaskStatusWaitingForService reason:@"服务不可用"];
            }
        }
    });
}

- (void)onAppDidBecomeActive {
    // 回前台时主动检测网络并尝试恢复任务
    [self tryResumeAllRecoverableTasks];
    [self pollAllTasks];
}

- (void)onAppWillResignActive {
    [self persistDirtyTasksIfNeeded];
}

- (void)onAppWillTerminate {
    [self persistDirtyTasksIfNeeded];
}

#pragma mark - 断点续传/任务恢复
- (void)tryResumeAllRecoverableTasks {
    dispatch_async(self.syncQueue, ^{
        if (!self.serviceRunning || !self.networkReachable) {
            return;
        }
        for (YYBAria2Task *task in self.taskIdMap.allValues) {
            [self tryResumeTask:task];
        }
    });
}

- (void)tryResumeTask:(YYBAria2Task *)task {
    if (!task.isStarted) {
        return;
    }
    
    if ([task isRecoverable]) {
        if (!task.gid.length) {
            [self realAddDownloadWithTask:task];
        } else {
            if ([task canUnpause]) {
                [self resumeTask:task completion:^(BOOL success, NSError * _Nullable error) {
                    YYBMacLogInfo(kLogTag, @"[tryResumeAllRecoverableTasks] 恢复结果: %@, error=%@", @(success), error);
                }];
            } else {
                // 清空gid，重新去换gid
                [self.gidMap removeObjectForKey:task.gid];
                task.gid = nil;
                [task updateStatus:YYBAria2TaskStatusPending reason:@"尝试恢复任务时发现gid不存在，还原"];
                [self realAddDownloadWithTask:task];
            }
        }
    }
}

- (BOOL)canUnpauseTask:(YYBAria2Task *)task {
    return (task.status == YYBAria2TaskStatusPaused);
}


#pragma mark - 任务下发/重试机制
- (void)realAddDownloadWithTask:(YYBAria2Task *)task {
    [self realAddDownloadWithTask:task needTry:YES completion:nil];
}

- (void)realAddDownloadWithTask:(YYBAria2Task *)task
                        needTry:(BOOL)needTry
                     completion:(nullable void(^)(NSString *gid, NSError *error))completion {
    if (![task canAddUri]) {
        YYBMacLogInfo(kLogTag, @"任务已添加过且不是终态，不能重复添加~ %@", task);
        if (completion) {
            [YYBAria2Error errorWithCode:YYBAria2ErrorAddUriDuplicate taskId:task.taskId message:@"任务不能重复添加-addUri" userInfo:@{@"status": task.statusString}];
            // 不返回error，否则web侧会判定为失败
            completion(task.gidForUser, nil);
        }
        return;
    }
    if (task.inAddUrl) {
        YYBMacLogInfo(kLogTag, @"任务在添加中，不能重复添加~ %@", task);
        if (completion) {
            [YYBAria2Error errorWithCode:YYBAria2ErrorAddUriDuplicate taskId:task.taskId message:@"任务添加中，不能重复添加-addUri" userInfo:nil];
            // 不返回error，否则web侧会判定为失败
            completion(task.gidForUser, nil);
        }
        return;
    }
    task.inAddUrl = YES;
    NSString *outFile = task.fileName;
    NSString *dir = task.destPath;
    NSDictionary *options = @{@"out": outFile, @"dir": dir};
    __weak typeof(self) weakSelf = self;
    [self.sessionManager addDownloadWithURL:task.url options:options completion:^(NSString *gid, NSError *error) {
        if (!weakSelf) return;
        dispatch_async(weakSelf.syncQueue, ^{
            task.inAddUrl = NO;
            if (gid && gid.length) {
                if (task.gid) {
                    // 如果此前有gid，一定要先清理 并解绑
                    [weakSelf.gidMap removeObjectForKey:task.gid];
                    [[YYBAria2TaskIdGidMap sharedMap] unbindGid:task.gid];
                    [[YYBAria2TaskIdGidMap sharedMap] unbindTaskId:task.taskId];
                }
                task.gid = gid;
                weakSelf.gidMap[gid] = task;
                // 1. 保存taskid和gid的映射关系
                [[YYBAria2TaskIdGidMap sharedMap] bindTaskId:task.taskId toGid:gid];
                YYBMacLogInfo(kLogTag, @"[realAddDownloadWithTask] 任务下发成功: %@", task);
                [task updateStatus:YYBAria2TaskStatusWaiting reason:@"任务下发成功"];
                [weakSelf adjustPollIntervalIfNeeded];
                [weakSelf pollAllTasks];
                if (completion) {
                    // 2. 返回对外的gid
                    completion(task.gidForUser, nil);
                }
            } else {
                [task updateStatus:YYBAria2TaskStatusRetrying reason:@"任务下发失败，准备重试（rpc任务不重试）"];
                task.retryCount++;
                NSUInteger baseDelay = MIN(pow(2, task.retryCount), 60);
                NSUInteger jitter = arc4random_uniform(1000) / 1000.0 * baseDelay;
                NSUInteger delay = baseDelay + jitter;
                if (needTry && task.retryCount <= task.maxRetryCount) {
                    YYBMacLogWarn(kLogTag, @"[%@][realAddDownloadWithTask] 任务下发失败，第%lu次重试，%lu秒后重试: %@, error=%@", kLogTag, (unsigned long)task.retryCount, (unsigned long)delay, task.url, error);
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delay * NSEC_PER_SEC)), weakSelf.syncQueue, ^{
                        __strong typeof(weakSelf) strongSelf = weakSelf;
                        if (!strongSelf) return;
                        [strongSelf realAddDownloadWithTask:task];
                    });
                } else {
                    YYBMacLogError(kLogTag, @"[realAddDownloadWithTask] 任务下发失败，已达最大重试次数: error=%@, taskInfo=%@", error, task);
                    [task updateStatus:[weakSelf checkErrorStatus] reason:@"任务下发失败，重试超限"];
                    NSError *currentError = [YYBAria2Error errorWithCode:error.code taskId:task.taskId message:@"任务下发失败,重试超限" userInfo:nil];
                    [weakSelf.callbackCenter dispatchStatus:task.status forTask:task error:currentError];
                    if (completion) {
                        completion(task.gidForUser, error);
                    }
                }
                [weakSelf adjustPollIntervalIfNeeded];
            }
        });
    }];
}

#pragma mark - 定时同步（自适应轮询间隔）
- (void)startSyncTimer {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        self.currentPollInterval = YYB_ARIA2_POLL_INTERVAL_ACTIVE;
        self.syncTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, self.syncQueue);
        dispatch_source_set_timer(self.syncTimer, dispatch_time(DISPATCH_TIME_NOW, 0), self.currentPollInterval * NSEC_PER_SEC, 0.1 * NSEC_PER_SEC);
        __weak typeof(self) weakSelf = self;
        dispatch_source_set_event_handler(self.syncTimer, ^{
            [weakSelf pollAllTasks];
        });
        dispatch_resume(self.syncTimer);
    });
}

- (void)adjustPollIntervalIfNeeded {
    BOOL hasActive = NO;
    for (YYBAria2Task *task in self.taskIdMap.allValues) {
        if ([task isActiveOrWaiting]) {
            hasActive = YES;
            break;
        }
    }
    NSTimeInterval newInterval = hasActive ? YYB_ARIA2_POLL_INTERVAL_ACTIVE : YYB_ARIA2_POLL_INTERVAL_IDLE;
    if (self.currentPollInterval != newInterval) {
        self.currentPollInterval = newInterval;
        if (self.syncTimer) {
            dispatch_source_set_timer(self.syncTimer, dispatch_time(DISPATCH_TIME_NOW, 0), self.currentPollInterval * NSEC_PER_SEC, 0.1 * NSEC_PER_SEC);
            YYBMacLogInfo(kLogTag, @"[Timer] 轮询间隔调整为%.1fs", self.currentPollInterval);
            if (newInterval == YYB_ARIA2_POLL_INTERVAL_ACTIVE) {
                [self pollAllTasks];
            }
        }
    }
}

#pragma mark - 任务状态同步
- (void)handleAria2Event:(YYBLibAria2DownloadEvent)event
                  taskId:(NSString *)taskId
                    info:(NSDictionary *)info
              completion:(void(^)(void))completion {
    dispatch_async(self.syncQueue, ^{
        YYBAria2Task *task = self.taskIdMap[taskId];
        if (!task) {
            DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
                if (completion) {
                    completion();
                }
            });
            return;
        }
        [self updateTask:task withTaskInfo:info source:@"event-changed"];
        YYBMacLogInfo(kLogTag, @"[handleAria2Event] 收敛到updateTask, event=%ld, taskId=%@, info=%@", (long)event, taskId, info);
        [self adjustPollIntervalIfNeeded];
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            if (completion) {
                completion();
            }
        });
    });
}

- (void)pollAllTasks {
    // 1. 获取所有本地gid快照，防止遍历时被修改
    NSArray *allGids = [[self.gidMap allKeys] copy];
    if (allGids.count == 0) return;
    
    // 2. 只对需要同步的任务继续查询（活跃、等待、暂停、重试、校验等状态）
    NSMutableArray *gidsToQuery = [NSMutableArray array];
    for (NSString *gid in allGids) {
        YYBAria2Task *task = self.gidMap[gid];
        if (!task) continue;
        if ([task needsStatusSync]) {
            [gidsToQuery addObject:gid];
        }
    }
    if (gidsToQuery.count == 0) return;
    
    // 3. 遍历查询每个gid的状态
    dispatch_group_t group = dispatch_group_create();
    NSMutableSet *remoteGids = [NSMutableSet set];
    for (NSString *gid in gidsToQuery) {
        NSString *currentGid = [gid copy];
        dispatch_group_enter(group);
        [self.sessionManager progressForGID:currentGid completion:^(NSDictionary *progress) {
            dispatch_async(self.syncQueue, ^{
                if (!progress || !progress.count) {
                    YYBMacLogError(kLogTag, @"[pollAllTasks] progressForGID接口异常: gid=%@", currentGid);
                    dispatch_group_leave(group);
                    return;
                }
                if (currentGid && [currentGid isKindOfClass:[NSString class]] && currentGid.length > 0) {
                    [remoteGids addObject:currentGid];
                }
                YYBAria2Task *task = self.gidMap[currentGid];
                if (!task) {
                    dispatch_group_leave(group);
                    return;
                }
                [self updateTask:task withTaskInfo:progress source:@"postProgress"];
                dispatch_group_leave(group);
            });
        }];
    }
    
    // 4. 检查本地gidMap中有但服务端已无的任务（被外部删除、已完成、已失败、被移除或异常丢失）
    dispatch_group_notify(group, self.syncQueue, ^{
        for (NSString *gid in gidsToQuery) {
            if ([remoteGids containsObject:gid]) continue;
            YYBAria2Task *task = self.gidMap[gid];
            if (!task) continue;
            if ([task inFinalState]) {
                // 终态会被web侧从服务器清理，无需再check
                continue;
            }

            // 4.1 记录详细日志(非终态才记录)
            YYBMacLogWarn(kLogTag, @"[pollAllTasks] 本地任务在服务端可能已消失，%@", task);
            
            // 4.2 本地文件不合法,则清理（但不清理文件）
            if (![self.fileValidator validateTaskFile:task]) {
                [self removeTask:task deleteFile:NO byUser:NO];
            }
        }
        [self adjustPollIntervalIfNeeded];
    });
}

- (void)updateTask:(YYBAria2Task *)task withTaskInfo:(NSDictionary *)taskInfo source:(NSString *)source {
    // 1. 解析状态字符串
    NSString *errorMessage = taskInfo[@"errorMessage"] ?: @"下载失败";
    YYBAria2TaskStatus oldStatus = task.status;
    double oldProgress = task.progress;
    
    // 2. 只在有 key 时才更新属性
    if (taskInfo[@"totalLength"] && ![taskInfo[@"totalLength"] isKindOfClass:[NSNull class]]) {
        int64_t total = [taskInfo[@"totalLength"] longLongValue];
        if (total > 0) {
            task.totalLength = total;
            int64_t completed = [taskInfo[@"completedLength"] longLongValue];
            task.completedLength = completed;
            double progress = (double)completed / total;
            task.progress = progress;
        }
    }
    
    // fileName & finalFilePath
    NSString *fileName = task.fileName;
    NSString *finalPath = task.finalFilePath;
    if ([taskInfo[@"files"] isKindOfClass:[NSArray class]] && [taskInfo[@"files"] count] > 0) {
        NSDictionary *fileInfo = taskInfo[@"files"][0];
        if (fileInfo[@"path"] && ![fileInfo[@"path"] isKindOfClass:[NSNull class]]) {
            fileName = [fileInfo[@"path"] lastPathComponent];
            finalPath = fileInfo[@"path"];
            task.fileName = fileName;
            task.finalFilePath = finalPath;
        }
    }
    
    // 3. 状态
    int errorCode = [taskInfo[@"errorCode"] intValue];
    if (taskInfo[@"status"] && ![taskInfo[@"status"] isKindOfClass:[NSNull class]]) {
        NSString *stat = [taskInfo[@"status"] stringValue];
        YYBAria2TaskStatus oldStatus = task.status;
        YYBAria2TaskStatus newStatus = [self statusFromString:stat];
        NSString *reasonString = newStatus == YYBAria2TaskStatusErrorFatal ?
        [NSString stringWithFormat:@"updateTask过程中发现失败, errorcode = %@", @(errorCode)] : @"updateTask过程中发现状态变化";
        // 特殊情况 ，报错13时，是add-url重复添加了的报错
        if (errorCode == 13) {
#ifdef DEBUG
            NSAssert(NO, @"%@, 重复添加Url， 请关注！taskInfo = %@", kLogTag, task);
#endif
            YYBMacLogError(kLogTag, @"重复添加Url， 请关注！！");
        }
        if (oldStatus != newStatus) {
            [task updateStatus:newStatus reason:[NSString stringWithFormat:@"source = %@,reason = %@", source, reasonString]];
        }
    }
    
    // 4. 持久化状态更新
    BOOL statusChanged = (oldStatus != task.status);
    if (statusChanged || fabs(task.progress - oldProgress) > 0.001) {
        [self.dirtyTaskIds addObject:task.taskId];
        [self schedulePersistDirtyTasks];
    }
    
    // 5. 进度回调
    [self.callbackCenter dispatchProgress:task.progress forTask:task];
 
    // 6. 终态处理
    [self updateTaskWithFinal:task
                    errorCode:errorCode
                 errorMessage:errorMessage];
}

- (void)updateTaskWithFinal:(YYBAria2Task *)task
                  errorCode:(int)errorCode
               errorMessage:(NSString *)errorMessage {
    if (![task inFinalState]) {
        return;
    }
    
    // 1 文件校验
    BOOL fileValid = [self.fileValidator validateTaskFile:task];
    BOOL needRetry = NO;
    NSInteger retryErrorCode = 0;
    BOOL isComplete = (task.status == YYBAria2TaskStatusComplete);
    
    if (!fileValid) {
        // 文件校验失败，立即置为ErrorFatal
        [task updateStatus:YYBAria2TaskStatusErrorFatal reason:@"文件校验失败，强制置为错误"];
        [self.fileValidator deleteFilesForTask:task];
        YYBMacLogError(kLogTag, @"[updateTask] 任务完成但文件校验失败，已置为ErrorFatal并删除文件: taskId=%@", task.taskId);
        needRetry = YES;
        retryErrorCode = -3;
        isComplete = NO;
    } else if (task.status == YYBAria2TaskStatusErrorFatal && fileValid) {
        // 状态为ErrorFatal但文件没坏，允许重试
        needRetry = YES;
        retryErrorCode = -4;
    } else if (task.status == YYBAria2TaskStatusErrorNetwork) {
        // 网络错误，等待自动恢复
        YYBMacLogWarn(kLogTag, @"[updateTask] 网络/服务不可用导致错误，任务还可待网络ok后尝试自动恢复: error=%@, taskInfo=%@", errorMessage, task);
    }
    
    // 2 重试逻辑(不清理回调，避免丢失业务回调, web旁路任务不重试)
    if (needRetry && task.source != YYBAria2TaskSourceWeb) {
        [self handleTaskRetryIfNeeded:task
                            errorCode:retryErrorCode
                         errorMessage:@"任务失败，准备重试"
                           logMessage:@"任务失败，准备重试"];
        return;
    }
    
    // 不为网络错误则清空回调
    BOOL clearCallBacks = task.status != YYBAria2TaskStatusErrorNetwork;
    
    // 3 回调业务侧
    NSError *error = (!isComplete) ? [YYBAria2Error errorWithCode:errorCode message:errorMessage userInfo:nil] : nil;
    [self.callbackCenter dispatchStatus:task.status forTask:task error:error];
    
    // 4 强制写入
    [self.taskSqlStore insertOrUpdateTask:task];
    
    // 5 清理回调
    if (clearCallBacks) {
        [self.callbackCenter clearCallbacksForTask:task];
    }
    YYBMacLogInfo(kLogTag, @"[updateTask] 任务进入终态: %@, 回调清理: %@, taskId=%@", @(task.status), @(clearCallBacks), task.taskId);
}

/// 检查重试次数，超限则终止任务并回调失败，否则返回YES表示可以继续重试
- (BOOL)handleTaskRetryIfNeeded:(YYBAria2Task *)task
                      errorCode:(NSInteger)errorCode
                   errorMessage:(NSString *)errorMessage
                     logMessage:(NSString *)logMessage {
    task.retryCount++;
    if (task.retryCount > task.maxRetryCount) {
        // 超过最大重试次数，终止任务
        YYBMacLogError(kLogTag, @"[%@][updateTask] %@，已达最大重试次数(%ld)，任务终止: %@, taskId=%@", kLogTag, logMessage, (long)task.maxRetryCount, task.finalFilePath, task.taskId);
        NSError *error = [YYBAria2Error errorWithCode:errorCode taskId:task.taskId message:[NSString stringWithFormat:@"%@，已达最大重试次数", errorMessage] userInfo:nil];
        [self.callbackCenter dispatchStatus:task.status forTask:task error:error];
        // 强制写入并清理回调（但不删除记录）
        [self.taskSqlStore insertOrUpdateTask:task];
        [self.callbackCenter clearCallbacksForTask:task];
        return NO;
    }
    
    // 未超重试次数，可以清空并还原状态 继续重试
    YYBMacLogWarn(kLogTag, @"[updateTask] %@，第%ld次重试: %@", logMessage, (long)task.retryCount, task);
    if (task.gid) {
        [self.gidMap removeObjectForKey:task.gid];
    }
    task.gid = nil;
    [task updateStatus:YYBAria2TaskStatusPending reason:
     [NSString stringWithFormat:@"失败了(%@)，但未超重试次数，状态还原", @(errorCode)]];
    [self tryResumeAllRecoverableTasks];
    return YES;
}

/// 发生错误时先check一下确认是不是网络或服务还没起来等引起的错误
- (YYBAria2TaskStatus)checkErrorStatus {
    if (!self.networkReachable || !self.serviceRunning) {
        return YYBAria2TaskStatusErrorNetwork;
    }
    return YYBAria2TaskStatusErrorFatal;
}

- (YYBAria2TaskStatus)statusFromString:(NSString *)stat {
    if ([stat isEqualToString:@"active"]) return YYBAria2TaskStatusActive;
    if ([stat isEqualToString:@"waiting"]) return YYBAria2TaskStatusWaiting;
    if ([stat isEqualToString:@"paused"]) return YYBAria2TaskStatusPaused;
    if ([stat isEqualToString:@"complete"]) return YYBAria2TaskStatusComplete;
    if ([stat isEqualToString:@"error"]) {
        return [self checkErrorStatus];
    }
    if ([stat isEqualToString:@"removed"]) return YYBAria2TaskStatusRemoved;
    return YYBAria2TaskStatusPending;
}

/// 批量校验所有带gid的任务的gid有效性，恢复后自动调用
- (void)validateAllGidsAfterRestore {
    dispatch_async(self.syncQueue, ^{
        // 只校验需要和服务端保持一致的状态
        NSMutableArray<YYBAria2Task *> *gidsToCheck = [NSMutableArray array];
        for (NSString *gid in self.gidMap.allKeys) {
            YYBAria2Task *task = self.gidMap[gid];
            if (!task) continue;
            if ([task needsGidValidation]) {
                [gidsToCheck addObject:task];
            }
        }
        if (gidsToCheck.count == 0) {
            YYBMacLogInfo(kLogTag, @"[validateAllGidsAfterRestore] 无需校验，gidsToCheck为空");
            return;
        }
        YYBMacLogInfo(kLogTag, @"[validateAllGidsAfterRestore] 开始批量校验gid有效性, 共%lu个", (unsigned long)gidsToCheck.count);

        dispatch_group_t group = dispatch_group_create();
        NSMutableSet *invalidGids = [NSMutableSet set];

        for (YYBAria2Task *task in gidsToCheck) {
            dispatch_group_enter(group);
            NSString *gid = task.gid;
            [self.sessionManager progressForGID:gid completion:^(NSDictionary *progress) {
                BOOL resultOk = [progress isKindOfClass:[NSDictionary class]] && progress.count > 0;
                if (!resultOk) {
                    [invalidGids addObject:gid];
                    YYBMacLogWarn(kLogTag, @"[validateAllGidsAfterRestore] gid无效: %@", gid);
                } else {
                    YYBMacLogInfo(kLogTag, @"[validateAllGidsAfterRestore] gid有效: %@, 刷新gid状态", gid);
                    [self updateTask:task withTaskInfo:progress source:@"validate"];
                }
                dispatch_group_leave(group);
            }];
        }

        dispatch_group_notify(group, self.syncQueue, ^{
            if (invalidGids.count == 0) {
                YYBMacLogInfo(kLogTag, @"[validateAllGidsAfterRestore] 所有gid均有效，无需修正");
                return;
            }
            YYBMacLogWarn(kLogTag, @"[validateAllGidsAfterRestore] 发现%lu个无效gid，开始修正", (unsigned long)invalidGids.count);
            for (NSString *gid in invalidGids) {
                YYBAria2Task *task = self.gidMap[gid];
                if (!task) continue;
                // 从gid数组中移除此key
                [self.gidMap removeObjectForKey:gid];
                [[YYBAria2TaskIdGidMap sharedMap] unbindTaskId:task.taskId];
                [[YYBAria2TaskIdGidMap sharedMap] unbindGid:gid];
                // 清理gid，重置为pending
                task.gid = nil;
                [task updateStatus:YYBAria2TaskStatusPending reason:@"校验发现服务器不存在此gid，还原状态"];
                [self.dirtyTaskIds addObject:task.taskId];
                YYBMacLogWarn(kLogTag, @"[validateAllGidsAfterRestore] 已修正无效gid: %@, taskInfo=%@", gid, task);
            }
            // 持久化修正
            [self persistDirtyTasksIfNeeded];
            // 修正后可自动触发恢复
            [self tryResumeAllRecoverableTasks];
        });
    });
}

#pragma mark - 持久化机制
- (void)schedulePersistDirtyTasks {
    static dispatch_source_t persistTimer = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        persistTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, self.syncQueue);
        dispatch_source_set_timer(persistTimer, dispatch_time(DISPATCH_TIME_NOW, 5 * NSEC_PER_SEC), 5 * NSEC_PER_SEC, 0.1 * NSEC_PER_SEC);
        dispatch_source_set_event_handler(persistTimer, ^{
            [self persistDirtyTasksIfNeeded];
        });
        dispatch_resume(persistTimer);
    });
}

- (void)persistDirtyTasksIfNeeded {
    dispatch_async(self.syncQueue, ^{
        if (self.isPersisting || self.dirtyTaskIds.count == 0) {
            return;
        }
        self.isPersisting = YES;
        NSDate *startTime = [NSDate date];
        NSArray<NSString *> *dirtyTaskIdSnapshot = [self.dirtyTaskIds allObjects];
        NSMutableArray<YYBAria2Task *> *dirtyTasks = [NSMutableArray arrayWithCapacity:dirtyTaskIdSnapshot.count];
        NSMutableArray<NSString *> *persistedTaskIds = [NSMutableArray arrayWithCapacity:dirtyTaskIdSnapshot.count];
        @try {
            for (YYBAria2Task *task in self.taskIdMap.allValues) {
                if ([self.dirtyTaskIds containsObject:task.taskId]) {
                    [dirtyTasks addObject:task];
                    [persistedTaskIds addObject:task.taskId];
                }
            }
            if (dirtyTasks.count > 0) {
                [self.taskSqlStore batchInsertOrUpdateTasks:dirtyTasks];
                YYBMacLogInfo(kLogTag, @"[persistDirtyTasksIfNeeded] 批量持久化%lu个脏任务到SQL", (unsigned long)dirtyTasks.count);
            }
            // 只有成功后才移除已持久化的任务ID
            [self.dirtyTaskIds minusSet:[NSSet setWithArray:persistedTaskIds]];
        } @catch (NSException *e) {
            // SQL异常时不移除dirtyTaskIds，等待下次重试
            YYBMacLogError(kLogTag, @"[persistDirtyTasksIfNeeded] SQL持久化异常: %@", e);
            // TODO: 上报到质量监控
        }
        self.isPersisting = NO;
        // 持久化耗时监控
        NSTimeInterval cost = [[NSDate date] timeIntervalSinceDate:startTime];
        if (cost > 1.0) {
            YYBMacLogWarn(kLogTag, @"[persistDirtyTasksIfNeeded] 持久化耗时过长: %.2fs", cost);
        }
    });
}

- (void)restoreTasksFromDisk {
    dispatch_async(self.syncQueue, ^{
        @try {
            NSArray<YYBAria2Task *> *tasks = [self.taskSqlStore allTasks];
            NSUInteger fixedCount = 0;
            NSUInteger errorCount = 0;
            for (YYBAria2Task *task in tasks) {
                task.manager = self;
                YYBMacLogInfo(kLogTag, @"[restoreTasksFromDisk] 恢复任务 taskInfo=%@", task);
                BOOL needUpdate = NO;
                // 对任务做完整校验
                BOOL valid = [self.fileValidator validateTaskFile:task];
                if (!valid) {
                    // 文件丢失或损坏，修正为错误状态
                    [task updateStatus:YYBAria2TaskStatusErrorFatal reason:@"恢复时本地文件校验失败"];
                    task.progress = 0;
                    task.completedLength = 0;
                    task.finalFilePath = @"";
                    needUpdate = YES;
                    errorCount++;
                    YYBMacLogError(kLogTag, @"[restoreTasksFromDisk] 任务%@ 恢复时本地文件校验失败，已修正为错误状态", task.taskId);
                } else if (task.status == YYBAria2TaskStatusComplete) {
                    // 已完成任务，进度强制100%
                    task.completedLength = task.totalLength;
                    task.progress = 1.0;
                    needUpdate = YES;
                }
                // 恢复到内存
                if (task.gid && task.gid.length) {
                    self.gidMap[task.gid] = task;
                }
                self.taskIdMap[task.taskId] = task;
                
                // 如有修正，写回SQL
                if (needUpdate) {
                    [self.taskSqlStore insertOrUpdateTask:task];
                }
            }
            YYBMacLogInfo(kLogTag, @"[restoreTasksFromDisk] SQL恢复本地任务，共%lu个，修正进度%lu个，修正为错误%lu个", (unsigned long)tasks.count, (unsigned long)fixedCount, (unsigned long)errorCount);
            
            // 只在服务已running且有网时批量校验所有gid有效性
            [self checkAndValidateAllGidsAfterRestore];
            // 恢复后主动检测网络并尝试恢复任务
            [self tryResumeAllRecoverableTasks];
            [self adjustPollIntervalIfNeeded];
        } @catch (NSException *e) {
            YYBMacLogError(kLogTag, @"[restoreTasksFromDisk] SQL恢复异常: %@", e);
        }
    });
}

@end

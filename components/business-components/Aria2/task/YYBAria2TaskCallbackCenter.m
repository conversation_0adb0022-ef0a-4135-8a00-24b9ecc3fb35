//  YYBAria2TaskCallbackCenter.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//  负责所有任务的进度/完成/失败回调注册、分发、清理，支持 block、delegate、listener（多对1监听）

#import "YYBAria2TaskCallbackCenter.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "MacroUtils.h"

static NSString *const kLogTag = @"YYBAria2TaskCallbackCenter";

@interface YYBAria2TaskCallbackCenter ()
// 一级分组=Listener类名, 二级分组=downloadKey，value=Weak set of listener对象
// 结构: {@"YYBBaseDownloadViewModel": {@"md5str1": NSHashTable}}
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableDictionary<NSString *, NSHashTable<id<YYBDownloadListener>> *> *> *listenerGroups;
// 串行队列保证线程安全
@property (nonatomic, strong) dispatch_queue_t syncQueue;
@end

@implementation YYBAria2TaskCallbackCenter

- (instancetype)init {
    if (self = [super init]) {
        // 强key-强key-弱value，自动回收无主监听，避免内存泄漏
        _listenerGroups = [NSMutableDictionary dictionary];
        _syncQueue = dispatch_queue_create("com.yyb.aria2.callbackcenter.key", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

- (void)dispatchProgress:(double)progress forTask:(YYBAria2Task *)task {
    void (^progressBlock)(YYBAria2Task *, double) = task.progressBlock;
    id<YYBAria2TaskDelegate> delegate = task.delegate;
    __weak typeof(task) weakTask = task;
    if (progressBlock) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            if (progressBlock) {
                @try {
                    progressBlock(weakTask, progress);
                } @catch (NSException *e) {
                    YYBMacLogError(kLogTag, @"[Callback] progressBlock崩溃: %@", e);
                }
            }
        });
    }
    if (delegate && [delegate respondsToSelector:@selector(yybAria2Task:didUpdateProgress:)]) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            @try {
                [delegate yybAria2Task:weakTask didUpdateProgress:progress];
            } @catch (NSException *e) {
                YYBMacLogError(kLogTag, @"[Callback] delegate didUpdateProgress崩溃: %@", e);
            }
        });
    }

    [self dispatchDownloadListenerProgress:progress forTask:task];
}

- (void)dispatchStatus:(YYBAria2TaskStatus)status forTask:(YYBAria2Task *)task error:(NSError * _Nullable)error {
    void (^statusBlock)(YYBAria2Task *task, YYBAria2TaskStatus status, NSError * _Nullable error) = task.statusBlock;
    id<YYBAria2TaskDelegate> delegate = task.delegate;
    __weak typeof(task) weakTask = task;
    if (statusBlock) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            if (statusBlock) {
                @try {
                    statusBlock(weakTask, status, error);
                } @catch (NSException *e) {
                    YYBMacLogError(kLogTag, @"[Callback] statusBlock崩溃: %@", e);
                }
            }
        });
    }
    if (delegate && [delegate respondsToSelector:@selector(yybAria2Task:didChangeStatus:error:)]) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE(^{
            @try {
                [delegate yybAria2Task:weakTask didChangeStatus:status error:error];
            } @catch (NSException *e) {
                YYBMacLogError(kLogTag, @"[Callback] delegate didChangeStatus崩溃: %@", e);
            }
        });
    }
    YYBMacLogInfo(kLogTag, @"[Callback] 任务状态分发: taskId=%@, status=%@, error=%@", task.taskId, task.statusString, error);

    [self dispatchDownloadListenerStatus:status forTask:task error:error];
}

- (void)clearCallbacksForTask:(YYBAria2Task *)task {
    task.progressBlock = nil;
    task.statusBlock = nil;
    task.delegate = nil;
}

- (void)addDelegate:(id<YYBAria2TaskDelegate>)delegate forTask:(YYBAria2Task *)task {
    task.delegate = delegate;
}

- (void)removeDelegate:(id<YYBAria2TaskDelegate>)delegate forTask:(YYBAria2Task *)task {
    task.delegate = nil;
}


#pragma mark - Listener注册与移除(分组映射)

- (void)addDownloadListener:(id<YYBDownloadListener>)listener {
    if (!listener) return;
    NSString *classKey = NSStringFromClass([listener class]);
    NSString *listenerKey = [listener listenerDownloadKey];
    if (!classKey.length || !listenerKey.length) {
        YYBMacLogError(kLogTag, @"[addDownloadListener] 无效Listener: class=%@, key=%@", classKey, listenerKey);
        return;
    }
    dispatch_async(_syncQueue, ^{
        NSMutableDictionary *keyDict = self.listenerGroups[classKey];
        if (!keyDict) {
            keyDict = [NSMutableDictionary dictionary];
            self.listenerGroups[classKey] = keyDict;
        }
        NSHashTable *set = keyDict[listenerKey];
        if (!set) {
            set = [NSHashTable weakObjectsHashTable];
            keyDict[listenerKey] = set;
        }
        [set addObject:listener];
        YYBMacLogInfo(kLogTag, @"[addDownloadListener] %@ 注册到: class=%@, key=%@", listener, classKey, listenerKey);
    });
}

- (void)removeDownloadListener:(id<YYBDownloadListener>)listener {
    if (!listener) return;
    NSString *classKey = NSStringFromClass([listener class]);
    NSString *listenerKey = [listener listenerDownloadKey];
    if (!classKey.length || !listenerKey.length) {
        YYBMacLogError(kLogTag, @"[removeDownloadListener] 无效Listener: class=%@, key=%@", classKey, listenerKey);
        return;
    }
    dispatch_async(_syncQueue, ^{
        NSMutableDictionary *keyDict = self.listenerGroups[classKey];
        if (!keyDict) return;
        NSHashTable *set = keyDict[listenerKey];
        [set removeObject:listener];
        YYBMacLogInfo(kLogTag, @"[removeDownloadListener] %@ 从 class=%@, key=%@ 移除", listener, classKey, listenerKey);
        if (set.count == 0) [keyDict removeObjectForKey:listenerKey];
        if (keyDict.count == 0) [self.listenerGroups removeObjectForKey:classKey];
    });
}

#pragma mark - Listener分发

/// 主动分发进度事件: 会查找所有ListenerClass（适合多业务分发）
- (void)dispatchDownloadListenerProgress:(double)progress forTask:(YYBAria2Task *)task {
    // 为兼容多种Listener，遍历所有注册class分组
    dispatch_async(_syncQueue, ^{
        [self.listenerGroups enumerateKeysAndObjectsUsingBlock:^(NSString *classKey, NSMutableDictionary *keyDict, BOOL *stop) {
            Class listenerClass = NSClassFromString(classKey);
            if (![listenerClass respondsToSelector:@selector(downloadKeyForTask:)]) {
                YYBMacLogError(kLogTag, @"[DownloadListener-Progress] %@ 未实现+downloadKeyForTask:", classKey);
                return;
            }
            NSString *downloadKey = [listenerClass downloadKeyForTask:task];
            if (!downloadKey.length) {
                YYBMacLogInfo(kLogTag, @"[DownloadListener-Progress] ListenerClass:%@ 生成派发key为空, task:%@", classKey, task);
                return;
            }
            NSHashTable *listeners = keyDict[downloadKey];
            if (!listeners.count) {
                YYBMacLogInfo(kLogTag, @"[DownloadListener-Progress] ListenerClass:%@, key=%@ 下无监听", classKey, downloadKey);
                return;
            }
            for (id<YYBDownloadListener> listener in listeners.allObjects) {
                if (!listener) continue;
                dispatch_async(dispatch_get_main_queue(), ^{
                    @try {
                        [listener onDownloadProgress:task progress:progress];
                        YYBMacLogInfo(kLogTag, @"[DownloadListener-Progress] 分发给 Listener:%@, class=%@, key=%@, taskId=%@", listener, classKey, downloadKey, task.taskId);
                    } @catch (NSException *e) {
                        YYBMacLogError(kLogTag, @"[DownloadListener-Progress] Listener(%@)崩溃: %@", listener, e);
                    }
                });
            }
        }];
    });
}

/// 主动分发状态事件: 支持所有ListenerClass并分组精确派发
- (void)dispatchDownloadListenerStatus:(YYBAria2TaskStatus)status forTask:(YYBAria2Task *)task error:(NSError *)error {
    dispatch_async(_syncQueue, ^{
        [self.listenerGroups enumerateKeysAndObjectsUsingBlock:^(NSString *classKey, NSMutableDictionary *keyDict, BOOL *stop) {
            Class listenerClass = NSClassFromString(classKey);
            if (![listenerClass respondsToSelector:@selector(downloadKeyForTask:)]) {
                YYBMacLogError(kLogTag, @"[DownloadListener-Status] %@ 未实现+downloadKeyForTask:", classKey);
                return;
            }
            NSString *downloadKey = [listenerClass downloadKeyForTask:task];
            if (!downloadKey.length) {
                YYBMacLogInfo(kLogTag, @"[DownloadListener-Status] ListenerClass:%@ 生成派发key为空, task:%@", classKey, task);
                return;
            }
            NSHashTable *listeners = keyDict[downloadKey];
            if (!listeners.count) {
                YYBMacLogInfo(kLogTag, @"[DownloadListener-Status] ListenerClass:%@, key=%@ 下无监听", classKey, downloadKey);
                return;
            }
            for (id<YYBDownloadListener> listener in listeners.allObjects) {
                if (!listener) continue;
                dispatch_async(dispatch_get_main_queue(), ^{
                    @try {
                        [listener onDownloadStatusChanged:task status:status error:error];
                        YYBMacLogInfo(kLogTag, @"[DownloadListener-Status] 分发给 Listener:%@, class=%@, key=%@, taskId=%@", listener, classKey, downloadKey, task.taskId);
                    } @catch (NSException *e) {
                        YYBMacLogError(kLogTag, @"[DownloadListener-Status] Listener(%@)崩溃: %@", listener, e);
                    }
                });
            }
        }];
    });
}

@end

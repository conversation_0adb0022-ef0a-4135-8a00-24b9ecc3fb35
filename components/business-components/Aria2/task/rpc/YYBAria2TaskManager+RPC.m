//
//  YYBAria2TaskManager+RPC.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/31.
//

#import "YYBAria2TaskManager.h"
#import "YYBAria2Error.h"


@implementation YYBAria2TaskManager (RPC)

#pragma mark - rpc适配

- (void)handleJSONRPCMethod:(NSString *)method
                    params:(nullable id)params
                     source:(YYBAria2TaskSource)source
                completion:(void(^)(NSDictionary * _Nullable result, NSData * _Nullable rawData, NSError * _Nullable error))completion {
    static NSSet *businessMethods;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        businessMethods = [NSSet setWithObjects:
            @"aria2.addUri", @"aria2.pause", @"aria2.unpause", @"aria2.remove", @"aria2.tellStatus", nil];
    });

    if (![businessMethods containsObject:method]) {
        NSString *errorMessage = [NSString stringWithFormat:@"Task-RPC-不支持的方法 method = %@", method];
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorRPCMethodNotSupported message:errorMessage userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }

    // 参数标准化
    NSArray *paramsArray = [params isKindOfClass:[NSArray class]] ? params : (params ? @[params] : @[]);

    // 分发到具体实现
    if ([method isEqualToString:@"aria2.addUri"]) {
        [self handleAddUri:paramsArray source:source completion:completion];
    } else if ([method isEqualToString:@"aria2.pause"]) {
        [self handlePause:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.unpause"]) {
        [self handleUnpause:paramsArray completion:completion];
    } else if ([method isEqualToString:@"aria2.remove"]) {
        [self handleRemove:paramsArray completion:completion];
    }
}

#pragma mark - 单独分支实现

- (void)handleAddUri:(NSArray *)paramsArray
              source:(YYBAria2TaskSource)source
          completion:(void(^)(NSDictionary *, NSData *, NSError *))completion {
    // 只支持单URL
    NSString *url = nil;
    NSDictionary *options = nil;
    if (paramsArray.count > 0) {
        id urlParam = paramsArray[0];
        if ([urlParam isKindOfClass:[NSString class]]) {
            url = urlParam;
        } else if ([urlParam isKindOfClass:[NSArray class]]) {
            NSArray *arr = (NSArray *)urlParam;
            if (arr.count > 0 && [arr[0] isKindOfClass:[NSString class]]) {
                url = arr[0];
            }
        }
    }
    if (paramsArray.count > 1 && [paramsArray[1] isKindOfClass:[NSDictionary class]]) {
        options = paramsArray[1];
    }
    if (!url) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"参数无效, 无url-addUri" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    NSString *fileName = options[@"out"];
    NSString *destDir = options[@"dir"];
    YYBAria2Task *task = [self createDownloadTaskWithURL:url destDir:destDir fileName:fileName md5:nil];
    task.source = source;
    [self startTaskByRpc:task resultCallBack:^(NSString * _Nonnull gid, NSError * _Nonnull error) {
        // 对外的gid由taskId创建
        NSDictionary *result = @{
            @"result":
                @{@"gid": task.gidForUser ?: @"",
                  @"progress": [task progressDictionaryForWaitting]}
        };
        result = gid ? result : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
        }
    }];
}

- (void)handlePause:(NSArray *)paramsArray completion:(void(^)(NSDictionary *, NSData *, NSError *))completion {
    NSString *taskId = paramsArray.count > 0 ? paramsArray[0] : nil;
    if (!taskId) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"参数缺失gid（taskId）-pause" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    YYBAria2Task *task = [self taskForTaskId:taskId];
    if (!task) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorTaskNotFound taskId:taskId message:@"未找到任务-pause" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    [self pauseTask:task byUser:YES completion:^(BOOL success, NSError * _Nullable error) {
        NSDictionary *result = success ? @{@"result": taskId} : nil;
        if (completion) completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
    }];
}

- (void)handleUnpause:(NSArray *)paramsArray completion:(void(^)(NSDictionary *, NSData *, NSError *))completion {
    NSString *taskId = paramsArray.count > 0 ? paramsArray[0] : nil;
    if (!taskId) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"参数缺失gid（taskId）-unPause" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    YYBAria2Task *task = [self taskForTaskId:taskId];
    if (!task) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorTaskNotFound taskId:taskId message:@"未找到任务-unPause" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    [self resumeTask:task completion:^(BOOL success, NSError * _Nullable error) {
        // 对外的gid由taskId创建
        NSDictionary *result = @{
            @"result":
                @{@"gid": task.gidForUser ?: @"",
                  @"progress": [task progressDictionaryForWaitting]}
        };
        result = success ? result : nil;
        if (completion) completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
    }];
}

- (void)handleRemove:(NSArray *)paramsArray completion:(void(^)(NSDictionary *, NSData *, NSError *))completion {
    NSString *taskId = paramsArray.count > 0 ? paramsArray[0] : nil;
    if (!taskId) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorInvalidParam message:@"参数缺失gid（taskId）-unRemove" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    YYBAria2Task *task = [self taskForTaskId:taskId];
    if (!task) {
        NSError *err = [YYBAria2Error errorWithCode:YYBAria2ErrorTaskNotFound taskId:taskId message:@"未找到任务-remove" userInfo:nil];
        if (completion) completion(nil, nil, err);
        return;
    }
    [self cancelTask:task deleteFile:NO completion:^(BOOL success, NSError * _Nullable error) {
        NSDictionary *result = success ? @{@"result": taskId} : nil;
        if (completion) {
            completion(result, result ? [NSJSONSerialization dataWithJSONObject:result options:0 error:nil] : nil, error);
        }
    }];
   
}

@end

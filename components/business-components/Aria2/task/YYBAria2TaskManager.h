//
//  YYBAria2TaskManager.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//  任务状态机、调度、查重、自动恢复、重试机制

#import <Foundation/Foundation.h>
#import "YYBAria2Task.h"
#import "YYBAria2TaskCallbackCenter.h"
#import "YYBAria2TaskFileValidator.h"
#import "YYBLibAria2EventCenter.h"

NS_ASSUME_NONNULL_BEGIN

@class YYBLibAria2SessionManager;

@interface YYBAria2TaskManager : NSObject

- (instancetype)initWithSessionManager:(YYBLibAria2SessionManager *)sessionManager
                        callbackCenter:(YYBAria2TaskCallbackCenter *)callbackCenter
                         fileValidator:(YYBAria2TaskFileValidator *)fileValidator;

// 任务相关API
- (YYBAria2Task *)createDownloadTaskWithURL:(NSString *)url
                                    destDir:(nullable NSString *)destDir
                                   fileName:(nullable NSString *)fileName
                                        md5:(nullable NSString *)md5;

- (void)startTask:(YYBAria2Task *)task;

- (void)startTaskByRpc:(YYBAria2Task *)task
        resultCallBack:(void(^)(NSString *gid, NSError *error))resultCallBack;

- (void)cancelTask:(YYBAria2Task *)task deleteFile:(BOOL)deleteFile;
- (void)cancelTask:(YYBAria2Task *)task
        deleteFile:(BOOL)deleteFile
        completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;
- (void)pauseTask:(YYBAria2Task *)task
           byUser:(BOOL)byUser
       completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;
- (void)resumeTask:(YYBAria2Task *)task completion:(nullable void(^)(BOOL success, NSError * _Nullable error))completion;
- (void)clearAllTasksDeleteFiles:(BOOL)deleteFiles;
- (nullable YYBAria2Task *)findLocalCompleteFileWithMD5:(NSString *)md5;

- (void)handleAria2Event:(YYBLibAria2DownloadEvent)event
                  taskId:(NSString *)taskId
                    info:(NSDictionary *)info
              completion:(void(^)(void))completion;

/// 查询所有“有效”任务（不含已删除/移除任务）
- (NSArray<YYBAria2Task *> *)allTasks;

/// 查询单个任务
- (YYBAria2Task *)taskForTaskId:(NSString *)taskId;

/// 查询所有“已删除”任务
- (NSArray<YYBAria2Task *> *)allRemoveTasks;

// 供Network/ServiceMonitor调用
- (void)onNetworkChange:(BOOL)reachable;
- (void)onServiceAvailable;
- (void)onServiceUnavailable;
- (void)onAppDidBecomeActive;
- (void)onAppWillResignActive;
- (void)onAppWillTerminate;

@end

NS_ASSUME_NONNULL_END

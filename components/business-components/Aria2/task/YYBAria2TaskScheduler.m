//
//  YYBAria2TaskScheduler.m
//  YYBMacBusinessComponentshttps://down.pc.yyb.qq.com/yyb-client-pc/offline202508041556.7z
//
//  Created by 凌刚 on 2025/8/4.
//

#import "YYBAria2TaskScheduler.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBAria2Task.h"
#import <YYBMacFusionSDK/YYBMacShiplyConfig.h>

#define kLogTag @"YYBAria2TaskScheduler"

/// 用户可感知的最大任务数
#define YYB_ARIA2_TASK_MAX_USER_VISIBLE_CONCURRENT 5

// 参数远端key
static NSString *const kYYBAria2TaskMaxUserVisibleConcurrentKey = @"yyb_aria2_task_max_user_visible_concurrent";

@implementation YYBAria2TaskScheduleResult

// 是否需要等待
- (BOOL)needWait {
    return self.scheduleStatus == YYBAria2TaskScheduleStatusWaiting ||
    self.scheduleStatus == YYBAria2TaskScheduleStatusPaused;
}

@end

@interface YYBAria2TaskScheduler ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *lastScheduleStatusMap;
@property (nonatomic, assign, readwrite) BOOL hasUserActive;

@end

@implementation YYBAria2TaskScheduler

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        _maxUserVisibleConcurrent = [[YYBMacShiplyConfig sharedInstance] intValueWithKey:kYYBAria2TaskMaxUserVisibleConcurrentKey
                                                                            defaultValue:YYB_ARIA2_TASK_MAX_USER_VISIBLE_CONCURRENT];
        _lastScheduleStatusMap = [NSMutableDictionary dictionary];
    }
    return self;
}

#pragma mark - 主调度入口
/// 只返回scheduleStatus有变化的任务
- (NSDictionary<NSString *, YYBAria2TaskScheduleResult *> *)scheduleChangedTasksFromAllTasks:(NSArray<YYBAria2Task *> *)allTasks {
    // 1. 全量调度
    NSDictionary<NSString *, YYBAria2TaskScheduleResult *> *allResult = [self scheduleAllTasks:allTasks];
    NSMutableDictionary<NSString *, YYBAria2TaskScheduleResult *> *changedResult = [NSMutableDictionary dictionary];

    // 2. 对比上次状态，只返回有变化的
    for (YYBAria2Task *task in allTasks) {
        YYBAria2TaskScheduleResult *result = allResult[task.taskId];
        if (!result) continue;
        NSNumber *lastStatusNum = self.lastScheduleStatusMap[task.taskId];
        YYBAria2TaskScheduleStatus lastStatus = (YYBAria2TaskScheduleStatus)(lastStatusNum ? lastStatusNum.integerValue : YYBAria2TaskScheduleStatusNone);
        if (result.scheduleStatus != lastStatus) {
            changedResult[task.taskId] = result;
            YYBMacLogInfo(kLogTag, @"[scheduleChangedTasks] 任务状态变化: taskId=%@, %@ -> %@, reason=%@", task.taskId, @(lastStatus), @(result.scheduleStatus), result.reason);
        }
        // 更新lastScheduleStatusMap
        self.lastScheduleStatusMap[task.taskId] = @(result.scheduleStatus);
    }

    // 3. 清理已不存在的任务
    NSMutableSet *currentTaskIds = [NSMutableSet set];
    for (YYBAria2Task *task in allTasks) {
        [currentTaskIds addObject:task.taskId];
    }
    NSArray *allKeys = [self.lastScheduleStatusMap allKeys];
    for (NSString *taskId in allKeys) {
        if (![currentTaskIds containsObject:taskId]) {
            [self.lastScheduleStatusMap removeObjectForKey:taskId];
        }
    }

    YYBMacLogInfo(kLogTag, @"[scheduleChangedTasks] 本轮有变化的任务数: %lu", (unsigned long)changedResult.count);
    return changedResult;
}

/// 批量调度，返回每个任务的调度建议
- (NSDictionary<NSString *, YYBAria2TaskScheduleResult *> *)scheduleAllTasks:(NSArray<YYBAria2Task *> *)allTasks {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    
    // 1. 处理终态/特殊状态任务，分类剩余任务
    NSMutableArray *highPriorityTasks = [NSMutableArray array];
    NSMutableArray *userVisibleTasks = [NSMutableArray array];
    NSMutableArray *silentTasks = [NSMutableArray array];
    [self classifyTasks:allTasks
      highPriorityTasks:highPriorityTasks
       userVisibleTasks:userVisibleTasks
            silentTasks:silentTasks
                 result:result];
    
    // 2. 高优任务全部激活
    [self activateHighPriorityTasks:highPriorityTasks result:result];
    
    // 3. 感知任务调度
    [self scheduleUserVisibleTasks:userVisibleTasks result:result];
    
    // 4. 静默任务调度
    [self scheduleSilentTasks:silentTasks userVisibleTasks:userVisibleTasks result:result];
    
    YYBMacLogInfo(kLogTag, @"调度: 本轮调度完成，任务总数=%lu，高优=%lu，感知=%lu，静默=%lu",
                  (unsigned long)allTasks.count,
                  (unsigned long)highPriorityTasks.count,
                  (unsigned long)userVisibleTasks.count,
                  (unsigned long)silentTasks.count);
    
    return result;
}

#pragma mark - 任务分类与特殊状态处理

/// 分类任务，并处理终态/特殊状态任务
- (void)classifyTasks:(NSArray<YYBAria2Task *> *)allTasks
    highPriorityTasks:(NSMutableArray *)highPriorityTasks
     userVisibleTasks:(NSMutableArray *)userVisibleTasks
          silentTasks:(NSMutableArray *)silentTasks
               result:(NSMutableDictionary *)result
{
    for (YYBAria2Task *task in allTasks) {
        // 终态/特殊状态任务直接给出调度建议，不参与名额统计
        if ([self handleFinalOrSpecialStateForTask:task result:result]) {
            continue;
        }
        // 分类
        if (task.priority == YYBAria2TaskPriorityHigh) {
            [highPriorityTasks addObject:task];
        } else if (task.visibility == YYBAria2TaskVisibilityUser) {
            [userVisibleTasks addObject:task];
        } else {
            [silentTasks addObject:task];
        }
    }
}

/// 处理终态/特殊状态任务，返回YES表示已处理
- (BOOL)handleFinalOrSpecialStateForTask:(YYBAria2Task *)task result:(NSMutableDictionary *)result {
    YYBAria2TaskScheduleResult *r = [YYBAria2TaskScheduleResult new];
    if (!task.isStarted) {
        r.scheduleStatus = YYBAria2TaskScheduleStatusNone;
        r.reason = @"用户未启动";
        [self setTask:task scheduleStatus:r.scheduleStatus reason:r.reason result:result];
        return YES;
    }
    if (task.userPaused) {
        r.scheduleStatus = YYBAria2TaskScheduleStatusUserPaused;
        r.reason = @"用户手动暂停";
        [self setTask:task scheduleStatus:r.scheduleStatus reason:r.reason result:result];
        return YES;
    }
    switch (task.status) {
        case YYBAria2TaskStatusComplete:
            r.scheduleStatus = YYBAria2TaskScheduleStatusCompleted;
            r.reason = @"任务已完成";
            [self setTask:task scheduleStatus:r.scheduleStatus reason:r.reason result:result];
            return YES;
        case YYBAria2TaskStatusErrorFatal:
            r.scheduleStatus = YYBAria2TaskScheduleStatusFailed;
            r.reason = @"任务不可恢复错误";
            [self setTask:task scheduleStatus:r.scheduleStatus reason:r.reason result:result];
            return YES;
        case YYBAria2TaskStatusErrorNetwork:
            r.scheduleStatus = YYBAria2TaskScheduleStatusNetworkError;
            r.reason = @"任务网络异常";
            [self setTask:task scheduleStatus:r.scheduleStatus reason:r.reason result:result];
            return YES;
        case YYBAria2TaskStatusRemoved:
            r.scheduleStatus = YYBAria2TaskScheduleStatusRemoved;
            r.reason = @"任务被移除";
            [self setTask:task scheduleStatus:r.scheduleStatus reason:r.reason result:result];
            return YES;
        default:
            break;
    }
    return NO;
}

#pragma mark - 高优任务调度

/// 高优任务全部激活（不占用感知名额）
- (void)activateHighPriorityTasks:(NSArray<YYBAria2Task *> *)highPriorityTasks
                           result:(NSMutableDictionary *)result
{
    for (YYBAria2Task *task in highPriorityTasks) {
        [self setTask:task
       scheduleStatus:YYBAria2TaskScheduleStatusActive
               reason:@"高优任务，强制激活"
               result:result];
        YYBMacLogInfo(kLogTag, @"调度: 高优任务激活 taskId=%@", task.taskId);
    }
}

#pragma mark - 感知任务调度

/// 感知任务调度，按优先级和激活时间排序，最多maxUserVisibleConcurrent个active
- (void)scheduleUserVisibleTasks:(NSMutableArray<YYBAria2Task *> *)userVisibleTasks
                          result:(NSMutableDictionary *)result
{
    // 赋值scheduleActiveTime
    NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
    for (YYBAria2Task *task in userVisibleTasks) {
        // 如果scheduleActiveTime为0，说明重新进入调度，赋当前时间
        if (task.scheduleActiveTime == 0) {
            task.scheduleActiveTime = now;
            YYBMacLogInfo(kLogTag, @"调度: 任务重新进入调度队列，scheduleActiveTime赋值 taskId=%@, scheduleActiveTime=%.3f", task.taskId, now);
        }
    }
    
    // 排序：优先级降序，激活时间升序
    [userVisibleTasks sortUsingComparator:^NSComparisonResult(YYBAria2Task *t1, YYBAria2Task *t2) {
        if (t1.priority != t2.priority) {
            return t1.priority < t2.priority ? NSOrderedDescending : NSOrderedAscending;
        }
        NSTimeInterval q1 = t1.scheduleActiveTime > 0 ? t1.scheduleActiveTime : t1.createTime;
        NSTimeInterval q2 = t2.scheduleActiveTime > 0 ? t2.scheduleActiveTime : t2.createTime;
        if (q1 < q2) return NSOrderedAscending;
        if (q1 > q2) return NSOrderedDescending;
        return NSOrderedSame;
    }];
    
    NSUInteger userActiveCount = 0;
    for (YYBAria2Task *task in userVisibleTasks) {
//        YYBAria2TaskScheduleStatus oldStatus = task.scheduleStatus;
        NSString *oldStatusString = task.scheduleStatusString;
        if (userActiveCount < self.maxUserVisibleConcurrent) {
            // 激活
            [self setTask:task
           scheduleStatus:YYBAria2TaskScheduleStatusActive
                   reason:@"感知任务，排队靠前，激活"
                   result:result];
            userActiveCount++;
            YYBMacLogInfo(kLogTag, @"调度: 感知任务激活, taskId=%@, scheduleActiveTime=%.3f, oldStatus = %@", task.taskId, task.scheduleActiveTime, oldStatusString);
        } else {
            // 排队等待
            [self setTask:task
           scheduleStatus:YYBAria2TaskScheduleStatusWaiting
                   reason:@"感知任务，排队靠后，排队等待"
                   result:result];
        }
        YYBMacLogInfo(kLogTag, @"调度: 感知任务调度结果 taskId=%@, status=%ld, reason=%@", task.taskId, (long)task.scheduleStatus, [result[task.taskId] reason]);
    }
}

#pragma mark - 静默任务调度

/// 静默任务调度
- (void)scheduleSilentTasks:(NSMutableArray<YYBAria2Task *> *)silentTasks
           userVisibleTasks:(NSMutableArray<YYBAria2Task *> *)userVisibleTasks
                     result:(NSMutableDictionary *)result
{
    BOOL hasUserActive = NO;
    for (YYBAria2Task *task in userVisibleTasks) {
        if (task.scheduleStatus == YYBAria2TaskScheduleStatusActive) {
            hasUserActive = YES;
            break;
        }
    }
    self.hasUserActive = hasUserActive;
    if (!hasUserActive) {
        // 无感知active，静默任务可active
        [silentTasks sortUsingComparator:^NSComparisonResult(YYBAria2Task *t1, YYBAria2Task *t2) {
            if (t1.priority != t2.priority) {
                return t1.priority < t2.priority ? NSOrderedDescending : NSOrderedAscending;
            }
            return t1.createTime < t2.createTime ? NSOrderedAscending : NSOrderedDescending;
        }];
        for (YYBAria2Task *task in silentTasks) {
            [self setTask:task
           scheduleStatus:YYBAria2TaskScheduleStatusActive
                   reason:@"无感知任务active，静默任务激活"
                   result:result];
            YYBMacLogInfo(kLogTag, @"调度: 静默任务激活 taskId=%@", task.taskId);
        }
    } else {
        // 有感知active，静默任务全部paused
        for (YYBAria2Task *task in silentTasks) {
            [self setTask:task
           scheduleStatus:YYBAria2TaskScheduleStatusPaused
                   reason:@"有感知任务active，静默任务暂停"
                   result:result];
            YYBMacLogInfo(kLogTag, @"调度: 静默任务暂停 taskId=%@", task.taskId);
        }
    }
}

#pragma mark - 工具方法

/// 统一设置任务调度状态、原因、激活时间（如需清零）
- (void)setTask:(YYBAria2Task *)task
 scheduleStatus:(YYBAria2TaskScheduleStatus)scheduleStatus
         reason:(NSString *)reason
         result:(NSMutableDictionary *)result
{
    task.scheduleStatus = scheduleStatus;
    if (scheduleStatus != YYBAria2TaskScheduleStatusActive) {
        // 非激活状态，激活时间清零
        task.scheduleActiveTime = 0;
    }
    YYBAria2TaskScheduleResult *r = [YYBAria2TaskScheduleResult new];
    r.scheduleStatus = scheduleStatus;
    r.reason = reason;
    result[task.taskId] = r;
}

#pragma mark - 单任务调度

/// 单任务调度，返回调度建议
- (YYBAria2TaskScheduleResult *)scheduleForTask:(YYBAria2Task *)task inAllTasks:(NSArray<YYBAria2Task *> *)allTasks {
    NSDictionary *all = [self scheduleAllTasks:allTasks];
    YYBAria2TaskScheduleResult *r = all[task.taskId];
    if (!r) {
        r = [YYBAria2TaskScheduleResult new];
        r.scheduleStatus = YYBAria2TaskScheduleStatusNone;
    }
    task.scheduleStatus = r.scheduleStatus;
    return r;
}

@end

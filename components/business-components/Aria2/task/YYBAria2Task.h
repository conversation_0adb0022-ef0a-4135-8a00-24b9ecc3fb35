//
//  YYBAria2Task.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/10.
//

#import <Foundation/Foundation.h>
#import "YYBAria2TaskDelegate.h"
#import "YYBAria2TaskScheduler.h"
@class YYBAria2TaskManager;

NS_ASSUME_NONNULL_BEGIN

/// 任务状态
typedef NS_ENUM(NSInteger, YYBAria2TaskStatus) {
    YYBAria2TaskStatusPending,              // 待下发(任务刚创建，还未启动)
    YYBAria2TaskStatusWaitingForNetwork,    // 等待网络恢复（待下发）
    YYBAria2TaskStatusWaitingForService,    // 等待服务起来（待下发）
    YYBAria2TaskStatusRetrying,             // 重试下发中（下发任务不成功的重试）
    YYBAria2TaskStatusWaiting,              // 任务等待中（已经下发触达服务器，可能会因为排队一直待待）
    YYBAria2TaskStatusActive,               // 下载中
    YYBAria2TaskStatusPaused,               // 暂停
    YYBAria2TaskStatusComplete,             // 完成
    YYBAria2TaskStatusErrorNetwork,         // 网络/服务不可用导致的错误，会尝试自动恢复
    YYBAria2TaskStatusErrorFatal,           // 不可恢复的错误（如文件损坏、重试超限）
    YYBAria2TaskStatusRemoved,              // 取消
};

/// 任务来源
typedef NS_ENUM(NSInteger, YYBAria2TaskSource) {
    YYBAria2TaskSourceStore,             // 默认为商店内部
    YYBAria2TaskSourceWeb,               // web来源（通常通过rpc驱动的任务即判定为web来源）
    YYBAria2TaskSourceDownloadButton,    // 商店内部原生下载按钮
    YYBAria2TaskSourceDebug,             // 调试页面
};

/// 任务感知属性
typedef NS_ENUM(NSInteger, YYBAria2TaskVisibility) {
    YYBAria2TaskVisibilityUser,    // 用户感知（非静默），任务有数量控制且不受静默任务影响
    YYBAria2TaskVisibilitySilent,  // 静默（非用户感知），非最高优先级任务，需感知任务全部下载完后才能下载（闲时下载）
};

/// 任务优先级
typedef NS_ENUM(NSInteger, YYBAria2TaskPriority) {
    YYBAria2TaskPriorityHigh = 3,   // 最高优先级：任何情况下都需要保障下载
    YYBAria2TaskPriorityNormal = 2,
    YYBAria2TaskPriorityLow = 1,
};

typedef void(^YYBAria2TaskProgressBlock)(YYBAria2Task *task, double progress);
typedef void(^YYBAria2TaskStatusBlock)(YYBAria2Task *task, YYBAria2TaskStatus status, NSError * _Nullable error);

@interface YYBAria2Task : NSObject

@property (nonatomic, copy, readonly) NSString *taskId; ///< 业务任务id
@property (nonatomic, copy, nullable) NSString *gid;    ///< Aria2任务GID (aria2下载服务启动成功后生成)

@property (nonatomic, copy, readonly) NSString *url;              ///< 下载URL
@property (nonatomic, copy, readonly) NSString *destPath;         ///< 目标文件夹
@property (nonatomic, copy, readonly, nullable) NSString *md5;    ///< 文件的md5 (业务侧需要强校验时设置)

@property (nonatomic, assign, readonly) YYBAria2TaskStatus status;

@property (nonatomic, assign) int64_t totalLength;
@property (nonatomic, assign) int64_t completedLength;
@property (nonatomic, assign) double progress;          ///< 0~1
@property (nonatomic, copy) NSString *fileName;         ///< 文件名
@property (nonatomic, copy) NSString *finalFilePath;    ///< 下载完成后文件路径

@property (nonatomic, copy, readonly) NSString *fileExtension; ///< 文件扩展名

@property (nonatomic, assign) NSUInteger retryCount;        // 失败重试次数
@property (nonatomic, assign) NSUInteger maxRetryCount;     // 最大失败重试次数

@property (nonatomic, assign) BOOL inAddUrl;                // 是否在请求url中（避免重入）

@property (nonatomic, weak) YYBAria2TaskManager *manager; // 反向引用

@property (nonatomic, assign) BOOL isStarted; ///< 任务是否已被业务启动
@property (nonatomic, assign) YYBAria2TaskSource source;
@property (nonatomic, assign) YYBAria2TaskVisibility visibility;
@property (nonatomic, assign) YYBAria2TaskPriority priority;
@property (nonatomic, assign) NSTimeInterval createTime;          // 任务创建时间戳
@property (nonatomic, assign) YYBAria2TaskScheduleStatus scheduleStatus;    // 调度建议
@property (nonatomic, assign) BOOL userPaused; ///< 是否被用户手动暂停

@property (nonatomic, copy, nullable) YYBAria2TaskProgressBlock progressBlock;   ///< 进度回调
@property (nonatomic, copy, nullable) YYBAria2TaskStatusBlock statusBlock;       ///< 状态回调
@property (nonatomic, weak, nullable) id<YYBAria2TaskDelegate> delegate;         ///< 代理（和上面2个回调二选一设置）

@property (nonatomic, assign) NSTimeInterval scheduleActiveTime;                 // 最后一次调度激活时间

@property (nonatomic, copy, nullable) NSString *pkgName;                         // 包名

- (instancetype)initWithTaskId:(NSString *)taskId
                           md5:(nullable NSString *)md5
                           url:(NSString *)url
                      destPath:(NSString *)destPath;


// 下载的文件存在性判断
- (BOOL)fileExists;

// 下载的文件大小
- (unsigned long long)fileSize;

// 校验下载文件的md5
- (BOOL)validateMD5IfNeededWithLogTag:(NSString *)logTag;

// 校验是否：无.aria2临时文件
- (BOOL)noAria2TempFileWithLogTag:(NSString *)logTag;

// 获取task信息
- (NSDictionary *)getTaskInfoDic;

// 更新任务状态
- (void)updateStatus:(YYBAria2TaskStatus)status reason:(NSString *)reason;


#pragma mark - 枚举转换
- (NSString *)statusString;
- (NSString *)sourceString;
- (NSString *)scheduleStatusString;

#pragma mark - 对外gid(调度管控后，对外不再暴露真实的gid)
- (NSString *)gidForUser;

#pragma mark - 状态聚合判断（新增）

/// 任务状态能否复用
- (BOOL)canReusable;

/// 是否为可以添加uri(aria2在任务没取消前或到，不允许重复添加
- (BOOL)canAddUri;

/// 是否为活跃/等待/待下发状态（用于轮询/活跃判断）
- (BOOL)isActiveOrWaiting;

/// 是否为可恢复状态（断点续传/自动恢复）
- (BOOL)isRecoverable;

/// 是否需要同步状态（pollAllTasks时用）
- (BOOL)needsStatusSync;

/// 是否需要校验gid有效性（validateAllGidsAfterRestore时用）
- (BOOL)needsGidValidation;

/// 是否可unpause（可直接恢复）
- (BOOL)canUnpause;

/// 是否是终态（完成/致命错误/网络错误）
- (BOOL)inFinalState;

#pragma mark - 进度信息构造
/// 任务等待中进度信息构造
-  (NSDictionary *)progressDictionaryForWaitting;

@end

NS_ASSUME_NONNULL_END

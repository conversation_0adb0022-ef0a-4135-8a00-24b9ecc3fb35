//
//  YYBAria2ServiceMonitor.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//  负责aria2服务状态监听，服务变化时通知TaskManager

#import <Foundation/Foundation.h>

@protocol YYBAria2ServiceMonitorDelegate <NSObject>

@optional

- (void)yybAria2OnServiceAvailable;
- (void)yybAria2OnServiceUnavailable;
- (void)yybAria2OnAppDidBecomeActive;
- (void)yybAria2OnAppWillResignActive;
- (void)yybAria2OnAppWillTerminate;

@end

NS_ASSUME_NONNULL_BEGIN

@interface YYBAria2ServiceMonitor : NSObject

- (instancetype)initWithDelegate:(id<YYBAria2ServiceMonitorDelegate>)delegate;

@end

NS_ASSUME_NONNULL_END

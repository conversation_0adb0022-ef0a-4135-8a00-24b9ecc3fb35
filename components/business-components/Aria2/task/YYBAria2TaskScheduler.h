//
//  YYBAria2TaskScheduler.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/4.
//

#import <Foundation/Foundation.h>
@class YYBAria2Task;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, YYBAria2TaskScheduleStatus) {
    YYBAria2TaskScheduleStatusNone,        // 未调度(默认不处理，不参与调度，比如未启动下载)
    YYBAria2TaskScheduleStatusActive,      // 调度建议：激活
    YYBAria2TaskScheduleStatusWaiting,     // 调度建议：排队
    YYBAria2TaskScheduleStatusPaused,      // 调度建议：暂停
    YYBAria2TaskScheduleStatusUserPaused,  // 用户手动暂停（不再占用调度名额，除非用户启动）
    YYBAria2TaskScheduleStatusCompleted,    // 已完成（不占用调度名额）
    YYBAria2TaskScheduleStatusFailed,       // 已失败（不占用调度名额）
    YYBAria2TaskScheduleStatusNetworkError, // 网络异常（不占用调度名额）
    YYBAria2TaskScheduleStatusRemoved,      // 被移除（不占用调度名额）
};

/// 调度建议结果
@interface YYBAria2TaskScheduleResult : NSObject

@property (nonatomic, assign) YYBAria2TaskScheduleStatus scheduleStatus;
@property (nonatomic, copy) NSString *reason;

// 是否需要等待
- (BOOL)needWait;

@end

@interface YYBAria2TaskScheduler : NSObject

@property (nonatomic, assign) NSUInteger maxUserVisibleConcurrent;
/// 当前是否有用户感知任务（注意：瞬间态，会发生变化）
@property (nonatomic, assign, readonly) BOOL hasUserActive;

/// 只返回scheduleStatus有变化的任务
- (NSDictionary<NSString *, YYBAria2TaskScheduleResult *> *)scheduleChangedTasksFromAllTasks:(NSArray<YYBAria2Task *> *)allTasks;

/// 批量调度，返回每个任务的调度建议
- (NSDictionary<NSString *, YYBAria2TaskScheduleResult *> *)scheduleAllTasks:(NSArray<YYBAria2Task *> *)allTasks;

/// 单任务调度，返回调度建议
- (YYBAria2TaskScheduleResult *)scheduleForTask:(YYBAria2Task *)task inAllTasks:(NSArray<YYBAria2Task *> *)allTasks;

@end

NS_ASSUME_NONNULL_END

//
//  YYBAria2NetworkMonitor.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/7/26.
//

#import "YYBAria2NetworkMonitor.h"
#import <SystemConfiguration/SystemConfiguration.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <AppKit/AppKit.h>
#import <SystemConfiguration/SystemConfiguration.h>


static NSString *const kLogTag = @"YYBAria2NetworkMonitor";


@interface YYBAria2NetworkMonitor ()

@property (nonatomic, weak) id<YYBAria2NetworkMonitorDelegate> delegate;
@property (nonatomic) SCNetworkReachabilityRef reachabilityRef;
@property (nonatomic, assign) BOOL networkReachable;

- (void)handleNetworkChange:(BOOL)reachable;

@end

static void YYBAria2NetworkReachabilityCallback(SCNetworkReachabilityRef target, SCNetworkReachabilityFlags flags, void *info) {
    YYBAria2NetworkMonitor *monitor = (__bridge YYBAria2NetworkMonitor *)info;
    BOOL reachable = (flags & kSCNetworkReachabilityFlagsReachable) && !(flags & kSCNetworkReachabilityFlagsConnectionRequired);
    [monitor handleNetworkChange:reachable];
}


@implementation YYBAria2NetworkMonitor

- (instancetype)initWithDelegate:(id<YYBAria2NetworkMonitorDelegate>)delegate {
    if (self = [super init]) {
        _delegate = delegate;
        [self setupNetworkReachability];
    }
    return self;
}

- (void)dealloc {
    if (self.reachabilityRef) {
        CFRelease(self.reachabilityRef);
    }
}

- (void)setupNetworkReachability {
    struct sockaddr_in address;
    bzero(&address, sizeof(address));
    address.sin_len = sizeof(address);
    address.sin_family = AF_INET;
    self.reachabilityRef = SCNetworkReachabilityCreateWithAddress(NULL, (struct sockaddr *)&address);
    SCNetworkReachabilityContext context = {0, (__bridge void *)self, NULL, NULL, NULL};
    SCNetworkReachabilitySetCallback(self.reachabilityRef, YYBAria2NetworkReachabilityCallback, &context);
    SCNetworkReachabilityScheduleWithRunLoop(self.reachabilityRef, CFRunLoopGetMain(), kCFRunLoopDefaultMode);
    SCNetworkReachabilityFlags flags;
    if (SCNetworkReachabilityGetFlags(self.reachabilityRef, &flags)) {
        self.networkReachable = (flags & kSCNetworkReachabilityFlagsReachable) && !(flags & kSCNetworkReachabilityFlagsConnectionRequired);
    }
    
    // 主动同步一次，避免用户启动时就是无网状态但上层没有感知
    [self handleNetworkChange:self.networkReachable];
}

- (void)handleNetworkChange:(BOOL)reachable {
    self.networkReachable = reachable;
    if (self.delegate && [self.delegate respondsToSelector:@selector(yybAria2OnNetworkChange:)]) {
        [self.delegate yybAria2OnNetworkChange:reachable];
    }
}

@end

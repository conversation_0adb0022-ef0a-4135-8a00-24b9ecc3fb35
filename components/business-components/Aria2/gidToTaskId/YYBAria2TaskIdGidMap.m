//
//  YYBAria2TaskIdGidMap.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/4.
//  gid <-> taskId的映射表

#import "YYBAria2TaskIdGidMap.h"

@interface YYBAria2TaskIdGidMap ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *taskIdToGid;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *gidToTaskId;
@property (nonatomic, strong) dispatch_queue_t syncQueue;

@end

@implementation YYBAria2TaskIdGidMap

+ (instancetype)sharedMap {
    static YYBAria2TaskIdGidMap *map;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = [[YYBAria2TaskIdGidMap alloc] init];
        map.taskIdToGid = [NSMutableDictionary dictionary];
        map.gidToTaskId = [NSMutableDictionary dictionary];
        map.syncQueue = dispatch_queue_create("com.yyb.aria2.taskidgidmap", DISPATCH_QUEUE_SERIAL);
    });
    return map;
}

- (void)bindTaskId:(NSString *)taskId toGid:(NSString *)gid {
    if (!taskId || !gid) return;
    dispatch_async(self.syncQueue, ^{
        // 1. 若taskId已有旧gid，则清除旧gid的映射
        NSString *oldGid = self.taskIdToGid[taskId];
        if (oldGid && ![oldGid isEqualToString:gid]) {
            [self.gidToTaskId removeObjectForKey:oldGid];
        }
        // 2. 若gid已有旧taskId，也清除
        NSString *oldTaskId = self.gidToTaskId[gid];
        if (oldTaskId && ![oldTaskId isEqualToString:taskId]) {
            [self.taskIdToGid removeObjectForKey:oldTaskId];
        }
        // 3. 新绑定
        self.taskIdToGid[taskId] = gid;
        self.gidToTaskId[gid] = taskId;
    });
}

- (void)unbindTaskId:(NSString *)taskId {
    if (!taskId) return;
    dispatch_async(self.syncQueue, ^{
        NSString *gid = self.taskIdToGid[taskId];
        if (gid) [self.gidToTaskId removeObjectForKey:gid];
        [self.taskIdToGid removeObjectForKey:taskId];
    });
}

- (void)unbindGid:(NSString *)gid {
    if (!gid) return;
    dispatch_async(self.syncQueue, ^{
        NSString *taskId = self.gidToTaskId[gid];
        if (taskId) [self.taskIdToGid removeObjectForKey:taskId];
        [self.gidToTaskId removeObjectForKey:gid];
    });
}

- (NSString *)taskIdForGid:(NSString *)gid {
    if (!gid) return nil;
    __block NSString *taskId = nil;
    dispatch_sync(self.syncQueue, ^{
        taskId = self.gidToTaskId[gid];
    });
    return taskId;
}

- (NSString *)gidForTaskId:(NSString *)taskId {
    if (!taskId) return nil;
    __block NSString *gid = nil;
    dispatch_sync(self.syncQueue, ^{
        gid = self.taskIdToGid[taskId];
    });
    return gid;
}

@end

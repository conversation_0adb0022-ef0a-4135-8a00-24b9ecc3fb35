//
//  YYBAria2TaskIdGidMap.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/// gid <-> taskId的映射表
@interface YYBAria2TaskIdGidMap : NSObject

+ (instancetype)sharedMap;

/// 绑定、解绑 taski和gid
- (void)bindTaskId:(NSString *)taskId toGid:(NSString *)gid;
- (void)unbindTaskId:(NSString *)taskId;
- (void)unbindGid:(NSString *)gid;


/// 查询
- (nullable NSString *)taskIdForGid:(NSString *)gid;
- (nullable NSString *)gidForTaskId:(NSString *)taskId;

@end

NS_ASSUME_NONNULL_END

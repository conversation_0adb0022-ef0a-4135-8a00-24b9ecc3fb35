//
//  YYBAria2Error.h
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const YYBAria2ErrorDomain;

/// Aria2官方错误码（正数，详见官方文档 https://aria2.github.io/manual/en/html/aria2c.html#id1 ）

/// 包装层错误码（负数，业务/SDK自定义，不与官方冲突）
typedef NS_ENUM(NSInteger, YYBAria2ErrorCode) {
    YYBAria2ErrorInvalidParam           = -1001, ///< 参数无效
    YYBAria2ErrorTaskNotFound           = -1002, ///< 未找到任务
    YYBAria2ErrorFileCorrupted          = -1003, ///< 文件损坏
    YYBAria2ErrorNetworkUnavailable     = -1004, ///< 网络不可用
    YYBAria2ErrorServiceUnavailable     = -1005, ///< 服务不可用
    YYBAria2ErrorAddUriDuplicate        = -1006, ///< 任务重复添加
    YYBAria2ErrorAddUriFailed           = -1007, ///< 任务下发失败
    YYBAria2ErrorRetryLimitExceeded     = -1008, ///< 重试超限
    YYBAria2ErrorUserCancelled          = -1009, ///< 用户取消
    YYBAria2ErrorTaskRemoved            = -1010, ///< 任务被移除
    YYBAria2ErrorTaskPaused             = -1011, ///< 任务被暂停
    YYBAria2ErrorTaskAlreadyCompleted   = -1012, ///< 任务已完成
    YYBAria2ErrorTaskInFinalState       = -1013, ///< 任务已终态
    YYBAria2ErrorSessionInitFailed      = -1014, ///< Session初始化失败
    YYBAria2ErrorSessionRunLoopCrashed  = -1015, ///< Session线程崩溃
    YYBAria2ErrorRPCMethodNotSupported  = -1016, ///< 不支持的rpc方法
    YYBAria2ErrorRPCNotGetFiles         = -1017, ///< rpc找不到文件信息
    YYBAria2ErrorRPCNotGetUris          = -1018, ///< rpc找不到文件uri信息
    // ...可扩展
};

/// 统一构造NSError
@interface YYBAria2Error : NSObject
+ (NSError *)errorWithCode:(NSInteger)code
                   message:(nullable NSString *)message
                 userInfo:(nullable NSDictionary *)userInfo;

+ (NSError *)errorWithCode:(NSInteger)code
                    taskId:(nullable NSString*)taskId
                   message:(nullable NSString *)message
                 userInfo:(nullable NSDictionary *)userInfo;
@end

NS_ASSUME_NONNULL_END

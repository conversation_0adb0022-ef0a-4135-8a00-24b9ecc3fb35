//
//  YYBAria2Error.m
//  YYBMacBusinessComponents
//
//  Created by 凌刚 on 2025/8/9.
//

#import "YYBAria2Error.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

#define kLogTag @"YYBAria2Error"

NSString *const YYBAria2ErrorDomain = @"com.yyb.aria2.error";
NSString *const YYBAria2ErrorTaskId = @"com.yyb.aria2.taskId";

@implementation YYBAria2Error

+ (NSError *)errorWithCode:(NSInteger)code
                   message:(NSString *)message
                 userInfo:(NSDictionary *)userInfo
{
    return [self errorWithCode:code
                        taskId:nil
                       message:message
                      userInfo:userInfo];
}

+ (NSError *)errorWithCode:(NSInteger)code
                    taskId:(nullable NSString*)taskId
                   message:(nullable NSString *)message
                  userInfo:(nullable NSDictionary *)userInfo {
    NSMutableDictionary *info = userInfo ? [userInfo mutableCopy] : [NSMutableDictionary dictionary];
    if (message) {
        info[NSLocalizedDescriptionKey] = message;
    }
    if (taskId) {
        info[YYBAria2ErrorTaskId] = taskId;
    }
    YYBMacLogError(kLogTag, @"taskId: %@, erroCode: %@, message: %@, userInfo: %@", taskId, @(code), message, userInfo);
    return [NSError errorWithDomain:YYBAria2ErrorDomain code:code userInfo:info];
}

@end

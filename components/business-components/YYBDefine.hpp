//
//  YYBDefine.hpp
//  YYBMacApp
//
//  Created by halehuang on 2025/8/5.
//

#ifndef YYBDefine_hpp
#define YYBDefine_hpp

#include <string>

namespace YYB {

// 应用名称
static const std::string YYBBundleName = "腾讯应用宝";
static const std::string YYBEngineBundleName = "yyb_mac";

// Bundle ID
static const std::string YYBMainBundleID = "com.tencent.yybmac";
static const std::string YYBEngineBundleID = "com.tencent.yybmac.engine";
static const std::string YYBPackageBundleID = "com.tencent.yybmac.app";
static const std::string YYBServiceBundleID = "com.tencent.yybmac.yybService";

// Process Name
static const std::string ProcessEngine = "engine";
static const std::string ProcessAppStore = "appstore";
static const std::string ProcessBoastcast = "boastcast";
static const std::string ProcessService = "service";

} // namespace YYB

#endif /* YYBDefine_hpp */

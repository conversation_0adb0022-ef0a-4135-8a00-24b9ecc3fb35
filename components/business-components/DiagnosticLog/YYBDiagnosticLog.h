//
//  YYBDiagnosticLog.h
//  YYBMacApp
//
//  Created by halehuang on 2025/7/21.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^ZipDiagnosticProgressBlock)(float progress, NSString * _Nullable currentFile);
typedef void(^ZipDiagnosticLogCompletion)(BOOL success, NSString * _Nullable zipPath, NSError * _Nullable error);
typedef void(^UploadDiagnosticLogCompletion)(BOOL result, NSString *_Nullable errMsg);

@interface YYBDiagnosticLog : NSObject

/// 生成诊断日志
/// @param useDefaultProgressPanel 是否弹出进度弹窗
/// @param useDefaultSavePanel 是否弹出另存为的弹窗
+ (void)zipDiagnosticLogWithWindwow:(NSWindow *)window
            useDefaultProgressPanel:(BOOL)useDefaultProgressPanel
                useDefaultSavePanel:(BOOL)useDefaultSavePanel
                      progressBlock:(nullable ZipDiagnosticProgressBlock)progressBlock
                         completion:(nullable ZipDiagnosticLogCompletion)completion;

/// 弹出输入摘要的弹窗，并在用户点击发送后上传日志
+ (void)showLogSummaryInputPanelAndUpload:(NSWindow *)window
                               completion:(nullable UploadDiagnosticLogCompletion)completion;

@end

NS_ASSUME_NONNULL_END

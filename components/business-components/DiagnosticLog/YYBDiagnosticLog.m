//
//  YYBDiagnosticLog.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/21.
//

#import "YYBDiagnosticLog.h"
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacLogUpload.h>
#else
#import <YYBMacLog.h>
#import <YYBMacLogUpload.h>
#endif
#import "MacroUtils.h"

static NSString *const kTag = @"YYBDiagnosticLog";
static NSPanel *logProgressPanel;
static NSProgressIndicator *logProgressIndicator;
static NSTextField *logProgressLabel;

@implementation YYBDiagnosticLog

#pragma mark - 日志上传摘要输入弹窗及上传逻辑

+ (void)zipDiagnosticLogWithWindwow:(NSWindow *)window
            useDefaultProgressPanel:(BOOL)useDefaultProgressPanel
                useDefaultSavePanel:(BOOL)useDefaultSavePanel
                      progressBlock:(ZipDiagnosticProgressBlock)progressBlock
                         completion:(ZipDiagnosticLogCompletion)completion {
    [[YYBMacLogUpload sharedInstance] addExtraFilePath:@"/tmp/yyb_mac.log"];
    [[YYBMacLogUpload sharedInstance] addExtraFilePath:@"/tmp/yybservice.err"];

    if (useDefaultProgressPanel) {
        [self showLogSaveProgressPanel];
    }
    __weak typeof(self) weakSelf = self;
    [[YYBMacLogUpload sharedInstance] saveLog:nil
                                     progress:^(float progress, NSString * _Nullable currentFile) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
            if (useDefaultProgressPanel) {
                [weakSelf updateLogSaveProgress:progress currentFile:currentFile];
            }
            if (progressBlock) {
                progressBlock(progress, currentFile);
            }
        });
        
    } completion:^(BOOL success, NSString * _Nullable zipPath, NSError * _Nullable error) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
            [weakSelf hideLogSaveProgressPanel];
            if (completion) {
                completion(success, zipPath, error);
            }
        });
    } useDefaultSavePanel:useDefaultSavePanel];
}

/// 弹出输入摘要的弹窗，并在用户点击发送后上传日志
+ (void)showLogSummaryInputPanelAndUpload:(NSWindow *)window
                               completion:(UploadDiagnosticLogCompletion)completion {
    // 创建弹窗
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = @"建议输入日志摘要，检索定位更高效";
//    alert.informativeText = @"可输入本次日志的关键问题点或摘要信息，便于检索定位";
    alert.alertStyle = NSAlertStyleInformational;
    // 输入框
    NSTextField *inputField = [[NSTextField alloc] initWithFrame:NSMakeRect(0, 0, 300, 24)];
    inputField.placeholderString = @"请输入日志摘要（可选）";
    alert.accessoryView = inputField;
    // 按钮
    [alert addButtonWithTitle:@"发送"];
    [alert addButtonWithTitle:@"取消"];
    // 日志
    YYBMacLogInfo(kTag, @"弹出日志上传摘要输入框");
    // 展示弹窗
    __weak typeof(self) weakSelf = self;
    [alert beginSheetModalForWindow:window completionHandler:^(NSModalResponse response) {
        if (response == NSAlertFirstButtonReturn) {
            NSString *summary = inputField.stringValue ?: @"";
            YYBMacLogInfo(kTag, @"用户点击发送日志，摘要: %@", summary);
            [weakSelf uploadTodayLogWithSummary:summary completion:completion];
        } else {
            YYBMacLogInfo(kTag, @"用户取消日志上传");
            if (completion) {
                completion(NO, @"User cancel");
            }
        }
    }];
}

/// 实际上传日志逻辑
+ (void)uploadTodayLogWithSummary:(NSString *)summary completion:(UploadDiagnosticLogCompletion)completion {
    NSString *tag = @"debug";
    NSTimeInterval currentTime = time(0);
    NSTimeInterval startTime = currentTime - 24 * 60 * 60;  // 24小时
    YYBMacLogInfo(kTag, @"开始上传日志，tag: %@, summary: %@", tag, summary);
    [[YYBMacLogUpload sharedInstance] uploadLogWithTag:tag
                                               summary:summary.length > 0 ? summary : @""
                                             startTime:startTime
                                               endTime:currentTime
                                            completion:^(BOOL result, NSString *_Nullable errMsg) {
        DISPATCH_ASYNC_ON_MAIN_QUEUE( ^{
            if (completion) {
                completion(result, errMsg);
            }
        });
    }];
}

#pragma mark - 日志保存进度UI

/// 展示日志保存进度面板
+ (void)showLogSaveProgressPanel {
    if (logProgressPanel) {
        [self hideLogSaveProgressPanel];
    }
    // 创建进度面板
    logProgressPanel = [[NSPanel alloc] initWithContentRect:NSMakeRect(0, 0, 400, 120)
                                                       styleMask:(NSWindowStyleMaskTitled | NSWindowStyleMaskClosable)
                                                         backing:NSBackingStoreBuffered
                                                           defer:NO];
    logProgressPanel.title = @"诊断日志打包中...";
    logProgressPanel.floatingPanel = YES;
    logProgressPanel.level = NSModalPanelWindowLevel;
    // 进度条
    logProgressIndicator = [[NSProgressIndicator alloc] initWithFrame:NSMakeRect(30, 60, 340, 20)];
    logProgressIndicator.indeterminate = NO;
    logProgressIndicator.minValue = 0.0;
    logProgressIndicator.maxValue = 1.0;
    logProgressIndicator.doubleValue = 0.0;
    [logProgressPanel.contentView addSubview:logProgressIndicator];
    // 进度文本
    logProgressLabel = [[NSTextField alloc] initWithFrame:NSMakeRect(30, 30, 340, 20)];
    logProgressLabel.editable = NO;
    logProgressLabel.bezeled = NO;
    logProgressLabel.drawsBackground = NO;
    logProgressLabel.stringValue = @"准备中...";
    [logProgressPanel.contentView addSubview:logProgressLabel];
    // 居中显示
    [logProgressPanel center];
    [logProgressPanel makeKeyAndOrderFront:nil];
    YYBMacLogInfo(kTag, @"诊断日志保存进度面板已显示");
}

/// 更新日志保存进度
+ (void)updateLogSaveProgress:(float)progress currentFile:(NSString *)currentFile {
    if (!logProgressPanel) return;
    logProgressIndicator.doubleValue = progress;
    NSString *fileStr = currentFile.length > 0 ? [currentFile lastPathComponent] : @"";
    logProgressLabel.stringValue = [NSString stringWithFormat:@"进度: %.0f%% %@", progress * 100, fileStr];
    YYBMacLogDebug(kTag, @"诊断日志保存进度: %.2f, 当前文件: %@", progress, fileStr);
}

/// 隐藏日志保存进度面板
+ (void)hideLogSaveProgressPanel {
    if (logProgressPanel) {
        [logProgressPanel orderOut:nil];
        logProgressPanel = nil;
        logProgressIndicator = nil;
        logProgressLabel = nil;
        YYBMacLogInfo(kTag, @"诊断日志保存进度面板已关闭");
    }
}

#pragma mark - 获取WebKit缓存等打包文件路径

+ (NSString *)getWebKitAllFilePath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *appSupportPath = [paths firstObject];
    NSString *baseBundlePath = [appSupportPath stringByAppendingPathComponent:[[NSBundle mainBundle] bundleIdentifier]];
    return [baseBundlePath stringByAppendingPathComponent:@"WebKit"];
}


@end

framework module YYBMacFusionSDK {
  umbrella header "YYBMacFusion.h"

  export *
  module * { export * }
  
  module YYBMacLog {
      header "YYBMacLog.h"
      header "YYBMacLogUpload.h"
      export *
  }
  module YYBMacBugly {
      header "YYBMacBugly.h"
      export *
  }
  module YYBMacHttp {
      header "YYBMacHttp.h"
      header "IYYBMacHttpListener.h"
      header "IYYBMacJsonCmdListener.h"
      header "YYBMacDataRequest.h"
      header "YYBMacHttpC.h"
      header "YYBMacHttpDefine.h"
      header "YYBMacHttpRequest.h"
      header "YYBMacHttpResponse.h"
      header "YYBMacJsonCmdRequest.h"
      header "YYBMacJsonCmdResponse.h"
      header "YYBMacJsonRequest.h"
      export *
  }
  module YYBMacDataReport {
      header "YYBMacDataReport.h"
      header "YYBMacDataReportDefine.h"
      export *
  }
  module <PERSON><PERSON>BMacMMKV {
      header "YYBMacMMKV.h"
      export *
  }
  module YYBMacQimei {
      header "YYBMacQimei.h"
      export *
  }
  module YYBMacResHub {
      header "YYBMacResHub.h"
      export *
  }
  module YYBMacShiplyConfig {
      header "YYBMacShiplyConfig.h"
      export *
  }
  module Reshub {
      header "ResHubModel.h"
      header "ResHubSubResModel.h"
      header "ResHubError.h"
      header "ResHubCommonDefines.h"
      header "ResHubResRefreshListener.h"
  }
  
  module RaftInterface {
      header "RAFTDownloadProtocol.h"
  }
  
  module YYBMacUpdater {
      header "YYBMacUpdateManager.h"
      header "YYBMacUpdateConfig.h"
      header "YYBUpdater.h"
      header "YYBUpdaterParam.h"
      header "YYBUpgradeModel.h"
      header "YYBExternalUpgradeDelegate.h"
      header "YYBAppcastItem.h"
  }
  
  module ShiplyMacUpgrade {
      header "SUGrayTacticsModel.h"
  }
}

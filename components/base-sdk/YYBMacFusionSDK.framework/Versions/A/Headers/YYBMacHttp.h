//
//  YYBMacHttp.h
//  YYBMacHttp
//
//  Created by gannicuswu on 2025/8/14.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <YYBMacFusionSDK/IYYBMacHttpListener.h>
#import <YYBMacFusionSDK/IYYBMacJsonCmdListener.h>
#import <YYBMacFusionSDK/YYBMacHttpRequest.h>
#import <YYBMacFusionSDK/YYBMacJsonCmdRequest.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacHttp : NSObject

+ (instancetype)sharedInstance;
- (instancetype)init NS_UNAVAILABLE;
- (instancetype)new NS_UNAVAILABLE;

- (void)sendHttpRequest:(nonnull YYBMacHttpRequest *)request
                    listener:(nullable id<IYYBMacHttpListener>)listener;

- (void)sendYYBJsonCmd:(nonnull YYBMacJsonCmdRequest *)request
              listener:(nullable id<IYYBMacJsonCmdListener>)listener;


@end

NS_ASSUME_NONNULL_END

//
//  SUGrayTacticsModel.h
//  DCLUpgradeSDK
//
//  Created by readyhe on 2021/5/20.
//  Copyright © 2021 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN


typedef long long SUTimeStampMsec;

/**
 flag    int    否    请求类型
 1 手动请求
 0 自动请求
 后台暂时不关心该字段，端上传。（防骚扰逻辑）
 localTacticsId    string    否    本地策略id，用于校验撤回逻辑
 localTacticsTime    long    否    本地策略时间（单位毫秒）
 localBaseStrategyTime    long    否    本地基本策略时间
 strategyType
 （之前的系统放在reserved map里面）    int    是    请求的策略类型
 0 不区分更新类型
 2. 全量或者灰度
 3.热更新
 ignoreNoDisturbPeriod  bool  拉取不受免打扰时间影响

 */
// 这个结构存在http body中，其余的参数带在http header中
@interface SUGrayTacticsRequest : NSObject
@property (nonatomic, assign) NSInteger flag;  // 1 手动请求 0 自动请求
@property (nonatomic, strong) NSString *localTacticsId;
@property (nonatomic, assign) SUTimeStampMsec localTacticsTime;
@property (nonatomic, assign) NSInteger strategyType;
@property (nonatomic, assign) BOOL ignoreNoDisturbPeriod;  // 拉取不受免打扰时间影响
@end

typedef NS_ENUM(NSInteger, SUPackageType) {
    SUPackageTypeAPK = 1,        // apk
    SUPackageTypeTestfight = 2,  // testfight
    SUPackageTypePlist = 3,      // plist
    SUPackageTypeAppStore = 4,   // appstore
    SUPackageTypePatch = 5,      // 补丁包
    SUPackageTypeHarmony = 6,    // 鸿蒙灰度包
    SUPackageTypeHarmonyEnterprise = 7,// 鸿蒙企业分发灰度
    SUPackageTypeHarmonyTest = 8,// 鸿蒙灰度包（应用尝鲜灰度）
    SUPackageTypeMacSparkle = 9, // Mac Sparkle
};

/**
 pkgName    string            release_alpha_1616648452.apk
 md5    string            21cff364d6e50f0016668f7a64524a1e
 version    string            6.4.50.634
 versionCode    int            6450（如何获取,fanqiao，simonxlin确定）
 buildNo    int            634
 buildNoStr    string       6.4.50.634
 downloadUrl    string            https://exp-beta.cdn.qq.com/beta/2021/03/25/comtencentnews_6.4.50.634_1f6de05b-71e5-4e42-976e-f4b362933ebf.apk
 pgkSize    int            45858441
 bundleId    string            com.tencent.news
 plistUrl    string
 pkgType
 1 apk
 2 testfight
 3 plist
 */
@interface SUPackageBasicInfo : NSObject
@property (nonatomic, copy) NSString *pkgName;
@property (nonatomic, copy) NSString *md5;
@property (nonatomic, copy) NSString *version;
@property (nonatomic, assign) NSInteger versionCode;
@property (nonatomic, assign) NSInteger buildNo;
@property (nonatomic, copy) NSString *buildNoStr;  // 部分业务构建号用的string类型，因此新增字段做兼容，新版SDK都使用buildNoStr
@property (nonatomic, copy) NSString *downloadUrl;
@property (nonatomic, assign) NSInteger pkgSize;
@property (nonatomic, copy) NSString *bundleId;
@property (nonatomic, copy) NSString *plistUrl;
@property (nonatomic, assign) SUPackageType pkgType;
@end

/**
 type    int            1  目前支持弹窗样式1
 title    string            弹窗标题： 腾讯新闻6.4.50.634.6450
 description    string            弹窗描述信息
 */
@interface SUClientInfo : NSObject
@property (nonatomic, assign) NSInteger type;  // 支持的弹窗类型 目前支持弹窗样式1
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *theDescription;
@end
/**
 升级策略枚举
 */
typedef NS_ENUM(NSInteger, SUStrategy) {
    SUStrategyNone = 0,     // 不更新
    SUStrategySuggest = 1,  // 建议
    SUStrategyForce = 2,    // 强制
};

/**
 升级类型
 */
typedef NS_ENUM(NSInteger, SUType) {
    SUTypeGray = 1,  // 灰度升级
    SUTypeFull = 2,  // 全量升级
    SUTypeHot = 3,   // 热更新
};

/**
 策略提醒模式
 */
typedef NS_ENUM(NSInteger, SURemindType) {
    SURemindTypeAlertView = 1,  // 弹窗
    SURemindTypeHotDot = 2,     // 红点
    SURemindTypeAll = 3,        // 全部
};



NS_ASSUME_NONNULL_END

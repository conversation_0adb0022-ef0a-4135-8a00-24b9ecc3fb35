//
//  YYBMacLogUpload.h
//  YYBMacLog
//
//  Created by halehuang on 2024/1/4.
//  Copyright © 2024 Tencent. All rights reserved.
//

// 日志查询平台：https://bugly.woa.com/v2/diagnose/command_delivery/list?productId=e084796a57&pid=4

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacLogUpload : NSObject

typedef void(^YYBMacLogZipProgressBlock)(float progress, NSString * _Nullable currentFile);
typedef void(^YYBMacLogZipCompletionBlock)(BOOL success, NSString * _Nullable zipPath, NSError * _Nullable error);

+ (instancetype)sharedInstance;
- (instancetype)init NS_UNAVAILABLE;
- (instancetype)new NS_UNAVAILABLE;

/// 初始化，必须传入 deviceId、appId、appKey
- (void)setupWithDeviceId:(NSString *)deviceId appId:(NSString *)appId appKey:(NSString *)appKey;

/// 上传日志到TDOS
- (void)uploadLogWithTag:(NSString *)tag
                 summary:(NSString *)summary
               startTime:(NSTimeInterval)startTime
                 endTime:(NSTimeInterval)endTime
              completion:(nullable void (^)(BOOL result, NSString *_Nullable errMsg))completionBlock;

/// 本地保存日志(包含额外文件)
- (void)saveLog:(nullable NSString *)password;

/// 异步打包日志和额外文件，支持进度和结果回调
- (void)saveLog:(nullable NSString *)password
       progress:(nullable YYBMacLogZipProgressBlock)progressBlock
     completion:(nullable YYBMacLogZipCompletionBlock)completionBlock
useDefaultSavePanel:(BOOL)useDefaultSavePanel;

/// 写入日志
- (void)flushLog;

/// 注册额外需要打包的文件路径
- (void)addExtraFilePath:(NSString *)filePath;

/// 移除要打包的文件路径
- (void)removeExtraFilePath:(NSString *)filePath;

@end

NS_ASSUME_NONNULL_END

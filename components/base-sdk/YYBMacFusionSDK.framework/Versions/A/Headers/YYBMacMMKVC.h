//
//  YYBMacMMKVC.h
//  YYBMacMMKV
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// MMKV类型选择
typedef enum {
    YYBMacMMKVC_InstanceType_Default = 0,   // 单App全局
    YYBMacMMKVC_InstanceType_Public  = 1    // 多App/多进程共享（非必须的话：尽量使用Default）
} YYBMacMMKVC_InstanceType;

int YYBMacMMKVC_SetBool_Default(const char* key, int value);
int YYBMacMMKVC_SetInt32_Default(const char* key, int value);
int YYBMacMMKVC_SetUInt32_Default(const char* key, unsigned int value);
int YYBMacMMKVC_SetInt64_Default(const char* key, long long value);
int YYBMacMMKVC_SetUInt64_Default(const char* key, unsigned long long value);
int YYBMacMMKVC_SetFloat_Default(const char* key, float value);
int YYBMacMMKVC_SetDouble_Default(const char* key, double value);
int YYBMacMMKVC_SetString_Default(const char* key, const char* value);
int YYBMacMMKVC_SetData_Default(const char* key, const void* data, unsigned int len);
int YYBMacMMKVC_SetDate_Default(const char* key, double timestamp);

int YYBMacMMKVC_GetBool_Default(const char* key, int defaultValue);
int YYBMacMMKVC_GetInt32_Default(const char* key, int defaultValue);
unsigned int YYBMacMMKVC_GetUInt32_Default(const char* key, unsigned int defaultValue);
long long YYBMacMMKVC_GetInt64_Default(const char* key, long long defaultValue);
unsigned long long YYBMacMMKVC_GetUInt64_Default(const char* key, unsigned long long defaultValue);
float YYBMacMMKVC_GetFloat_Default(const char* key, float defaultValue);
double YYBMacMMKVC_GetDouble_Default(const char* key, double defaultValue);
const char* YYBMacMMKVC_GetString_Default(const char* key, const char* defaultValue);
int YYBMacMMKVC_GetData_Default(const char* key, void* outBuf, unsigned int bufLen, unsigned int* outDataLen);
double YYBMacMMKVC_GetDate_Default(const char* key, double defaultTimestamp);

int YYBMacMMKVC_SetBool(const char* key, int value, int instanceType);
int YYBMacMMKVC_SetInt32(const char* key, int value, int instanceType);
int YYBMacMMKVC_SetUInt32(const char* key, unsigned int value, int instanceType);
int YYBMacMMKVC_SetInt64(const char* key, long long value, int instanceType);
int YYBMacMMKVC_SetUInt64(const char* key, unsigned long long value, int instanceType);
int YYBMacMMKVC_SetFloat(const char* key, float value, int instanceType);
int YYBMacMMKVC_SetDouble(const char* key, double value, int instanceType);
int YYBMacMMKVC_SetString(const char* key, const char* value, int instanceType);
int YYBMacMMKVC_SetData(const char* key, const void* data, unsigned int len, int instanceType);
int YYBMacMMKVC_SetDate(const char* key, double timestamp, int instanceType);

int YYBMacMMKVC_GetBool(const char* key, int defaultValue, int instanceType);
int YYBMacMMKVC_GetInt32(const char* key, int defaultValue, int instanceType);
unsigned int YYBMacMMKVC_GetUInt32(const char* key, unsigned int defaultValue, int instanceType);
long long YYBMacMMKVC_GetInt64(const char* key, long long defaultValue, int instanceType);
unsigned long long YYBMacMMKVC_GetUInt64(const char* key, unsigned long long defaultValue, int instanceType);
float YYBMacMMKVC_GetFloat(const char* key, float defaultValue, int instanceType);
double YYBMacMMKVC_GetDouble(const char* key, double defaultValue, int instanceType);
const char* YYBMacMMKVC_GetString(const char* key, const char* defaultValue, int instanceType);
int YYBMacMMKVC_GetData(const char* key, void* outBuf, unsigned int bufLen, unsigned int* outDataLen, int instanceType);
double YYBMacMMKVC_GetDate(const char* key, double defaultTimestamp, int instanceType);


#ifdef __cplusplus
}
#endif

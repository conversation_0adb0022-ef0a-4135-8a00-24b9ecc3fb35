//
//  YYBExternalUpgradeDelegate.h
//  YYBMacUpdater
//
//  Created by bethahuang on 2025/8/18.
//

#import <Foundation/Foundation.h>

@class YYBUpdater;
@class YYBAppcastItem;

typedef NS_ENUM(NSUInteger, YYBUpgradeCheck) {
    YYBUpgradeCheckUnknown = -1,
    YYBUpgradeCheckUpdates = 0,
    YYBUpgradeCheckUpdatesInBackground = 1,
    YYBUpgradeCheckUpdateInformation = 2
};


NS_ASSUME_NONNULL_BEGIN

@protocol YYBExternalUpgradeDelegate <NSObject>

@optional

/**
 * 询问是否可以执行更新检查
 * @param updater YYBUpdater 实例
 * @param error 错误信息指针
 * @return 是否允许执行更新检查
 */
- (BOOL)updater:(YYBUpdater *)updater mayPerformUpdateCheck:(YYBUpgradeCheck)check error:(NSError * __autoreleasing *)error;

/**
 * 发现有效更新时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 */
- (void)updater:(YYBUpdater *)updater didFindValidUpdate:(YYBAppcastItem *)item;

#pragma mark - 下载相关方法

/**
 * 即将开始下载更新时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 * @param request 下载请求对象
 */
- (void)updater:(YYBUpdater *)updater willDownloadUpdate:(YYBAppcastItem *)item withRequest:(NSMutableURLRequest *)request;

/**
 * 下载更新完成时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 */
- (void)updater:(YYBUpdater *)updater didDownloadUpdate:(YYBAppcastItem *)item;

/**
 * 下载更新失败时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 * @param error 错误信息
 */
- (void)updater:(YYBUpdater *)updater failedToDownloadUpdate:(YYBAppcastItem *)item error:(NSError *)error;

/**
 * 用户取消下载时调用
 * @param updater YYBUpdater 实例
 */
- (void)userDidCancelDownload:(YYBUpdater *)updater;

#pragma mark - 安装相关方法

/**
 * 即将开始提取更新时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 */
- (void)updater:(YYBUpdater *)updater willExtractUpdate:(YYBAppcastItem *)item;

/**
 * 提取更新完成时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 */
- (void)updater:(YYBUpdater *)updater didExtractUpdate:(YYBAppcastItem *)item;

/**
 * 即将开始安装更新时调用
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 */
- (void)updater:(YYBUpdater *)updater willInstallUpdate:(YYBAppcastItem *)item;

/**
 * 询问是否应该推迟重启以安装更新
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 * @param installHandler 安装处理块
 * @return 是否推迟重启
 */
- (BOOL)updater:(YYBUpdater *)updater shouldPostponeRelaunchForUpdate:(YYBAppcastItem *)item untilInvokingBlock:(void (^)(void))installHandler;

/**
 * 即将重启应用程序时调用
 * @param updater YYBUpdater 实例
 */
- (void)updaterWillRelaunchApplication:(YYBUpdater *)updater;

/**
 * 询问是否在退出时安装更新
 * @param updater YYBUpdater 实例
 * @param item 更新项目信息
 * @param immediateInstallHandler 立即安装处理块
 * @return 是否在退出时安装
 */
- (BOOL)updater:(YYBUpdater *)updater willInstallUpdateOnQuit:(YYBAppcastItem *)item immediateInstallationBlock:(void (^)(void))immediateInstallHandler;

#pragma mark - 生命周期相关方法

/**
 * 更新过程中止并出现错误时调用
 * @param updater YYBUpdater 实例
 * @param error 错误信息
 */
- (void)updater:(YYBUpdater *)updater didAbortWithError:(NSError *)error;

/**
 * 更新周期完成时调用
 * @param updater YYBUpdater 实例
 * @param error 错误信息（如果有）
 */
- (void)updater:(YYBUpdater *)updater didFinishUpdateCycleForUpdateCheck:(YYBUpgradeCheck)check error:(nullable NSError *)error;

@end

NS_ASSUME_NONNULL_END

//
//  YYBMacBuglyC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacLogUploadC.h
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// 初始化
void YYBMacLogUploadC_Setup(const char* deviceId, const char* appId, const char* appKey);

// 上传日志到TDOS
typedef void (*YYBMacLogUploadC_CompletionCallback)(int result, const char* errMsg, void* user_data);
void YYBMacLogUploadC_UploadLog(const char* tag, const char* summary, double startTime, double endTime,
                                YYBMacLogUploadC_CompletionCallback cb, void* user_data);

// 本地保存日志（同步，无回调）
void YYBMacLogUploadC_SaveLog(void);

// 本地保存日志（异步，带进度和完成回调）
typedef void (*YYBMacLogUploadC_ZipProgressCallback)(float progress, const char* currentFile, void* user_data);
typedef void (*YYBMacLogUploadC_ZipCompletionCallback)(int result, const char* zipPath, const char* errMsg, void* user_data);
// 本地保存日志（异步，带进度和完成回调，可指定是否弹出保存面板）
void YYBMacLogUploadC_SaveLogAsync(const char* password,
                                    YYBMacLogUploadC_ZipProgressCallback progress_cb,
                                    YYBMacLogUploadC_ZipCompletionCallback completion_cb,
                                    void* user_data,
                                   int useDefaultSavePanel);

// 写入日志
void YYBMacLogUploadC_FlushLog(void);

// 注册额外需要打包的文件路径
void YYBMacLogUploadC_AddExtraFilePath(const char* filePath);

// 移除要打包的文件路径
void YYBMacLogUploadC_RemoveExtraFilePath(const char* filePath);

#ifdef __cplusplus
}
#endif

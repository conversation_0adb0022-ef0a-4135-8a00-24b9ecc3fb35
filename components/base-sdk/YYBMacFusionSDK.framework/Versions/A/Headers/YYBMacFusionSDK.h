//
//  YYBMacFusionSDK.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
@class YYBMacFusionSDKConfig;

NS_ASSUME_NONNULL_BEGIN

/// 基础SDK统一初始化及切换环境/用户入口
@interface YYBMacFusionSDK : NSObject

/// 单例
+ (instancetype)sharedInstance;

/// 统一初始化（只需调用一次，重复调用无效）
/// @param config 配置对象
/// @param completion 初始化完成回调（异步，qimei等准备好后回调）
- (void)setupWithConfig:(YYBMacFusionSDKConfig *)config
             completion:(void(^_Nullable)(BOOL success, NSError * _Nullable error))completion;


/// 初始化仅商店使用的sdk，需等qimei准备好后再调用
-(void)setupAppStoreWithConfig:(YYBMacFusionSDKConfig *)config;

/// 切换用户
- (void)switchUser:(nullable NSString *)userId;

/// 切换环境
- (void)switchEnvironment:(BOOL)isRelease;

@end

NS_ASSUME_NONNULL_END

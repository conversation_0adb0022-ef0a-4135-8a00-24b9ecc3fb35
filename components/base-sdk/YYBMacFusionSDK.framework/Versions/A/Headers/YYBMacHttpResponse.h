//  Copyright © 2020 Tencent Inc. All rights reserved. 
//
//  YYBMacHttpResponse.h
//  VBTransportServiceiOS
//
//  Created by gannicuswu on 2025/8/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacHttpResponse : NSObject

/**
 * 响应的 headers
 */
@property (nullable, nonatomic, copy) NSDictionary *responseHeaders;

@property (nonatomic, assign) BOOL isJson;

/**
 * 响应的二进制数据
 */
@property (nonatomic, copy, nullable) NSData *responseData;

/**
 * JSON格式的response
 * @discussion JSON格式的数据,已经解析为JSON对象,具体是Dictionary或者Array,需要使用方根据协议转换类型
 */
@property (nonatomic, copy, nullable) id responseJson;

/**
 * 获取特定header的值
 * @param key header的键
 * @return header的值，如果不存在则返回nil
 */
- (nullable NSString *)headerValueForKey:(NSString *)key;

@end

NS_ASSUME_NONNULL_END

//
//  YYBMacDataReport.h
//  YYBMacDataReport
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacDataReport : NSObject

#pragma mark - 单例及初始化
/// 获取单例
+ (instancetype)sharedInstance;

/// 初始化 上报 服务（必须先调用）
/// @param appKey 大同上报 分配的 appKey（必填，通常和qimei分配的是同一个key）
/// @param isRelease 是否为生产环境
- (void)setupWithAppKey:(NSString *)appKey
              isRelease:(BOOL)isRelease;

#pragma mark - 通用上报
- (void)reportEventCode:(NSString *)eventCode
                   data:(nullable NSDictionary *)data;

- (void)reportEventCode:(NSString *)eventCode
              eventName:(nullable NSString *)eventName
                   data:(nullable NSDictionary *)data;

#pragma mark - 公参

/// 读取公参
- (NSDictionary *)commonParams;

/// 添加公参
/// - Parameters:
///   - key: 公参的key
///   - value: 公参的value
- (void)addCommonParamWithKey:(NSString *)key
                        value:(NSString *)value;

#pragma mark - 便捷上报方法-曝光

/// 页面曝光/结束曝光上报
/// - Parameters:
///   - pageid: 页面id
///   - pageShow: 页面曝光/停止曝光
///   - data: 扩展参数
+ (void)reportExposureForPage:(NSString *)pageid
                     pageShow:(BOOL)pageShow
                         data:(nullable NSDictionary *)data;

/// 模块曝光上报
/// - Parameters:
///   - pageid: 页面id
///   - moduleid: 模块id
///   - data: 扩展参数
+ (void)reportExposureForModuelShow:(NSString *)pageid
                           moduleid:(NSString *)moduleid
                               data:(nullable NSDictionary *)data;

/// 元素曝光上报
/// - Parameters:
///   - pageid: 页面id
///   - moduleid: 模块id
///   - elementid: 元素id
///   - data: 扩展参数
+ (void)reportExposureForElementShow:(NSString *)pageid
                            moduleid:(NSString *)moduleid
                           elementid:(NSString *)elementid
                                data:(nullable NSDictionary *)data;

#pragma mark - 便捷上报方法-点击
/// 模块点击上报
/// - Parameters:
///   - pageid: 页面id
///   - moduleid: 模块id
///   - data: 扩展参数
+ (void)reportClickForModule:(NSString *)pageid
                    moduleid:(NSString *)moduleid
                        data:(nullable NSDictionary *)data;

/// 元素点击上报
/// - Parameters:
///   - pageid: 页面id
///   - moduleid: 模块id
///   - elementid: 元素id
///   - data: 扩展参数
+ (void)reportClickForElement:(NSString *)pageid
                     moduleid:(NSString *)moduleid
                    elementid:(NSString *)elementid
                         data:(nullable NSDictionary *)data;

#pragma mark - 通用action上报
/// action上报
/// - Parameters:
///   - eventCode: 事件code
///   - eventName: 事件名称
///   - pageid: 页面id
///   - data: 扩展参数
+ (void)reportAction:(NSString *)eventCode
           eventName:(NSString *)eventName
              pageid:(NSString *)pageid
                data:(nullable NSDictionary *)data;

/// action上报
/// - Parameters:
///   - eventCode: 事件code
///   - eventName: 事件名称
///   - pageid: 页面id
///   - moduleid: 模块id
///   - elementid: 元素id
///   - data: 扩展参数
+ (void)reportAction:(NSString *)eventCode
           eventName:(NSString *)eventName
              pageid:(nullable NSString *)pageid
            moduleid:(nullable NSString *)moduleid
           elementid:(nullable NSString *)elementid
                data:(nullable NSDictionary *)data;

@end

NS_ASSUME_NONNULL_END

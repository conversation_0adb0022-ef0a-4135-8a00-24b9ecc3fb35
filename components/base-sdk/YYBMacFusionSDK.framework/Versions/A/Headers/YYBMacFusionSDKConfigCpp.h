//
//  YYBMacFusionSDKConfig.h
//  YYBMacFusionSDKConfig
//
//  Created by jamieling on 2025/6/17.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacFusionSDKConfigCpp.h
#pragma once

#ifdef __cplusplus
#include <string>
#endif

#ifdef __cplusplus
struct YYBMacFusionSDKConfigCpp {
    std::string identifier;
    std::string buglyAppId;
    std::string buglyAppKey;
    std::string dataReportAppKey;
    std::string shiplyAppId;
    std::string shiplyAppKey;
    bool isReleaseApp = true;
    bool isReleaseEnvironment = true;
    std::string userId;
    std::string channel;
    // 目标文件夹路径（不指定的话：默认日志和mmkv都会放在bundleid生成的相关文件夹下）
    std::string destDictName;
    std::string reshubResourceStoragePath;
    int updateMode = 0;
    double updateIntervalTime = 4*60*60.0;
};
#endif

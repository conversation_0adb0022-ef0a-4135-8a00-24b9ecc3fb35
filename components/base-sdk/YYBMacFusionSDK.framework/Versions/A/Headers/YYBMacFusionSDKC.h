//
//  YYBMacFusionSDK.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//


// YYBMacFusionSDKC.h
#ifdef __cplusplus
extern "C" {
#endif

struct YYBMacFusionSDKConfigCpp; // 前置声明

/// 初始化FusionSDK，传入C++配置对象指针
void YYBMacFusionSDK_SetupWithCppConfig(const struct YYBMacFusionSDKConfigCpp *configCpp, void (*callback)(int success, const char *errorMsg));

/// 切换用户
void YYBMacFusionSDK_SwitchUser(const char *userId);

/// 切换环境
void YYBMacFusionSDK_SwitchEnvironment(int isRelease);

#ifdef __cplusplus
}
#endif

//
//  YYBMacShiplyConfig.h
//  YYBMacShiplyConfig
//
//  Created by jamieling on 2025/6/12.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// Shiply 配置管理器
@interface YYBMacShiplyConfig : NSObject

/// 单例获取
+ (instancetype)sharedInstance;

/// 初始化，必须传入 用户id、qimei（设备唯一标记）、环境标记、AppId、AppKey
/// @param userId 用户标识id
/// @param qimei 设备唯一标识
/// @param isRelease 是否为生产环境
/// @param appId 业务AppId
/// @param appKey 业务AppKey
- (void)setupWithUserId:(NSString *)userId
                  qimei:(NSString *)qimei
              isRelease:(BOOL)isRelease
                  appId:(NSString *)appId
                 appKey:(NSString *)appKey;

/// 切换全局用户id
- (void)switchUserId:(NSString *)userId;

/// 切换qimei
- (void)switchQimei:(NSString *)qimei;

/// 切换环境
- (void)switchEnvironment:(BOOL)isRelease;

/// 配置项读取接口
- (BOOL)isSwitchOn:(NSString *)key defaultValue:(BOOL)defaultValue;
- (NSInteger)intValueWithKey:(NSString *)key defaultValue:(NSInteger)defaultValue;
- (float)floatValueWithKey:(NSString *)key defaultValue:(float)defaultValue;
- (NSString *)stringValueWithKey:(NSString *)key defaultValue:(NSString *)defaultValue;
- (NSArray *)arrayValueWithKey:(NSString *)key defaultValue:(NSArray *)defaultValue;
- (NSDictionary *)mapValueWithKey:(NSString *)key defaultValue:(NSDictionary *)defaultValue;
- (BOOL)hasConfigInfoWithKey:(NSString *)key;

@end

NS_ASSUME_NONNULL_END

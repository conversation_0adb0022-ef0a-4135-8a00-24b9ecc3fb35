//  Created by 凌刚 on 2025/6/18.
//  Copyright © 2025 CocoaPods. All rights reserved.
//

#import <Foundation/Foundation.h>

//! Project version number for YYBMacFusionSDK.
FOUNDATION_EXPORT double YYBMacFusionSDKVersionNumber;

//! Project version string for YYBMacFusionSDK.
FOUNDATION_EXPORT const unsigned char YYBMacFusionSDKVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <YYBMacFusionSDK/PublicHeader.h"


// oc头文件 -------
#import <YYBMacFusionSDK/YYBMacFusionSDK.h>         // 统一以下所有sdk的初始化及环境、账号切换方法
#import <YYBMacFusionSDK/YYBMacFusionSDKConfig.h>

// c++头文件
#import <YYBMacFusionSDK/YYBMacFusionSDKC.h>        // 统一以下所有sdk的初始化及环境、账号切换方法
#import <YYBMacFusionSDK/YYBMacFusionSDKConfigCpp.h>

#import <YYBMacFusionSDK/YYBMacBuglyC.h>            // bugly
#import <YYBMacFusionSDK/YYBMacDataReportC.h>       // 大同上报
#import <YYBMacFusionSDK/YYBMacLogC.h>              // 日志
#import <YYBMacFusionSDK/YYBMacQimeiC.h>            // qimei
#import <YYBMacFusionSDK/YYBMacResHubC.h>           // shiply->资源管理
#import <YYBMacFusionSDK/YYBMacShiplyConfigC.h>     // shiply->配置开关
#import <YYBMacFusionSDK/YYBMacMMKVC.h>             // mmkv
#import <YYBMacFusionSDK/YYBMacLogUploadC.h>        // 日志上传/导出
#import <YYBMacFusionSDK/YYBMacHttpC.h>            // 网络库

//
//  YYBMacLog.h
//  YYBMacLog
//
//  Created by halehuang on 2024/1/4.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define YYBMacLogImpl(level, tag_name, formatStr, ...) \
do { \
    [[YYBMacLog sharedInstance] log:level \
                               tag:tag_name \
                              file:__FILE__ \
                              func:__func__ \
                              line:__LINE__ \
                           format:formatStr, ##__VA_ARGS__]; \
} while (0)

#define YYBMacLogVerbose(tag, format, ...) \
    YYBMacLogImpl(YYBMacLogLevelVerbose, tag, format, ##__VA_ARGS__)

#define YYBMacLogDebug(tag, format, ...) \
    YYBMacLogImpl(YYBMacLogLevelDebug, tag, format, ##__VA_ARGS__)

#define YYBMacLogInfo(tag, format, ...) \
    YYBMacLogImpl(YYBMacLogLevelInfo, tag, format, ##__VA_ARGS__)

#define YYBMacLogWarn(tag, format, ...) \
    YYBMacLogImpl(YYBMacLogLevelWarn, tag, format, ##__VA_ARGS__)

#define YYBMacLogError(tag, format, ...) \
    YYBMacLogImpl(YYBMacLogLevelError, tag, format, ##__VA_ARGS__)

#define YYBMacLogFatal(tag, format, ...) \
    YYBMacLogImpl(YYBMacLogLevelFatal, tag, format, ##__VA_ARGS__)


typedef NS_ENUM(NSInteger, YYBMacLogLevel) {
    YYBMacLogLevelVerbose,
    YYBMacLogLevelDebug,
    YYBMacLogLevelInfo,
    YYBMacLogLevelWarn,
    YYBMacLogLevelError,
    YYBMacLogLevelFatal,
};

// 日志旁路回调类型
typedef void(^YYBMacLogBypassBlock)(YYBMacLogLevel level,
                                    NSString *tag,
                                    const char *file,
                                    const char *func,
                                    int line,
                                    NSString *message);

@interface YYBMacLog : NSObject

/// 初始化日志系统（建议在App启动时调用）
/// @param isRelease 是否为Release环境
/// @param identifier 日志目录唯一标识（如@"main"、@"phone"、@"ipc"等，避免多进程/多app日志冲突）
/// @param destDictName 日志根目录名称
+ (void)setupIsRelease:(BOOL)isRelease
            identifier:(nullable NSString *)identifier
          destDictName:(nullable NSString *)destDictName;
+ (instancetype)sharedInstance;
- (instancetype)init NS_UNAVAILABLE;
- (instancetype)new NS_UNAVAILABLE;

- (void)log:(YYBMacLogLevel)level tag:(NSString *)tag file:(const char *)file func:(const char *)func line:(int)line msg:(NSString *)msg;
- (void)log:(YYBMacLogLevel)level
        tag:(NSString *)tag
       file:(const char *)file
       func:(const char *)func
       line:(int)line
     format:(NSString *)format, ... __attribute__((format(__NSString__, 6, 7)));

/// 注册日志旁路
+ (void)registerBypassHandler:(nullable YYBMacLogBypassBlock)handler;

@end

NS_ASSUME_NONNULL_END

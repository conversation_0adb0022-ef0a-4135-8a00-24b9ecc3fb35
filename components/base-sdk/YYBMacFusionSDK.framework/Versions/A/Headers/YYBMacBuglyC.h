//
//  YYBMacBuglyC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacBuglyC.h
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/// 初始化 Bugly
void YYBMacBuglyC_Setup(const char* appId,
                        const char* appKey,
                        const char* deviceId,
                        const char* userId,
                        const char* channel,
                        int isRelease);

/// 更新用户标识
void YYBMacBuglyC_UpdateUserIdentifier(const char* userId);

/// 异常上报
void YYBMacBuglyC_ReportException(unsigned int category,
                                  const char* name,
                                  const char* reason,
                                  const char* callStackJson, // JSON array string
                                  const char* extraInfoJson, // JSON dict string
                                  int terminateApp);

#ifdef __cplusplus
}
#endif

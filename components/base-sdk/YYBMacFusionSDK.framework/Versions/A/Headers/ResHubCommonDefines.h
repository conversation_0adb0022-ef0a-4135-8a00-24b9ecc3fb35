//
//  ResHubCommonDefines.h
//  ResHub
//
//  Created by j<PERSON><PERSON><PERSON><PERSON>(刘锦) on 2021/7/15.
//  Copyright © 2021 Tencent. All rights reserved.
//

#ifndef ResHubCommonDefines_h
#define ResHubCommonDefines_h

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>
#import <YYBMacFusionSDK/ResHubError.h>
#import <YYBMacFusionSDK/ResHubModel.h>

typedef void(^ResHubLoadProgressBlock)(CGFloat progress);
typedef void(^ResHubLoadCompletionBlock)(BOOL success, NSError * _Nullable error, ResHubModel * _Nullable resModel);
typedef void(^ResHubBatchLoadProgressBlock)(NSUInteger completedCount, NSUInteger totalCount, CGFloat progress);
typedef void(^ResHubBatchLoadCompletionBlock)(BOOL allSuccess,
                                              NSDictionary<NSString *, NSError *> * _Nullable errorMap,
                                              NSDictionary<NSString *, ResHubModel *> * _Nullable resModelMap);
typedef void(^ResConfigsFetchCompletedBlock)(NSArray<ResHubModel *> * _Nullable resConfigs,
                                             NSError * _Nullable error);

typedef NSDictionary<NSString *, NSString *> ResAndTaskIdMap;

typedef NS_ENUM(NSUInteger, ResHubLocalResStatus) {
    ResHubLocalResStatusGOOD,           // 本地有最新配置和文件，正常
    ResHubLocalResStatusNeedDownload,   // 本地没有配置和文件，待下载
    ResHubLocalResStatusNeedUpdate,     // 本地有旧的配置和文件，待更新
    ResHubLocalResStatusDisabled,       // 本地有最新配置，但资源被禁用(关闭状态/不满足最低版本号要求/App版本超过了资源要求的最大版本等)
    ResHubLocalResStatusFileInvalid,    // 本地有最新配置，但资源文件异常不可用(通常是文件校验失败/文件被异常删除等)
    ResHubLocalResStatusNotExist,       // 没找到这个资源
    ResHubLocalResStatusLocalOnly,      // 仅本地资源有效，远程配置不正常(远程资源被删除/低于本地版本)
    ResHubLocalResStatusLocalUnknow     // 未知不正常状态(未启用RDelivery等)
};

#endif /* ResHubCommonDefines_h */

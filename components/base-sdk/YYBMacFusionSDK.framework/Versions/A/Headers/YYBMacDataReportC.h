//
//  YYBMacBuglyC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacDataReportC.h
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

#pragma mark - 初始化
void YYBMacDataReportC_Setup(const char* appKey, int isRelease);

#pragma mark - 通用上报
void YYBMacDataReportC_ReportEvent(const char* eventCode, const char* dataJson);
void YYBMacDataReportC_ReportEventWithName(const char* eventCode, const char* eventName, const char* dataJson);

#pragma mark - 添加公参
void YYBMacDataReportC_AddCommonParam(const char* key, const char* value);

#pragma mark - 便捷上报方法-曝光
void YYBMacDataReportC_ReportExposureForPage(const char* pageid, int pageShow, const char* dataJson);
void YYBMacDataReportC_ReportExposureForModule(const char* pageid, const char* moduleid, const char* dataJson);
void YYBMacDataReportC_ReportExposureForElement(const char* pageid, const char* moduleid, const char* elementid, const char* dataJson);

#pragma mark - 便捷上报方法-点击
void YYBMacDataReportC_ReportClickForModule(const char* pageid, const char* moduleid, const char* dataJson);
void YYBMacDataReportC_ReportClickForElement(const char* pageid, const char* moduleid, const char* elementid, const char* dataJson);

#pragma mark - 通用action上报
void YYBMacDataReportC_ReportAction(const char* eventCode, const char* eventName, const char* pageid, const char* dataJson);
void YYBMacDataReportC_ReportActionFull(const char* eventCode, const char* eventName, const char* pageid, const char* moduleid, const char* elementid, const char* dataJson);

#ifdef __cplusplus
}
#endif

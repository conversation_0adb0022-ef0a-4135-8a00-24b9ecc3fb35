//
//  YYBUpdater.h
//  YYBMacUpdater
//
//  Created by bethahuang on 2025/8/15.
//

#import <Foundation/Foundation.h>

@class YYBUpgradeModel;
@class YYBUpdaterParam;
NS_ASSUME_NONNULL_BEGIN

@interface YYBUpdater : NSObject

/// 初始化更新器
- (void)setUpWithConfig:(YYBUpdaterParam*)params;

/// 主动检查更新（调用时会发起后台请求，拉取最新更新）
/// 当检查到有更新时，会出弹窗
- (void)checkForUpdates;


/// 后台检查更新
- (void)checkForUpdatesInBackground;


/// 读取本地配置 Model
- (nullable YYBUpgradeModel *)getConfigModelWithError:(NSError **)error;

/// 查询远程配置（不会触发 Sparkle 弹窗）
/// @param completion 完成回调，返回更新配置对象和错误信息
- (void)updateConfigWithCompletion:(void (^)(YYBUpgradeModel * _Nullable, NSError * _Nullable))completion;

/// 获取缓存统计信息
/// @return 包含缓存统计的字典
- (NSDictionary *)getEventCacheStatistics;


@end

NS_ASSUME_NONNULL_END

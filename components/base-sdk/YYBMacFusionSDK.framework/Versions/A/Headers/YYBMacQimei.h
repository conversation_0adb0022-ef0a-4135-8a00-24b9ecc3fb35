//
//  YYBMacQimei.h
//  YYBMacQimei
//
//  Created by shirley on 2024/11/25.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// Qimei 相关服务单例
@interface YYBMacQimei : NSObject

/// 获取单例
+ (instancetype)sharedInstance;

/// 初始化 Qimei 服务（必须先调用）
/// @param appKey Qimei 分配的 appKey（必填）
/// @param isRelease 是否为生产环境
- (void)setupWithAppKey:(NSString *)appKey
              isRelease:(BOOL)isRelease;

/// 预热 Qimei（异步，建议启动时调用，加快首次获取速度）
- (void)preWarm;

/// 强制同步拉取 Qimei（一般不建议主动调用，除非业务需要）
- (void)fetch;

/// 获取 Qimei（老版本），如未获取到返回空字符串
- (NSString *)getQimei;

/// 获取 Qimei36（新版本），如未获取到返回空字符串
- (NSString *)getQimei36;

/// 异步获取 Qimei 和 Qimei36，优先本地缓存，首次安装可能需网络
- (void)getQIMEIWithBlock:(void (^)(NSString *qimei, NSString *qimei36))block;

@end

NS_ASSUME_NONNULL_END

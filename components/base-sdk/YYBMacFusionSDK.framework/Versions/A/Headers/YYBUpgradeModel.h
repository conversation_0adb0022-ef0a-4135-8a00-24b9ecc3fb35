//
//  YYBUpgradeModel.h
//  YYBMacUpdater
//
//  Created by bethahuang on 2025/8/15.
//

#import <Foundation/Foundation.h>

@class SUPackageBasicInfo;

NS_ASSUME_NONNULL_BEGIN

@interface YYBUpgradeModel : NSObject

/// 包信息
@property (nonatomic, strong) SUPackageBasicInfo *apkBasicInfo;

/// 扩展字段
@property (nonatomic, copy, nullable) NSDictionary *extra;

/// 标题
@property (nonatomic, copy, nullable) NSString *title;
/// 新功能描述
@property (nonatomic, copy, nullable) NSString *theNewFeature;

/// 更新策略
@property (nonatomic, strong, nullable) NSNumber *updateStrategy;

/// 策略ID
@property (nonatomic, copy, nullable) NSString *tacticsId;
/// 策略状态
@property (nonatomic, assign) NSInteger status;

@end

NS_ASSUME_NONNULL_END

//
//  YYBMacUpdateConfig.h
//  YYBMacUpdater
//
//  Created by bethahuang on 2025/8/16.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacUpdateConfig : NSObject

// 商店相关配置
@property(nonatomic, copy, nonnull) NSString* marketAppId;
@property(nonatomic, copy, nonnull) NSString* marketAppKey;

// 引擎相关配置
@property(nonatomic, copy, nonnull) NSString* engineAppId;
@property(nonatomic, copy, nonnull) NSString* engineAppKey;

// 通用配置
@property(nonatomic, copy, nullable) NSString* qimei36;
@property(nonatomic, assign) BOOL isRelease;
@property (nonatomic, assign) NSTimeInterval configUpdateInterval;
@property(atomic, assign) NSInteger configUpdateMode;
@end

NS_ASSUME_NONNULL_END

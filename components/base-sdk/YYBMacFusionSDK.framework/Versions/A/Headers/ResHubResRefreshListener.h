//
//  ResHubResRefreshListener.h
//  ResHub
//
//  Created by j<PERSON><PERSON><PERSON><PERSON>(刘锦) on 2022/5/9.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <YYBMacFusionSDK/ResHubModel.h>

NS_ASSUME_NONNULL_BEGIN

/// 资源更新监听者协议
@protocol ResHubResRefreshListener <NSObject>

@optional
/// 当前进程下，某个资源首次加载/读取成功
/// @param resId 资源ID
/// @param resHubModel 资源对象
- (void)onResFirstLoadedWithId:(NSString *)resId model:(ResHubModel *)resHubModel;

/// 当前进程下已经被加载/读取过的某个资源，发生了版本更新或配置更新(如本地路径/FileExtra)
/// @param resId 资源ID
/// @param resHubModel 资源对象
- (void)onResRefreshed:(NSString *)resId model:(ResHubModel *)resHubModel;

/// 资源配置全量拉取成功（仅配置）
/// @param resHubModelDict 全量资源配置
- (void)onResConfigInfoPullTypeAllFinished:(NSDictionary<NSString *, ResHubModel *> *)resHubModelDict;

/// 监听资源配置删除事件
/// 在配置删除时回调
/// @param resIds 被删除resId的数组
- (void)onConfigDeleted:(NSArray<NSString *> *)resIds;

@end

NS_ASSUME_NONNULL_END

//
//  YYBMacDataReport.h
//  YYBMacDataReport
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - -- 事件定义
/// 页面曝光
static NSString *const YYB_MAC_EVENT_CODE_PAGE_EXPOSURE = @"yyb_mac_page_exposure";
static NSString *const YYB_MAC_EVENT_NAME_PAGE_EXPOSURE = @"页面曝光";

/// 页面结束曝光
static NSString *const YYB_MAC_EVENT_CODE_PAGE_STOP_EXPOSURE = @"yyb_mac_stop_exposure";
static NSString *const YYB_MAC_EVENT_NAME_PAGE_STOP_EXPOSURE = @"页面结束曝光";

/// 模块曝光
static NSString *const YYB_MAC_EVENT_CODE_MODEL_EXPOSURE = @"yyb_mac_model_exposure";
static NSString *const YYB_MAC_EVENT_NAME_MODEL_EXPOSURE = @"模块曝光";

/// 元素曝光
static NSString *const YYB_MAC_EVENT_CODE_ELEMENT_EXPOSURE = @"yyb_mac_ele_exposure";
static NSString *const YYB_MAC_EVENT_NAME_ELEMENT_EXPOSURE = @"元素曝光";

/// 模块点击
static NSString *const YYB_MAC_EVENT_CODE_MODEL_CLICK = @"yyb_mac_modclick_action";
static NSString *const YYB_MAC_EVENT_NAME_MODEL_CLICK = @"模块点击跳转";

/// 元素点击
static NSString *const YYB_MAC_EVENT_CODE_ELEMENT_CLICK = @"yyb_mac_eleclick_action";
static NSString *const YYB_MAC_EVENT_NAME_ELEMENT_CLICK = @"元素点击跳转";


#pragma mark - -- 核心参数key
static NSString *const YYB_MAC_EVENT_KEY_PAGE_ID = @"pgid";
static NSString *const YYB_MAC_EVENT_KEY_MODEL_ID = @"mod_id";
static NSString *const YYB_MAC_EVENT_KEY_ELEMENT_ID = @"element_id";

NS_ASSUME_NONNULL_END

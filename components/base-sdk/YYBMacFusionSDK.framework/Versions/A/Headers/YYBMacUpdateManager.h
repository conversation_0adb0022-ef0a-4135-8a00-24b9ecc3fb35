//
//  YYBMacUpdateManager.h
//  YYBMacUpdater
//  应用宝主端工程更新器
//  Created by bethahuang on 2025/8/15.
//

#import <Foundation/Foundation.h>

@class YYBUpdater;
@class YYBMacUpdateConfig;

NS_ASSUME_NONNULL_BEGIN


@interface YYBMacUpdateManager : NSObject

+ (instancetype)sharedInstance;

/// sdk初始化
- (void)setUpWithConfig:(YYBMacUpdateConfig*)config;


/// 获取商店更新器（需在sdk初始化后调用）
- (YYBUpdater*)getMarketUpdater;


/// 获取引擎更新器（需在sdk初始化后调用)
- (YYBUpdater*)getEngineUpdater;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END

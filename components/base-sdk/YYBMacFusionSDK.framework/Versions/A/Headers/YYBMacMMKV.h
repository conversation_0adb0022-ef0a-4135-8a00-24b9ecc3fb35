//
//  YYBMacMMKV.h
//  YYBMacMMKV
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// YYBMacMMKV 封装类，支持单App全局和多App/多进程共享MMKV
@interface YYBMacMMKV : NSObject

/// 初始化MMKV（建议在App启动时调用）
/// @param isRelease 是否为Release环境
/// @param identifier 日志目录唯一标识（如@"main"、@"phone"、@"ipc"等，避免多进程/多app生成产物冲突）
/// @param destDictName 日志根目录
+ (void)setupIsRelease:(BOOL)isRelease
            identifier:(nullable NSString *)identifier
          destDictName:(nullable NSString *)destDictName;

/// 单App全局默认MMKV
+ (YYBMacMMKV *)sharedInstance;

/// 多App/多进程可共享的MMKV（非必须的话：尽量使用sharedInstance）
/// @note 需保证所有App都能访问publicMMKVPath指定的目录
+ (YYBMacMMKV *)publicMMKV;

/// --- 常用KV接口 ---
- (BOOL)setObject:(nullable NSObject<NSCoding> *)object forKey:(NSString *)key;
- (BOOL)setBool:(BOOL)value forKey:(NSString *)key;
- (BOOL)setInt32:(int32_t)value forKey:(NSString *)key;
- (BOOL)setUInt32:(uint32_t)value forKey:(NSString *)key;
- (BOOL)setInt64:(int64_t)value forKey:(NSString *)key;
- (BOOL)setUInt64:(uint64_t)value forKey:(NSString *)key;
- (BOOL)setFloat:(float)value forKey:(NSString *)key;
- (BOOL)setDouble:(double)value forKey:(NSString *)key;
- (BOOL)setString:(NSString *)value forKey:(NSString *)key;
- (BOOL)setDate:(NSDate *)value forKey:(NSString *)key;
- (BOOL)setData:(NSData *)value forKey:(NSString *)key;

- (nullable id)getObjectOfClass:(Class)cls forKey:(NSString *)key;
- (BOOL)getBoolForKey:(NSString *)key;
- (BOOL)getBoolForKey:(NSString *)key defaultValue:(BOOL)defaultValue;
- (int32_t)getInt32ForKey:(NSString *)key;
- (int32_t)getInt32ForKey:(NSString *)key defaultValue:(int32_t)defaultValue;
- (uint32_t)getUInt32ForKey:(NSString *)key;
- (uint32_t)getUInt32ForKey:(NSString *)key defaultValue:(uint32_t)defaultValue;
- (int64_t)getInt64ForKey:(NSString *)key;
- (int64_t)getInt64ForKey:(NSString *)key defaultValue:(int64_t)defaultValue;
- (uint64_t)getUInt64ForKey:(NSString *)key;
- (uint64_t)getUInt64ForKey:(NSString *)key defaultValue:(uint64_t)defaultValue;
- (float)getFloatForKey:(NSString *)key;
- (float)getFloatForKey:(NSString *)key defaultValue:(float)defaultValue;
- (double)getDoubleForKey:(NSString *)key;
- (double)getDoubleForKey:(NSString *)key defaultValue:(double)defaultValue;
- (nullable NSString *)getStringForKey:(NSString *)key;
- (nullable NSString *)getStringForKey:(NSString *)key defaultValue:(nullable NSString *)defaultValue;
- (nullable NSDate *)getDateForKey:(NSString *)key;
- (nullable NSDate *)getDateForKey:(NSString *)key defaultValue:(nullable NSDate *)defaultValue;
- (nullable NSData *)getDataForKey:(NSString *)key;
- (nullable NSData *)getDataForKey:(NSString *)key defaultValue:(nullable NSData *)defaultValue;

@end

NS_ASSUME_NONNULL_END

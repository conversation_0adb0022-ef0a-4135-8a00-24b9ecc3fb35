//
//  YYBMacBuglyC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacQimeiC.h
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/// 初始化 Qimei 服务
void YYBMacQimeiC_Setup(const char* appKey, int isRelease);

/// 预热 Qimei
void YYBMacQimeiC_PreWarm(void);

/// 强制同步拉取 Qimei
void YYBMacQimeiC_Fetch(void);

/// 获取 Qimei（老版本），如未获取到返回空字符串
const char* YYBMacQimeiC_GetQimei(void);

/// 获取 Qimei36（新版本），如未获取到返回空字符串
const char* YYBMacQimeiC_GetQimei36(void);

/// 异步获取 Qimei 和 Qimei36，回调参数为 (const char* qimei, const char* qimei36, void* user_data)
typedef void (*YYBMacQimeiC_Callback)(const char*, const char*, void*);
void YYBMacQimeiC_GetQIMEIAsync(YYBMacQimeiC_Callback cb, void* user_data);

#ifdef __cplusplus
}
#endif

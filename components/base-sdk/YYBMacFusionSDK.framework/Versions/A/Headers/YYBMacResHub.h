//
//  YYBMacResHub.h
//  YYBMacResHub
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//


#import <Foundation/Foundation.h>
@protocol ResHubResRefreshListener;


typedef enum {
    RD_UPDATE_MODE_NONE_C = 0, //不自动更新
    RD_UPDATE_MODE_START_C = 1<<0, //sdk初始化时更新，切换账号或环境后更新
    RD_UPDATE_MODE_SCHEDUAL_C = 1<<1, //app定时更新
    RD_UPDATE_MODE_ENTER_FOREGROUND_C = 1<<2, //app进入前台时更新(macOS不建议设置！)
    RD_UPDATE_MODE_NETWORK_CHANGE_C = 1<<3, //app切换网络时更新
    RD_UPDATE_MODE_ALL_C = RD_UPDATE_MODE_START_C | RD_UPDATE_MODE_SCHEDUAL_C | RD_UPDATE_MODE_ENTER_FOREGROUND_C | RD_UPDATE_MODE_NETWORK_CHANGE_C
} RDConfigUpdateModeC;


@class ResHubModel;
@class ResHubConfigItem;

@protocol RAFTDownloadProtocol;

NS_ASSUME_NONNULL_BEGIN


typedef void(^YYBResHubProgressBlock)(CGFloat progress);
typedef void(^YYBResHubCompletedBlock)(BOOL success, NSError * _Nullable error, ResHubModel * _Nullable resModel);
typedef void(^YYBResHubBatchProgressBlock)(NSUInteger completedCount, NSUInteger totalCount, CGFloat progress);
typedef void(^YYBResHubBatchCompletedBlock)(BOOL allSuccess, NSDictionary<NSString *,NSError *> * _Nullable errorMap, NSDictionary<NSString *,ResHubModel *> * _Nullable resModelMap);

/// ResHub统一包装管理器
@interface YYBMacResHub : NSObject

/// 单例
+ (instancetype)sharedInstance;

/// 初始化ResHub，必须在使用前调用
- (void)setupWithAppId:(NSString *)appId
                appKey:(NSString *)appKey
             isRelease:(BOOL)isRelease
                 qimei:(NSString *)qimei
        resStoragePath: (NSString*)resStoragePath
            updateMode:(NSInteger)updateMode
    updateInternalTime:(NSTimeInterval)internalTime
           downloadImpl:(nullable id<RAFTDownloadProtocol>)downloadImpl;

/// 同步获取本地资源（默认校验md5）
/// @param resId 资源ID
/// @param preferLocal 是否锁定（YES: 锁定，NO: 不锁定）
/// @return 资源模型，未初始化或异常时返回nil
- (ResHubModel *)resourceWithId:(NSString *)resId
                    preferLocal:(BOOL)preferLocal;

/// 同步获取本地资源（支持md5验证设置）
/// @param resId 资源ID
/// @param preferLocal 是否锁定（YES: 锁定，NO: 不锁定）
/// @param needValidate 是否需要md5校验
- (ResHubModel *)resourceWithId:(NSString *)resId
                    preferLocal:(BOOL)preferLocal
                   needValidate:(BOOL)needValidate;

/// 异步拉取单个资源
/// @param resId 资源ID
/// @param preferLocal 是否优先使用本地缓存（YES: 优先返回本地并自动更新，NO: 一定拉取最新）
/// @param progress 进度回调
/// @param completed 完成回调
- (void)loadResourceWithId:(NSString *)resId
               preferLocal:(BOOL)preferLocal
                  progress:(YYBResHubProgressBlock)progress
                 completed:(YYBResHubCompletedBlock)completed;

/// 批量异步拉取资源
/// @param resIds 资源ID数组
/// @param preferLocal 是否优先使用本地缓存
/// @param progress 进度回调
/// @param completed 完成回调
- (void)batchLoadResourcesWithIds:(NSArray<NSString *> *)resIds
                      preferLocal:(BOOL)preferLocal
                         progress:(YYBResHubBatchProgressBlock)progress
                        completed:(YYBResHubBatchCompletedBlock)completed;

/// 按场景异步拉取资源
/// @param sceneId 场景ID
/// @param preferLocal 是否优先使用本地缓存
/// @param progress 进度回调
/// @param completed 完成回调
- (void)batchLoadResourcesWithSceneId:(NSString *)sceneId
                          preferLocal:(BOOL)preferLocal
                             progress:(YYBResHubBatchProgressBlock)progress
                            completed:(YYBResHubBatchCompletedBlock)completed;

/// 删除某个资源的指定版本
- (void)deleteResWithId:(NSString *)resId version:(NSInteger)version;

/// 删除某个资源
- (void)deleteResWithId:(NSString *)resId;

/// 删除所有资源
- (void)deleteAllRes;

/// 获取所有配置信息
- (nullable NSDictionary<NSString *, ResHubConfigItem *> *)allReshubConfigDict;

/// 获取某个资源的配置信息
- (nullable ResHubModel *)fetchedResConfigWithId:(NSString *)resId;

/// 获取本地资源状态
- (NSInteger)getResStatusWithId:(NSString *)resId;

/// 手动异步拉取全量配置。
/// 拉取全量配置和拉取部分配置的计费都一样，接口与计费相关，建议不要频繁调用
- (void)updateAllConfigs:(void(^ _Nullable)(NSError * _Nullable error))completedBlock;

/// 更新自定义属性
/// @param key 自定义属性的 Key
/// @param value 自定义属性的 Value
- (void)setCustomPropertyValue:(NSString *)value forKey:(NSString *)key;

/// 为资源更新添加一个监听者
/// @param listener 监听者
- (void)addResRefreshListener:(id<ResHubResRefreshListener>)listener;

/// 移除资源更新监听者
/// @param listener 监听者
- (void)removeResRefreshListener:(id<ResHubResRefreshListener>)listener;

@end

NS_ASSUME_NONNULL_END

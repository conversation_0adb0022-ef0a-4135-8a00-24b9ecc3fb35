//
//  YYBMacBuglyC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacResHubC.h
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

typedef void (*YYBMacResHubC_ProgressCallback)(float progress, void* user_data);
typedef void (*YYBMacResHubC_CompletedCallback)(int success, const char* errorMsg, const char* resModelJson, void* user_data);

typedef void (*YYBMacResHubC_BatchProgressCallback)(unsigned int completedCount, unsigned int totalCount, float progress, void* user_data);
typedef void (*YYBMacResHubC_BatchCompletedCallback)(int allSuccess, const char* errorMapJson, const char* resModelMapJson, void* user_data);

/// 初始化ResHub
void YYBMacResHubC_Setup(const char* appId, const char* appKey, int isRelease, const char* qimei, const char* resStoragePath);

/// 同步获取资源，返回资源模型JSON字符串，失败返回空字符串（preferLocal 是否优先使用本地缓存（YES: 返回本地缓存，NO: 返回最新资源））
const char* YYBMacResHubC_ResourceWithId(const char* resId, int preferLocal);


// 单个资源异步拉取
void YYBMacResHubC_LoadResourceAsync(const char* resId, int preferLocal,
                                     YYBMacResHubC_ProgressCallback progress_cb,
                                     YYBMacResHubC_CompletedCallback completed_cb,
                                     void* user_data);

// 批量资源异步拉取
void YYBMacResHubC_BatchLoadResourcesAsync(const char* resIdsJson, int preferLocal,
                                           YYBMacResHubC_BatchProgressCallback progress_cb,
                                           YYBMacResHubC_BatchCompletedCallback completed_cb,
                                           void* user_data);

// 按场景异步拉取
void YYBMacResHubC_BatchLoadResourcesWithSceneAsync(const char* sceneId, int preferLocal,
                                                    YYBMacResHubC_BatchProgressCallback progress_cb,
                                                    YYBMacResHubC_BatchCompletedCallback completed_cb,
                                                    void* user_data);

/// 删除某个资源的指定版本
void YYBMacResHubC_DeleteResWithIdVersion(const char* resId, int version);

/// 删除某个资源
void YYBMacResHubC_DeleteResWithId(const char* resId);

/// 删除所有资源
void YYBMacResHubC_DeleteAllRes(void);

/// 获取所有配置信息，返回JSON字符串
const char* YYBMacResHubC_AllReshubConfigDict(void);

/// 获取某个资源的配置信息，返回资源模型JSON字符串
const char* YYBMacResHubC_FetchedResConfigWithId(const char* resId);

#ifdef __cplusplus
}
#endif

//
//  ResHubModel.h
//  ResHub
//
//  Created by j<PERSON><PERSON><PERSON><PERSON>(刘锦) on 2021/7/15.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <YYBMacFusionSDK/ResHubSubResModel.h>

NS_ASSUME_NONNULL_BEGIN

/// 资源配置模型
@interface ResHubModel : NSObject

/// 资源id
@property (nonatomic, copy, readonly) NSString *resId;
/// 资源版本号
@property (nonatomic, assign, readonly) NSInteger version;
/// 任务id
@property (nonatomic, copy) NSString *taskId;
/// 资源大小 单位：kb
@property (nonatomic, assign, readonly) NSInteger size;
/// 资源md5
@property (nonatomic, copy, readonly) NSString *md5;
/// 资源crc32
@property (nonatomic, copy) NSString *crc32;
/// 内部文件MD5映射
@property (nonatomic, copy, readonly) NSDictionary *innerMd5Map;
/// 内部文件CRC32映射
@property (nonatomic, copy) NSDictionary<NSString *, NSString *> *innerCrc32Map;
/// 资源下载地址
@property (nonatomic, copy, readonly) NSString *downloadUrl;
/// 额外信息
@property (nonatomic, copy, readonly) NSString *fileExtra;
/// 是否无需解压
@property (nonatomic, assign, readonly) BOOL noNeedUnZip;
/// 本地地址
@property (nonatomic, copy, readonly) NSString *localPath;
/// 原始资源文件的本地地址
@property (nonatomic, copy, readonly) NSString *sourceLocalPath;
/// 记录资源创建时间
@property(nonatomic, assign, readonly) NSTimeInterval timestamp;
/// 是否是内置资源
@property (nonatomic, assign, readonly) BOOL isPresetResource;

/// 获取资源大包内的小资源信息
/// @param resName 小资源名
- (nullable ResHubSubResModel *)subResModelWithName:(NSString *)resName;


/// 根据基准制品版本，获取差量包的大小
/// @param baseResVersion 基准制品版本号
- (NSUInteger)diffSizeForResVersion:(NSInteger)baseResVersion;

@end

NS_ASSUME_NONNULL_END

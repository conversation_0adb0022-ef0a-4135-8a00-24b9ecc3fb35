//
//  ResHubError.h
//  ResHub
//
//  Created by j<PERSON><PERSON><PERSON><PERSON>(刘锦) on 2021/12/1.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSErrorDomain const _Nonnull ResHubErrorDomain;

// 文档: https://doc.weixin.qq.com/sheet/e3_m_TokeTDQJcYpZ?scode=AJEAIQdfAAow0w00d7ACEAWQbdAFw&tab=BB08J2
typedef NS_ENUM(NSUInteger, ResHubErrorCode) {
    ResHubErrorUnknow = 0, // 未知错误
    
    ResHubErrorConfigFetchHTTPError = 1001, // 拉取配置HTTP请求错误
    ResHubErrorConfigFetchOtherNetError = 1002, // 拉取配置请求调用错误
    ResHubErrorConfigFetchIllegalRet = 1003, // 接口返回异常（ret不为0）
    ResHubErrorConfigFetchEmptyResponse = 1004, // 接口返回数据为空
    ResHubErrorConfigFetchInvalidResponse = 1005, // 接口返回数据校验失败
    ResHubErrorConfigFetchIllegalParam = 1006, // 接口入参错误
    ResHubErrorConfigFetchEmptyResource = 1007, // 资源为空
    ResHubErrorConfigFetchFailedToAccessRedis = 1008, // 访问redis资源失败
    ResHubErrorConfigFetchInvalidResId = 1009, // 请求的资源id不合法（比如id为空或空字符串）
    ResHubErrorPreloadInterceptor = 1010, // CDN高负载状态，延迟下载
    
    ResHubErrorTotalDownloadHTTPError = 2001, // 全量文件下载HTTP错误
    ResHubErrorTotalDownloadOtherNetError = 2002, // 全量文件下载其他传输错误
    ResHubErrorTotalDownloadFlagLimit = 2003, // 全量文件下载网络状态限制（如配置中要求4g，实际网络弱于4g）
    ResHubErrorTotalDownloadOutOfSpace = 2004, // 全量文件下载空间不足
    ResHubErrorTotalDownloadFileError = 2005, // 全量文件下载文件操作错误：文件（创建/移动/重命名）失败
    ResHubErrorTotalDownloadCheckMD5Error = 2006, // 全量文件MD5校验失败
    ResHubErrorTotalDecryptionError = 2007, // 全量文件解密错误
    
    ResHubErrorUnzipError = 3001, // 解压失败
    ResHubErrorUnzipCheckMD5Error = 3002, // 解压后文件校验失败
    
    ResHubErrorUpdateConfigError = 4001, // 配置更新出错
    ResHubErrorReadLocalResError = 4002, // 读取到的本地资源无效
    ResHubErrorResConfigNotExistError = 4003,  // 配置不存在
    
    ResHubErrorDiffDownloadHTTPError = 5001, // 差量文件下载HTTP错误
    ResHubErrorDiffDownloadOtherNetError = 5002, // 差量文件下载其他传输错误
    ResHubErrorDiffDownloadFlagLimit = 5003, // 差量文件下载网络状态限制（如配置中要求4g，实际网络弱于4g）
    ResHubErrorDiffDownloadOutOfSpace = 5004, // 差量文件下载空间不足
    ResHubErrorDiffDownloadFileError = 5005, // 差量文件下载文件操作错误：文件（创建/移动/重命名）失败
    ResHubErrorDiffCheckMD5Error = 5006, // 差量文件MD5校验失败
    ResHubErrorDiffPatchError = 5007, // 差量文件Patch失败
    ResHubErrorDiffPatchCheckMD5Error = 5008, // Patch后文件MD5校验失败
    
    ResHubErrorInitError = 10001, // 初始化错误
    ResHubErrorBadSDKConfig = 10002, // 外部配置错误
    ResHubErrorCallbackException = 10003, // 外部状态回调异常
    
    ResHubErrorInvalidLocalRes = 11001, // 获取本地资源时，资源文件无效
};

NS_ASSUME_NONNULL_END

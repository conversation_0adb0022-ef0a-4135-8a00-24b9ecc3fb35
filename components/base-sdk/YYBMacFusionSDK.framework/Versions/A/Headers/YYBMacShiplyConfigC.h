//
//  YYBMacBuglyC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacShiplyConfigC.h
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/// 初始化
void YYBMacShiplyConfigC_Setup(const char* userId, const char* qimei, int isRelease, const char* appId, const char* appKey);

/// 切换全局用户id
void YYBMacShiplyConfigC_SwitchUserId(const char* userId);

/// 切换qimei
void YYBMacShiplyConfigC_SwitchQimei(const char* qimei);

/// 切换环境
void YYBMacShiplyConfigC_SwitchEnvironment(int isRelease);

/// 配置项读取接口
int YYBMacShiplyConfigC_IsSwitchOn(const char* key, int defaultValue);
int YYBMacShiplyConfigC_IntValueWithKey(const char* key, int defaultValue);
float YYBMacShiplyConfigC_FloatValueWithKey(const char* key, float defaultValue);
const char* YYBMacShiplyConfigC_StringValueWithKey(const char* key, const char* defaultValue);
const char* YYBMacShiplyConfigC_ArrayValueWithKey(const char* key, const char* defaultValueJson);
const char* YYBMacShiplyConfigC_MapValueWithKey(const char* key, const char* defaultValueJson);
int YYBMacShiplyConfigC_HasConfigInfoWithKey(const char* key);

#ifdef __cplusplus
}
#endif

//
//  YYBMacFusionSDKConfig.h
//  YYBMacFusionSDKConfig
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

// YYBMacFusionSDKConfig.h
#import <Foundation/Foundation.h>

@protocol RAFTDownloadProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface YYBMacFusionSDKConfig : NSObject

@property (nonatomic, copy) NSString *identifier;
@property (nonatomic, copy) NSString *buglyAppId;
@property (nonatomic, copy) NSString *buglyAppKey;
@property (nonatomic, copy) NSString *dataReportAppKey;
@property (nonatomic, copy) NSString *shiplyAppId;
@property (nonatomic, copy) NSString *shiplyAppKey;
@property (nonatomic, assign) BOOL isReleaseApp;
@property (nonatomic, assign) BOOL isReleaseEnvironment;
@property (nonatomic, copy, nullable) NSString *userId;
@property (nonatomic, copy, nullable) NSString *channel;
// 父文件夹名称（不指定的话：默认日志和mmkv都会放在bundleid生成的相关文件夹下，不会聚合在cache或support下的同个文件夹中）
@property (nonatomic, copy, nullable) NSString *destDictName;

// reshub动态资源制品相关配置
@property (nonatomic, copy, nullable) NSString *reshubResourceStoragePath;
@property (nonatomic, assign) int updateMode;
@property (nonatomic, assign) NSTimeInterval updateIntervalTime;
@property (nonatomic, strong, nullable) id<RAFTDownloadProtocol> downloadImpl;

// 应用更新器相关配置
@property (nonatomic, assign) NSTimeInterval upgradeConfigUpdateInterval;
@property (atomic, assign) int configUpgradeUpdateMode;

@end

NS_ASSUME_NONNULL_END

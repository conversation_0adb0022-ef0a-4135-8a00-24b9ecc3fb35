//
//  YYBMacBugly.h
//  YYBMac
//
//  Created by halehuang on 2024/8/27.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// Crash 通知名
FOUNDATION_EXPORT NSNotificationName const YYBMacAppWillCrashNotification;

/// Bugly 适配器
@interface YYBMacBugly : NSObject

/// 获取单例
+ (instancetype)sharedInstance;

/// 禁止直接初始化
- (instancetype)init NS_UNAVAILABLE;
- (instancetype)new NS_UNAVAILABLE;

/// 初始化 Bugly
/// @param appId Bugly AppId（必填）
/// @param appKey Bugly AppKey（必填）
/// @param deviceId 设备唯一标识
/// @param userId 用户唯一标识
/// @param channel 渠道名
/// @param isRelease 是否为生产环境
- (void)setupWithAppId:(NSString *)appId
                appKey:(NSString *)appKey
             deviceId:(NSString *)deviceId
              userId:(NSString *)userId
              channel:(NSString *)channel
             isRelease:(BOOL)isRelease;

/// 更新用户标识
- (void)updateUserIdentifier:(NSString *)userId;

/// 异常上报
+ (void)reportExceptionWithCategory:(NSUInteger)category
                               name:(NSString *)aName
                             reason:(NSString *)aReason
                          callStack:(NSArray *)aStackArray
                          extraInfo:(NSDictionary *)info
                       terminateApp:(BOOL)terminate;

@end

NS_ASSUME_NONNULL_END

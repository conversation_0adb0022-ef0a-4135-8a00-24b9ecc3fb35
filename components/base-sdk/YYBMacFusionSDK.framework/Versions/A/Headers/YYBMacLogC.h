//
//  YYBMacLogC.h
//  YYBMac
//
//  Created by jamieling on 2025/6/16.
//  Copyright © 2025 Tencent. All rights reserved.
//

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// 日志级别定义（与OC侧RAFTLogLevel一致）
#define YYB_RAFTLOGLEVEL_VERBOSE 0
#define YYB_RAFTLOGLEVEL_DEBUG   1
#define YYB_RAFTLOGLEVEL_INFO    2
#define YYB_RAFTLOGLEVEL_WARN    3
#define YYB_RAFTLOGLEVEL_ERROR   4
#define YYB_RAFTLOGLEVEL_FATAL   5

// 只保留支持格式化参数的宏
#define YYBMacLogV(tag, fmt, ...) YYBMacLogC_Logf(YYB_RAFTLOGLEVEL_VERBOSE, tag, __FILE__, __func__, __LINE__, fmt, ##__VA_ARGS__)
#define YYBMacLogD(tag, fmt, ...) YYBMacLogC_Logf(YYB_RAFTLOGLEVEL_DEBUG,   tag, __FILE__, __func__, __LINE__, fmt, ##__VA_ARGS__)
#define YYBMacLogI(tag, fmt, ...) YYBMacLogC_Logf(YYB_RAFTLOGLEVEL_INFO,    tag, __FILE__, __func__, __LINE__, fmt, ##__VA_ARGS__)
#define YYBMacLogW(tag, fmt, ...) YYBMacLogC_Logf(YYB_RAFTLOGLEVEL_WARN,    tag, __FILE__, __func__, __LINE__, fmt, ##__VA_ARGS__)
#define YYBMacLogE(tag, fmt, ...) YYBMacLogC_Logf(YYB_RAFTLOGLEVEL_ERROR,   tag, __FILE__, __func__, __LINE__, fmt, ##__VA_ARGS__)
#define YYBMacLogF(tag, fmt, ...) YYBMacLogC_Logf(YYB_RAFTLOGLEVEL_FATAL,   tag, __FILE__, __func__, __LINE__, fmt, ##__VA_ARGS__)

// 初始化日志系统（建议在App启动时调用）
// @param isRelease 是否为Release环境
// @param identifier 日志目录唯一标识（如@"main"、@"phone"、@"ipc"等，避免多进程/多app日志冲突）
// @param destDictName 日志根目录名称
void YYBMacLogC_Setup(int isRelease, const char* identifier, const char* destDictName);

// 支持C/C++格式化参数的日志输出
void YYBMacLogC_Logf(int level, const char* tag, const char* file, const char* func, int line, const char* fmt, ...);

// C 侧旁路回调类型（便于C/CPP场景使用）
typedef void (*YYBMacLogC_BypassFunc)(int level, const char* tag, const char* file, const char* func, int line, const char* msg);
// 注册 C 侧旁路回调
void YYBMacLogC_RegisterBypassHandler(YYBMacLogC_BypassFunc func);

#ifdef __cplusplus
}
#endif

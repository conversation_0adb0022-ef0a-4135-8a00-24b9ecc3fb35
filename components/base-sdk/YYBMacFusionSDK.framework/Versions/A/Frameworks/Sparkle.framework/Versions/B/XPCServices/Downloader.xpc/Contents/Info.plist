<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>24F74</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>Downloader</string>
	<key>CFBundleIdentifier</key>
	<string>org.sparkle-project.DownloaderService</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Downloader</string>
	<key>CFBundlePackageType</key>
	<string>XPC!</string>
	<key>CFBundleShortVersionString</key>
	<string>2.7.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>2045</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>24B75</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>15.1</string>
	<key>DTSDKBuild</key>
	<string>24B75</string>
	<key>DTSDKName</key>
	<string>macosx15.1</string>
	<key>DTXcode</key>
	<string>1610</string>
	<key>DTXcodeBuild</key>
	<string>16B40</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.13</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
	</dict>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2016 Sparkle Project. All rights reserved.</string>
	<key>XPCService</key>
	<dict>
		<key>RunLoopType</key>
		<string>NSRunLoop</string>
		<key>ServiceType</key>
		<string>Application</string>
	</dict>
</dict>
</plist>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Base.lproj/SUUpdateAlert.nib</key>
		<data>
		PcmPQ5PkNUQbIm63Lz9sGqS3Zo4=
		</data>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-101300.nib</key>
		<data>
		JrbMp4B+sF01oqE++7qM4+lesU8=
		</data>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-110000.nib</key>
		<data>
		tkAMCg7eArVqgWxlHOgjeWwIh2I=
		</data>
		<key>Resources/Base.lproj/Sparkle.strings</key>
		<data>
		pAyxkvXEa1VYttb53ia0t3P1TGY=
		</data>
		<key>Resources/Info.plist</key>
		<data>
		wXJdsGhwBC78/vnlxpGNRcoJOo4=
		</data>
		<key>Resources/ReleaseNotesColorStyle.css</key>
		<data>
		NjIvb1z7eJuLCKf9HS15O5heg50=
		</data>
		<key>Resources/SUStatus.nib</key>
		<data>
		F10odNSB22LnWavm8BKYRztljaQ=
		</data>
		<key>Resources/ar.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qTOMJ1P/HhCcJQi4qSJV9l/b7q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zZ/0sjHdlPnBGe10CetKo1kF1xQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5Ukin0TnIF0ot6Daz8OSgIoDZJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l9CaCmAXFcs+Z+8rRt7PX9onkf8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KNs5vPpTuHWbG6nFpC1whO4KeAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G9Wgf14zMhU2alRSZvqclMmlTCA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rhsuTqRoVAfmLW+GJ1vvxJPRJ0U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BWMaDw6qC8BVuA7Sgi4yCI9PLE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NEt5JVKz+OoMSynKxJC18KXMGaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			s6oFpgOPENk+LCyXJoLfVqZauVQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HLtVLvZwASiygsiyWKQbf/xJUxs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YLQxXHDo3e3Udzaj8LHDIjotWzE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			W8+shbfn38JAPBpgHTMWuU0oHfQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+UtK947GbWlGrHkPf2vScu5uqbE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BS+NpAFPK7X/XzX+n99gJLhlNKU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TNa05IunzylN4fz2uHvkj5EnyRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9UeCcfb2L6dVPSZnXLzdkFW2GDM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FSez7jCd0gDTFFGHiWL1QXY8OUU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7+SiSQLU1hqbN74YfiBS1cQFVqU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q36SuanjGk70efU6liei3uz+Uds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kNfRs9Pgn30BdjtuNzhRvKXcqu0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			s5wDya2zzL7tcyVlcPo9/F8I9/c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kXmDOKYT484fFJrriUe3zomlV64=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			My5YiAuNV+4oR1vPL1np+nMMMOI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NcjaY8nD4cpjcpK4M878R5JDK1s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KRoZrUbgs7+EwIxs18H121Szw+0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ffz6ccHMgxcBdH6by1YAYX1jpOQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p4tAU3Ek6hEWqW9e8+C1L8WMQIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uTXM8PB96z88GkvNFhyvR/7ZCOc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UqQyzt7i1BzLE/1l70C8EbAHpPw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dexoxvq5Mj4kIvh+qtUm1tChHvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ytz0i0mchT2bWQ89CgV2ZjC+HFo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b/ru54Y0QwvH9Kz9sfRPEoP5z5k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rdv7bU5k1tUG/tyNsQ1i/Rniypk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bUdgUfCRgtrzw1YzsRrGCVY9Tms=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VD/QPXFfEHRW7ksDLYiiO1xl1LQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PMarJZpNhDysjzZuBuyKv8KBTXQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5CCN2xKgiom6y3+mcWd48RVdX48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bQiB5tUCaD24QKubEYeBTXsAF1g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vGNXtUX/4qNYIzE89IO7e4GxS60=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tplLwN1kGq9MoWLnyPQhozI6c54=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Yev0Ro2PsLfgCLoY7JNED63PnqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IGMzQ4TCQgpEQaOcESzlhe8ny9I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4dXESUaHE2dFeufUHNwRIqZX9Wc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aqXsBwqycwXfSX0SDJcWfHOnzWA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cAx3RQDMM17OoU39/UaI2sMDZLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PSzuLg6o8oUsLxsm8t81XLFGCZg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qw6/m/LeQ/b+ApIWLXJp2ByBp7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ddXQhUAjY+oi62JXHPY2OYvbpa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PG5i0JLjiHsmQaKDJEna5hDIsnk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fck+vL9Sgcx19X7HthrjizRGhu8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eiq9zVX/y56Q0ymxVNFnYahFbxs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bV8ftjOesaSv8Q38w8irgIv/al4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5ZpTsHPgV4inhhYiISGjC03BMG4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LgvDZbmPK7Ox9+gNe7zXN3egxlM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5ICRko8SDlROYWigTD41Ukfe13U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OSmchqvF31OYAu223yZI6p3J4g4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xNizW6Xg8cPtYBhWBmexjNf5j8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zeWvaM0pXtKqlA2EbY5ZKjcJ0ws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HX2RXVrN+fpwO4I60/UDyNuGj5Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			abNyxpda7OkXoR5Ok35XgMr9eBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oWDxhBptd82i6ZmAqb12V/ie16Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YFXY6v+45ptf8TuBq2MsKKdhfQ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2iCpI0fy7Tm1zxR19dV1iCYW3bo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bqAxqCt98Zz3ABFiwwwYXQ5RlKo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pWRHcAJRvjUt7BOLr/gd+IupcGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Om//DOu8+gBjHYrCHVmxKxBDvPs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			k2BwHr4kNubgvwIIu5MHJl0uWck=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			a/RNqEdkehva+SwGWz11MktFGWA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JDMBsS6fp2v5X+C0d1EJAREHIkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			d8GetrPioKdukXC3+9RWZjJr8GY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Lmn0e5MDPfan55gnani1dQbR10Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			z+XqvyZR2X6cb0PioKpfYDCF3YY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NX1iJXeJfZM/lLLj7Tt/9vRGlhs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8o3l6mjHafwy5sLMMO2rZIe7xiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			s3Cllq+eYT+urMLfXvnwsMkboWQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vR3wiQ+pBcmOi9SbgnU3KN+pfeQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ny5EoZGpd5UK5c3eMIUKLR8x4/I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vbP9bj0jn5GKz9uEu3amXnozkWo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jlj1XBE0qnu8pOj1JWwNTkuApsw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YWicg3ZZLCEoiJ9WOUUZ6WoTZJY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JKbTlT+iKE5KOwvLD9N/Go2K+q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UH7udC5C4WHwnnx4Eg6Io23rBzk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Cd6guArNrSoJO3e2ntd1Eys3bok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			L4ZWMKTKnMsbMsL8V2V6OLySKLE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cHFejOD/e8AZNJ6m+9pJO20GLO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B+Y273q3UDMsG2/yaWuxMUGr2dI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NstD9tjAfqULKbFJEcULPgVeJKk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vH9BCtbtOSwK4l2PV03JXOAdkUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6/WdcAg1mJs1/HT5krHhOxqyMWk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I4B4qXPwnMhj/A+yU0vvngP7oak=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q2Yszs2/8NW6kuRp8rQasC+ddL0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DjCjxSor6wnKAz8bFLcPCnW1Kw0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xeCj4c1ifxxhDFeLtNsSc4NgBFw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rbBF5STK0BIDGJVCzYm2QBsEBWw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+9prrb68fl57+m9WFQ+8Ay6XjRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5Pazf5ErH02Ny5mFB+R+dwCWPVM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MZXR0fFAMrdBQPKAjYvzVQdzNCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			167IbTfOhYu699bxXBhaGehjrco=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SNJz+3Rb1AJ2cKstnbGWL6Q8OW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1cp4nThwvuJlVhetrBKY1Dh0IJA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Autoupdate</key>
		<dict>
			<key>cdhash</key>
			<data>
			qQxnZU1QdwbUQBXZyrH2V1ChI/A=
			</data>
			<key>requirement</key>
			<string>cdhash H"a90c67654d507706d44015d9cab1f65750a123f0" or cdhash H"2db2b41972e392d082f8e4389149c75b33f67bcc"</string>
		</dict>
		<key>Resources/Base.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			bgRZIOcTfZL9pl4BT/HvgIT5ZPhs3PrnmLIjNgITHQc=
			</data>
		</dict>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			fy81zcr5Zv60ws5tSpTwuIz6KA+G/FvUGqQi1VeNU+w=
			</data>
		</dict>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-110000.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			YphQq0fD3fZ5t8uYO1r6pZg0EERR6ufHAkOS/3bi4m4=
			</data>
		</dict>
		<key>Resources/Base.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pJuZ4AXoYAPXaXMNPVsYwbjuBIkF7E74sl51ZQ6aS6Q=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			I/DvncfquNtI8SR/sgA4MwNNnnEehgIG1Q8GbOtRpaA=
			</data>
		</dict>
		<key>Resources/ReleaseNotesColorStyle.css</key>
		<dict>
			<key>hash2</key>
			<data>
			dr1pmXWP2OUdF+a0gttDT5tHaMArA3r2vS46AAzoy8E=
			</data>
		</dict>
		<key>Resources/SUStatus.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			msjlCHgfQ2V9V4Dt6Z5968eaMn6zeL/en7Mg22/5Pso=
			</data>
		</dict>
		<key>Resources/ar.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			33nOBJb6OPaZt3PKT2iUJ3RfF/c59DAGmt9TCQVn74A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ku6BdTbNrkSmKEdwyNA1hmoKbQ3uRv8JR4LK4cjqgpA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yx9tkKjj3aOHvgdYCWXM89uhlyVeNb4oqcAenJxibwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			18qLsTRnJfi0wDf6A85XbiMXGORSmuo9Ul3IK4m5gq0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			q5q/aoEk+YqUvSwYTQoIFsLkOCGi0GrSU9M7jJjbFls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qSoDl0PIYv+OrSxtJfUYk9xeQihmzfaxAf+egKyw4y4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			obkk1c1EawdfEyPHqo5ddIzsUcWfClFUbg895zj3/Ag=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jJ0IN1A7sFqS9MHzYIVu5VNP397YTaDN17HzroZOEtg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aKNcPadrNnf7wuYmBAxoRzES9XhxXRHMrW/+9MtZBQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xremLoAOqEfufOFyjOrH8S8ZkWwRklRhGCGB/igGD38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LirzjdLk2XjCuG2WQSlj/+Ri7kuJWGCKHYjlAbEs/u8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			A6JiLH5c4UX2iobAPXPHv7TLiBInrdHvtvqnnsTBxLI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GPjZbm0EAKfj0CK7Pb1UITo5WoDzNpf4m2XELfj3eio=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+s2Td5ADN+Rd4/RXNcTw6CpoP1A8s39Gg9Pe5+9bP+g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			utAXO7a8Od4ICYV3R0WQBa8ncUQ30SfruZACTuvyDxk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V0h7tXPJI0b1Z0FEMxe7RJIn2oWGg9QUhF/cRSz7aWE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lipDQYoREFmgYjH/L6Hxgq7ZUzqDs4eCf3uF9ud8V6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EBVS8ZfEIJxGSghO17emwoHQo0LVWWzBJMFs8RwvKWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MN0HeTdXIxqALqUMUoLnVkRcDcvnDXqjsifU07tV3a8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8KSmmlZHYEiMGUwXQRV+ZDxs07XmaeH4XIYI+di1ono=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GEtUsrVDWqXyHAV8lWPrEUWQm30jetvOjJZdlI7egwQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			a/q2vGBx2sEPgWuCojPnHLSaxlyIyFQScfSU9kJCW6I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RySqsJrzYm/5ovwmAzh14SCylM/HQhMZnk/Ig1o7DkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			O+ja0EMKj5RxMmW3TRALc9XTpMJ7Y7dwXm706E33rUA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MfjVC0QQ0Dxvz6Rt03EhMaahM5Gh5rhqMSJFEqzSRLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			O6+s8+GKGX06x08WB1v526jOSl30MEoNnzjhYKe4IA0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Avyaxx14FRXq/CTIDvvF7uww42SRhYgNSc960h7MCfc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qlNtkoH6vAA93/yxp8Stav74m46gvKb+3R26QDMSsXs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7mKHxz1DsYdoE7f7nXPNJn+bKFSFhL/rtJ8v7kj4cbo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WsgO6lp/qlATRSKTuRsgSEyWC3VdH7EHaf6dwQH2R7E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			l8hApwbD8wJajAI7FMKOztb0glfifBsELIttJ0rut5I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			b9A+ycAkv1kaeQx2DWRY6g/YF5OKutwp3OgZTqsnMJ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			L7shRNgdZIfbt5y5pioLEIo+A9I7VtgIUFpzoCFkB1I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5x5zRCWzWYlbd7MkUcbPs5ZWrWQRDZnj3s9K2LmsnBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CDNSmpfDfg9ej5euTcVZn9qdwrITMM9eGHvazN/qaoI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8LbsWTkMSczHFa4Rh9XZDRo0uCOyrV9VXUYEiEvnG7I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			twHi8JXysxao7MlTGr178ZpB8yz1mXkij2V5n8NJWSQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0UBqgjXjtRG51lEacNaLTmNvj5aFUeJ7oo1J4WYkrCw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			04Q9PpqtuYz6kfVhf6eI9XBxJn0LQB9Ck/ceBq1ztGU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			heK63dQF7YJvehrOEQk1jesq6v3bQBJy2jL+w5jjMlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5/4A+HgH/PhUAQ3NVnURPeiIJsQYJyZ28sAObmxJlVU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1zxkJlohqYtSJb0pj93fJXlPkedYm2IllbilGRDFo90=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XjyG4RGvcVUZia4jJHGYQEfgocs1iEx7iljn+vue5xY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VtrQu7072Gm3W1Ftss0bT1HaDNXans+x1OSpryBAnIw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+grczSeMRPV+Oh8FAj+IPqtvuOC4WhJdknfh0eNdHWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pG/1lv3zRpIQkIlhEdvLHcTuPW/GfIPM8FALHstdt5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9/986n4BuwYWJvE7kvt3dEFYcqRUBomWR9K/VRhqVCY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/sIr/9ihcLhTxpc9CG0ZmCyhtI5lxHnRU2AY2Z7KjnA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Q6RJbOzFDzk17QpGPfUciOrtuitReWCBlt8ucofX2AM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+FiqGTbCIl9mzgETQukEUOwPP+yCyIhH1rVtteYpRFk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RQbKlvLGnVjjVMP5eHHNUCv5kLJl4EA6zNGdDKatbH0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			F85iUA2aHbvo23Z0jJ/T/pwJ2HOQdYD5eRyAow9cSgY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HaK7lCMSQ/6QAhjF2NPsMSHsIjD4gGVRyno7LU4CfkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tp3fY8ogv+xcQOFkz5BkDNTZHIaRrhGgT9uKfCjDB70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yrwc9ESTayZoqv2JWm0nD1IHGLeAiBncPc2OeaVz8J8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0FzLPeENJRrP48p4R9sZQ8Idb7RSPgGfxKfo3G7hHPQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dpngnl3ALnXwnnfHDsUnHAQGvaHux00hwlsOA/Q+Ixs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V7VdecLbSo5sr8pMVUBnsJ04no1tM8XXsU/SHRWphs8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			l74nJ72QXsvfX67e7wiYv5R75YhNPEYYmhQRUPZgS0k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oB2rGM/SPnJLdvhUz2CJfm8TS6XhrhmHD2gFyrVSq8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ubFfFWaG2RKXgeGR4DRtvbY0fH+SNJZzBebSPCo6K5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			T2dYzgWycdgWtM2GradgZ/AVnqEQO2SoegYAL0W2blQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hDN04zbJliR6KRqEv4lEuAVNTjbkmyYUpKjCbWKaKdU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6NvKj6GmzGQLAsGBC6IUvRBoLSRfEJuWi7ZitoTyoTk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			v/+cq4Y3AZRgzPAw+Uayb16a/B4DsW9CREPLZ0ZeAzU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gto4ribWYRWZl0Eez6/7XZg3EesExPlGb5Nz1YVTuzE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			st4wdNoLjj5sVIFHqDAh3cjRFhxhpzkcFP7AJSXjYkw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HLPjh7OJ3y4u8VF5tWq4uwlZM+WN+R36xnBspxYufA0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PZZnueQNOLmQuEtkELhzxhnG+MDu7RyeOaySHSoHmYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IkIaDJ/HgpnBNNkP+MF2JGSd+rNgAI+o9c2aNor+ewo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yxv56WlVa4Rme1qqV2WWPKFO1L5YcTZlpmPGMIYNxy0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wdiMmOcek4MJvdl1u2OoccWD56zCu2lKDGUd40bnMb8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/uARbwIQFupNOZvlyWgeE722GAsKcu4/QooLAEGHCBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PuJX1B8K6ruacb5r12BMc806qHX0YYUsdKzIQFfDxHE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FDJ/dTwG5X34BF9lDDkFVGJUwpLeKi1MUbF072nYass=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xa+UPXIC+og1IpGE6bA/+51E2uR9ZG+HGWKFA83tTNU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qSS0yMxRzil00PjSBaev/rOo2hIw2L3weCw7KuC3WPA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			OAq7ojI6K/xR4nFEK1OBTiJeNaHqgb8xCgzZ5Y3P7Uo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ik4klDAQYgMT5hrecUkfi+K/tuGyvOYk96xp+z98l74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4g17Sb7/h2sQcSt3m+NyotwcVYQs7Bni47UBAs/+2c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LYXEBB7MF82Ig5MgIM9pTtJJAYJL51nzYzbVW1kdSGI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XDORKepHKWfDihFVMFnshPW/qjSLPLoU/zHqJQbRoBk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nidShaf5BKX1wQntkXeQhcGQoWUzNgVQhfHSM44GXVM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dvWO9t2NYZ+cQoe/9B3Tib+EPOdPp4wgatHaVVhu8gQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			mxCL8k437ikdUpl3px2Ii/2fZqL85x1Fn/xe7h1YI6E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NLP4i+CKF5ehWqBBJuaKp5e5s4ghXesi+SNu1fKjhKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LbuuEfntGOLEDD5x0g9XHJ0C/aSUk14ltYe7Lr8tBXQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cPWCofME3hhaqw5W8btHUEa2OMuG1gW/GQTnXET0/m0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nDvfKMpj859i2WgGrsJ7SU8aApnABAX4fI6Oi8XgFRE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lR6DOvFkMHpmbtXQJNE1aXtRXgBbd0siVMoq01D4dhM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+MphWMKEy2hsIqrjroJQcq+x1mytcNeZm+z3Lv+ll6A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zIBA0lJgtMZBvE35PSaFseeRbAuXK8+hs8f1K15msRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NXEAoNAKcjI5GBtGxYcUXmtz+rP06ocJSSVlaR/lnMA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8co96MJCMuKNvaPFe13uh2d028P/Cgpa8iOiNml9rfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nporciTF4tpZdmXibARMfweij3VC0/ffScoI32t3h1E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zegYIhIFwtdJa87mjTlkalyYSz31LrnhiwNWDJDPqBU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			54ogzTvsgJOl4aSWIQRzRzky1TddmGlpamTLhHMJWb0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			K0EiYSQbTZEZG84QzfFr0YmJT6Lx8z0y63vYBmojCzc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jdmB9inrJUf1OmYmVnORSMfdz5z1SWmBtdv39I776K4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4tCLZKKcNuOJ1up1IgFXUeEp7s5U5BOBGHC1EZMyrhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			f9/Ie/kQ1VCKt2xveY7KlxJq9G1Qy/LttGgtIfSoxsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Updater.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			hgqdqSJKiC72OINHzBKQdH/kUMo=
			</data>
			<key>requirement</key>
			<string>cdhash H"860a9da9224a882ef6388347cc1290747fe450ca" or cdhash H"0aee5266308de5eb155033b2a74bf37d6591569a"</string>
		</dict>
		<key>XPCServices/Downloader.xpc</key>
		<dict>
			<key>cdhash</key>
			<data>
			rzbdk6/kj7md9qiwOdSKKy5RTv4=
			</data>
			<key>requirement</key>
			<string>cdhash H"af36dd93afe48fb99df6a8b039d48a2b2e514efe" or cdhash H"e2247c8ac568d5dc90b6af02828fb6bfed800882"</string>
		</dict>
		<key>XPCServices/Installer.xpc</key>
		<dict>
			<key>cdhash</key>
			<data>
			cEeLZtkcKkxQwVXALAU01/pu8kk=
			</data>
			<key>requirement</key>
			<string>cdhash H"70478b66d91c2a4c50c155c02c0534d7fa6ef249" or cdhash H"e15aca1cf22ca1821e482d7c090fb266860fbf17"</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>

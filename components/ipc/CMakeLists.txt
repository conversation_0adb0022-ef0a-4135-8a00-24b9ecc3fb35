cmake_minimum_required(VERSION 3.10)
project(YYBIPC)

# 添加所有源文件
file(GLOB SOURCES
    "*.mm"
    "YYBSocketClient/*.cpp"
    "YYBSocketClient/yyjson/yyjson.c"
    "YYBSocketClient/nlohmann/*.hpp"
)


# 创建动态库
add_library(YYBIPC SHARED ${SOURCES})

# 设置安装路径
install(TARGETS YYBIPC
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

set_target_properties(YYBIPC PROPERTIES
    BUILD_WITH_INSTALL_RPATH TRUE
    MACOSX_RPATH ON
)

target_link_libraries(YYBIPC PRIVATE
    "-framework Foundation"
    "-F${CMAKE_BINARY_DIR}/Frameworks"
    YYBMacFusionSDK
)

# 包含头文件目录（PUBLIC使依赖项目也能访问）
target_include_directories(YYBIPC PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/YYBSocketClient
    ${CMAKE_CURRENT_SOURCE_DIR}/YYBSocketClient/nlohmann
    ${CMAKE_CURRENT_SOURCE_DIR}/YYBSocketClient/yyjson
)

target_link_directories(YYBIPC PRIVATE
    ${CMAKE_BINARY_DIR}/Frameworks
)

target_compile_options(YYBIPC PRIVATE -fexceptions)
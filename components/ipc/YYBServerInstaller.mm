//
//  YYBServerInstaller.m
//  YYBIPC
//
//  Created by <PERSON> on 2025/7/2.
//


#import <Foundation/Foundation.h>
#if __has_include(<YYBMacFusionSDK/YYBMacLog.h>)
#import <YYBMacFusionSDK/YYBMacLog.h>
#else
#import <YYBMacLog.h>
#endif
#import "YYBServerInstaller.h"

static NSString *const kTag = @"YYBServerInstaller";

std::string serverPlistPath() {
    NSString *homeDirectory = NSHomeDirectory();
    if (homeDirectory.length == 0) {
        YYBMacLogError(kTag, @"Failed to get home directory.");
        return "";
    }

    NSString *agentDir = [homeDirectory stringByAppendingPathComponent:@"Library/LaunchAgents"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    BOOL success = [fileManager createDirectoryAtPath:agentDir
                          withIntermediateDirectories:YES
                                           attributes:nil
                                                error:&error];

    if (!success) {
        YYBMacLogError(kTag, @"Failed to create LaunchAgents directory: %@", error.localizedDescription);
        return "";
    }

    NSString *plistPath = [agentDir stringByAppendingPathComponent:@"com.tencent.yybmac.yybService.plist"];
    return plistPath.UTF8String;
}

bool startServer() {
    std::string launchPath = serverPlistPath();
    if (launchPath.empty()) {
        return false;
    }
    
//    错误码 = 256 * exit_status
//    0: 成功
//    1: 一般错误
//    3: 命令无效
//    5: 服务已加载
//    6: 服务未加载
//    37: 权限被拒绝
//    113: 服务未找到
    std::string bootstrapCommand = "launchctl bootstrap gui/$UID " + launchPath;
    int result = system(bootstrapCommand.c_str());
    if (result != 0) {
        YYBMacLogWarn(kTag, @"Launchd bootstrap service (code: %d)", result);
        return false;
    }
    
    YYBMacLogInfo(kTag, @"Launchd bootstrap service success");
    return true;
}

bool installServer(std::string plistPath, std::string serverPath) {
    std::string launchPath = serverPlistPath();
    if (launchPath.empty()) {
        return false;
    }
    
    NSMutableDictionary *newPlist = [[NSMutableDictionary alloc] initWithContentsOfFile:[NSString stringWithUTF8String:plistPath.c_str()]];
    NSString *nsLaunchPath = [NSString stringWithUTF8String:launchPath.c_str()];
    NSDictionary *oldPlist = [[NSDictionary alloc] initWithContentsOfFile:nsLaunchPath];
    NSString *nsServerPath = [NSString stringWithUTF8String:serverPath.c_str()];

    // 版本一致
    if ([oldPlist[@"ProgramArguments"][0] isEqualToString:nsServerPath] &&
        [newPlist[@"md5"] isEqualToString:oldPlist[@"md5"]]) {
        return startServer();
    }

    // 版本不一致
    // 关闭原有的Service
    if ([[NSFileManager defaultManager] fileExistsAtPath:nsServerPath]) {
        std::string bootoutCommand = "launchctl bootout gui/$UID " + launchPath;
        int result = system(bootoutCommand.c_str());
        if (result != 0) {
            YYBMacLogError(kTag, @"Launchd bootout service failed (code: %d), falling back to direct execution", result);
        }
    }
    // 写入新版本的plist
    newPlist[@"ProgramArguments"][0] = nsServerPath;
    
    if (![newPlist writeToFile:nsLaunchPath atomically:YES]) {
        YYBMacLogError(kTag, @"Failed to write plist file");
        return false;
    }
    
    NSError *error = nil;
    NSDictionary *attributes = @{NSFilePosixPermissions: @0644};
    if (![[NSFileManager defaultManager] setAttributes:attributes ofItemAtPath:nsLaunchPath error:&error]) {
        YYBMacLogError(kTag, @"Failed to set plist permissions:%@", error.localizedDescription);
        return false;
    }
    YYBMacLogInfo(kTag, @"Install Server Success");
    
    // 启动服务
    return startServer();
}

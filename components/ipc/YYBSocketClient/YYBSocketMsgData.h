//
//  YYBSocketMsgData.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/6/24.
//

#pragma once

//#include "json.hpp"
#include <map>
#include <string>
#include <optional>

namespace SocketMessageType {
constexpr const char* event = "event";
constexpr const char* request = "request";
constexpr const char* response = "response";
}

namespace SocketError {
// 错误码定义
enum ErrorCode {
    ERROR_NONE = 0,        // 无错误
    ERROR_TIMEOUT = 1001,  // 请求超时
    ERROR_SERVICE = 1002,  // SERVICE错误（中间层）
    ERROR_SERVER = 1003,  // 服务器异常
    ERROR_NOT_FOUND = 1004, // 未找到响应
    ERROR_UNKNOWN = 9999        // 未知错误
};
}

class YYBSocketMsgData {
public:
    std::string type = "";  // 消息类型，见：SocketMessageType
    std::string action = ""; // 具体操作
    std::string from = ""; // 发送消息的进程name: appstore, engine, boastcast, packageName
    std::string to = "";  // 发送消息的目标进程name: appstore, engine, boastcast, packageName
    int pid = -1; // 发送消息的进程id
    std::optional<std::map<std::string, std::string>> info;  // 消息内容
    std::string request_id = ""; // 请求ID，用于匹配请求和响应
    int64_t error_code = SocketError::ERROR_NONE; // 错误码
    int64_t timeout_ms = 0; // 超时时间（毫秒）

    YYBSocketMsgData() = default;

    static std::optional<YYBSocketMsgData> from_json(
            const std::string& json_str);
    std::string to_json() const;
};

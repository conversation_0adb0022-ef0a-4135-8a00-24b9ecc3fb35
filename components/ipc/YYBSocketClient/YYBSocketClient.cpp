//
//  YYBSocketClient.cpp
//
//  Created by <PERSON> on 2025/6/23.
//

#include <dispatch/dispatch.h>
#include <mach-o/dyld.h>
#include <sys/event.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/un.h>
#include <sys/unistd.h>
#include <unistd.h>
#include <cerrno>
#include <cstring>
#include <future>
#include <iostream>
#include <queue>
#if __has_include(<YYBMacFusionSDK/YYBMacLogC.h>)
#import <YYBMacFusionSDK/YYBMacLogC.h>
#else
#import <YYBMacLogC.h>
#endif
#include "YYBSocketClient.h"

#define BUFFER_SIZE 4096

static const char* const kSocketClientLogTag = "SocketClient";

YYBSocketClient::YYBSocketClient(const std::string& path)
    : sockfd(-1),
    socketPath(path),
    running(false),
    connected(false),
    reconnecting(false),
    isRelease(false),
    sendThreadRunning(false){
}

YYBSocketClient::~YYBSocketClient() {
    disconnect();
}

bool YYBSocketClient::connectToServer() {
    if (connected) {
        return true;
    }

    sockfd = socket(AF_UNIX, SOCK_STREAM, 0);

    if (sockfd < 0) {
        YYBMacLogE(kSocketClientLogTag, "创建socket失败: %s", strerror(errno));
        return false;
    }

    sockaddr_un addr;
    std::memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    std::strncpy(addr.sun_path, socketPath.c_str(), sizeof(addr.sun_path) - 1);

    if (connect(sockfd, (sockaddr *)&addr, sizeof(addr)) < 0) {
        YYBMacLogE(kSocketClientLogTag, "连接服务器失败: %s", strerror(errno));
        close(sockfd);
        sockfd = -1;
        return false;
    }

    connected = true;
    YYBMacLogI(kSocketClientLogTag, "成功连接到服务器: %s", socketPath.c_str());

    // 启动接收线程
    running = true;
    receiveThread = std::thread(&YYBSocketClient::receiveThreadFunc, this);

    // 启动发送线程
    sendThreadRunning = true;
    sendThread = std::thread(&YYBSocketClient::sendThreadFunc, this);

    return true;
}

bool YYBSocketClient::sendMessage(const std::string& msg) {
    // 将消息加入队列
    {
        std::lock_guard<std::mutex> lock(sendQueueMutex);
        sendQueue.push(msg);
        YYBMacLogI(kSocketClientLogTag, "消息已加入发送队列，当前队列长度: %zu", sendQueue.size());
    }
    if (!connected) {
        YYBMacLogW(kSocketClientLogTag, "发送消息失败: 未连接到服务器");
        return false;
    }

    sendQueueCond.notify_one();
    return true;
}

void YYBSocketClient::setMessageCallback(MessageCallback callback) {
    std::lock_guard<std::mutex> lock(callbackMutex);
    messageCallback = callback;
}

bool YYBSocketClient::isConnected() const {
    return connected;
}

void YYBSocketClient::receiveThreadFunc() {
    while (running) {
      uint32_t len;
      ssize_t n = read(sockfd, &len, sizeof(len));

      if (n != sizeof(len)) {
        if (n == 0) {
          YYBMacLogW(kSocketClientLogTag, "服务器关闭了连接，将尝试重新连接...");
        } else if (n < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
          YYBMacLogE(kSocketClientLogTag, "读取消息长度错误 (%s)，将尝试重新连接...", strerror(errno));
        }
        break;
      }

      len = ntohl(len);
      if (len == 0) continue;   // 空消息跳过
      // 限制消息大小：Mac上跨进程通信一般建议限制在8MB以内
      // 对于Unix域套接字，理论上没有严格限制，但考虑到内存和性能因素
      // 8MB是一个较为合理的上限，可以满足大多数场景需求
      if (len > 8 * 1024 * 1024) {
        YYBMacLogE(kSocketClientLogTag, "消息过大: %u 字节，超过限制(8MB)", len);
        break;
      }

      auto buffer = std::make_unique<char[]>(len + 1);
      n = read(sockfd, buffer.get(), len);
      if (n != static_cast<ssize_t>(len)) {
        YYBMacLogE(kSocketClientLogTag, "消息读取不完整，期望 %u 字节，实际读取 %zd 字节", len, n);
        break;
      }

      buffer[len] = '\0';
      std::string message(buffer.get(), len);
      std::lock_guard<std::mutex> lock(callbackMutex);
      if (messageCallback) {
        try {
          messageCallback(message);
        } catch (const std::exception& e) {
          YYBMacLogE(kSocketClientLogTag, "消息回调函数异常: %s", e.what());
        }
      }
    }
    // 退出接收线程，准备重连
    running = false;
    connected = false;

    if (!reconnecting && !isRelease) {
        reconnecting = true;

        if (reconnectThread.joinable()) {
            reconnectThread.join();
        }

        reconnectThread = std::thread(&YYBSocketClient::reconnectThreadFunc, this);
    }
}

// 添加发送线程函数
void YYBSocketClient::sendThreadFunc() {
    while (true) {
        std::unique_lock<std::mutex> lock(sendQueueMutex);
        sendQueueCond.wait(lock, [this] {
            return !sendQueue.empty() || !sendThreadRunning;
        });

        if (!sendThreadRunning && sendQueue.empty()) {
            break;
        }

        if (!sendQueue.empty()) {
            std::string msg = std::move(sendQueue.front());
            sendQueue.pop();
            size_t remainingSize = sendQueue.size();
            lock.unlock();
            YYBMacLogI(kSocketClientLogTag, "从发送队列取出一条消息，剩余消息数: %zu", remainingSize);

            if (sockfd >= 0) {
              try {
                uint32_t len = htonl(msg.size());
                ssize_t bytesWritten = write(sockfd, &len, sizeof(len));
                if (bytesWritten != sizeof(len)) {
                  YYBMacLogE(kSocketClientLogTag, "写入消息长度失败: %s", strerror(errno));
                  throw std::runtime_error("Failed to write message length");
                }

                bytesWritten = write(sockfd, msg.c_str(), msg.size());
                if (bytesWritten != (ssize_t)msg.size()) {
                  YYBMacLogE(kSocketClientLogTag, "写入消息内容失败: %s", strerror(errno));
                  throw std::runtime_error("Failed to write message content");
                }
                YYBMacLogI(kSocketClientLogTag, "成功发送消息，长度: %zu 字节", msg.size());
              } catch (const std::exception& e) {
                YYBMacLogE(kSocketClientLogTag, "发送错误: %s - %s", e.what(), strerror(errno));
              }
            }
        }
    }
}

// 在disconnect()中确保所有线程停止
void YYBSocketClient::disconnect() {
    if (isRelease) return;
    isRelease = true;
    
    YYBMacLogI(kSocketClientLogTag, "断开与服务器的连接");
    // 停止接收线程
    running = false;
    if (sockfd >= 0) {
      shutdown(sockfd, SHUT_RDWR);
      close(sockfd);
      sockfd = -1;
    }

    // 停止发送线程
    sendThreadRunning = false;
    sendQueueCond.notify_one();
    
    // 等待线程结束
    if (receiveThread.joinable()) {
      receiveThread.join();
    }
    if (reconnectThread.joinable()) {
      reconnectThread.join();
    }
    if (sendThread.joinable()) {
      sendThread.join();
    }
    connected = false;
    YYBMacLogI(kSocketClientLogTag, "已完全断开连接");
}

void YYBSocketClient::reconnectThreadFunc() {
    YYBMacLogI(kSocketClientLogTag, "启动重连线程");

    if (isRelease) {
        YYBMacLogI(kSocketClientLogTag, "客户端已释放，取消重连");
        return;
    }
    if (receiveThread.joinable()) {
        receiveThread.join();
    }

    // 停止发送线程
    sendThreadRunning = false;
    sendQueueCond.notify_one();

    if (sendThread.joinable()) {
        sendThread.join();
    }

    // 清空发送队列
    clear();

    while (!connected && !isRelease) {

        std::this_thread::sleep_for(std::chrono::seconds(1));
        YYBMacLogI(kSocketClientLogTag, "尝试重新连接服务器，第 %d 次尝试", reconnectCount + 1);

        if (connectToServer()) {
            reconnectCount = 0;
            YYBMacLogI(kSocketClientLogTag, "重新连接服务器成功");

            // 重连成功后启动发送线程（如果未运行）
            if (!sendThread.joinable()) {
                sendThreadRunning = true;
                sendThread = std::thread(&YYBSocketClient::sendThreadFunc, this);
            }

            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                messageCallback("registerToServer");
            });
            break;
        } else {
            reconnectCount++;
        }

        if (reconnectCount >= reconnectMaxCount) {
            // 最大重连次数后，启动服务
            YYBMacLogW(kSocketClientLogTag, "达到最大重连次数 %d，尝试重启服务", reconnectMaxCount);
            messageCallback("restartService");
        }
    }
    reconnecting = false;
    YYBMacLogI(kSocketClientLogTag, "重连线程结束");
}

void YYBSocketClient::clear() {
    std::lock_guard<std::mutex> lock(sendQueueMutex);
    size_t queueSize = sendQueue.size();
    std::queue<std::string> empty;
    sendQueue.swap(empty);
    YYBMacLogI(kSocketClientLogTag, "清空发送队列，丢弃 %zu 条消息", queueSize);
}

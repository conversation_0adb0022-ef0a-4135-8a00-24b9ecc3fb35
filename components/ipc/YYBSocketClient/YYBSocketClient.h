//
//  YYBSocketClient.h
//
//  Created by <PERSON> on 2025/6/23.
//

#pragma once
#include <functional>
#include <thread>
#include <mutex>
#include <queue>

class YYBSocketClient {
public:
    using MessageCallback = std::function<void(const std::string&)>;

    explicit YYBSocketClient(const std::string& path);
    ~YYBSocketClient();
    bool connectToServer();
    void disconnect();
    bool sendMessage(const std::string& msg);
    void setMessageCallback(MessageCallback callback);
    bool isConnected() const;
    
    // 清空队列
    void clear();

private:
    void receiveThreadFunc();
    void sendThreadFunc();
    void reconnectThreadFunc();
    bool startDetachedServer();
    std::mutex stateMutex;  // 连接状态锁

    int sockfd;
    int reconnectMaxCount = 10; // 当次最大重连次数
    std::string socketPath;

    std::atomic<bool> isRelease;     // 是否正在释放
    std::atomic<bool> running;       // 控制接收线程
    std::atomic<bool> connected;     // 连接状态
    std::atomic<bool> reconnecting;  // 是否正在重连
    std::atomic<int>  reconnectCount = 0;  // 重连次数
    
    std::thread receiveThread;
    std::thread reconnectThread;

    // 添加发送线程和消息队列
    std::thread sendThread;
    std::queue<std::string> sendQueue;
    std::mutex sendQueueMutex;
    std::condition_variable sendQueueCond;
    std::atomic<bool> sendThreadRunning{false};

    std::mutex callbackMutex;
    MessageCallback messageCallback;

    // 删除拷贝构造函数和赋值运算符
    YYBSocketClient(const YYBSocketClient&) = delete;
    YYBSocketClient& operator=(const YYBSocketClient&) = delete;
};


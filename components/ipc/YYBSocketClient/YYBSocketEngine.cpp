//
//  YYBSocketEngine.cpp
//
//  Created by <PERSON> on 2025/6/23.
//

#include <iostream>
#include <sys/stat.h>
#include <dispatch/dispatch.h>
#include <thread>
#if __has_include(<YYBMacFusionSDK/YYBMacLogC.h>)
#import <YYBMacFusionSDK/YYBMacLogC.h>
#else
#import <YYBMacLogC.h>
#endif
#include "YYBSocketEngine.h"
#include "YYBSocketMsgData.h"
#include "YYBServerInstaller.h"

static const char* const kSocketEngineLogTag = "SocketEngine";

// 利用一个全局函数实现在多个dylib的单例模式
YYBSocketEngine& getSharedYYBSocketEngineInstance() {
    static YYBSocketEngine instance;
    return instance;
}

YYBSocketEngine& YYBSocketEngine::instance() {
    return getSharedYYBSocketEngineInstance();
}

YYBSocketEngine::YYBSocketEngine() : mClient("/tmp/yybMac.socket") {
    mMessageHandlers = std::unordered_map<std::string, MessageHandler>();
    mRequestHandlers = std::unordered_map<std::string, RequestHandler>();
    
    mClient.setMessageCallback([&](const std::string& msg) {
        try {
            YYBMacLogI(kSocketEngineLogTag, "收到消息: %s", msg.c_str());
            if (msg == "restartService") {
                cleanupPendingRequestsWhenServiceRestart();
                ::startServer();
            } else if (msg == "registerToServer") {
                registerToServer();
            } else {
                auto optMsgData = YYBSocketMsgData::from_json(msg);
                if (!optMsgData) {
                    YYBMacLogE(kSocketEngineLogTag, "解析消息失败，无效的消息格式: %s", msg.c_str());
                    throw std::runtime_error("无效的消息格式");
                }
                
                if (optMsgData->type == SocketMessageType::response) {
                    // 处理响应消息
                    handleResponse(*optMsgData);
                } else if (optMsgData->type == SocketMessageType::request) {
                    // 处理请求消息
                    handleRequest(*optMsgData);
                } else {
                    // 处理普通消息
                    handleMessage(*optMsgData);
                }
            }
        } catch (const std::exception& e) {
            YYBMacLogE(kSocketEngineLogTag, "消息处理错误: %s", e.what());
        }
    });
}


bool YYBSocketEngine::waitForServiceReady(const std::function<bool()>& handle, int timeoutSeconds) {
    using namespace std::chrono;
    auto start = steady_clock::now();
    int delayMs = 100;
    while (duration_cast<seconds>(steady_clock::now() - start).count() < timeoutSeconds) {
        if (handle()) {
            YYBMacLogI(kSocketEngineLogTag, "服务连接成功");
            return true;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(delayMs));
        if (delayMs < 1000) delayMs *= 2;
        YYBMacLogI(kSocketEngineLogTag, "尝试连接服务，延迟: %d毫秒", delayMs);
    }
    
    // 连接失败，尝试重启server
    YYBMacLogW(kSocketEngineLogTag, "连接服务超时，准备重试，剩余重试次数: %d", mRetryStartServerTimes);
    if (mRetryStartServerTimes > 0) {
        mRetryStartServerTimes--;
        YYBMacLogI(kSocketEngineLogTag, "重启服务器并尝试重新连接");
        ::startServer();
        if (waitForServiceReady(handle)) {
            YYBMacLogI(kSocketEngineLogTag, "重试连接成功");
            return true;
        }
        YYBMacLogE(kSocketEngineLogTag, "重试连接失败");
    }
    return false;
}

void YYBSocketEngine::startServer(std::string plistPath, std::string serverPath) {
    YYBMacLogI(kSocketEngineLogTag, "启动服务器, plist路径: %s, 服务器路径: %s", plistPath.c_str(), serverPath.c_str());
    if (serverPath.empty() || serverPath.find("..") != std::string::npos) {
        YYBMacLogE(kSocketEngineLogTag, "无效的服务器路径");
        return ;
    }
    
    try {
        ::installServer(plistPath, serverPath);
        YYBMacLogI(kSocketEngineLogTag, "服务器启动命令已执行");
    } catch (const std::exception& e) {
        YYBMacLogE(kSocketEngineLogTag, "启动服务器异常: %s", e.what());
    }
}


bool YYBSocketEngine::connect(const std::string& name, const int pid) {
    mName = name;
    mPid = pid;
    YYBMacLogI(kSocketEngineLogTag, "开始连接服务 - 客户端名称: %s, PID: %d", name.c_str(), pid);

    bool ret = waitForServiceReady([this]() -> bool {
        return mClient.connectToServer();;
    });

    if (!ret) {
        YYBMacLogE(kSocketEngineLogTag, "连接服务失败，所有重试均已失败");
        mClient.clear();
        return false;
    }
    
    return registerToServer();
}

void YYBSocketEngine::disconnect() {
    YYBMacLogI(kSocketEngineLogTag, "断开服务连接");
    // 清理消息处理器和请求处理器
    {
        std::unique_lock<std::shared_mutex> lock(mMessageHandlersMtx);
        YYBMacLogI(kSocketEngineLogTag, "清理消息处理器, 数量: %zu", mMessageHandlers.size());
        mMessageHandlers.clear();
    }
    
    {
        std::unique_lock<std::shared_mutex> lock(mRequestHandlersMtx);
        YYBMacLogI(kSocketEngineLogTag, "清理请求处理器, 数量: %zu", mRequestHandlers.size());
        mRequestHandlers.clear();
    }
    
    mClient.disconnect();
    YYBMacLogI(kSocketEngineLogTag, "服务连接已断开");
}

// 注册消息处理器
void YYBSocketEngine::registerMessageHandler(std::unordered_map<std::string, MessageHandler> handles) {
    YYBMacLogI(kSocketEngineLogTag, "注册消息处理器, 数量: %zu", handles.size());
    std::unique_lock<std::shared_mutex> lock(mMessageHandlersMtx);
    for (const auto& kv : handles) {
        YYBMacLogD(kSocketEngineLogTag, "注册消息处理器: %s", kv.first.c_str());
        mMessageHandlers[kv.first] = kv.second;
    }
}

// 注册单个请求处理器
void YYBSocketEngine::registerRequestHandler(const std::string& action, RequestHandler handler) {
    YYBMacLogI(kSocketEngineLogTag, "注册请求处理器: %s", action.c_str());
    std::unique_lock<std::shared_mutex> lock(mRequestHandlersMtx);
    mRequestHandlers[action] = handler;
}

// 注册多个请求处理器
void YYBSocketEngine::registerRequestHandler(std::unordered_map<std::string, RequestHandler> handlers) {
    YYBMacLogI(kSocketEngineLogTag, "注册多个请求处理器, 数量: %zu", handlers.size());
    std::unique_lock<std::shared_mutex> lock(mRequestHandlersMtx);
    for (const auto& pair : handlers) {
        YYBMacLogI(kSocketEngineLogTag, "注册请求处理器: %s", pair.first.c_str());
        mRequestHandlers[pair.first] = pair.second;
    }
}

bool YYBSocketEngine::registerToServer() {
    YYBMacLogI(kSocketEngineLogTag, "向服务器注册客户端");
    YYBSocketMsgData msg;
    msg.action = "regist";
    if (engineIsReady) {
        msg.info = std::map<std::string, std::string>{{"engineIsReady", "true"}};
    }
    bool result = sendMessage(msg);
    YYBMacLogI(kSocketEngineLogTag, "客户端注册%s", result ? "成功" : "失败");
    return result;
}

bool YYBSocketEngine::sendMessage(const YYBSocketMsgData& msg) {
    YYBSocketMsgData finalMsg = msg;
    finalMsg.type = SocketMessageType::event;
    finalMsg.from = mName;
    finalMsg.pid = mPid;
    std::string msgJson = finalMsg.to_json();
    YYBMacLogI(kSocketEngineLogTag, "发送消息: %s", msgJson.c_str());
    return mClient.sendMessage(msgJson);
}

// 异步请求方法
bool YYBSocketEngine::sendRequest(const YYBSocketMsgData& request, ResponseCallback callback, int timeout_ms) {
    YYBSocketMsgData finalRequest = request;
    finalRequest.type = SocketMessageType::request;
    finalRequest.from = mName;
    finalRequest.pid = mPid;
    finalRequest.request_id = generateRequestId();
    finalRequest.timeout_ms = timeout_ms;

    // 添加到等待响应的请求列表
    {
        std::unique_lock<std::shared_mutex> requestLock(mPendingRequestsMtx);
        mPendingRequests[finalRequest.request_id] = {callback};
    }
    
    // 发送请求
    std::string requestJson = finalRequest.to_json();
    YYBMacLogI(kSocketEngineLogTag, "发送请求: %s", requestJson.c_str());
    return mClient.sendMessage(requestJson);
}


// 生成唯一请求ID
std::string YYBSocketEngine::generateRequestId() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 35); // 0-9, a-z
    std::string chars = "0123456789abcdefghijklmnopqrstuvwxyz";
    std::string id = "";
    // 生成16位随机字符串
    for (int i = 0; i < 16; ++i) {
        id += chars[dis(gen)];
    }
    
    return id;
}

// 清理超时请求
void YYBSocketEngine::cleanupPendingRequestsWhenServiceRestart() {
    YYBMacLogI(kSocketEngineLogTag, "服务重启，清理待处理请求");
    std::vector<ResponseCallback> callbacks;
    {
        std::unique_lock<std::shared_mutex> lock(mPendingRequestsMtx);
        for (auto it = mPendingRequests.begin(); it != mPendingRequests.end();) {
            if (it->second.callback) {
                callbacks.push_back(it->second.callback);
            }
            it = mPendingRequests.erase(it);
        }
    }
    
    if (!callbacks.empty()) {
        YYBMacLogI(kSocketEngineLogTag, "服务重启，%zu 个待处理回调发送服务异常响应", callbacks.size());
        YYBSocketMsgData errorResponse;
        errorResponse.error_code = SocketError::ERROR_SERVICE;
        if (errorResponse.info.has_value()) {
            errorResponse.info.value()[ERROR_KEY] = "Service异常";
        } else {
            errorResponse.info = std::map<std::string, std::string>{{ERROR_KEY, "Service异常"}};
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            for (const auto& callback : callbacks) {
                try {
                    callback(errorResponse, errorResponse.error_code);
                } catch (const std::exception& e) {
                    YYBMacLogE(kSocketEngineLogTag, "清理回调函数异常: %s", e.what());
                }
            }
        });
    }
}

// 处理收到的响应
void YYBSocketEngine::handleResponse(const YYBSocketMsgData& response) {
    YYBMacLogI(kSocketEngineLogTag, "处理响应消息, 动作: %s", response.action.c_str());
    if (response.request_id.empty()) {
        YYBMacLogW(kSocketEngineLogTag, "收到的响应没有请求ID");
        return;
    }
    
    ResponseCallback callback = nullptr;
    {
        std::unique_lock<std::shared_mutex> lock(mPendingRequestsMtx);
        auto it = mPendingRequests.find(response.request_id);
        
        if (it != mPendingRequests.end()) {
            callback = it->second.callback;
            mPendingRequests.erase(it);
        }
    }
    
    if (callback) {
        YYBSocketMsgData responseCopy = response;
        dispatch_async(dispatch_get_main_queue(), ^{
            try {
                callback(responseCopy, responseCopy.error_code);
            } catch (const std::exception& e) {
                YYBMacLogE(kSocketEngineLogTag, "回调函数异常: %s", e.what());
            }
        });
    } else if (!response.request_id.empty()) {
        YYBMacLogW(kSocketEngineLogTag, "未找到请求ID对应的请求: %s", response.request_id.c_str());
    }
}

// 处理收到的请求并发送响应
void YYBSocketEngine::handleRequest(const YYBSocketMsgData& request) {
    YYBMacLogI(kSocketEngineLogTag, "处理请求消息, 动作: %s, 来源: %s", request.action.c_str(), request.from.c_str());
    if (request.request_id.empty()) {
        YYBMacLogW(kSocketEngineLogTag, "收到的请求没有请求ID");
        return;
    }
    
    YYBSocketMsgData requestCopy = request;
    std::string action = requestCopy.action;
    RequestHandler handler = nullptr;
    {
        std::shared_lock<std::shared_mutex> lock(mRequestHandlersMtx);
        auto it = mRequestHandlers.find(action);
        if (it != mRequestHandlers.end()) {
            handler = it->second;
        }
    }
    
    auto responseCallback = [this, requestCopy](const YYBSocketMsgData& response) {
        YYBSocketMsgData finalResponse = response;
        finalResponse.type = SocketMessageType::response;
        finalResponse.request_id = requestCopy.request_id;
        finalResponse.from = mName;
        finalResponse.pid = mPid;
        finalResponse.to = requestCopy.from;
        std::string responseJson = finalResponse.to_json();
        YYBMacLogI(kSocketEngineLogTag, "发送响应: %s", responseJson.c_str());
        bool success = mClient.sendMessage(responseJson);
        if (!success) {
            YYBMacLogE(kSocketEngineLogTag, "发送响应失败, 请求ID: %s", requestCopy.request_id.c_str());
        }
    };
    
    if (handler) {
        try {
            handler(requestCopy, responseCallback);
        } catch (const std::exception& e) {
            // 服务器异常
            YYBMacLogE(kSocketEngineLogTag, "请求处理器异常: %s, 动作: %s, 请求ID: %s", e.what(), action.c_str(), requestCopy.request_id.c_str());
            YYBSocketMsgData errorResponse;
            errorResponse.type = SocketMessageType::response;
            errorResponse.request_id = requestCopy.request_id;
            errorResponse.from = mName;
            errorResponse.pid = mPid;
            errorResponse.to = requestCopy.from;
            errorResponse.action = action;
            errorResponse.error_code = SocketError::ERROR_SERVER;
            
            if (errorResponse.info.has_value()) {
                errorResponse.info.value()[ERROR_KEY] = e.what();
            } else {
                errorResponse.info = std::map<std::string, std::string>{{ERROR_KEY, e.what()}};
            }
            
            std::string errorJson = errorResponse.to_json();
            YYBMacLogI(kSocketEngineLogTag, "发送错误响应: %s", errorJson.c_str());
            bool success = mClient.sendMessage(errorJson);
            if (!success) {
                YYBMacLogE(kSocketEngineLogTag, "发送错误响应失败, 请求ID: %s", requestCopy.request_id.c_str());
            }
        }
    } else {
        // 未找到响应
        YYBMacLogW(kSocketEngineLogTag, "未找到请求处理器, 动作: %s, 请求ID: %s", action.c_str(), requestCopy.request_id.c_str());
        YYBSocketMsgData errorResponse;
        errorResponse.type = SocketMessageType::response;
        errorResponse.request_id = requestCopy.request_id;
        errorResponse.from = mName;
        errorResponse.pid = mPid;
        errorResponse.to = requestCopy.from;
        errorResponse.action = action;
        errorResponse.error_code = SocketError::ERROR_NOT_FOUND;
        
        if (errorResponse.info.has_value()) {
            errorResponse.info.value()[ERROR_KEY] = "未找到请求处理器";
        } else {
            errorResponse.info = std::map<std::string, std::string>{{ERROR_KEY, "未找到请求处理器"}};
        }
        
        std::string errorJson = errorResponse.to_json();
        YYBMacLogI(kSocketEngineLogTag, "发送未找到处理器响应: %s", errorJson.c_str());
        bool success = mClient.sendMessage(errorJson);
        if (!success) {
            YYBMacLogE(kSocketEngineLogTag, "发送未找到处理器响应失败, 请求ID: %s", requestCopy.request_id.c_str());
        }
    }
}

// 处理普通消息
void YYBSocketEngine::handleMessage(const YYBSocketMsgData& msgData) {
    YYBMacLogI(kSocketEngineLogTag, "处理普通消息, 动作: %s, 来源: %s", msgData.action.c_str(), msgData.from.c_str());
    MessageHandler handler = nullptr;
    std::string action = msgData.action;
    
    {
        std::shared_lock<std::shared_mutex> lock(mMessageHandlersMtx);
        auto it = mMessageHandlers.find(action);
        if (it != mMessageHandlers.end()) {
            handler = it->second;
        }
    }
    
    if (handler) {
        YYBSocketMsgData msgDataCopy = msgData;
        dispatch_async(dispatch_get_main_queue(), ^{
            try {
                handler(msgDataCopy);
            } catch (const std::exception& e) {
                YYBMacLogE(kSocketEngineLogTag, "消息处理器异常: %s", e.what());
            }
        });
    } else {
        YYBMacLogW(kSocketEngineLogTag, "没有找到消息处理器: %s", action.c_str());
    }
}

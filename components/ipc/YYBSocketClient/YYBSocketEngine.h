//
//  YYBSocketEngine.h
//
//  Created by <PERSON> on 2025/6/23.
//

#pragma once
#include <functional>
#include <unordered_map>
#include <shared_mutex>
#include <chrono>
#include <random>
#include <functional>
#include <unordered_map>
#include <shared_mutex>
#include <chrono>
#include <random>
#include "YYBSocketClient.h"
#include "YYBSocketMsgData.h"

// 默认请求超时时间（毫秒）
constexpr int DEFAULT_REQUEST_TIMEOUT_MS = 30000;

// 错误描述key
const std::string ERROR_KEY = "error";

class YYBSocketEngine;
YYBSocketEngine& getSharedYYBSocketEngineInstance();

class YYBSocketEngine {
public:
    static YYBSocketEngine& instance();
    friend YYBSocketEngine& getSharedYYBSocketEngineInstance();
    bool engineIsReady = false;

    // 如果server需要更新，重启server
    void startServer(std::string plistPath, std::string serverPath);
    // 连接到服务器
    bool connect(const std::string& name, const int pid);
    void disconnect();

    // 发送消息（事件模式）
    bool sendMessage(const YYBSocketMsgData& msg);
    
    // 发送消息（请求-响应模式）
    using ResponseCallback = std::function<void(const YYBSocketMsgData&, int64_t)>;
    bool sendRequest(const YYBSocketMsgData& request, ResponseCallback callback,
                     int timeout_ms = 30000);

    // 注册消息接收handler（事件模式）
    using MessageHandler = std::function<void(const YYBSocketMsgData&)>;
    void registerMessageHandler(std::unordered_map<std::string, MessageHandler> handles);

    // 注册消息接收handler（请求-响应模式）
    using RequestHandler = std::function<void(const YYBSocketMsgData&, std::function<void(const YYBSocketMsgData&)>)>;
    void registerRequestHandler(const std::string& action, RequestHandler handler);
    void registerRequestHandler(std::unordered_map<std::string, RequestHandler> handlers);

   private:
    int mRetryStartServerTimes = 3;
    std::string mName = "";
    int mPid = -1;
    std::string serverPath = "";
    YYBSocketClient mClient;
    
    std::shared_mutex mMessageHandlersMtx;
    std::unordered_map<std::string, MessageHandler> mMessageHandlers;
    
    std::shared_mutex mRequestHandlersMtx;
    std::unordered_map<std::string, RequestHandler> mRequestHandlers;
    
    struct PendingRequest {
        ResponseCallback callback;
    };
    
    std::shared_mutex mPendingRequestsMtx;
    std::unordered_map<std::string, PendingRequest> mPendingRequests;
    
    // 生成唯一请求ID
    std::string generateRequestId();
    
    // 当YYBService重启时，清理pending请求
    void cleanupPendingRequestsWhenServiceRestart();
    
    // 处理收到的响应
    void handleResponse(const YYBSocketMsgData& response);
    
    // 处理收到的请求并发送响应
    void handleRequest(const YYBSocketMsgData& request);
    
    // 处理收到的消息
    void handleMessage(const YYBSocketMsgData& message);
    
    bool registerToServer();
    bool waitForServiceReady(const std::function<bool()>& handle, int timeoutSeconds = 5);
    YYBSocketEngine();
    YYBSocketEngine(const YYBSocketEngine&) = delete;
    YYBSocketEngine& operator=(const YYBSocketEngine&) = delete;
};

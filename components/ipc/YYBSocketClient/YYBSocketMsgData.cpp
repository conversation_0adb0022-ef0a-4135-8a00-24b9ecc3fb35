//
//  YYBSocketMsgData.cpp
//  YYBMacApp
//
//  Created by <PERSON> on 2025/6/24.
//

#if __has_include(<YYBMacFusionSDK/YYBMacLogC.h>)
#import <YYBMacFusionSDK/YYBMacLogC.h>
#else
#import <YYBMacLogC.h>
#endif
#include "YYBSocketMsgData.h"
#include "nlohmann/json.hpp"

// 日志TAG常量
static const char* const kSocketMsgLogTag = "SocketMsg";


int SafeGetInt(const nlohmann::json& j, const std::string& key, int default_value = 0) {
    if (!j.contains(key)) return default_value;
    const auto& val = j[key];
    try {
        if (val.is_number_integer()) {
            return val.get<int>();
        } else if (val.is_string()) {
            const std::string& str = val.get_ref<const std::string&>();
            if (str.empty()) return default_value;
            return std::stoi(str);
        }
    } catch (const std::exception& e) {
        YYBMacLogE(kSocketMsgLogTag, "JSON解析异常 SafeGetInt %s: %s",key.c_str(), e.what());
        return default_value;
    }
    YYBMacLogE(kSocketMsgLogTag, "JSON解析异常,key:%s", key.c_str());
    return default_value;
}

std::optional<YYBSocketMsgData> YYBSocketMsgData::from_json(
        const std::string& json_str) {
    try {
        YYBMacLogI(kSocketMsgLogTag, "开始解析Socket消息JSON: %s", json_str.c_str());
        nlohmann::json j;
        auto result = nlohmann::json::parse(json_str, nullptr, false);
        if (result.is_discarded()) {
            YYBMacLogE(kSocketMsgLogTag, "JSON解析失败，格式不正确");
            return std::nullopt;
        }
        j = result;

        if (!j.contains("type") || !j.contains("action")) {
            YYBMacLogE(kSocketMsgLogTag, "JSON缺少必要字段type或action");
            return std::nullopt;
        }
        
        YYBSocketMsgData msg;
        msg.type = j["type"].get<std::string>();
        msg.action = j["action"].get<std::string>();
        YYBMacLogI(kSocketMsgLogTag, "解析消息 - 类型: %s, 动作: %s", msg.type.c_str(), msg.action.c_str());
        
        if (j.contains("to")) {
            msg.to = j["to"].get<std::string>();
            YYBMacLogI(kSocketMsgLogTag, "消息目标: %s", msg.to.c_str());
        }
        
        if (j.contains("from")) {
            msg.from = j["from"].get<std::string>();
            YYBMacLogI(kSocketMsgLogTag, "消息来源: %s", msg.from.c_str());
        }

        if (j.contains("info") && j["info"].is_object()) {
            msg.info = j["info"].get<std::map<std::string, std::string>>();
        }
        
        if (j.contains("request_id")) {
            msg.request_id = j["request_id"].get<std::string>();
        }
        
        if (j.contains("error_code")) {
            msg.error_code = ::SafeGetInt(j, "error_code");
        }
        
        if (j.contains("timeout_ms")) {
            msg.timeout_ms = j["timeout_ms"].get<int64_t>();
        }
        
        YYBMacLogI(kSocketMsgLogTag, "Socket消息解析成功");
        return msg;
    } catch (const nlohmann::json::exception& e) {
        YYBMacLogE(kSocketMsgLogTag, "JSON解析异常: %s", e.what());
        return std::nullopt;
    } catch (const std::exception& e) {
        YYBMacLogE(kSocketMsgLogTag, "解析过程发生未知异常: %s", e.what());
        return std::nullopt;
    }
}

std::string YYBSocketMsgData::to_json() const {
    YYBMacLogI(kSocketMsgLogTag, "开始将Socket消息转换为JSON");
    nlohmann::json j;
    j["type"] = type;
    j["to"] = to;
    j["from"] = from;
    j["action"] = action;
    j["pid"] = pid;
    YYBMacLogI(kSocketMsgLogTag, "构建JSON - 类型: %s, 动作: %s", type.c_str(), action.c_str());
    if (info.has_value()) {
        j["info"] = info.value();
    }
    if (!request_id.empty()) {
        j["request_id"] = request_id;
    }
    if (error_code != 0) {
        j["error_code"] = error_code;
    }
    
    if (timeout_ms > 0) {
        j["timeout_ms"] = timeout_ms;
    }
    
    std::string result = j.dump();
    YYBMacLogI(kSocketMsgLogTag, "Socket消息JSON转换完成: %s", result.c_str());
    return result;
}
